# 国际化功能说明 / Internationalization Guide

## 概述 / Overview

本项目已实现中英文双语切换功能，基于 React Intl 构建。

This project has implemented bilingual switching between Chinese and English, built on React Intl.

## 功能特性 / Features

### ✅ 已实现 / Implemented
- 🌐 中英文语言切换
- 💾 语言偏好本地存储
- 🎯 导航菜单国际化
- 🔧 工具提示国际化
- ⚠️ 过期提示国际化
- 📱 响应式语言切换按钮

### 🚧 待完善 / To be improved
- 📝 表单验证消息国际化
- 📊 数据表格国际化
- 🔔 通知消息国际化
- 📄 页面内容国际化

## 使用方法 / Usage

### 1. 语言切换 / Language Switching
在页面右上角点击语言图标 🌐，选择所需语言：
- 中文 (Chinese)
- English

Click the language icon 🌐 in the top right corner and select your preferred language.

### 2. 在组件中使用国际化文本 / Using Internationalized Text in Components

#### 方法一：使用 LocaleFormatter 组件
```tsx
import { LocaleFormatter } from '@/locales'

// 在JSX中使用
<LocaleFormatter id="nav.overview" />
```

#### 方法二：使用 useLocale Hook
```tsx
import { useLocale } from '@/locales'

const MyComponent = () => {
    const { formatMessage } = useLocale()
    
    const text = formatMessage({ id: 'nav.overview' })
    return <div>{text}</div>
}
```

### 3. 添加新的翻译文本 / Adding New Translation Text

#### 步骤 1：在中文语言包中添加
编辑 `src/locales/zh-CN/menu/index.ts`：
```typescript
export const zhCN_menu = {
    // 主导航菜单
    'nav.overview': '总览',
    // ... 其他现有翻译
    'your.new.key': '你的中文文本',
}
```

#### 步骤 2：在英文语言包中添加对应翻译
编辑 `src/locales/en-GB/menu/index.ts`：
```typescript
export const enGB_menu = {
    // Main navigation menu
    'nav.overview': 'Overview',
    // ... 其他现有翻译
    'your.new.key': 'Your English Text',
}
```

#### 步骤 3：在组件中使用
```tsx
<LocaleFormatter id="your.new.key" />
```

## 当前可用的翻译Key / Available Translation Keys

### 导航菜单 / Navigation Menu
- `nav.overview` - 总览 / Overview
- `nav.services` - 服务 / Services
- `nav.policies` - 策略 / Policies
- `nav.logs` - 日志 / Logs
- `nav.devices` - 设备 / Devices
- `nav.users` - 用户 / Users
- `nav.resources` - 资源 / Resources
- `nav.settings` - 设置 / Settings
- `nav.networks` - 网络 / Network

### 工具提示 / Tooltips
- `tooltip.help` - 查看帮助文档 / View Help Documentation
- `tooltip.theme` - 选择主题 / Select Theme
- `tooltip.darkMode` - 切换到暗色模式 / Switch to Dark Mode
- `tooltip.lightMode` - 切换到亮色模式 / Switch to Light Mode
- `tooltip.language` - 切换语言 / Switch Language

### 语言选项 / Language Options
- `language.chinese` - 中文 / 中文
- `language.english` - English / English

### 通用文本 / Common Text
- `common.expired` - 已过期 / Expired
- `common.expiresSoon` - 后到期 / expires in
- `common.accountExpired` - 账号已过期 / Account Expired
- `common.clickToRenew` - 点击此处续费 / Click here to renew
- `common.expireTime` - 过期时间 / Expiration time

### 总览页面 / Overview Page
- `overview.lastQueryTime` - 上次查询时间 / Last Query Time
- `overview.getOverviewFailed` - 获取总览信息失败, 请稍后重试 / Failed to get overview information, please try again later

#### 刷新选项 / Refresh Options
- `overview.refresh.none` - 无 / None
- `overview.refresh.5s` - 5秒 / 5s
- `overview.refresh.10s` - 10秒 / 10s
- `overview.refresh.30s` - 30秒 / 30s
- `overview.refresh.1m` - 1分 / 1m
- `overview.refresh.5m` - 5分 / 5m
- `overview.refresh.15m` - 15分 / 15m

#### 地图组件 / Map Component
- `overview.map.title` - 全网设备分布 / Global Device Distribution
- `overview.map.totalDevices` - 总设备 / Total Devices
- `overview.map.onlineDevices` - 总在线 / Online
- `overview.map.totalUsers` - 总用户数 / Total Users
- `overview.map.onlineUsers` - 在线用户 / Online Users
- `overview.map.totalDevicesCount` - 总设备数 / Total Devices
- `overview.map.onlineDevicesCount` - 在线设备 / Online Devices
- `overview.map.city` - 城市 / City

#### 设备在线趋势 / Device Online Trend
- `overview.deviceOnline.title` - 设备连接数趋势 / Device Connection Trend
- `overview.deviceOnline.deviceCount` - 设备连接数 / Device Connections
- `overview.deviceOnline.recent6h` - 最近6小时 / Last 6 Hours
- `overview.deviceOnline.recent12h` - 最近12小时 / Last 12 Hours
- `overview.deviceOnline.recent24h` - 最近24小时 / Last 24 Hours
- `overview.deviceOnline.recent3d` - 最近3天 / Last 3 Days
- `overview.deviceOnline.recent7d` - 最近7天 / Last 7 Days

#### 设备类型分布 / Device Type Distribution
- `overview.deviceType.title` - 设备类型分布 / Device Type Distribution
- `overview.deviceType.name` - 设备类型 / Device Type

#### 网络流量 / Network Traffic
- `overview.traffic.title` - 网络流量 / Network Traffic
- `overview.traffic.recent6h` - 最近6小时 / Last 6 Hours
- `overview.traffic.recent12h` - 最近12小时 / Last 12 Hours
- `overview.traffic.recent24h` - 最近24小时 / Last 24 Hours
- `overview.traffic.recent3d` - 最近3天 / Last 3 Days
- `overview.traffic.recent7d` - 最近7天 / Last 7 Days
- `overview.traffic.upPeak` - 上行流量峰值 / Upload Peak
- `overview.traffic.downPeak` - 下行流量峰值 / Download Peak
- `overview.traffic.upTotal` - 上行流量总计 / Upload Total
- `overview.traffic.downTotal` - 下行流量总计 / Download Total
- `overview.traffic.receive` - 接收 / Receive
- `overview.traffic.send` - 发送 / Send

#### 客户端版本 / Client Version
- `overview.clientVersion.title` - 客户端版本分布 / Client Version Distribution
- `overview.clientVersion.name` - 客户端版本 / Client Version
- `overview.clientVersion.deviceType` - 设备类型 / Device Type

### 组件翻译 / Component Translations

#### 通用组件文本 / Common Component Text
- `components.common.cancel` - 取消 / Cancel
- `components.common.confirm` - 确定 / Confirm
- `components.common.close` - 关闭 / Close
- `components.common.save` - 保存 / Save
- `components.common.delete` - 删除 / Delete
- `components.common.edit` - 编辑 / Edit
- `components.common.add` - 添加 / Add
- `components.common.search` - 搜索 / Search
- `components.common.reset` - 重置 / Reset
- `components.common.submit` - 提交 / Submit
- `components.common.loading` - 加载中... / Loading...
- `components.common.noData` - 暂无数据 / No Data
- `components.common.all` - 全部 / All
- `components.common.online` - 在线 / Online
- `components.common.offline` - 离线 / Offline
- `components.common.status` - 状态 / Status
- `components.common.operatingSystem` - 操作系统 / Operating System

#### 设备选择器 / Machine Selector
- `components.machineSelector.title` - 选择设备 / Select Device
- `components.machineSelector.searchPlaceholder` - 根据用户，设备名称，IP，版本号等搜索 / Search by user, device name, IP, version, etc.

#### 客服组件 / Customer Service
- `components.customerService.title` - 联系专属客服 / Contact Customer Service
- `components.customerService.description` - 请使用微信扫码联系专属客服 / Please scan the QR code with WeChat to contact customer service

#### 主题切换 / Theme Switch
- `components.themeSwitch.feiyueCloud` - 飞越云 / Feiyue Cloud
- `components.themeSwitch.feiyueCloudColor` - 飞越云彩色主题 / Feiyue Cloud Color Theme

#### 展开组件 / Expandable
- `components.expandable.expand` - + 展开 / + Expand
- `components.expandable.collapse` - - 收起 / - Collapse

#### 激活组件 / Activation
- `components.activation.title` - 输入授权码 / Enter License Key
- `components.activation.machineCode` - 机器码 / Machine Code
- `components.activation.licenseKey` - 授权码 / License Key
- `components.activation.getLicenseKey` - 获取授权码 / Get License Key
- `components.activation.placeholder` - 请在此处粘贴或输入您的授权码 / Please paste or enter your license key here
- `components.activation.success` - 授权成功 / License activated successfully
- `components.activation.failed` - 授权失败 / License activation failed

#### 用户指示器 / User Indicator
- `components.userIndicator.profile` - 个人中心 / Profile
- `components.userIndicator.appPanel` - 应用面板 / App Panel
- `components.userIndicator.logout` - 退出登录 / Logout

#### 错误页面 / Error Page
- `components.error.goLogin` - 去登录 / Go to Login
- `components.error.backHome` - 回到首页 / Back to Home

#### 加载组件 / Loading
- `components.loading.text` - 正在加载中... / Loading...

## 技术实现 / Technical Implementation

### 架构组件 / Architecture Components

1. **App.tsx**: 语言切换Context提供者
2. **LocaleContext**: 全局语言状态管理
3. **useLocaleSwitch**: 语言切换Hook
4. **LocaleFormatter**: 国际化文本组件
5. **useLocale**: 国际化Hook

### 语言文件结构 / Language File Structure
```
src/locales/
├── index.tsx          # 国际化工具和配置
├── zh-CN/            # 中文语言包
│   ├── index.ts      # 中文主入口
│   └── menu/         # 菜单相关翻译
│       └── index.ts
└── en-GB/            # 英文语言包
    ├── index.ts      # 英文主入口
    └── menu/         # 菜单相关翻译
        └── index.ts
```

### 存储机制 / Storage Mechanism
- 语言偏好保存在 `localStorage` 中
- Key: `app-locale`
- 默认语言: `zh_CN`

## 开发指南 / Development Guide

### 添加新语言 / Adding New Languages

1. 在 `src/locales/` 下创建新的语言文件夹
2. 复制现有语言文件结构
3. 在 `App.tsx` 中添加新语言的支持
4. 在语言切换下拉菜单中添加新选项

### 最佳实践 / Best Practices

1. **命名规范**: 使用点分隔的层级命名，如 `nav.overview`
2. **文件组织**: 按功能模块组织翻译文件
3. **一致性**: 保持所有语言包的key一致
4. **测试**: 切换语言后测试所有功能

## 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **翻译不显示**: 检查翻译key是否在所有语言包中都存在
2. **语言切换无效**: 检查LocaleContext是否正确包装组件
3. **页面刷新后语言重置**: 检查localStorage读取逻辑

### 调试方法 / Debugging Methods

1. 检查浏览器控制台是否有错误
2. 验证localStorage中的语言设置
3. 确认翻译key在语言文件中存在

## 更新日志 / Changelog

### v1.6.0 (2024-12-24)
- 🛡️ 完成 `src/pages/policies` 目录下策略管理模块的全面国际化
- 📋 国际化主机别名管理(hosts)、自动审批规则(autoapprovers)、标签管理(tagowners)等组件
- 🔧 国际化策略组件选择器(components/all-selector等)
- 🌐 添加30+个策略相关的翻译key
- 🧹 清理更多未使用的变量、导入和参数
- ✅ 修复所有TypeScript警告和错误
- 🎯 策略管理模块国际化覆盖率达到95%+

### v1.5.0 (2024-12-24)
- 🛡️ 继续完善 `src/pages/policies` 目录下策略管理模块的国际化
- 📋 国际化策略组添加/编辑/删除、表达式管理、策略新建/编辑等组件
- 🌐 添加50+个策略相关的翻译key
- 🧹 清理更多未使用的变量、导入和参数
- ✅ 修复所有TypeScript警告和错误
- 🔧 优化表单验证、通知消息等用户体验

### v1.4.0 (2024-12-24)
- 🛡️ 完成 `src/pages/policies` 目录下策略管理模块的国际化
- 📋 国际化策略列表、策略组、IP组、表达式等核心功能
- 🌐 添加100+个策略相关的翻译key
- 🧹 清理大量未使用的变量、导入和参数
- ✅ 修复所有TypeScript警告和错误
- 🔧 优化表格列定义、过滤器、操作菜单等UI组件

### v1.3.0 (2024-12-24)
- 🧩 完成 `src/components` 目录下核心组件的国际化
- 🔧 国际化设备选择器、客服组件、主题切换、激活组件等
- 🌐 添加40+个组件相关的翻译key
- 🧹 清理所有未使用的变量和导入
- ✅ 修复所有TypeScript警告和错误

### v1.2.0 (2024-12-24)
- 🌐 完成 `pages/overview` 目录下所有组件的国际化
- 📊 国际化地图组件、设备在线趋势、设备类型分布、网络流量、客户端版本分布
- 🧹 清理所有未使用的变量和导入
- 🔧 修复所有TypeScript警告和错误
- 📝 添加overview相关的完整翻译key

### v1.1.0 (2024-12-24)
- 🧹 清理无用的翻译资源项
- 📝 移除未使用的示例翻译数据
- 🎯 保留实际使用的翻译key
- 📚 更新文档，添加可用翻译key列表

### v1.0.0 (2024-12-24)
- ✅ 实现基础中英文切换功能
- ✅ 添加导航菜单国际化
- ✅ 实现语言偏好持久化存储
- ✅ 添加语言切换UI组件

---

如有问题或建议，请联系开发团队。
For questions or suggestions, please contact the development team.
