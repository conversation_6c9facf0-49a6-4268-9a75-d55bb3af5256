# console-feiyue-cloud

飞越云控制台

1. 设备是否设置密码
2. 系统是否开启更新

更新json文本更新全部配置


1. 本地分页无服务
2. 远程分页有服务
3. 有服务改造功能

172.16.0.174

./flylayer-k8s install -c conf/config.yaml

#fyymagic1



# 路由展示

## 展示默认全局路由
1. 路由冲突部分突出展示
2. 显示最终生效全局路由

多个网络服务宣告冲突路由


## 本地路由表


1.展示安装flylayer前本地路由表备份
2.展示启用flylayer后实际路由表（额外显示冲突中路由） 
3.如果接受路由表与本地路由表有冲突，保留本地路由表（本地路由表实际生效），上传冲突信息，在控制台展示

## 展示设备有访问权限的路由


#  设备接受路由配置： 
1. 设备接受全局路由
2. 设备接受部分路由（设备上单独配置），以上二选一
3. 设备上设置排除路由

# 问题：

## 网络服务： 
状态：发布与未发布

# 网络服务连接器组概念
单连接器： 主
双连接器： 主-备
多连接器： 主-备-备。。。

一个特殊的设备组类型
连接器组

## 网络服务只能发布到一个连接器组


权益设置独立

四层服务
 建四层服务时都要选三层网络服务
1.直连 
a. VIP 
b. 无VIP 可能通过联接器

域名，IP，端口， 协议 （TCP，UDP）

2.负载均衡
服务节点列表

1.新建 
2.发布 
选三层网络服务，或新建一个三层网络服务，在节点层级（不同节点可能不使用一个网络服务）
如果是负载均衡，选一个负载均衡，负载均衡有端口

服务组 （发布时可修改）
设备接收的时候选中默认服务组，可修改为其他服务组



策略页面问题
1. 页面调不整算宽度插件问题
2. 新增策略红色Banner刷新问题


Banner问题
网络准入策略 提示模式改成非侵入式


系统服务无物理位置，暂不支持域名，只支持显示域名（系统内配置数据）

网络服务预填数据
1.默认网络服务
2.分段网络服务数据

网络服务删除验证：被引用后不能删除
删除时显示被引用数据

设备安装参考tailscale

删除时验证数据关系


初始化数据脚本
策略数据设备与设备组

这里添加策略需要注意的是：如果目标解析后是零信任组网 IP的需要自动执行 设置 mesh模式这个操作

管理员手册


网络设置 基础

添加零信任组网网段设置
应用面板移走

发布个人中心页面


个人中心
查看个人密钥功能接口 api_key auth_key

控制台
管理所有人密钥 api_key auth_key

策略置顶有bug


初始化：
默认用户组，设备组，策略组
新建用户， 设备，策略时选中第一个

服务组添加描述字段

策略组显示处，加popover，显示备注

这里添加策略需要注意的是：如果目标解析后是零信任组网 IP的需要自动执行 设置 mesh模式这个操作

本地推送
docker buildx build --platform linux/arm64 -f deploy/Dockerfile -t fyymagic-docker.pkg.coding.net/console/platform/console-feiyue-cloud-local:a3b45b1 . --push

docker buildx build --platform linux/amd64,linux/arm64 -f deploy/Dockerfile -t fyymagic-docker.pkg.coding.net/console/platform/console-feiyue-cloud-development:a3b45b1 . --push



1. 资源列表重构




整个网络设置收起
自定义域名解析默认收起
其他也收起


路由设置： 
1. 系统内置的，禁止修改
2. 用户设置的，修改加提示

策略筛选

准入策略

根据hostname和flynetid查设备信息，开放接口



# 数据约束功能

删除内容对策略的影响
1. 删除策略组 ，删除时提醒
2. 删除设备时直接删除策略
3. 删除设备组时直接删除策略
4. 删除用户与用户组时直接删除策略
5. 删除标签时直接删除策略
6. 删除资源组时删除策略
7. 删除服务与服务组时删除策略

删除设备时从连接器组删除

删除用户时删除设备

修改帐号可用字段时问题
删除服务可见范围时检查

修改中继服务配置时检查

属性编辑时检查？

推送目标编辑时检查
推送事件编辑时检查


源->目标 策略查看


安能升级注意事项

1. 用户属性设置
2. 远程桌面未开

策略中设备演示为 设备名（IP）



删除用户时删除tagowner

密钥，描述放到表格里
