{"name": "console-feiyue-cloud", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "NODE_ENV=development VITE_LOCAL_PAGER_AND_FILTER=true VITE_USE_LICENSE=false VITE_USE_DEVELOP_FEATURE=false  VITE_USE_POLICY_PATCH=true VITE_USE_KEY=true vite --host", "zto56": "NODE_ENV=development VITE_LOCAL_PAGER_AND_FILTER=true VITE_USE_LICENSE=false VITE_USE_DEVELOP_FEATURE=false VITE_USE_POLICY_PATCH=true VITE_USE_KEY=true vite", "std": "NODE_ENV=development VITE_LOCAL_PAGER_AND_FILTER=true VITE_USE_LICENSE=true VITE_USE_DEVELOP_FEATURE=false VITE_USE_POLICY_PATCH=true vite --host", "zto": "NODE_ENV=development VITE_LOCAL_PAGER_AND_FILTER=false VITE_USE_LICENSE=false VITE_USE_DEVELOP_FEATURE=false VITE_USE_KEY=true vite", "ane": "NODE_ENV=development VITE_LOCAL_PAGER_AND_FILTER=false VITE_USE_LICENSE=false VITE_USE_DEVELOP_FEATURE=false VITE_USE_KEY=true VITE_USE_POLICY_PATCH=true vite", "devw": "set NODE_ENV=development VITE_LOCAL_PAGER_AND_FILTER=false VITE_USE_LICENSE=false VITE_USE_DEVELOP_FEATURE=false VITE_USE_KEY=true && vite", "build": "tsc && vite build", "buildLocal": "NODE_ENV=product VITE_LOCAL_PAGER_AND_FILTER=true tsc && vite build", "preview": "vite preview", "prepare": "husky install"}, "dependencies": {"@buf/flylayer_api.bufbuild_connect-es": "^0.13.0-20250822112031-1b579474e32b.3", "@buf/flylayer_logs.bufbuild_es": "^1.10.0-20240702142859-10f12bf8cd7f.1", "@buf/flylayer_logs.connectrpc_es": "^1.4.0-20240702142859-10f12bf8cd7f.3", "@bufbuild/connect": "^0.13.0", "@bufbuild/connect-web": "^0.13.0", "@douyinfe/semi-ui": "^2.46.1", "@monaco-editor/react": "^4.6.0", "@semi-bot/semi-theme-feiyue-cloud": "^1.0.8", "@semi-bot/semi-theme-feiyue-cloud-color": "^1.0.0", "axios": "^1.7.7", "comment-json": "^4.2.3", "console-feiyue-cloud": "file:", "diff-match-patch": "^1.0.5", "dompurify": "^3.2.5", "echarts": "^5.4.2", "echarts-for-react": "^3.0.2", "handlebars": "^4.7.7", "js-base64": "^3.7.7", "json5": "^2.2.3", "lodash": "^4.17.21", "moment": "^2.29.4", "monaco-editor": "^0.50.0", "papaparse": "^5.4.1", "query-string": "^8.1.0", "react": "^18.2.0", "react-device-detect": "^2.2.3", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.4", "react-infinite-scroll-component": "^6.1.0", "react-intl": "^6.4.1", "react-qr-code": "^2.0.11", "react-router-dom": "^6.10.0", "reset-css": "^5.0.1", "semver": "^7.5.4", "tiny-pinyin": "^1.3.2"}, "devDependencies": {"@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@types/dompurify": "^3.0.2", "@types/lodash": "^4.14.195", "@types/papaparse": "^5.3.14", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.43.0", "@vitejs/plugin-react-swc": "^3.7.0", "eslint": "^8.0.1", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.32.2", "husky": "^8.0.3", "react-dnd": "^16.0.1", "sass": "^1.62.0", "typescript": "*", "vite": "^5.3.4", "vite-plugin-semi-theme": "^0.5.0", "vite-plugin-static-copy": "^1.0.6"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}