import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
// import SemiPlugin from "vite-plugin-semi-theme";

import {viteStaticCopy} from 'vite-plugin-static-copy';

import { resolve } from 'path'
// https://vitejs.dev/config/
export default defineConfig({
  base: '/mesh',
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    }
  },
  plugins: [
    react(),    
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/monaco-editor/min/vs',
          dest: 'public'
        }
      ]
    })
    // SemiPlugin({
    //   theme: "@semi-bot/semi-theme-feiyue-cloud"
    // }),
  ],
  server: {
    base: '/mesh',
    proxy: {
      // '/api/': {
      //   "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
      //   "changeOrigin": true, // 允许域名进行转换
      //   "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1751638615,1753846873; dev_sandbox_feiyue_cloud_session_continuity=MTc1NTkzOTE4NHxEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fD7BkO3_zriAF0NrdQh23OZ33F0YuLBuG0sUENQH2VwT; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=ctRQiyPNEU0Fifr6UyxydEJ6VtE3S/aWI3Ilz/2IZFc=; dev_sandbox_feiyue_cloud_session=MTc1NTkzOTE4NXwzSE5QQ2ppOXBrX1ZRMTR6LWlWcDl0a0FtMFlyNVdLbzN0NHNrOHdWNE1LNEpWNHI4SEdUZm9xcEVRaVMzR3ZTbnliYTEyYVBQb0RfcTJoTzFQY2J5SXNxb0I2UjJVRm15RHExV3o3SzRKZzRKLWJOOFlTZlBZeU1FdjgzU3lranBvMFhwZmZYRVFDSWhxcWdPWG03LXIyUWJNSFZ0TXdpYnRCdlBCQm1IZ1RQY3huQTZONGNzR3d1TzVZV2NPMjVVUnVMaVRkWjNTSk5kUTRiQzFBamdKYWd6aXZ5UUE9PXx0ONxLkAe1wue-Ind1mUNY15MhstIO1B09b8XZNQNdVw==; dev_sandbox_feiyue_cloud_session=MTc1NTkzOTE4NXxIS29PWm1FRGdld183MXN2c3VhcDJBQ2RkSWJVeTJwR1RLYlVKSjJOLTZ0YXZOT0dIOXprU25jZVNfQkxYSHU3ZGtIYXpmZEtEc2U4TzdYT29DWUtnbW9MdjBwQUN6em92NXRGV01EWllwb0s4d0ZfUW9mcTJnRFA1TE9XOXpQN0RONlVsTXNtdll2REJLZUV4YXZTUWdmeW96UXJ2ZUc2Ykc2bXZzU1VSWVBadlFLOEZua05RLVNGRVN1Q1JIV1B4bm12Wi1FRDhkeXBvZGFpZUYwMXBVY3BkUzZpVmc9PXyFLjlN-uEsCttDQwiusxC2gAPGsuOCNg1ePtYc1AxzDQ==" },
      // },
      '/api/': {
        "target": "http://localhost:8080/", // 服务端域名
        changeOrigin: true,
        secure: false, // Set to true if your API uses HTTPS
        rewrite: (path) => path.replace(/^\/api\/mesh/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            proxyReq.setHeader('Authorization', `Bearer RXwrieAYFJyY_DbLlTElUcaBLUZdaIotRlC`)
            proxyReq.setHeader('Accept', 'application/json')
          })
        }
      },
      '/auth/sessions/whoami': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
        "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1751638615,1753846873; dev_sandbox_feiyue_cloud_session_continuity=MTc1NTkzOTE4NHxEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fD7BkO3_zriAF0NrdQh23OZ33F0YuLBuG0sUENQH2VwT; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=ctRQiyPNEU0Fifr6UyxydEJ6VtE3S/aWI3Ilz/2IZFc=; dev_sandbox_feiyue_cloud_session=MTc1NTkzOTE4NXwzSE5QQ2ppOXBrX1ZRMTR6LWlWcDl0a0FtMFlyNVdLbzN0NHNrOHdWNE1LNEpWNHI4SEdUZm9xcEVRaVMzR3ZTbnliYTEyYVBQb0RfcTJoTzFQY2J5SXNxb0I2UjJVRm15RHExV3o3SzRKZzRKLWJOOFlTZlBZeU1FdjgzU3lranBvMFhwZmZYRVFDSWhxcWdPWG03LXIyUWJNSFZ0TXdpYnRCdlBCQm1IZ1RQY3huQTZONGNzR3d1TzVZV2NPMjVVUnVMaVRkWjNTSk5kUTRiQzFBamdKYWd6aXZ5UUE9PXx0ONxLkAe1wue-Ind1mUNY15MhstIO1B09b8XZNQNdVw==; dev_sandbox_feiyue_cloud_session=MTc1NTkzOTE4NXxIS29PWm1FRGdld183MXN2c3VhcDJBQ2RkSWJVeTJwR1RLYlVKSjJOLTZ0YXZOT0dIOXprU25jZVNfQkxYSHU3ZGtIYXpmZEtEc2U4TzdYT29DWUtnbW9MdjBwQUN6em92NXRGV01EWllwb0s4d0ZfUW9mcTJnRFA1TE9XOXpQN0RONlVsTXNtdll2REJLZUV4YXZTUWdmeW96UXJ2ZUc2Ykc2bXZzU1VSWVBadlFLOEZua05RLVNGRVN1Q1JIV1B4bm12Wi1FRDhkeXBvZGFpZUYwMXBVY3BkUzZpVmc9PXyFLjlN-uEsCttDQwiusxC2gAPGsuOCNg1ePtYc1AxzDQ==" },
      },
      '/auth/self-service/logout/browser': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
      '/auth/self-service/logout': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
    }
  }
})