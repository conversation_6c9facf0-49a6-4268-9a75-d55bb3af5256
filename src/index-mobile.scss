// 移动端右上角菜单切换器，在PC情况下显示
.mobile-toggle {
    display: none;
}

.semi-layout-header {
    width: 100%!important;
}

// 移动端的一些适配样式
.app-mobile {
    .mobile-visible {
        display:unset;
    }
    // 页面边距重设
    .general-page,
    .settings-page {
        padding: 20px 20px 0 20px;
    }

    h1 {
        font-size: 24px!important;
    }

    header {

        // 移动端右上角菜单切换器，在移动端情况下显示
        .mobile-toggle {
            display: block;
            position: absolute;
            content: '';
            width: 32px;
            height: 32px;
            right: 7px;
            top: 14px;
        }

        .semi-navigation-horizontal {
            padding-right: 40px !important;
        }

        .semi-navigation-header {
            margin-right: 0 !important;
            // width: 160px;
            >div {
                // width: auto !important;
            }
        }

        .semi-navigation-footer {
            padding-right: 20px;
            width: auto !important;
        }

        // 移动端的导航栏其他项目隐藏
        .semi-navigation-list-wrapper {

            ul {
                display: none;
                width: 100%;
                position: absolute;
                flex-direction: column;
                left: 0;
                top: 60px;
                z-index: 1;
                padding-top: 10px !important;
                background-color: var(--semi-color-bg-1) !important;

                li {
                    width: 100%;
                    // padding-left: 20px!important;
                    margin-right: 0 !important;
                    margin-bottom: 5px !important;
                    background-color: var(--semi-color-bg-1) !important;
                }

                li:hover {
                    background-color: var(--semi-color-fill-0) !important;
                }

                border-bottom: 1px solid var(--semi-color-border);
                // 朝下方阴影
                box-shadow: var(--semi-shadow-elevated);
            }
        }

        .mobileMenuVisible {
            ul {
                display: block !important;
            }
        }
    }

    // 搜索栏需要添加 .search-bar 类名
    .search-bar {
        >.semi-layout-has-sider {
            flex-direction: column;

            >main {
                padding-right: 0 !important;
                margin-bottom: 10px;
            }

            .semi-layout-sider-children {
                >div {
                    display: block;
                    width: 100%;

                    >div {
                        width: 100% !important;
                        margin-bottom: 10px;
                    }
                }
            }

        }

    }

    // space布局搜索栏需要添加 .space-search-bar 类名
    .space-search-bar {
        flex-direction: column;

        >div, button {
            width: 100% !important;
        }
    }

    // 表格样式重置
    table {

        th,
        td,
        colgroup {
            display: none !important;
        }

        // 表格只显示第一列和最后一列
        th:first-of-type,
        td:first-of-type {
            display: table-cell !important;
            width: 80% !important;
        }

        th:last-of-type,
        td:last-of-type {
            display: table-cell !important;
            width: 20% !important;
        }
    }

    // 分页样式重置
    .semi-table-pagination-outer {
        .semi-table-pagination-info {
            // display: none !important;
        }

        .semi-page-item {
            display: none !important;
        }

        .semi-page-prev,
        .semi-page-next {
            display: flex !important;
        }
    }


    // 日志表格
    .log-table {
        table {


            // 表格只显示第一列和最后一列
            th:first-of-type,
            td:first-of-type {
                display: table-cell !important;
                width: 10% !important;
            }

            th:last-of-type,
            td:last-of-type {
                display: table-cell !important;
                width: 90% !important;
            }
        }

    }

    // 详情表格
    .semi-descriptions {

        th,
        td,
        colgroup {
            display: unset !important;
        }

        // 表格只显示第一列和最后一列
        th:first-of-type,
        td:first-of-type {
            // display: table-cell !important;
            width: unset !important;
        }

        th:last-of-type,
        td:last-of-type {
            // display: table-cell !important;
            width: unset !important;
        }

        .semi-descriptions-item {
            padding-right: 10px !important;
        }
    }

    // 对话框样式重置
    .semi-modal {
        width: 100% !important;

        .semi-modal-content {
            border-radius: 0 !important;

            .semi-modal-body {
                padding-left: 0 !important;
                padding-right: 0 !important;
            }
        }
    }

    .semi-layout-has-sider {
        flex-direction: column;

        aside {
            min-height: auto !important;
            width: 100% !important;

            .semi-layout-sider-children {
                width: 100% !important;

                .semi-navigation {
                    position: static;
                    width: 100% !important;
                    padding-left: 20px !important;
                    padding-right: 20px !important;
                }
            }
        }
    }

    // 隐藏边栏
    main>section>.semi-layout-sider.mobile-hide {
        display: none !important;
    }

    // 标签页样式重置
    .semi-tabs-bar {
        .semi-tabs-tab {
            padding-left: 6px !important;
            padding-right: 6px !important;
            margin-right: 0 !important;
        }

        .semi-tabs-bar-extra {
            display: none !important;
        }
    }

    .semi-tabs-content {
        .semi-layout {
            aside {
                display: none;
            }
        }
    }

    form {

        .semi-row {
            min-width: unset !important;
            padding-bottom: 5px !important;
            margin-bottom: 10px !important;
        }

        .semi-form-field {
            padding: 0 0 10px 0 !important;
        }

        .semi-col {
            padding: 0 10px 0 0 !important;

            button {
                padding: 0 !important;
                margin: 0 0 20px 0 !important;
                height: auto !important;
            }
        }
    }

    .semi-select {
        width: 100% !important;
    }


    // 用户向导页
    .guide-list {
        .semi-list-items {
            flex-direction: column;

            .semi-list-item {
                padding: 12px 24px 12px 12px !important;
                border-right: none!important;
                border-radius: var(--semi-border-radius-large);
                margin-bottom: 10px!important;
                .semi-list-item-body-main>div {
                    width: 100%;
                    display: flex;
                    // align-items: center;

                    .semi-avatar {
                        flex-shrink: 0;
                        width: 50px;
                        height: 50px;
                        margin-right: 10px;
                    }

                    h4 {
                        font-size: 14px;
                        margin-bottom: 0 !important;
                        text-align: left;
                    }

                    p {
                        font-size: 12px;
                        text-align: left;
                    }
                }

            }
        }
    }

    .traffic-title {
        flex-direction: column;
    }

    .semi-modal-confirm {
        .semi-modal-withIcon {
            margin-left: 24px;
        }
    }
}