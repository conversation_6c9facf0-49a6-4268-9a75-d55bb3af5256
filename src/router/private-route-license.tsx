import React, { FC, ReactElement, ReactNode, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { useLocation } from 'react-router'
import { Spin } from '@douyinfe/semi-ui';
import { UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";

import Error from '@/components/error'

import { BASE_PATH, DEFAULT_ROUTER } from '@/constants/router'

import { ErrorBoundary } from "react-error-boundary";
import useUserProfile, { UserProfileContext } from '@/hooks/useUserProfile';
import useFlynetGeneral, { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import useGlobalConfig, { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { useLocale } from '@/locales';

import { useLicense, LicenseContext } from '@/hooks/useLicense';

const PrivateRouteLicense = (props: any) => {
	const { formatMessage } = useLocale();
	const { isSignIn, isSignQuery, userProfile } = useUserProfile();
	const { isFlynetQuery, flynetGeneral, isFlynetGeneralSelected } = useFlynetGeneral();
	const { isGlobalConfigQuery, globalConfig } = useGlobalConfig();

	
	const { license } = useLicense();

	const location = useLocation()
	const { pathname } = location

	const pathFlynetSelector = BASE_PATH + '/flynet-selector';

	const isSelectorPath = pathname.indexOf('/flynet-selector') >= 0;

	if (isSignQuery || isFlynetQuery || isGlobalConfigQuery) {
		return <div style={{ display: 'flex', alignContent: 'center', justifyContent: 'center', height: '100vh', alignItems: 'center' }}><Spin size='large' /></div>;
	}
	if (!isSelectorPath && !isFlynetGeneralSelected) {
		window.location.pathname = pathFlynetSelector;
	}
	if (isSignIn) {
		
		// if(VITE_LOCAL_PAGER_AND_FILTER && pathname === BASE_PATH + '/policies') {
		// 	return <Navigate to={{ pathname: `${BASE_PATH}/policies/acl` }} replace />
		// }
		

		if (flynetGeneral.userRole == UserRole.FLYNET_USER.toString()
			&& (pathname === BASE_PATH + '/' || pathname === BASE_PATH)
		) {
			return <Navigate to={BASE_PATH + '/download'} replace />
		}

		if (document.title && document.title.indexOf(globalConfig.name) < 0) {
			document.title = document.title + (globalConfig.name ? ' - ' + globalConfig.name : '')
		}
		return <ErrorBoundary fallback={<></>} onError={() => { window.location.reload(); }}><UserProfileContext.Provider value={userProfile}>
			<GlobalConfigContext.Provider value={globalConfig}>
				<FlynetGeneralContext.Provider value={flynetGeneral}>
					<LicenseContext.Provider value={license}>
					{pathname === BASE_PATH + '/' || pathname === BASE_PATH ? (
						<Navigate to={{ pathname: DEFAULT_ROUTER }} replace />
					) : (
						props.element
					)}
					</LicenseContext.Provider>
					</FlynetGeneralContext.Provider></GlobalConfigContext.Provider></UserProfileContext.Provider></ErrorBoundary>;
	} else {
		return <><Error title={formatMessage({ id: 'router.noPermission' })} description={formatMessage({ id: 'router.notLoggedIn' })} type="401" /></>
	}

}

export default PrivateRouteLicense
