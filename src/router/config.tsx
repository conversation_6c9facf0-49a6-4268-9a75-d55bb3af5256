import { FC, Suspense, useContext } from 'react'
import { PathRouteProps } from 'react-router'
import PrivateRoute from './private-route'
import PrivateRouteLicense from './private-route-license'
import useGlobalConfig, { GlobalConfigContext } from '@/hooks/useGlobalConfig'
import { VITE_USE_LICENSE } from '@/utils/service';
import { useLocale } from '@/locales';

export interface WrapperRouteProps extends PathRouteProps {
	/** document title id */
	title: string
	/** authorization */
	auth?: boolean
}

const PublicRoute = (props: any) => {
	return props.element
}
const WrapperRouteComponent: FC<WrapperRouteProps> = ({ title, auth, ...props }) => {
	const { formatMessage } = useLocale();
	const WitchRoute = auth ? ( VITE_USE_LICENSE ? PrivateRouteLicense : PrivateRoute) : PublicRoute
	if(title) {
		// If title contains '.', treat it as translation key
		const translatedTitle = title.includes('.') ? formatMessage({ id: title }) : title;
		document.title = translatedTitle;
	}
	return <WitchRoute {...props} />
}


export interface WrapperPageProps extends PathRouteProps {
	title: string
}
const WrapperPageComponent: FC<WrapperPageProps> = ({ title, ...props }) => {
	const { formatMessage } = useLocale();
    const globalConfig = useContext(GlobalConfigContext);
	if(title) {
		// If title contains '.', treat it as translation key
		const translatedTitle = title.includes('.') ? formatMessage({ id: title }) : title;
		document.title = translatedTitle + (globalConfig.name ? ' - ' + globalConfig.name : '')

	}
	return <>{props.element}</>
}

const WrapperRouteWithOutLayoutComponent: FC<WrapperRouteProps> = ({ title, auth, ...props }) => {
		const { formatMessage } = useLocale();
		const { globalConfig } = useGlobalConfig();

	if (title) {
		// If title contains '.', treat it as translation key
		const translatedTitle = title.includes('.') ? formatMessage({ id: title }) : title;
		document.title = translatedTitle + (globalConfig.name ?  ' - ' + globalConfig.name : '')
	}
	return <Suspense fallback={<></>}>{props.element}</Suspense>
}
export { WrapperRouteComponent, WrapperRouteWithOutLayoutComponent, WrapperPageComponent }