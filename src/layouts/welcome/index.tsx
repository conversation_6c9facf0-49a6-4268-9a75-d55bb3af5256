import React, { Suspense, useContext } from 'react'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import { Layout, Nav, Popover, Skeleton, Button } from '@douyinfe/semi-ui';
import { IconUser, IconSetting, IconServer, IconLock, IconFolder, IconHelpCircle, IconGlobeStroke, IconWifi } from '@douyinfe/semi-icons';
import logo from '@/assets/logo.png';
import UserIndicator from '@/components/user-indicator';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';

import styles from './index.module.scss'
import { BASE_PATH } from '@/constants/router';

import { UserProfileContext } from '@/hooks/useUserProfile';
const { Header, Content } = Layout;

const Index: React.FC = () => {
    // 用户登录信息
    const userProfile = useContext(UserProfileContext)
    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);
    const navigate = useNavigate()

    const location = useLocation();

    let pathName = location.pathname.replace(BASE_PATH, '');
    // 取第一级路径
    if (pathName.split('/').length > 2) {
        pathName = `/${pathName.split('/')[1]}`;
    }

    return <Layout className={styles.layout}>
        <Header className={styles.header}>
            <div>
                <Nav mode="horizontal" selectedKeys={[pathName]}>
                    <Nav.Header>
                        <div className={styles.logo} onClick={() => navigate(`${BASE_PATH}`)}><img src={globalConfig.logo}></img><span>{globalConfig.name}</span></div>
                    </Nav.Header>
                    <Nav.Footer>
                        {/* <a href={`${BASE_PATH}/download`} className={styles.navLink}>下载</a>    */}

                        <a href={`/docs`} title='文档' target='_blank' className={styles.navIcon}><IconHelpCircle size='large' title='文档' /></a>
                        {userProfile.identity?.traits?.email && <UserProfileContext.Provider value={userProfile}>
                            <UserIndicator></UserIndicator>
                        </UserProfileContext.Provider>}
                        
                    </Nav.Footer>
                </Nav>
            </div>
        </Header>
        <Content className={styles.content}>
            <Suspense fallback={<Skeleton placeholder={<div className='general-page'>
                <Skeleton.Title style={{ height: 32 }} className='mb10' />
                <Skeleton.Paragraph className='mb40'></Skeleton.Paragraph>
                <Skeleton.Image style={{ height: 200 }} /></div>} loading={true}></Skeleton>}>
                <Outlet />
            </Suspense>
        </Content>

    </Layout>
}

export default Index;