.logo {
    margin-left: -10px;
    display: flex;
    align-items: center;
    position: relative;
    margin-right: 20px;
    min-width: 180px;
    
    img {
        width: 40x;
        height: 40px;
        cursor: pointer;
        flex-shrink: 0;
    }

    
    .logoText {
        cursor: pointer;
        font-family: <PERSON><PERSON>, -apple-system, BlinkMacSystemFont, <PERSON><PERSON>, roboto slab, droid serif, segoe ui, system-ui, Arial, sans-serif;
        margin-left: 10px;
        color: var(--semi-color-text-1);
        flex-shrink: 0;
    }
    .logoBadge {
        cursor: pointer;
        font-size: 12px;
        // position: absolute;
        // right: 20px;
        margin-top: -20px;
        margin-left: 4px;
        background-color: var(--semi-color-danger);
        color: #fff;
        border-radius: 4px;
        padding-left: 4px;
        padding-right: 4px;
        height: 14px;
        line-height: 14px;
        flex-shrink: 0;
    }
}

.layout {
    // height: 100vh;
}

.content {
    padding-top: 60px;
    min-height: calc(100vh - 60px);
    // min-height: 100vh;
}

.header {
    top: 0;
    width: calc(100% - 5px);
    position: fixed;
    background-color: #fff;
    z-index: 2;
}

.navLink {
    font-size: 14px;
    line-height: 1;
    margin-right: 20px;
    color: var(--semi-color-text-2);
}

.navIcon {
    margin-right: 20px;
    line-height: 1;
    width: 20px;
    height: 20px;
    color: var(--semi-color-text-2);
}

.navIconLanguage {
    margin-right: 20px;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    height: 20px;
    color: var(--semi-color-text-2);
    cursor: pointer;
}

.footer {

    color: var(--semi-color-white);
    background-color: var(--semi-color-primary);
    padding-left: 24px;
    padding-right: 24px;
    
    display: flex;
    align-items: center;
    height: 60px;
    .logoRevert {
        cursor: pointer;
        margin-left: -10px;
        display: flex;
        align-items: center;
        img {
            width: 40x;
            height: 40px;
        }

        span {
            font-family: Mija, -apple-system, BlinkMacSystemFont, Roboto, roboto slab, droid serif, segoe ui, system-ui, Arial, sans-serif;
            margin-left: 10px;
        }
    }
    .memo {

        flex-grow: 1;
        text-align: right;
        font-size: 12px;
        color: var(--semi-color-text-3);
        a {
            color: var(--semi-color-text-3);
        }
    }
}

.headerNav {
    li[role="menuitem"] {
        margin-right: 20px !important;
    }
    
}