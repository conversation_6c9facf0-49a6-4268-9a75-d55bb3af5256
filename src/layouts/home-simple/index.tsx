import React, { Suspense, useEffect, useState, useContext } from 'react';
import { Layout, Nav, Tooltip } from '@douyinfe/semi-ui';
import { useNavigate, Outlet } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { IconDownload, IconHelpCircle, IconColorPalette, IconMoon, IconSun, IconBox, IconLanguage } from '@douyinfe/semi-icons';
import { LocaleFormatter, useLocale } from '@/locales';
import styles from './index.module.scss';
import UserIndicator from '@/components/user-indicator';
import useFlynetGeneral from '@/hooks/useFlynetGeneral'

import { GlobalConfigContext } from '@/hooks/useGlobalConfig'

import ThemeSwitch from '@/components/theme-switch';
import { useGlobalTheme, GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { useLocaleSwitch } from '@/hooks/useLocale';

import axios from 'axios';
import { VITE_LOCAL_PAGER_AND_FILTER } from '@/utils/service';
const { Header, Content } = Layout;
const Index: React.FC = () => {

    const { flynetGeneral } = useFlynetGeneral();
    const navigate = useNavigate()
    const [showConsole, setShowConsole] = React.useState(false);
    const { colorMode, theme, setTheme, switchColorMode } = useGlobalTheme();

    // 语言切换相关
    const { locale, switchLocale } = useLocaleSwitch();
    const { formatMessage } = useLocale();
    useEffect(() => {
        axios.post(`/api${BASE_PATH}/flylayer.v1.FlylayerService/ListFlynets`, {}).then(res => {
            setShowConsole(true)
        }).catch(err => {
            setShowConsole(false)
        })
    }, [])

    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);


    let pathName = location.pathname.replace(BASE_PATH, '');
    // 取第一级路径
    if (pathName.split('/').length > 2) {
        pathName = `/${pathName.split('/')[1]}`;
    }
    // 主题切换对话框是否显示
    const [themeSwitchVisible, setThemeSwitchVisible] = useState(false)

    return <><Layout className={styles.layout}>
        <Header>
            <div>
                <Nav mode="horizontal" selectedKeys={[pathName]}>
                    <Nav.Header>
                        <div className={styles.logo} onClick={() => navigate(`${BASE_PATH}`)}><img src={globalConfig.logo}></img><span>{globalConfig.name}</span></div>
                    </Nav.Header>
                    {flynetGeneral.applicationEnabled && <Nav.Item itemKey="/start" onClick={() => navigate(`${BASE_PATH}/start`)} text={<LocaleFormatter id="menu.applicationPanel" />} icon={<IconBox />} />}
                    <Nav.Item itemKey="/download" onClick={() => navigate(`${BASE_PATH}/download`)} text={<LocaleFormatter id="nav.download" />} icon={<IconDownload />} />
                    {/* <Nav.Item itemKey="/docs" onClick={() => location.href = `/docs/`} text="文档" icon={<IconHelpCircle />} /> */}

                    <Nav.Footer>
                        {showConsole && (flynetGeneral.userRole == 'USER_ROLE_SUPER_ADMIN' || flynetGeneral.userRole == 'USER_ROLE_FLYNET_ADMIN' || flynetGeneral.userRole == 'USER_ROLE_FLYNET_OWNER') ? <a href={`${BASE_PATH}`} className={styles.navLink}><LocaleFormatter id="menu.adminConsole" /></a> : ''}
                        {(!globalConfig.customized || VITE_LOCAL_PAGER_AND_FILTER) && <Tooltip content={<LocaleFormatter id="tooltip.userManual" />}><a href={`https://docs.flylayer.com/std/user-manual/v1?from=50001`} title='文档' target='_blank' className={styles.navIcon}><IconHelpCircle size='large' title='文档' /></a></Tooltip>}

                        {/* 语言切换 */}
                        <Tooltip content={<LocaleFormatter id="tooltip.language" />}>
                                <a className={styles.navIconLanguage} onClick={() => {
                                    switchLocale(locale === 'zh_CN' ? 'en_GB' : 'zh_CN')
                                }}>
                                    <IconLanguage size='large' /> &nbsp;{locale === 'zh_CN' ? '中文' : 'EN'}
                                </a>
                        </Tooltip>
                        {/* {(!globalConfig.customized || VITE_LOCAL_PAGER_AND_FILTER) && <Tooltip content={'查看用户手册'}><a href='https://fyymagic.feishu.cn/docx/GqgAdmKw3o4ozGxzLzJckZTGnWd' title='用户手册' target='_blank' className={styles.navIcon}><IconArticle size='large' title='用户手册' /></a></Tooltip>} */}
                        
                        <Tooltip content={<LocaleFormatter id="tooltip.theme" />}>
                            <a className={styles.navIcon} onClick={() => setThemeSwitchVisible(true)}>
                                <IconColorPalette size='large' />
                            </a>
                        </Tooltip>
                        <Tooltip content={<LocaleFormatter id={colorMode == 'light' ? 'tooltip.switchToDark' : 'tooltip.switchToLight'} />}>
                            <a className={styles.navIcon} onClick={() => switchColorMode()}>
                                {colorMode == 'light' ? <IconMoon size='large' /> : <IconSun size='large' />}
                            </a>
                        </Tooltip>

                        <UserIndicator></UserIndicator>

                    </Nav.Footer>
                </Nav>
            </div>
        </Header>
        <Content
            className={styles.content}
        >
            <Suspense fallback={<></>}>
                <GlobalThemeContext.Provider value={{ colorMode, theme }}>
                    <Outlet />
                </GlobalThemeContext.Provider>
            </Suspense>
        </Content>
    </Layout>
        {/**主题选择弹出框 */}
        {themeSwitchVisible && <ThemeSwitch
            visible={themeSwitchVisible}
            theme={theme}
            setTheme={setTheme}
            onClose={() => setThemeSwitchVisible(false)}
        ></ThemeSwitch>}
    </>
}
export default Index;
