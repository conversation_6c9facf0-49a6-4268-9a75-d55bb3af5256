import React from 'react'
import Error from '@/components/error';
import { useParams } from 'react-router-dom';

const Index: React.FC = () => {
    
    const params = useParams<{ type: string }>()
    const type = params.type ? params.type : '403'
    
    // let type: '404' | '403' | '401' = '403';

    let message = '';
    if (type === '401') {
        message = '登录超时 请重新登录';
    }else if (type === '403') {
        message = '您没有权限访问该页面';
    } else if (type === '404') {
        message = '您访问的页面不存在';
    } else {
        message = '出错了';
    }

    return <div>
        <Error type={type} description={message}/> 
    </div>
}

export default Index;
