import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { AccountSchema } from '@/interface/account-schema';
import { localeConfig } from '@/locales';

// 获取当前语言的翻译函数
const getTranslation = (key: string): string => {
    const currentLocale = localStorage.getItem('app-locale') || 'zh_CN';
    const messages = localeConfig[currentLocale] || localeConfig['zh_CN'];
    return messages[key] || key;
};

export const getAccountSchema = (schema_url: string): Promise<AccountSchema> => {
    return new Promise<AccountSchema>((reslove, reject) => {
        axios.get(schema_url).then(res => {
            let data = res.data;
            if (res.data && res.data.properties && res.data.properties.traits) {
                reslove(res.data.properties.traits);
            } else {
                reject(new Error(getTranslation('services.user.fetchUserFormFailed')));
            }

        }, err => {
            console.error(err);
            reject(err);
        });
    });
}