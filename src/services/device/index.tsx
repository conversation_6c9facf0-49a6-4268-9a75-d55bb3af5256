/**
 * 设备所有的与后台数据相关逻辑放到这里集中管理, TODO: 迁移分散调用
 */
import { flylayerClient } from '@/services/core';
import { isValidIP } from '@/utils/validators';
import { Machine, ListMachinesResponse, GetMachineRequest } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { Notification } from '@douyinfe/semi-ui';
import axios from 'axios';
import { localeConfig } from '@/locales';

// 获取当前语言的翻译函数
const getTranslation = (key: string): string => {
    const currentLocale = localStorage.getItem('app-locale') || 'zh_CN';
    const messages = localeConfig[currentLocale] || localeConfig['zh_CN'];
    return messages[key] || key;
};

/**
 * 启用/禁用 设备密钥过期
 * @param machineId 设备id
 * @param disabled true: 禁用密钥过期； fase: 启用密钥过期
 * @returns 
 */
export const setMachineKeyExpiry = (machineId: bigint, disabled: boolean): Promise<void> => {
    const optName = disabled ? getTranslation('services.device.disable') : getTranslation('services.device.enable');
    return new Promise<void>((reslove, reject) => {
        flylayerClient.setMachineKeyExpiry({
            machineId,
            disabled
        }).then(() => {
            Notification.success({ content: `${optName}${getTranslation('services.device.keyExpireSuccess')}`, position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: `${optName}${getTranslation('services.device.keyExpireFailed')}`, position: "bottomRight" })

            reject(err)
        })
    });
}

/**
 * 启用/禁用 MESH模式
 */
export const setMachineMeshEnabled = (machineId: bigint, meshEnabled: boolean): Promise<void> => {
    const optName = meshEnabled ? getTranslation('services.device.enable') : getTranslation('services.device.disable');
    return new Promise<void>((reslove, reject) => {
        flylayerClient.setMachineMeshEnabled({
            machineId,
            meshEnabled
        }).then(() => {
            Notification.success({ content: `${optName}${getTranslation('services.device.meshModeSuccess')}`, position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: `${optName}${getTranslation('services.device.meshModeFailed')}`, position: "bottomRight" })

            reject(err)
        })
    });
}

/**
 * 查询设备列表
 * @returns 设备列表
 */
export const listMachines = (flynetId: bigint, query?: string): Promise<ListMachinesResponse> => {
    return new Promise<ListMachinesResponse>((reslove, reject) => {
        flylayerClient.listMachines({
            flynetId,
            query: query || ''
        }).then(res => {
            reslove(res)
        }, err => {
            console.error(err)
            Notification.error({ content: getTranslation('services.device.fetchDeviceListFailed'), position: "bottomRight" })
            reject(err)
        })
    });
}

/** 查询用户设备列表 */
export const listUserMachines = (flynetId: bigint, userId: bigint): Promise<Array<Machine>> => {
    return new Promise<Array<Machine>>((reslove, reject) => {
        flylayerClient.listMachines({
            flynetId,
            query: `users=${userId}&offset=0`
        }).then(res => {
            reslove(res.machines)
        }, err => {
            console.error(err)
            Notification.error({ content: getTranslation('services.device.fetchDeviceListFailed'), position: "bottomRight" })
            reject(err)
        })
    });
}

/**
 * 
 * @param machineId 设备id
 * @returns 设备
 */
export const getMachine = (machineIpOrId: string): Promise<Machine> => {
    
    let isValidErr = isValidIP(machineIpOrId);
    let req = new GetMachineRequest();

    if (!isValidErr) {
        req.identifier = {
            case: 'ipv4',
            value: machineIpOrId
        };
    } else {
        req.identifier = {
            case: 'machineId',
            value: BigInt(machineIpOrId)
        };
    }
    return new Promise<Machine>((reslove, reject) => {
        flylayerClient.getMachine(req).then(res => {
            if (res.machine) {
                reslove(res.machine)
            } else {
                const msg = getTranslation('services.device.queryEmptyData');
                console.error(msg);
                Notification.error({ content: getTranslation('services.device.fetchDeviceFailed'), position: 'bottomRight' })
                reject(new Error(msg))
            }

        }, err => {
            console.error(err)
            reject(err)
        })
    })
}


/**
 * 审批设备
 * @param machineId 设备id
 * @returns 
 */
export const authorizeMachine = (machineId: bigint): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.authorizeMachine({ machineId }).then(() => {
            Notification.success({ content: getTranslation('services.device.approveDeviceSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.device.approveDeviceFailed'), position: "bottomRight" })

            reject(err)
        })
    });

}

export const getRelayName = (relayId: number): Promise<string> => {
    return new Promise<string>((reslove, reject) => {
        axios.get('/api/mesh/relaymap/default').then(res => {


            if (res.data.Regions && res.data.Regions[relayId] && res.data.Regions[relayId].RegionName) {

                reslove(res.data.Regions[relayId].RegionName)
            } else {
                reslove('')
            }
        }, err => {
            console.error(err);
            reject(err)
        })
    });
}

export const getRelayList = (): Promise<Array<{ id: number, name: string }>> => {
    return new Promise<Array<{ id: number, name: string }>>((reslove, reject) => {
        axios.get('/api/mesh/relaymap/default').then(res => {
            let relayList = []
            if (res.data.Regions) {
                let regions = res.data.Regions;
                for (let key in regions) {
                    relayList.push({ id: parseInt(key), name: regions[key].RegionName })
                }

            }
            reslove(relayList)
        }, err => {
            console.error(err);
            reject(err)
        })
    });
}


export const SaveMachineRdpSettings = (machineId: bigint, rdpEnabled: boolean): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.saveMachineRdpSettings({ machineId, rdpEnabled }).then(() => {
            Notification.success({ content: getTranslation('services.device.updateRdpSettingsSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.device.updateRdpSettingsFailed'), position: "bottomRight" })

            reject(err)
        })
    });

}

