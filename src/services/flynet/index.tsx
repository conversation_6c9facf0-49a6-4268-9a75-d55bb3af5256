/** 企业设置与后台交互 */
import { flylayerClient } from '@/services/core';
import { Flynet, UserFlynet, FlynetConstraint } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { Notification } from '@douyinfe/semi-ui';
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import { Timestamp } from "@bufbuild/protobuf";
import { LOCAL_STORAGE_USER_ROLE, setLocalStorage } from '@/utils/storage';
import { localeConfig } from '@/locales';

// 获取当前语言的翻译函数
const getTranslation = (key: string): string => {
    const currentLocale = localStorage.getItem('app-locale') || 'zh_CN';
    const messages = localeConfig[currentLocale] || localeConfig['zh_CN'];
    const result = messages[key] || key;
    // 调试信息（可以在生产环境中移除）
    console.log(`Translation: locale=${currentLocale}, key=${key}, result=${result}`);
    return result;
};
/** 简略企业信息 */
export type FlynetGeneral = {
    id: bigint;
    name: string;
    alias: string;
    expired: boolean;
    disabledExpiry: boolean;
    expiresAt?: Timestamp;
    userRole: string;
    applicationEnabled:boolean;
}

export const getUserFlynetList = (): Promise<UserFlynet[]> => {
    return new Promise<UserFlynet[]>((reslove, reject) => {

        flylayerClient.listUserAvailableFlynets({}).then(res => {
            if (!res.userFlynet || res.userFlynet.length < 0) {
                const msg = getTranslation('services.flynet.queryNetworkError');
                console.error(msg);
                Notification.error({ content: msg, position: "bottomRight" })
                reject(new Error(msg))
            } else {
                reslove(res.userFlynet)

            }
        }, err => {
            console.error(err);
            reject(err)
        })

    });
}

/** 取得企业信息 */
export const getFlynet = (id: bigint): Promise<{ flynet: Flynet, constraint?: FlynetConstraint }> => {
    return new Promise<{ flynet: Flynet, constraint?: FlynetConstraint }>((reslove, reject) => {

        flylayerClient.getFlynet({
            id
        }).then((res) => {
            
            if (res.flynet) {

                reslove({ flynet: res.flynet, constraint: res.constraint })
            } else {
                const msg = getTranslation('services.flynet.queryNetworkError');
                console.error(msg);
                Notification.error({ content: msg, position: "bottomRight" })
                reject(new Error(msg))
            }
        }, err => {
            console.error(err)
            reject(err)
        })
    });
}



export const enableMachineAuthorization = (flynetId: bigint): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.enableMachineAuthorization({
            flynetId
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.enableMachineAuthSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.enableMachineAuthFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}

export const setMeshEnabled = (flynetId: bigint, meshEnabled: boolean): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.setMeshEnabled({
            flynetId,
            meshEnabled
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.updateMeshSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.updateMeshFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}

export const setApplicationPanelEnabled = (flynetId: bigint, applicationEnabled: boolean): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.setApplicationEnabled({
            flynetId,
            applicationEnabled
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.updateAppPanelSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.updateAppPanelFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}

export const disableMachineAuthorization = (flynetId: bigint): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.disableMachineAuthorization({
            flynetId
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.disableMachineAuthSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.disableMachineAuthFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}

export const setDefaultKeyExpireDays = (flynetId: bigint, days: number): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.setDefaultKeyExpireDays({
            flynetId,
            days
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.updateKeyExpireSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.updateKeyExpireFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}

export const setRdpSettings = (flynetId: bigint, rdpAutoGrant: boolean, rdpEnabled: boolean): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.saveFlynetRdpSettings({
            flynetId,
            rdpAutoGrant,
            rdpEnabled
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.updateRdpSettingsSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.updateRdpSettingsFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}

export const saveAccountManualCreate = (flynetId: bigint, accountManualCreate: boolean): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.saveAccountManualCreate({
            flynetId,
            accountManualCreate
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.updateSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.updateFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}

export const setAccountAvailableField = (flynetId: bigint, fields: Field[]): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.setAccountAvailableField({
            flynetId,
            accountAvailableFields: fields
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.updateSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.updateFailed'), position: "bottomRight" })
            reject(err)
        })
    })
}

export const setAccountMfaEnabled = (flynetId: bigint, mfaEnabled: boolean): Promise<void> => {
    return new Promise<void>((reslove, reject) => {
        flylayerClient.setMfaEnabled({
            flynetId,
            mfaEnabled
        }).then(() => {
            Notification.success({ content: getTranslation('services.flynet.updateSuccess'), position: "bottomRight" })
            reslove()
        }).catch(err => {
            console.error(err);
            Notification.error({ content: getTranslation('services.flynet.updateFailed'), position: "bottomRight" })

            reject(err)
        })
    })
}