import axios from 'axios';
import { localeConfig } from '@/locales';

// 获取当前语言的翻译函数
const getTranslation = (key: string): string => {
    const currentLocale = localStorage.getItem('app-locale') || 'zh_CN';
    const messages = localeConfig[currentLocale] || localeConfig['zh_CN'];
    return messages[key] || key;
};

export const uploadFile = (uploadUrl: string, file: File): Promise<string> => {
    return new Promise<string>((resolve, reject) => {
         
        axios.put(uploadUrl, file, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        }).then(res => {
            resolve(getTranslation('services.file.uploadSuccess'));
        }, err => {
            console.error(err);
            reject(err);
        });
    });
}

export const uploadFileWithProgress = (
    url: string,
    file: File,
    onProgress?: (total:number, loaded: number) => void
): Promise<string> => {
    return new Promise<string>((resolve, reject) => {
        axios.put(url, file, {
            headers: {
                'Content-Type': file.type
            },
            onUploadProgress: (progressEvent) => {
                if (progressEvent.total && onProgress) {
                    // 直接计算百分比并传递
                    const percent = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total
                    );
                    onProgress(progressEvent.total, percent);
                }
            }
        }).then(res => {
            resolve(getTranslation('services.file.uploadSuccess'));
        }).catch(err => {
            console.error(getTranslation('services.file.uploadFailed'), err);
            reject(err);
        });
    });
};