import { Interceptor, createPromiseClient } from "@bufbuild/connect";
import { createConnectTransport } from "@bufbuild/connect-web";
import { ConnectError, Code } from "@bufbuild/connect";
import { FlylayerService } from '@buf/flylayer_api.bufbuild_connect-es/flylayer/v1/flylayer_connect';
import { LogsService } from '@buf/flylayer_logs.connectrpc_es/logs/v1/logs_connect';
import { BASE_PATH } from "@/constants/router";
import { LOCAL_STORAGE_FLYNET_ID, getLocalStorage } from '@/utils/storage';

// 在请求头里添加企业ID
const headerInterceptor: Interceptor = (next) => async (req) => {
  const flynetId = getLocalStorage(LOCAL_STORAGE_FLYNET_ID);
  req.header.set("X-FlYNET-ID", flynetId ? flynetId : '');
  
  let locale = localStorage.getItem('app-locale') || 'zh_CN'
  locale = locale.replace('_', '-');
  req.header.set("X-Language", locale);
  req.header.set("Accept-Language",locale);
  return await next(req);
};

// 通用错误拦截
const errorInterceptor: Interceptor = (next) => async (req) => {
  try {
    const res = await next(req);

    return res;
  } catch (e) {
    if (e instanceof ConnectError) {
      if (e.code === Code.Unauthenticated) {
        if(location.pathname.indexOf('download') === -1){
          // location.pathname =`${BASE_PATH}/download`;
          window.location.reload();
        }

        
      }
      else if (e.code === Code.PermissionDenied) {
        location.pathname =`${BASE_PATH}/error/403`;

        // 如果不是管理员，跳转到下载页

        // const role = localStorage.getItem(LOCAL_STORAGE_USER_ROLE);
        // if(role != UserRole.FLYNET_ADMIN.toString()) {
        //   if(location.pathname.indexOf('download') === -1) {
        //     location.pathname =`${BASE_PATH}/download`;
        //   }
        // }
        
      }
    }
    throw e;
  }


};

const transport = createConnectTransport({
  baseUrl: `/api${BASE_PATH}`,
  //   credentials: "include",
  interceptors: [headerInterceptor, errorInterceptor],
  jsonOptions: {
    ignoreUnknownFields: true
  },

});

const logsTransport = createConnectTransport({  
  baseUrl: `/api/logs`,
  //   credentials: "include",
  interceptors: [headerInterceptor, errorInterceptor],
  jsonOptions: {
    ignoreUnknownFields: true
  },

});

export const flylayerClient = createPromiseClient(FlylayerService, transport);
export const logsClient = createPromiseClient(LogsService, logsTransport);