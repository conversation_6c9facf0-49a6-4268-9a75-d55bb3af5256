body {
    --fyy-editor-height: calc(100vh - 280px);
    --semi-color-disabled-fill: rgba(var(--semi-grey-8), .04);
}

body,
html #root {
    padding: 0;
    margin: 0;
    overflow-y: overlay!important;
    min-height: 100vh;
    width: 100%!important;
    background-color: var(--semi-color-bg-0);
}


a {
    color: var(--semi-color-link);
    text-decoration: none;
}

.app {
    min-height: 100vh;
}

// 默认复制按钮位置不对，复写垂直剧中位置
.semi-typography-action-copy {
    vertical-align: text-bottom;
}

// 复写表格垂直居中位置
.semi-table-tbody>.semi-table-row>.semi-table-row-cell {
    vertical-align: top;
}

// 普通页面
.general-page {
    padding: 16px 32px 0 32px;
    min-height: 100vh;
}

// 设置页面
.settings-page {
    padding: 16px 32px;
    min-height: 100vh;
}

// 表格最后一列
.table-last-col {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

// 右侧按钮区域
.btn-right-col {
    display: flex !important;
    justify-content: right !important;
}

// 子元素垂直居中
.align-v-center {
    display: flex;
    align-items: center;
}

// 可复制代码
.copyable-code {
    border-radius: var(--semi-border-radius-medium);
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-fill-0);
    padding: 20px;
}

// 左边图标右边内容
.layout-left-icon {
    display: flex;

    >:first-child {
        min-width: 20px;
    }

    >:last-child {
        flex-grow: 1;
    }
}

.mb40 {
    margin-bottom: 40px !important;
}

.mb20 {
    margin-bottom: 20px !important;
}

.mb10 {
    margin-bottom: 10px !important;
}

.mb2 {
    margin-bottom: 2px !important;
}

.mb0 {
    margin-bottom: 0 !important;
}

.p10 {
    padding: 10px !important;
}

.pt5 {
    padding-top: 5px !important;
}

.pt10 {
    padding-top: 10px !important;
}

.pr10 {
    padding-right: 10px !important;
}

.mw200 {
    max-width: 200px;
}

.mw300 {
    max-width: 200px;
}

//滚动条样式
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

::-webkit-scrollbar-thumb {
    border-radius: 1em;
    background-color: rgba(50, 50, 50, .3);
}

::-webkit-scrollbar-track {
    border-radius: 1em;
    background-color: rgba(50, 50, 50, .1);
}


// 右锚点样式
.right-anchor {
    .semi-anchor-slide {
        left: unset !important;
        right: 2px !important;
    }

    .semi-anchor-link-title {
        text-align: right !important;
        padding-right: 10px;
    }
}

// 可展开表格的样式
tr[aria-level] {
    cursor: pointer;
}

.trail-wrap {
    min-height: calc(100vh - 60px);
    display: flex;
    align-items: center;
    justify-content: center;

    // 向导轮播图
    .trial-carousel {
        width: 880px;
        height: 400px;
        margin: auto;
    }
}

a.learnMore {
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    word-spacing: 2px;
    color: var(--semi-color-text-2);

    span {}

}

a.learnMore:hover {
    text-decoration: underline;
}


// 报表弹出
.marker-machine-info-win {
    padding: 10px;

    .info-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .info-content {
        font-size: 12px;

        ul {
            padding: 0;
            margin: 0;

            li {
                color: #6b7785;
                list-style: none;
                margin-bottom: 0px;
            }
        }
    }
}

// 重置样式bug
.semi-input-prefix-text,
.semi-input-suffix-text {
    color: var(--semi-color-text-2);
}

// 重置日历快捷按钮宽度
.semi-datepicker-quick-control-left-content-item-ellipsis,
.semi-datepicker-quick-control-right-content-item-ellipsis {
    width: 78px !important;
    text-align: left !important;
}

// 修复tooltip问题
.semi-tooltip-wrapper {
    max-width: unset !important;
}

.mono-font {
    font-family: "Consolas", "Menlo", "JetBrains Mono", "DejaVu Sans Mono", "Liberation Mono", "Ubuntu Mono", "Courier New", "andale mono", "lucida console", monospace;
}

.mobile-visible {
    display: none;
}

.infinite-scroll-component {
    overflow: hidden !important;
}


// 显示多行选择结果
.selector-mulit-line {
    .semi-tag-group {
        flex-wrap: wrap !important;
        flex-shrink: 1 !important;
    }
}

.table-line-jump {
    display: flex;
    align-items: center;

    a {
        color: var(--semi-color-link);
        margin-left: 4px;

        span {
            font-size: 12px;
        }
    }

    a:hover {
        color: var(--semi-color-link-hover)
    }
}


.link-external {
    display: inline-flex;
    align-items: center;
    // color: var(--semi-color-link);

    color: var(--semi-color-fill-3)!important;
    margin-left: 4px;
    margin-right: 4px;

    span {
        font-size: 12px;
    }
    &:visited {
        color: var(--semi-color-fill-3)!important;
    }
    &:hover {
        // color: var(--semi-color-link-hover)
        color: var(--semi-color-text-1)!important;
    }
}



.tableTitle {
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 8px;
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
        line-height: 32px;
    }
}

.subTableTitle {
    >div {
        padding-right: 8px;
    }
}


.tableBody {
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 10px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.tableBody:last-child {
    border-bottom: none;
}



.tableBodyError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);

    padding-top: 5px;
    padding-bottom: 5px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}



.subTableBody {
    margin-bottom: 10px;
    >div {
        padding-right: 8px;
    }

}

.templateEditor {
    >div {
        height: 450px !important;
    }
}
