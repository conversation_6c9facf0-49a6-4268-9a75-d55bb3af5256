import React, { useState, useContext } from 'react'
import { useNavigate } from 'react-router-dom'
import { IconTick } from '@douyinfe/semi-icons';
import { Typography, List, Avatar, RadioGroup, Row, Col, Button, Card, Space, Checkbox } from '@douyinfe/semi-ui';

import typeNetworking from '@/assets/type-networking.png'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import typeVpn from '@/assets/type-vpn.png'
import typeIntranetPenetration from '@/assets/type-intranet-penetration.png'
import styles from './index.module.scss'
import { BASE_PATH } from '@/constants/router';
import { LOCAL_GUIDE_FLAG, LOCAL_STORAGE_GUIDE_TYPE, getLocalStorage, setLocalStorage } from '@/utils/storage';
import MemberGuide from './member-guide';
import  { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { useLocale } from '@/locales';

const { Title, Paragraph, Text } = Typography;
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);
    // 是否是普通用户
    const [isMember, setIsMember] = useState(false);

    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    const data = [
        {
            title: formatMessage({ id: 'guide.mesh.title' }),
            content: formatMessage({ id: 'guide.mesh.content' }),
            value: 'networking',
            icon: typeNetworking,
            disabled: false
        },
        {
            title: formatMessage({ id: 'guide.vpn.title' }),
            content: formatMessage({ id: 'guide.vpn.content' }),
            value: 'vpn',
            icon: typeVpn,
            disabled: false
        },
        // {
        //     title: 'API连接器',
        //     content: 'API入口即服务，无需公网IP即可将本地服务发布至公网，方便开发者远程调试。',
        //     value: 'intranet-penetration',
        //     icon: typeIntranetPenetration,
        //     disabled: true
        // },
    ];
    const initGuideType = getLocalStorage(LOCAL_STORAGE_GUIDE_TYPE)
    const [guideType, setGuideType] = useState(initGuideType ? initGuideType : '');
    return <div className={styles.guideWrap}>
        {isMember ?
            <MemberGuide></MemberGuide> :
            <Card bordered={false} className={styles.guideCard}>
                <Title heading={1} style={{ marginBottom: 10, textAlign: 'center' }}>{formatMessage({ id: 'guide.title' })}</Title>
                <Paragraph type="secondary" style={{ marginBottom: 35, textAlign: 'center' }}>{formatMessage({ id: 'guide.description' })}</Paragraph>
                <List
                    className='mb40 guide-list'
                    style={{ width: '100%' }}
                    dataSource={data}
                    layout='horizontal'
                    renderItem={item => (
                        <List.Item
                            className={item.disabled ? styles.typeItemDisabled : guideType === item.value ? styles.typeItemActive + ' ' + styles.typeItem : styles.typeItem}
                            onClick={() => {
                                if (item.disabled) {
                                    return
                                }
                                setGuideType(item.value);
                            }}
                            main={

                                <div className={styles.typeItemContent}>
                                    {guideType === item.value ? <IconTick className={styles.iconTick} /> : null}
                                    <Avatar size='extra-large' shape='square' src={item.icon}></Avatar>
                                    <div>
                                        <Title heading={4} className='mb20'>
                                            {item.title} {item.disabled && <Text type='tertiary' style={{ fontWeight: 'normal' }}>(即将上线)</Text>}</Title>
                                        <p style={{ color: 'var(--semi-color-text-2)', margin: '4px 0' }}>
                                            {item.content}
                                        </p>
                                    </div>
                                </div>
                            }
                        />
                    )}
                />
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Space className='mb20'>
                        <Button style={{ width: 85 }} disabled={guideType == ''} size='large' theme='solid' onClick={() => {
                            setLocalStorage(LOCAL_STORAGE_GUIDE_TYPE, guideType);
                            navigate(BASE_PATH + '/guide/' + guideType)

                        }} >下一步</Button>
                        <Button style={{ width: 85 }} title={`如果您对${globalConfig.name}比较熟悉，可以跳过此引导`} size='large' onClick={() => {
                            setLocalStorage(LOCAL_GUIDE_FLAG + flynet.id, 'useGuide');
                            navigate(BASE_PATH)
                        }}>跳&nbsp;&nbsp;过</Button>
                    </Space>
                    {/* <!-- 跳过引导说明 --> */}

                </div>
                <Paragraph style={{ textAlign: 'center' }} className='mb40' type='tertiary'>如果您对{globalConfig.name}比较熟悉，可以跳过此引导</Paragraph>
            </Card>
        }
    </div>
}

export default Index;