import React, { useState, useContext } from 'react'
import { Typography, Card } from '@douyinfe/semi-ui';
import useGlobalConfig, { GlobalConfigContext } from '@/hooks/useGlobalConfig'

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useNavigate } from 'react-router-dom';
const { Title, Paragraph, Text } = Typography;
import { BASE_PATH } from '@/constants/router';
import { IconTick } from '@douyinfe/semi-icons';
import styles from './index.module.scss'
import { doLogout } from '@/hooks/useUserProfile';


import windowsLogin from '@/assets/images/windows-login.png'

const Index: React.FC = () => {
    
    
    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);
    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate()
    return <><Card bordered={false}>
        <div className={styles.tick}><IconTick /></div>
        <Title heading={1} style={{ marginBottom: 10, textAlign: 'center' }}>使用客户端连接网络</Title>
        <Paragraph type="tertiary" style={{ marginBottom: 20, textAlign: 'center' }}>
            从桌面端或移动端登录{globalConfig.name}，连接到 <b>{flynet.alias ? flynet.alias : flynet.name}</b> 网络
        </Paragraph>
        <img className={styles.demoImg} src={windowsLogin}></img>
        <Paragraph type="secondary" style={{ marginBottom: 35, textAlign: 'center' }}>
            在其他设备<a style={{fontWeight:800, textDecoration: 'underline'}} onClick={() => navigate(`${BASE_PATH}/download`)}>下载客户端</a>，连接您的网络。
        </Paragraph>

        <Paragraph style={{textAlign: 'center'}} type='tertiary' >使用另外一个网络</Paragraph>

        <Paragraph type="secondary" style={{ marginBottom: 35, textAlign: 'center' }}>
            <a onClick={() => doLogout()}><Text type='tertiary' style={{ textDecoration: 'underline' }}>切换账号</Text></a>
        </Paragraph>

    </Card></>
}

export default Index;