import { FC, useState } from 'react';
import { useNavigate } from 'react-router-dom'
import { Card, Typography, List, Button, Space, Avatar } from '@douyinfe/semi-ui';


import { IconTick } from '@douyinfe/semi-icons';
import styles from './index.module.scss';

import { BASE_PATH } from '@/constants/router';
import typeNetworking from '@/assets/type-networking.png'
import typeVpn from '@/assets/type-vpn.png'
interface Props {
}


const { Title, Paragraph, Text } = Typography;
const Index: FC<Props> = (props) => {
    const [guideType, setGuideType] = useState('');
    const navigate = useNavigate();
    const data = [
        {
            title: 'Mesh网络',
            content: '组建您的私有网络，把位于不同物理位置的设备联接成一个互通的网络。',
            value: 'personal',
            icon: typeNetworking,
            disabled: false
        },
        {
            title: '零信任SDP',
            content: '取代传统VPN的全新零信任访问模式，通过严格的身份认证和授权校验来保护资源。',
            value: 'team',
            icon: typeVpn,
            disabled: false
        }
    ];
    return <div className={styles.guideWrap}>
        <Card bordered={false} className={styles.guideCard}>
            <Title heading={1} style={{ marginBottom: 10, textAlign: 'center' }}>选择您的体验方式</Title>
            <Paragraph type="secondary" style={{ marginBottom: 35, textAlign: 'center' }}>根据您的体验方式，我们将为您提供不同的操作引导。</Paragraph>
            <List
                className='mb40 guide-list'
                style={{ width: '100%' }}
                dataSource={data}
                layout='horizontal'
                renderItem={item => (
                    <List.Item
                        className={item.disabled ? styles.typeItemDisabled : guideType === item.value ? styles.typeItemActive + ' ' + styles.typeItem : styles.typeItem}
                        onClick={() => {
                            if (item.disabled) {
                                return
                            }
                            setGuideType(item.value);
                        }}
                        main={

                            <div className={styles.typeItemContent}>
                                {guideType === item.value ? <IconTick className={styles.iconTick} /> : null}
                                <Avatar size='extra-large' shape='square' src={item.icon}></Avatar>
                                <div>
                                    <Title heading={4} className='mb20'>
                                        {item.title} {item.disabled && <Text type='tertiary' style={{ fontWeight: 'normal' }}>(即将上线)</Text>}</Title>
                                    <p style={{ color: 'var(--semi-color-text-2)', margin: '4px 0' }}>
                                        {item.content}
                                    </p>
                                </div>
                            </div>
                        }
                    />
                )}
            />
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Space className='mb20'>
                    <Button style={{ width: 85 }} disabled={guideType == ''} size='large' theme='solid' onClick={() => {
                        navigate(BASE_PATH + '/trial/' + guideType)

                    }} >下一步</Button>

                </Space>


            </div>
        </Card>
    </div>
};

export default Index;