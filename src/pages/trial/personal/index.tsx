import { FC, useState } from 'react';
import { Typography, Modal, Form, Row, Col, Notification } from '@douyinfe/semi-ui';
import { Carousel } from '@douyinfe/semi-ui';

import styles from './index.module.scss';

interface Props {
}


const Index: FC<Props> = (props) => {
    const imgList = [
        'https://lf3-static.bytednsdoc.com/obj/eden-cn/hjeh7pldnulm/SemiDocs/bg-1.png',
        'https://lf3-static.bytednsdoc.com/obj/eden-cn/hjeh7pldnulm/SemiDocs/bg-2.png',
        'https://lf3-static.bytednsdoc.com/obj/eden-cn/hjeh7pldnulm/SemiDocs/bg-3.png',
    ];
    return <>
    <div className='trail-wrap'>
        <Carousel className='trial-carousel' theme='dark'>
        {
                imgList.map((src, index) => {
                    return (
                        <div key={index} style={{ backgroundSize: 'cover', backgroundImage: `url(${src})` }}>
                          ddddddd
                        </div>
                    );
                })
            }
        </Carousel>
        </div>
    </>
};

export default Index;