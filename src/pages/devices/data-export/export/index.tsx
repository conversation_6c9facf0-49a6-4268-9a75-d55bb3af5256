import { FC, useContext, useEffect, useState } from 'react';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import SearchFilter from '@/components/search-filter-combo';
import { MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { Typography, Modal, CheckboxGroup, RadioGroup, Space, Select, Toast, Spin } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph, Text } = Typography;

interface FilterParam {
    name: string,
    label: string,
    placeholder: string,
    value: string | any,
    fixed?: boolean,
    filterComponent?: FC<{
        value: string | string[] | any,
        onChange: (val: string | string[] | any) => void
    }>,
    funGetDisplayValue?: (val: string | any) => string
}


export type DeviceFilter = {
    keywords: string,
    os: Array<string>,
    connectStatus: 'online' | 'offline' | '',
    groups: string,
    meshEnabled: 'enable' | 'disable' | ''
}

interface Props {
    close: () => void;
    success?: () => void;
}
const Index: FC<Props> = (props) => {
    const { formatMessage: _formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);

    const initFilterParams = (machineGroups: MachineGroup[]) => {
        setFilterParams([
            {
                name: 'keywords',
                // placeholder: '根据用户、设备名称、设备描述、IP、版本号、标签等搜索',
                placeholder: '根据设备名称、用户、IP、版本号、标签等搜索',
                label: '查询',
                value: '',
            }, {
                name: 'os',
                placeholder: '选择系统',
                label: '操作系统',
                fixed: true,
                value: '',
                filterComponent: ({ value, onChange }) => {
                    const [checkedList, setCheckedList] = useState(value ? value.split(',') : []);
                    return <Space className='mb10' style={{marginTop: 10}}>
                        <Text type="tertiary" style={{width: 100}}>操作系统</Text>
                        <CheckboxGroup
                            
                            style={{ width: '100%' }}
                            direction="horizontal"
                            value={checkedList}
                            onChange={value => {
                                setCheckedList(value)
                                onChange(value.join(','))
                            }}
                            options={[
                                { value: 'macOS', label: 'macOS' },
                                { value: 'iOS', label: 'iOS' },
                                { value: 'windows', label: 'Windows' },
                                { value: 'linux', label: 'Linux' },
                                { value: 'android', label: 'Android' },
                            ]}
                        ></CheckboxGroup>
                        {/* <Divider className='mb10' />
                        <Button block onClick={() => onChange(checkedList.join(','))}>应用</Button> */}
                    </Space>
                },
                funGetDisplayValue: (value: string) => {
                    let names: string[] = [];
                    if (value) {
                        let val = value.split(',');
                        val.forEach(v => {
                            names.push(v == 'macOS' ? 'macOS' : v == 'iOS' ? 'iOS' : v == 'windows' ? 'Windows' : v == 'linux' ? 'Linux' : v == 'android' ? 'Android' : '');
                        })
                    }
                    return names.join(',');
                }
            },

            {
                name: 'meshEnabled',
                placeholder: '选择Mesh模式',
                label: 'Mesh模式',
                fixed: true,
                value: '',
                filterComponent: ({ value, onChange }) => {
                    const [val, setVal] = useState(value);
                    return <Space className='mb10' style={{ width: '100%' }}>
                        <Text type="tertiary" style={{width: 100}}>Mesh模式</Text>
                        <RadioGroup
                            
                            value={val}
                            onChange={e => {
                                setVal(e.target.value)
                                onChange(e.target.value);}}
                            options={[
                                { value: 'enable', label: '开启' },
                                { value: 'disable', label: '关闭' },
                            ]}
                        />
                        {/* <Divider className='mb10' />
                        <Button block onClick={() => onChange(val)}>应用</Button> */}
                    </Space>
                },
                funGetDisplayValue: (value: any) => {
                    return value == 'enable' ? '开启' : value == 'disable' ? '关闭' : '';
                }
            }, {
                name: 'groups',
                placeholder: '请选择设备组',
                label: '设备组',
                fixed: true,
                value: '',
                filterComponent: ({ value, onChange }) => {
                    const [val, setVal] = useState(value ? value.split(',') : []);
                    return <Space className='mb10'>
                        <Text type="tertiary" style={{width: 100}}>设备组</Text>
                        <Select
                            placeholder='请选择设备组'
                            value={val}
                            onChange={(val) => {
                                setVal(val);
                                onChange((val as any).join(','));
                            }}
                            style={{ width: 180 }}
                            optionList={machineGroups.map(group => {
                                return { value: group.id + '', label: group.alias }
                            })}
                            multiple
                            maxTagCount={1}
                        >
                        </Select>
                        {/* <Divider className='mb10' />
                        <Button block onClick={() => onChange(val.join(','))}>应用</Button> */}
                    </Space>
                },
                funGetDisplayValue: (val: string) => {
                    let names: string[] = [];
                    if (val) {
                        let values = val.split(',');
                        values.forEach(v => {
                            let group = machineGroups.find(group => group.id + '' == v);
                            if (group) {
                                names.push(group.alias);
                            }
                        })
                    }
                    return names.join(',');
                }
            }
        ]);
    }


    const query = async (_deviceFilter?: DeviceFilter) => {
        const res = await flylayerClient.listMachineGroups({
            flynetId: flynet.id
        })

        initFilterParams(res.groups)
    }


    useEffect(() => {
        query()
    }, []);

    // 过滤参数
    const [filter, setFilter] = useState<DeviceFilter>(
        {
            keywords: '',
            os: [],
            connectStatus: '',
            groups: '',
            meshEnabled: ''
        }
    );

    const [saveLoading, setSaveLoading] = useState(false);

    const handleSubmit = async () => {
        setSaveLoading(true);
        let queryArray = [];
        if (filter.keywords) {
            queryArray.push(`keywords=${filter.keywords}`);
        }
        if (filter.os  && filter.os.length > 0) {
            queryArray.push(`os=${filter.os}`);
        }
        if (filter.connectStatus) {
            queryArray.push(`connectStatus=${encodeURIComponent(filter.connectStatus)}`);
        }
        if (filter.groups) {
            queryArray.push(`groups=${encodeURIComponent(filter.groups)}`);
        }
        if (filter.meshEnabled) {
            queryArray.push(`mesh_disabled=${filter.meshEnabled == 'disable' ? true : false}`);
        }   

        flylayerClient.exportMachines({
            flynetId: flynet.id,
            query: queryArray.join('&')
        }).then((res) => {
            setSaveLoading(false);
            if (res) {
                props.success && props.success();
                props.close();
            }
            window.open(res.downloadUrl, '_blank');
        }).catch((err) => {
            setSaveLoading(false);
            if (err) {
                if (err.response) {
                    if (err.response.status === 403) {
                        Toast.error('没有权限');
                    } else {
                        Toast.error(err.response.data.message);
                    }
                }
            }
        });



     }




    return <>
        <Modal
            width={700}
            title="导出设备数据"
            visible={true}
            onOk={handleSubmit}
            onCancel={props.close}
            okText="导出"
            okButtonProps={{ loading: saveLoading }}
            className="semi-modal"
            maskClosable={false}
        >
            {filterParams.length == 0 ? <Spin size='large' /> : <>
                <Paragraph type="tertiary" className='mb20'>
                支持导出所有设备或符合下面搜索条件的设备
            </Paragraph>
            <SearchFilter onChange={(val: string, filterParam) => {

                setFilter({ ...filter, [filterParam.name]: val } as DeviceFilter)

                const newFilterParams = filterParams.map((item) => {
                    if (item.name == filterParam.name) {
                        item.value = val;
                    }
                    return item;
                })
                setFilterParams(newFilterParams);
            }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>

                </> }
            
        </Modal>
    </>
}

export default Index;