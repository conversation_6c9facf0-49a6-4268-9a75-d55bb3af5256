import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification } from '@douyinfe/semi-ui';
import { Machine, MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { useLocale } from '@/locales';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';

import { getSimpleMachineGroupName, getSimpleMachineName } from '@/utils/machine';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    group: MachineGroup,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const aclName = getSimpleMachineName(props.record);
    const aclGroupName = getSimpleMachineGroupName(props.group);
    
    const handleSubmit = () => {
        setLoading(true);
        
        flylayerClient.removeMachineFromGroup({
            machineId: props.record.id,
            groupId: props.group.id,
            flynetId: flynetGeneral.id
        }).then(_res => {
            Notification.success({
                title: formatMessage({ id: 'devices.removeFromGroup.successTitle' }),
                content: `成功将设备${aclName}从设备组${aclGroupName}中移除`
            });
            props.close();
            props.success && props.success();
        }).catch(_err => {
            Notification.error({
                title: formatMessage({ id: 'devices.removeFromGroup.failedTitle' }),
                content: `将设备${aclName}从设备组${aclGroupName}中移除失败，请稍后重试`
            });
        }).finally(() => {
            setLoading(false);
        });
        
    }

    return <>
        <Modal
            title={formatMessage({ id: 'devices.removeFromGroup.title' })}
            visible={true}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'>确定要将设备<b>{aclName}</b>从设备组<b>{aclGroupName}</b>中移除吗？</Paragraph>
        </Modal></>
}
export default Index;
