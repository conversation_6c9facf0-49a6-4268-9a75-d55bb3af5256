import { FC } from 'react'
import { Modal } from '@douyinfe/semi-ui';
import { Platform_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/install_pb';

import Download from '@/components/download';
import { useLocale } from '@/locales';


interface Props {
    close: () => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    return <>
        <Modal
            width={640}
            title={formatMessage({ id: 'devices.deploy.title' })}
            visible={true}
            maskClosable={false}
            onOk={() => props.close()}
            onCancel={() => props.close()}
            closeOnEsc={true}
            hasCancel={false}
            footer={null}
            
        >
            <div >
            <Download currentTab={Platform_Type.LINUX}/>
            </div>
        </Modal>
    </>
}

export default Index;