import { FC, useState, useEffect, useContext } from 'react'
import { Typography, Modal, Form, Notification, Skeleton, Divider, Checkbox } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { Service, ServiceNode, ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { MachineRoutes, AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss';
import { useLocale } from '@/locales';

const { Title, Text } = Typography;

const { Switch, RadioGroup, Radio } = Form

interface Props {
    close: () => void,
    record: Machine,
    advertisedRoutes: AdvertisedRoute[],
    success?: (routes?: MachineRoutes) => void
}

// 校验重重复的路由

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{ value: string, enable: boolean, remark: string, routeType: AdvertisedRoute_RouteType }>>()

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);


    const [routingServicesLoading, setRoutingServicesLoading] = useState(false);
    const [routingServices, setRoutingServices] = useState<Service[]>([]);
    const query = () => {
        setRoutingServicesLoading(true);

        flylayerClient.listServices({
            flynetId: flynet.id,
            serviceType: ServiceType.ROUTING
        }).then((res) => {
            setRoutingServices(res.services);
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.addRouteNew.getServicesFailed' }), position: "bottomRight" })
        }).finally(() => setRoutingServicesLoading(false))

    }



    const [routingNodes, setRoutingNodes] = useState<ServiceNode[]>([]);

    useEffect(() => {
        query();
    }, []);

    // 点击确定按钮
    const handleOk = async () => {
        if (!formApi) {
            return
        }
        await formApi.validate();
        let err = formApi.getError('value');

        if (err) {
            return;
        }
        let { enable, routeType } = formApi.getValues();


        setLoading(true);

        const advertisedRoutes = routingNodes.map((node) => {
            return new AdvertisedRoute({
                route: node.ipv4,
                enabled: enable,
                remark: node.name,
                routeType: routeType == AdvertisedRoute_RouteType.ADVERTISED ? AdvertisedRoute_RouteType.ADVERTISED : AdvertisedRoute_RouteType.EXCLUDE
            })
        })

        flylayerClient.createAdvertisedRoutes({
            machineId: props.record.id,
            advertisedRoutes: advertisedRoutes

        }).then((res) => {
            props.success && props.success(res.routes);
            Notification.success({ content: formatMessage({ id: 'devices.addRoute.addSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.addRoute.addFailed' }), position: "bottomRight" })

        }).finally(() => setLoading(false))
    }
    const handleCancel = () => {
        props.close();
    };

    const handleNameChange = () => {
    }

    return <>
        <Modal
            width={500}
            title={`添加设备${props.record.givenName}的子网路由`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okText={formatMessage({ id: 'devices.addRoute.confirm' })}
            okButtonProps={{ loading }}
        >
            <div className={styles.addRoute}>
                <Form getFormApi={SetFormApi} initValues={{
                    routeType: AdvertisedRoute_RouteType.ADVERTISED,
                    enable: true,
                }} render={() => (<>

                    <RadioGroup label={formatMessage({ id: 'devices.addRoute.type' })} field='routeType' name='routeType' >
                        <Radio value={AdvertisedRoute_RouteType.ADVERTISED} checked>{formatMessage({ id: 'devices.addRoute.advertisedRoute' })}</Radio>
                        <Radio value={AdvertisedRoute_RouteType.EXCLUDE}>{formatMessage({ id: 'devices.addRoute.excludeRoute' })}</Radio>
                    </RadioGroup>
                    <Skeleton loading={routingServicesLoading} placeholder={<Skeleton.Image style={{ width: '100%' }}></Skeleton.Image>}>
                        <div >
                            {
                                routingServices.map((pool, index) => {
                                    return <div key={index} className="mb40">
                                        <Title heading={5}>{pool.name} <Text type='tertiary'>{pool.name}</Text></Title>
                                        <Divider className="mb10"></Divider>

                                        {
                                            pool.serviceNodes.map((node, index) => {
                                                return <div key={index} className='mb10'>
                                                    <Checkbox value={node.ipv4}
                                                        onChange={(e) => {
                                                            const checked = e.target.checked;
                                                            if (checked) {
                                                                setRoutingNodes([...routingNodes, node]);
                                                            } else {
                                                                setRoutingNodes(routingNodes.filter(item => (item.ipv4 !== node.ipv4)));
                                                            }

                                                        }}
                                                    >{node.name}&nbsp;<Text type="tertiary">{node.ipv4}</Text></Checkbox>
                                                </div>
                                            })
                                        }

                                    </div>
                                })
                            }
                        </div>
                    </Skeleton>
                    <Switch onChange={handleNameChange} field='enable' label={formatMessage({ id: 'devices.addRoute.enableImmediately' })} />
                </>)} />
            </div>
        </Modal>
    </>
}

export default Index;