import { FC, useState } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');
    return <>
        <Modal
            width={600}
            title={`${formatMessage({ id: 'devices.delete.title' })}${props.record.givenName}`}
            visible={true}
            okButtonProps={{
                disabled: props.record.name != confirmVal,
                loading, type: 'danger' }}
            onOk={() => {
                setLoading(true)
                flylayerClient.deleteMachine({
                    machineId: props.record.id
                }).then(() => {
                    Notification.success({ content: formatMessage({ id: 'devices.delete.success' }), position: "bottomRight" })
                    if (props.success) {
                        props.success();
                    }
                }).catch((err) => {
                    console.error(err);
                    Notification.error({ content: formatMessage({ id: 'devices.delete.failed' }), position: "bottomRight" })
                }).finally(() => setLoading(false))
            }}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'devices.delete.description' })}</Paragraph>
            <Paragraph>{formatMessage({ id: 'devices.delete.confirmText' })} <b>{props.record.name}</b> {formatMessage({ id: 'devices.delete.confirmAction' })}</Paragraph>
            <Input value={confirmVal} onChange={(val)=>setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
