import React, { useState, useEffect, useContext } from 'react'
import { Typography, Table, Row, Col, Button, Select, Space, Input, Layout, Tag, Spin, BackTop, Tabs, TabPane, Popover } from '@douyinfe/semi-ui';
import { IconSearch } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
import useTable, { DeviceFilter } from './useTable-local';
import Deploy from './deploy';
import Del from './del';
import Expiry from './expiry';
import EditRoute from './edit-route';
import EditRouteNew from './edit-route-new';
import Rename from './rename';
import EditRelay from './edit-relay';
import AclTag from './acl-tag';
import RemoveFromGroup from './remove-from-group';
import EditGroup from './edit-group';
import EditRejectRoute from './edit-reject-route';
import EditMeshEnabled from './edit-mesh-enabled';
import RangeAccept from './range-accept';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import InfiniteScroll from 'react-infinite-scroll-component';
import { getQueryParam } from '@/utils/query';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { VITE_USE_DEVELOP_FEATURE } from '@/utils/service';
import { LicenseContext } from '@/hooks/useLicense';
import { getRadioEntitlementVal } from '@/utils/common';

import qs from 'query-string';

import Tagowners from '@/pages/policies/tagowners';

import { Machine, MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';

const { Title, Paragraph } = Typography;
const { Sider, Content } = Layout;

// 根据URL参数设置过滤参数
const getDeviceFilter = (location: Location): DeviceFilter => {
    const keywords: string = getQueryParam('keywords', location) as string;
    const osQuery = getQueryParam('os', location);
    const connectStatus: string = getQueryParam('connectStatus', location) as string;
    const group: string = getQueryParam('group', location) as string;
    const meshEnabled: string = getQueryParam('meshEnabled', location) as string;



    let os: string[] = [];
    if (osQuery && Array.isArray(osQuery)) {
        os = osQuery as string[];
    }
    if (osQuery && typeof osQuery == 'string') {
        os = [osQuery as string];
    }
    return {
        keywords: keywords || '',
        os: os,
        connectStatus: connectStatus == 'online' || connectStatus == 'offline' ? connectStatus : '',
        group: group || '',
        meshEnabled: meshEnabled == 'enable' || meshEnabled == 'disable' ? meshEnabled : ''
    }
}
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const location = useLocation();
    const navigate = useNavigate();
    const license = useContext(LicenseContext);
    const entitlementAllowImportExport = getRadioEntitlementVal('import_export', license.entitlements);

    // 访问控制策略
    const [aclPolicy, setACLPolicy] = useState<ACLPolicy>();

    // 过滤参数
    const initFilter: DeviceFilter = getDeviceFilter(location);

    // 过滤参数改变时跳转路由
    const doNavigate = (param: DeviceFilter) => {

        let query = '';
        if (param.keywords || param.os.length > 0 || param.connectStatus || param.group || param.meshEnabled) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/devices?${query}`)
        } else {
            navigate(`${BASE_PATH}/devices`)
        }
    }

    // 查询参数
    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        // 查询参数从有值变化为无值时，重新加载数据
        if (location.search == '' && search != '') {
            setFilterParam(initFilter);
        }
        setSearch(location.search);
    }, [location])

    useEffect(() => {
        flylayerClient.getACLPolicy({
            flynetId: flynet.id
        }).then((res) => {
            setACLPolicy(res.policy)
        });
    }, []);

    // 标签弹出框是否可见
    const [tagownersVisible, setTagownersVisible] = useState(false);

    // 是否显示部署弹框
    const [deployVisible, setDeployVisible] = useState(false);
    // 设备列表HOOKS
    const { columns, devices, allDevices: _allDevices, loading, curDevice, setCurDevice, delVisible, setExpiryVisible, expiryVisible, setDelVisible, renameVisible, setRenameVisible, editRouteVisible, setEditRouteVisible, editAclTagVisible, setEditAclTagVisible, setReloadFlag, filterParam, setFilterParam, page: _page, total, addPage, pageSize, handleSort,
        groups,
        curGroup,
        setCurGroup,
        handleGroupChange,
        removeFromGroupVisible,
        setRemoveFromGroupVisible,
        editGroupVisible,
        setEditGroupVisible,
        editRelayVisiable,
        setEditRelayVisiable,
        editRejectRouteVisible,
        setEditRejectRouteVisible,
        editMeshEnabledVisible,
        setEditMeshEnabledVisible,
        editRouteNewVisible,
        setEditRouteNewVisible,
        rangeAcceptVisible,
        setRangeAcceptVisible,
    } = useTable(initFilter);


    const listConnectStatus = [
        { value: '', label: formatMessage({ id: 'devices.all' }) },
        { value: 'online', label: formatMessage({ id: 'devices.table.online' }) },
        { value: 'offline', label: formatMessage({ id: 'devices.table.offline' }) }
    ];
    const listOs = [
        { value: 'macOS', label: 'macOS' },
        { value: 'iOS', label: 'iOS' },
        { value: 'windows', label: 'Windows' },
        { value: 'linux', label: 'Linux' },
        { value: 'android', label: 'Android' },
    ];
    const listMeshEnabled = [
        { value: '', label: formatMessage({ id: 'devices.all' }) },
        { value: 'enable', label: formatMessage({ id: 'devices.enabled' }) },
        { value: 'disable', label: formatMessage({ id: 'devices.disabled' }) }
    ];

    const handleConnectStatusChange = (value: any) => {
        setFilterParam({ ...filterParam, connectStatus: value })
        doNavigate({ ...filterParam, connectStatus: value });
    }
    const handleOsChange = (value: any) => {

        setFilterParam({ ...filterParam, os: value })
        doNavigate({ ...filterParam, os: value });
    }

    const handleMeshEnabledChange = (value: any) => {
        setFilterParam({ ...filterParam, meshEnabled: value })
        doNavigate({ ...filterParam, meshEnabled: value });

    }

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, keywords: value })
        doNavigate({ ...filterParam, keywords: value });
    }

    return <><div className='general-page'>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>{formatMessage({ id: 'devices.title' })}</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    {entitlementAllowImportExport && <><Button
                        onClick={() => navigate(`${BASE_PATH}/devices/export/`)}
                    >{formatMessage({ id: 'devices.dataExport' })}</Button>
                        <Button
                            onClick={() => navigate(`${BASE_PATH}/devices/import/`)}
                        >{formatMessage({ id: 'devices.dataImport' })}</Button></>}

                    {VITE_USE_DEVELOP_FEATURE && <Button onClick={() => navigate(`${BASE_PATH}/devices/connector-group`)}>{formatMessage({ id: 'devices.connectorGroup' })}</Button>}
                    <Button
                        onClick={() => navigate(`${BASE_PATH}/devices/group/`)}>{formatMessage({ id: 'devices.deviceGroup' })}</Button>
                    <Button onClick={() => setTagownersVisible(true)}>{formatMessage({ id: 'devices.tags' })}</Button>
                    <Button theme='solid'
                        onClick={() => setDeployVisible(true)}>{formatMessage({ id: 'devices.deploy' })}</Button>
                </Space>
            </div></Col>
        </Row>
        <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'devices.description' })}</Paragraph>
        <Layout className='mb20 search-bar' >
            <Layout>
                <Content className='pr10'>
                    <Input value={filterParam.keywords}
                        onChange={handleQueryChange}
                        style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={formatMessage({ id: 'devices.searchPlaceholder' })}></Input>
                </Content>
                <Sider> <Space>
                    <Select style={{ width: 200 }}
                        optionList={listConnectStatus}
                        insetLabel={formatMessage({ id: 'devices.status' })}
                        onChange={handleConnectStatusChange}
                        value={filterParam.connectStatus}></Select>

                    <Select multiple
                        maxTagCount={1}
                        style={{ width: 200 }}
                        optionList={listOs}
                        insetLabel={formatMessage({ id: 'devices.filter.operatingSystem' })}
                        onChange={handleOsChange}
                        value={filterParam.os}></Select>
                    <Select
                        style={{ width: 200 }}
                        insetLabel={formatMessage({ id: 'devices.meshMode' })}
                        optionList={listMeshEnabled}
                        onChange={handleMeshEnabledChange}
                        value={filterParam.meshEnabled}
                    ></Select>
                </Space></Sider>
            </Layout>

        </Layout>
        <Tabs
            type="card"
            collapsible
            onChange={(activeKey: string) => {
                let activeGroup: MachineGroup | undefined = undefined;
                if (activeKey) {
                    groups.forEach(group => {
                        if (group.id + '' === activeKey) {
                            setCurGroup(group)
                            activeGroup = group;
                        }
                    })
                } else {
                    setCurGroup(undefined)
                }
                handleGroupChange(activeGroup);

                setFilterParam({ ...filterParam, group: activeKey })
                doNavigate({ ...filterParam, group: activeKey })
            }}
            activeKey={curGroup ? curGroup.id + '' : ''}
        >
            <TabPane tab={formatMessage({ id: 'devices.allDevices' })} itemKey=''></TabPane>
            {groups.map((group, index) => {
                return <TabPane key={index}
                    tab={group.alias ?
                        <Popover position='top' content={<div className='p10'>{group.description ? group.description : group.name}</div>}>{group.alias}</Popover>
                        : <span>{group.name}</span>}
                    itemKey={group.id + ''}></TabPane>
            })}
        </Tabs>

        <div style={{ height: 20 }} className='mb10' >  {!loading && <Tag>  {formatMessage({ id: 'devices.totalCount' })} {total}</Tag>} </div>
        <InfiniteScroll
            dataLength={devices.length} //This is important field to render the next data
            next={addPage}
            hasMore={devices.length < total}
            loader={<div><Spin></Spin></div>}
            endMessage={
                <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                    {devices.length > pageSize && <Paragraph type='tertiary'>{formatMessage({ id: 'devices.endMessage' })}</Paragraph>}
                </div>
            }
        >
            <Table
                rowKey={(record?: Machine) => record ? record.id + '' : ''}
                onChange={handleSort}
                empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={devices} pagination={false} />
        </InfiniteScroll>
        <BackTop style={{ right: 10 }} />
    </div>
        {deployVisible ?
            <Deploy close={() => { setDeployVisible(false) }} /> : null}
        {delVisible && curDevice ?
            <Del record={curDevice} success={() => {
                setDelVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }} close={() => { setDelVisible(false) }} /> : null}
        {expiryVisible && curDevice ?
            <Expiry record={curDevice} success={() => {
                setExpiryVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }} close={() => { setExpiryVisible(false) }} /> : null}
        {editRouteVisible && curDevice ?
            <EditRoute record={curDevice} success={() => { setReloadFlag(true) }} close={() => { setEditRouteVisible(false) }} /> : null}
        {renameVisible && curDevice ?
            <Rename record={curDevice}
                success={() => {
                    setRenameVisible(false);
                    setCurDevice(undefined)
                    setReloadFlag(true)
                }}
                close={() => { setRenameVisible(false); setCurDevice(undefined) }} /> : null}
        {editAclTagVisible && curDevice ?
            <AclTag
                success={() => {
                    setEditAclTagVisible(false);
                    setCurDevice(undefined)
                    setReloadFlag(true)
                }}
                record={curDevice} close={() => { setEditAclTagVisible(false); setCurDevice(undefined) }}></AclTag>
            : null}

        {curDevice && editGroupVisible && <EditGroup
            close={() => {
                setCurDevice(undefined)
                setEditGroupVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditGroupVisible(false)
                setReloadFlag(true)
            }}
        ></EditGroup>
        }
        {curDevice && curGroup && removeFromGroupVisible && <RemoveFromGroup
            close={() => {
                setCurDevice(undefined)
                setRemoveFromGroupVisible(false)
            }}
            record={curDevice}
            group={curGroup}
            success={() => {
                setRemoveFromGroupVisible(false)
                setReloadFlag(true)
            }}
        ></RemoveFromGroup>}
        {curDevice && editRelayVisiable && <EditRelay
            close={() => {
                setCurDevice(undefined)
                setEditRelayVisiable(false)
            }}
            record={curDevice}
            success={() => {
                setEditRelayVisiable(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditRelay>}
        {curDevice && editRejectRouteVisible && <EditRejectRoute
            close={() => {
                setCurDevice(undefined)
                setEditRejectRouteVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditRejectRouteVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditRejectRoute>}
        {curDevice && editMeshEnabledVisible && <EditMeshEnabled
            close={() => {
                setCurDevice(undefined)
                setEditMeshEnabledVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditMeshEnabledVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditMeshEnabled>}
        {curDevice && editRouteNewVisible && <EditRouteNew
            close={() => {
                setCurDevice(undefined)
                setEditRouteNewVisible(false)
            }}
            record={curDevice}
            success={() => {
                setEditRouteNewVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></EditRouteNew>}
        {curDevice && rangeAcceptVisible && <RangeAccept
            close={() => {
                setCurDevice(undefined)
                setRangeAcceptVisible(false)
            }}
            record={curDevice}
            success={() => {
                setRangeAcceptVisible(false)
                setCurDevice(undefined)
                setReloadFlag(true)
            }}
        ></RangeAccept>}

        {tagownersVisible && aclPolicy && <Tagowners
            aclPolicy={aclPolicy}
            onChange={(policy) => setACLPolicy(policy)}
            close={() => {
                setTagownersVisible(false);
            }}
        ></Tagowners>}
    </>
}

export default Index;
