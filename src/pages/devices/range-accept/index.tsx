import { FC, useState, useContext, useEffect } from 'react'
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { Typography, Modal, Notification, Button, Row, Col, Divider, Space, TreeSelect } from '@douyinfe/semi-ui';
import useServiceGroup from '@/pages/srv/group/useServicesGroup';
import { ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { NetworkService } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { IconChevronLeft, IconChevronRight } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';

const { Title, Text, Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [loading, setLoading] = useState(false);
    const flynet = useContext(FlynetGeneralContext);

    const { serviceGroupTreeData } = useServiceGroup(ServiceType.ROUTING);

    const [_networkServices, setNetworkServices] = useState<NetworkService[]>([]);

    const [acceptServices, setAcceptServices] = useState<NetworkService[]>([]);
    const [excludeServices, setExcludeServices] = useState<NetworkService[]>([]);

    useEffect(() => {
        flylayerClient.listNetworkServices({
            flynetId: flynet.id
        }).then((res) => {
            setNetworkServices(res.services);
            setAcceptServices(res.services);
        }).catch((_err) => {
            Notification.error({
                title: formatMessage({ id: 'devices.rangeAccept.getServicesFailed' })
            })
        })
    }, [])

    const handleSubmit = () => {
        setLoading(true);
    }
    return (
        <>
            <Modal
                width={800}
                title={formatMessage({ id: 'devices.rangeAccept.title' })}
                visible={true}
                onCancel={props.close}
                onOk={handleSubmit}
                okButtonProps={{ loading: loading }}
                className='semi-modal'
                maskClosable={false}
            >
                <Title className="mb10" heading={6} type="tertiary">{formatMessage({ id: 'devices.rangeAccept.serviceGroups' })}</Title>
                <TreeSelect className='mb20' style={{ width: '100%' }}
                    multiple
                    expandAll
                    checkRelation='unRelated'
                    treeData={
                        serviceGroupTreeData
                    }
                    filterTreeNode
                    showFilteredOnly
                    dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                ></TreeSelect>
                <Divider className='mb20'></Divider>
                <Title className="mb10" heading={6} type="tertiary">{formatMessage({ id: 'devices.rangeAccept.networkServices' })}</Title>
                <Row gutter={16}>
                    <Col span={12} style={{ borderRight: '1px solid #f0f0f0' }}>
                        <Paragraph className='mb10' type='secondary'>{formatMessage({ id: 'devices.rangeAccept.acceptServices' })}</Paragraph>
                        {acceptServices.map((item) => {
                            return (
                                <Row className='mb10'>
                                    <Col span={16}>
                                        <Text>{item.name}</Text>
                                    </Col>
                                    <Col span={8} className="btn-right-col">
                                        <Button size='small' type='tertiary' icon={<IconChevronRight />} onClick={() => {
                                            setAcceptServices(acceptServices.filter((service) => service.id != item.id));
                                            setExcludeServices([...excludeServices, item]);
                                        }}></Button>
                                    </Col>
                                </Row>
                            )
                        })}
                    </Col>

                    <Col span={12}>
                        <Paragraph className='mb10' type='secondary'>{formatMessage({ id: 'devices.rangeAccept.excludeServices' })}</Paragraph>
                        {excludeServices.map((item) => {
                            return (
                                <Paragraph className='mb10'>
                                    <Space>
                                    <Button size='small' type='tertiary' icon={<IconChevronLeft />} onClick={() => {
                                        setExcludeServices(excludeServices.filter((service) => service.id != item.id));
                                        setAcceptServices([...acceptServices, item]);
                                    }}></Button>
                                    <Text>{item.description} ({item.name})</Text>
                                    </Space>
                                </Paragraph>
                            )
                        })}
                    </Col>
                </Row>

            </Modal>
        </>
    )
}

export default Index;
