import { FC, useState } from 'react'
import { Typography, Modal, Form, Notification, Row, Col, <PERSON><PERSON>, Card, Collapse } from '@douyinfe/semi-ui';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

const { Paragraph, Text } = Typography;
import { MachineRoutes, AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss';
import { useLocale } from '@/locales';

import { isValidIPRangeOrCIDR } from '@/utils/validators';

interface Props {
    close: () => void,
    record: Machine,
    advertisedRoutes: AdvertisedRoute[],
    success?: (routes?: MachineRoutes) => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi<{ value: string, excludedValue: string, enable: boolean }>>()

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    // 初始化路由数据
    const initCSV = (advertisedRoutes: AdvertisedRoute[],
    ) => {
        const lines: string[] = [];
        const excludedLines: string[] = [];
        advertisedRoutes.forEach((route) => {
            const enable = route.enabled;
            let remark = route.remark;
            if (route.sys) {
                return;
            }
            if (route.routeType == AdvertisedRoute_RouteType.EXCLUDE) {
                excludedLines.push(`${enable},${route.route},${remark}`);
            } else {
                lines.push(`${enable},${route.route},${remark}`);
            }
        });

        return {
            lines: lines.join('\n'),
            excludedLines: excludedLines.join('\n')
        }
    }

    // 系统路由
    const sysRoutes = props.advertisedRoutes.filter((route) => route.sys && route.routeType == AdvertisedRoute_RouteType.ADVERTISED);
    // 系统排除路由
    const sysExcludedRoutes = props.advertisedRoutes.filter((route) => route.sys && route.routeType == AdvertisedRoute_RouteType.EXCLUDE);

    // 转换路由数据
    const parseRoutesFromCsv = (csvStr: string, routeType: AdvertisedRoute_RouteType): AdvertisedRoute[] => {
        if (!csvStr) {
            return [];
        }
        const lines = csvStr.split('\n');
        const advertisedRoutes: AdvertisedRoute[] = [];
        lines.forEach((line) => {
            const cols = line.split(',');
            if (cols.length !== 3) {
                return;
            }
            const [enable, cidr, remark] = cols;
            advertisedRoutes.push(new AdvertisedRoute({
                route: cidr.trim(),
                enabled: enable === 'true',
                remark: remark.trim(),
                routeType: routeType
            }));
        });
        return advertisedRoutes;
    }

    const initValue = initCSV(props.advertisedRoutes);

    // 点击确定按钮
    const handleOk = () => {
        if (!formApi) {
            return
        }
        let err = formApi.getError('value');
        if (err) {
            return;
        }
        const { value, excludedValue } = formApi.getValues();

        setLoading(true);

        const advertisedRoutes = parseRoutesFromCsv(value, AdvertisedRoute_RouteType.ADVERTISED);
        const excludedRoutes = parseRoutesFromCsv(excludedValue, AdvertisedRoute_RouteType.EXCLUDE);

        const routes = sysRoutes.concat(advertisedRoutes.concat(excludedRoutes));

        flylayerClient.saveMachineRoutes({
            machineId: props.record.id,
            advertisedRoutes: routes,
        }).then((res) => {
            props.success && props.success(res.routes);
            Notification.success({ content: formatMessage({ id: 'devices.batchEditRoute.updateSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.batchEditRoute.updateFailed' }), position: "bottomRight" })
        }).finally(() => setLoading(false))
    }

    const handleCancel = () => {
        props.close();
    };


    // 校验子网路由,csv格式，分三列，第一列是否启用（bool），第二列子网网段(cidr格式)，第三列描述信息(最大长度64)
    const validateValue = (val: string) => {

        const lines = val.split('\n');
        const mapIP = new Map<string, number>();
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const fields = line.split(',');
            if (line.length === 0) {
                if (i != lines.length - 1) {
                    return `第${i + 1}行为空行`;
                } else {
                    return ''
                }

            }

            if (fields.length !== 3) {
                return `第${i + 1}行缺少字段。每行应为三个字段:是否立即启用,子网网段(CIDR格式),描述信息。例如:true, **********/24, 办公一区`;
            }

            const field0 = fields[0].trim();
            const field1 = fields[1].trim();
            const field2 = fields[2].trim();

            if (field0 !== 'true' && field0 !== 'false') {
                return `第${i + 1}行第一列必须是true或者false`;
            }

            const cidrValidateResult = isValidIPRangeOrCIDR(field1);
            if (cidrValidateResult) {
                return `第${i + 1}行${cidrValidateResult}`;
            }
            if (field2.length > 64) {
                return `第${i + 1}行第三列描述信息最大长度64`;
            }

            const mapValue = mapIP.get(field1);
            if (mapValue !== undefined) {
                return `第${i + 1}行第二列子网网段${field1}与第${mapValue + 1}行重复`;
            } else {
                mapIP.set(field1, i);
            }
        }


        return '';
    }

    const [previewVisible, setPreviewVisible] = useState(false);

    const [previewLoading, setPreviewLoading] = useState(false);

    const [previewRoutes, setPreviewRoutes] = useState<string[]>([]);


    const handlePreview = () => {
        if (!formApi) {
            return
        }
        let err = formApi.getError('value');
        if (err) {
            return;
        }
        const { value, excludedValue } = formApi.getValues();
        setPreviewLoading(true);
        const advertisedRoutes = parseRoutesFromCsv(value, AdvertisedRoute_RouteType.ADVERTISED);
        const excludedRoutes = parseRoutesFromCsv(excludedValue, AdvertisedRoute_RouteType.EXCLUDE);
        flylayerClient.previewMachineRoutes({
            machineId: props.record.id,
            advertisedRoutes: advertisedRoutes.concat(excludedRoutes),
        }).then((res) => {
            setPreviewRoutes(res.routes);
            setPreviewVisible(true);
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.batchEditRoute.updateFailed' }), position: "bottomRight" })
        }).finally(() => setPreviewLoading(false))

    }



    return <>
        <Modal
            width={800}
            title={`批量编辑设备${props.record.givenName}的子网路由`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okText="确定"
            okButtonProps={{ loading }}
        >
            <div className={styles.batchEditRoute}>
                <Form getFormApi={SetFormApi} initValues={{ value: initValue.lines, excludedValue: initValue.excludedLines }} render={() => (<>
                    <Paragraph className='mb20'>{formatMessage({ id: 'devices.batchEditRoute.description' })}
                        {formatMessage({ id: 'devices.batchEditRoute.example' })} <Text type='warning'>{formatMessage({ id: 'devices.batchEditRoute.warning' })}</Text>
                    </Paragraph>
                    <Row className='mb10' gutter={12}>
                        <Col span={12}>
                            {/* <Title heading={6} className='mb10'>宣告路由</Title> */}
                            <Collapse defaultActiveKey={"2"} keepDOM>
                                <Collapse.Panel header="系统宣告路由" itemKey="1">
                                    {sysRoutes.map(route => {
                                        return <Paragraph type='tertiary' className='mb2'>{route.enabled ? 'true' : 'false'},{route.route},{route.remark}</Paragraph>
                                    })}
                                </Collapse.Panel>
                                <Collapse.Panel header="宣告路由" itemKey="2" className={styles.editPanel}>
                                    <Form.TextArea
                                        trigger='blur'
                                        autosize
                                        rows={20}
                                        className={styles.routeTextArea}
                                        field='value'
                                        noLabel
                                        validate={validateValue}
                                        placeholder='true, **********/24, 办公一区' />
                                </Collapse.Panel>
                            </Collapse>
                        </Col>
                        <Col span={12}>
                            {/* <Title heading={6} className='mb10'>排除路由</Title> */}
                            <Collapse defaultActiveKey={"2"} keepDOM>
                                <Collapse.Panel header="系统排除路由" itemKey="1" disabled={!sysExcludedRoutes || sysExcludedRoutes.length == 0}>
                                    <div className='p10'>
                                        {sysExcludedRoutes.map(route => {
                                            return <Paragraph type='tertiary' className='mb2'>{route.enabled ? 'true' : 'false'},{route.route},{route.remark}</Paragraph>
                                        })}
                                    </div>
                                </Collapse.Panel>
                                <Collapse.Panel header="排除路由" itemKey="2" className={styles.editPanel}>
                                    <Form.TextArea
                                        trigger='blur'
                                        autosize
                                        rows={20}
                                        className={styles.routeTextArea}
                                        field='excludedValue'
                                        noLabel
                                        validate={validateValue}
                                        placeholder='true, **********/24, 办公一区' />
                                </Collapse.Panel>
                            </Collapse>

                        </Col>
                    </Row>
                    <Button onClick={handlePreview} type='primary' disabled={!formApi?.getValues().excludedValue && !formApi?.getValues().value} loading={previewLoading}>预览</Button>
                </>)} />
            </div>
        </Modal>
        {
            previewVisible && <Modal
                width={800}
                title={`预览子网路由`}
                visible={true}
                onOk={() => setPreviewVisible(false)}
                onCancel={() => setPreviewVisible(false)}
                hasCancel={false}
                maskClosable={false}
                closeOnEsc={true}
                okText="关闭"
            >
                <div className={styles.batchEditRoute}>
                    {previewRoutes.length > 0 ? <div className='mb20'>{previewRoutes.length > 0 ? <Row gutter={4}>{previewRoutes.map((route, index) => {
                        return <Col xs={24} xl={6} key={index}><Text>{route}</Text></Col>
                    })}</Row> : <Card>这台设备没有宣告任何路由</Card>}</div> : <Card>这台设备没有宣告任何路由</Card>}
                </div>
            </Modal>
        }
    </>
}

export default Index;