import { FC, useEffect, useState } from 'react'
import { Typo<PERSON>, Modal, Switch, Row, Col, Notification, Card, Popover, Input, Banner, Button, Space, Pagination, Tabs, TabPane, Tooltip } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { MachineRoutes, AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";

import { flylayerClient } from '@/services/core';
import { IconInfoCircle, IconMinus, IconEdit, IconTick, IconSearch, IconUndo } from '@douyinfe/semi-icons';

import AddRoute from '@/pages/devices/add-route';
import BatchEditRoute from '@/pages/devices/batch-edit-route';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss';
import { isIPInRange } from '@/utils/format';
import { getMachine } from '@/services/device';
import { isValidIPRangeOrCIDR } from '@/utils/validators';
import { caseInsensitiveIncludes } from '@/utils/common';
import { useLocale } from '@/locales';

interface Props {
    close: () => void,
    record: Machine,
    success?: () => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 宣告路由
    const [advertisedRoutes, setAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 宣告路由过滤结果
    const [filteredAdvertisedRoutes, setFilteredAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 排除路由
    const [excludedRoutes, setExcludedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 排除路由过滤结果
    const [filteredExcludedRoutes, setFilteredExcludedRoutes] = useState<Array<AdvertisedRoute>>([]);

    const [allRoutes, setAllRoutes] = useState<Array<AdvertisedRoute>>([]);

    const [enableRoutesCount, setEnableRoutesCount] = useState(0);

    const [advertisedExitNode, setAdvertisedExitNode] = useState(false);
    const [enabledExitNode, setEnabledExitNode] = useState(false);

    const [ctrlRoutableIpsEnabled, _setCtrlRoutableIpsEnabled] = useState(false);
    const [ctrlRoutableIpsEnabledLoading, setCtrlRoutableIpsEnabledLoading] = useState(false);

    // 正在修改的路由节点
    const [loadingRoute, setLoadingRoute] = useState<string>();
    // 出口路由是否正在修改
    const [_exitLoading, setExitLoading] = useState(false);

    // 是否显示新建路由对话框
    const [showAddRoute, setShowAddRoute] = useState(false);

    // 是否显示批量编辑路由对话框
    const [showBatchEditRoute, setShowBatchEditRoute] = useState(false);

    const calEnableRoutesCount = (routes: Array<AdvertisedRoute>) => {
        let count = 0;
        routes.forEach(value => {
            if (value.enabled) {
                count++;
            }
        });
        setEnableRoutesCount(count);
    }

    const [previewRoutes, setPreviewRoutes] = useState<string[]>([]);

    const [machine, setMachine] = useState<Machine>();


    const query = () => {
        flylayerClient.getMachineRoutes({ machineId: props.record.id }).then(res => {
            if (res.routes) {
                const _advertisedRoutes: AdvertisedRoute[] = [];
                const _excludedRoutes: AdvertisedRoute[] = [];
                if (res.routes.advertisedRoutes && res.routes.advertisedRoutes.length > 0) {
                    res.routes.advertisedRoutes.forEach((value, _index) => {
                        if (value.routeType == AdvertisedRoute_RouteType.EXCLUDE) {
                            _excludedRoutes.push(value);
                        }
                        else {
                            _advertisedRoutes.push(value);
                        }
                    });
                    setAllRoutes(res.routes.advertisedRoutes);
                } else {
                    setAllRoutes([]);
                }

                setAdvertisedRoutes(_advertisedRoutes);
                setFilteredAdvertisedRoutes(_advertisedRoutes);
                setExcludedRoutes(_excludedRoutes);
                setFilteredExcludedRoutes(_excludedRoutes);

                // setCtrlRoutableIpsEnabled(res.routes.ctrlRoutableIpsEnabled);

                setAdvertisedExitNode(res.routes.advertisedExitNode);
                setEnabledExitNode(res.routes.enabledExitNode);
                calEnableRoutesCount(res.routes.advertisedRoutes);

                flylayerClient.previewMachineRoutes({
                    machineId: props.record.id,
                    advertisedRoutes: res.routes.advertisedRoutes,
                }).then((res) => {
                    setPreviewRoutes(res.routes);
                }).catch((err) => {
                    console.error(err);
                    Notification.error({ content: formatMessage({ id: 'devices.editRoute.previewFailed' }), position: "bottomRight" })
                })
            }
        }, err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.getRoutesFailed' }) })
        })

        getMachine(props.record.ipv4).then(res => {
            setMachine(res);
        })
    };

    useEffect(() => {
        query();
    }, [])

    const handleEnableMachineRoutes = (route: string) => {
        setLoadingRoute(route)
        flylayerClient.enableMachineRoutes({
            machineId: props.record.id,
            routes: [route],
            replace: false
        }).then(() => {
            query()

            setDataEdited(true);

            Notification.success({ content: formatMessage({ id: 'devices.editRoute.updateSuccess' }), position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.updateFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoadingRoute(undefined)
        })

    }

    const handleDisableMachineRoutes = (route: string) => {
        setLoadingRoute(route)
        flylayerClient.disableMachineRoutes({
            machineId: props.record.id,
            routes: [route],
        }).then(() => {
            query()
            setDataEdited(true);
            Notification.success({ content: formatMessage({ id: 'devices.editRoute.updateSuccess' }), position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.updateFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoadingRoute(undefined)
        })

    }

    const _handleEnableExitNode = () => {
        setExitLoading(true)
        flylayerClient.enableExitNode({
            machineId: props.record.id,
        }).then(() => {
            setDataEdited(true);
            Notification.success({ content: formatMessage({ id: 'devices.editRoute.updateSuccess' }), position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.updateFailed' }), position: "bottomRight" })
        }).finally(() => {
            setExitLoading(false)
        })
    }

    const _handleDisableExitNode = () => {
        setExitLoading(true)
        flylayerClient.disableExitNode({
            machineId: props.record.id,
        }).then(() => {
            setDataEdited(true);
            Notification.success({ content: formatMessage({ id: 'devices.editRoute.updateSuccess' }), position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.updateFailed' }), position: "bottomRight" })
        }).finally(() => {
            setExitLoading(false)
        })
    }

    // 添加子网路由
    const handleAddMachineRoutes = () => {
        setShowAddRoute(true);
    }

    // 批量编辑子网路由
    const handleBatchEditMachineRoutes = () => {
        setShowBatchEditRoute(true);
    }

    // 删除子网路由
    const handleDeleteMachineRoutes = (route: string) => {
        flylayerClient.deleteAdvertisedRoutes({
            machineId: props.record.id,
            advertisedRoutes: [route],
        }).then(() => {
            setFilter('');
            setPage(1)
            query();
            setDataEdited(true);
            Notification.success({ content: formatMessage({ id: 'devices.editRoute.updateSuccess' }), position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.updateFailed' }), position: "bottomRight" })
        })
    }


    const handleCtrlRoutableIpsEnabledChange = () => {
        setCtrlRoutableIpsEnabledLoading(true);
        flylayerClient.disableCtrlRoutableIPs({
            machineId: props.record.id
        }).then(() => {
            query();
            Notification.success({ content: formatMessage({ id: 'devices.editRoute.updateSuccess' }), position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.updateFailed' }), position: "bottomRight" })
        }).finally(() => {
            setCtrlRoutableIpsEnabledLoading(false);
        })
    }

    const pageSize = 20;
    const [page, setPage] = useState(1);
    const [excludedPage, setExcludedPage] = useState(1);

    const [editIndex, setEditIndex] = useState(-1);
    const [editLoading, setEditLoading] = useState(false);
    const [editRouteValue, setEditRouteValue] = useState('');
    const [editRemarkValue, setEditRemarkValue] = useState('');

    const [excludedEditIndex, setExcludedEditIndex] = useState(-1);
    const [excludedEditLoading, setExcludedEditLoading] = useState(false);
    const [excludedEditRouteValue, setExcludedEditRouteValue] = useState('');
    const [excludedEditRemarkValue, setExcludedEditRemarkValue] = useState('');


    const [filter, setFilter] = useState('');
    const handleFilter = (filterTxt: string) => {
        // const filterTxt = filter.trim();
        if (filterTxt == '') {
            setFilteredAdvertisedRoutes(advertisedRoutes);
            setFilteredExcludedRoutes(excludedRoutes);
            return;
        }

        setPage(1);
        setExcludedPage(1);
        const newRoutes: AdvertisedRoute[] = [];
        advertisedRoutes.forEach((value, _index) => {
            if (value.route.indexOf(filterTxt) != -1 || caseInsensitiveIncludes(value.remark, filterTxt) || isIPInRange(filterTxt, value.route)) {
                newRoutes.push(value);
            }
        });
        setFilteredAdvertisedRoutes(newRoutes);

        const newExcludedRoutes: AdvertisedRoute[] = [];
        excludedRoutes.forEach((value, _index) => {
            if (value.route.indexOf(filterTxt) != -1 || caseInsensitiveIncludes(value.remark, filterTxt) || isIPInRange(filterTxt, value.route)) {
                newExcludedRoutes.push(value);
            }
        });
        setFilteredExcludedRoutes(newExcludedRoutes);

    }

    // 数据是否进行了修改
    const [dataEdited, setDataEdited] = useState(false);

    const handleExit = () => {
        if (dataEdited && props.success) {
            props.success()
        }

        props.close();
    }

    const handleRouteEdit = (index: number) => {
        setEditLoading(false);
        const route = filteredAdvertisedRoutes[index];

        const newFilteredRoutes: any[] = filteredAdvertisedRoutes.map((value) => {
            if (value.id == route.id) {
                return { ...value, remark: editRemarkValue, route: editRouteValue }
            }
            return value;
        })
        setFilteredAdvertisedRoutes(newFilteredRoutes);

        flylayerClient.saveMachineRoutes({
            machineId: props.record.id,
            advertisedRoutes: allRoutes.map((value, _index) => {
                if (value.id == route.id) {
                    return { ...value, remark: editRemarkValue, route: editRouteValue }
                }
                return value;
            }),
        }).then((_res) => {
            query();
            setEditRemarkValue('');
            setEditRouteValue('');
            setEditIndex(-1);
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.saveRoutesFailed' }), position: "bottomRight" })
        }).finally(() => setEditLoading(false))
    }

    const handleExcludedRouteEdit = (index: number) => {

        setExcludedEditLoading(false);

        const route = filteredExcludedRoutes[index];

        const newFilteredRoutes: any[] = filteredExcludedRoutes.map((value, _index) => {
            if (value.id == route.id) {
                return { ...value, remark: excludedEditRemarkValue, route: excludedEditRouteValue }
            }
            return value;
        })
        setFilteredExcludedRoutes(newFilteredRoutes);

        flylayerClient.saveMachineRoutes({
            machineId: props.record.id,
            advertisedRoutes: allRoutes.map((value, _index) => {
                if (value.id == route.id) {
                    return { ...value, remark: excludedEditRemarkValue, route: excludedEditRouteValue }
                }
                return value;
            }),
        }).then((_res) => {
            query();
            setExcludedEditRemarkValue('');
            setExcludedEditRouteValue('');
            setExcludedEditIndex(-1);
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.editRoute.saveRoutesFailed' }), position: "bottomRight" })
        }).finally(() => setExcludedEditLoading(false))
    }

    return <>
        <Modal
            width={800}
            title={formatMessage({ id: 'devices.editRoute.title' }).replace('{deviceName}', props.record.givenName)}
            visible={true}
            onOk={handleExit}
            onCancel={handleExit}
            closeOnEsc={true}
            maskClosable={false}
            hasCancel={false}
            okText={formatMessage({ id: 'components.common.close' })}
        >
            {
                !props.record.keyExpiryDisabled ? <> {enableRoutesCount > 0 && advertisedRoutes.length > 0 || advertisedExitNode && enabledExitNode ? <Banner
                    className='mb20'
                    type="warning"
                    fullMode={false}
                    closeIcon={null}
                    title={formatMessage({ id: 'devices.editRoute.keyExpired' })}
                    description={formatMessage({ id: 'devices.editRoute.keyExpiredDesc' })}
                /> : ''}
                </> : ''
            }

            <Row className='mb10'>
                <Col span={20}>
                    <div><Title heading={4} className="mb2">{formatMessage({ id: 'devices.editRoute.connector' })}</Title>
                    </div></Col>
                <Col span={4}><div className='btn-right-col'>

                </div></Col>
            </Row>
            <Row className='mb10'>
                <Col><Paragraph>{formatMessage({ id: 'devices.editRoute.connectorDesc' })}
                    {/* <a className='learnMore' target='_blank' href='/docs/start/editnode'>了解更多<IconChevronRight /></a> */}
                    </Paragraph></Col>
                <Col></Col>
            </Row>
            <Row className='mb10'>
                <Col span={12}>
                    <Space>
                        {!machine || !machine.allowControlAdvertised ?
                            <>
                                <Tooltip content={<div dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.editRoute.enableFromControl' }) }} />} >
                                    <Button disabled theme='solid'>{formatMessage({ id: 'devices.editRoute.addRoute' })}</Button>
                                </Tooltip>
                                <Tooltip content={<div dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.editRoute.enableFromControl' }) }} />} >
                                    <Button disabled theme='solid'>{formatMessage({ id: 'devices.editRoute.batchEdit' })}</Button>
                                </Tooltip>
                            </> : <>
                                <Button theme='solid' onClick={handleAddMachineRoutes}>{formatMessage({ id: 'devices.editRoute.addRoute' })}</Button>
                                <Button theme='solid' onClick={handleBatchEditMachineRoutes}>{formatMessage({ id: 'devices.editRoute.batchEdit' })}</Button>
                            </>}
                        <Input
                            placeholder={formatMessage({ id: 'devices.editRoute.searchPlaceholder' })}
                            style={{ width: 260 }}
                            suffix={<IconSearch />}
                            value={filter} onChange={(val) => {
                                setFilter(val)
                                handleFilter(val.trim())
                            }}></Input>
                    </Space>
                </Col>
                <Col span={12}>{ctrlRoutableIpsEnabled && <div className={styles.overrideLocalDns}>
                    <Popover content={<div className="p10 mw400" dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.editRoute.disableLocalRoutesDesc' }) }} />}>
                        <IconInfoCircle />
                    </Popover>&nbsp;
                    {formatMessage({ id: 'devices.editRoute.disableLocalRoutes' })}&nbsp;<Switch size="small" loading={ctrlRoutableIpsEnabledLoading}
                        onChange={() => Modal.confirm({
                            title: formatMessage({ id: 'devices.editRoute.useLocalRoutes' }),
                            content: formatMessage({ id: 'devices.editRoute.useLocalRoutesConfirm' }),
                            okText: formatMessage({ id: 'components.common.confirm' }),
                            cancelText: formatMessage({ id: 'components.common.cancel' }),
                            okButtonProps: { type: 'danger' },
                            onOk: () => {
                                handleCtrlRoutableIpsEnabledChange()
                            }
                        })}
                        checked={true}></Switch>
                </div>}</Col>
            </Row>
            {/* 
            <Divider className='mb10' /> */}

            <Tabs type='card'>
                <TabPane tab={formatMessage({ id: 'devices.editRoute.advertisedRoutes' })} itemKey="1" style={{ paddingTop: 10 }}>
                    {filteredAdvertisedRoutes.map((route: AdvertisedRoute, index: number) => {
                        if (index >= (page - 1) * pageSize && index < page * pageSize) {
                            return <Row key={index} className='mb10' gutter={8} style={{opacity: route.sys ? 0.3 : 1,}}>
                                <Col span={4}><div className={styles.colFormItem}>
                                    <Switch
                                        checked={route.enabled}
                                        loading={loadingRoute == route.id + ''}
                                        onChange={(checked) => {
                                            if(route.sys) {
                                                return;
                                            }
                                            if (checked) {
                                                handleEnableMachineRoutes(route.id + '')
                                            } else {
                                                handleDisableMachineRoutes(route.id + '')
                                            }
                                        }}
                                    /></div></Col>
                                <Col span={8}>
                                    {index != editIndex ? <Paragraph>{route.route}</Paragraph> :
                                        <Input onChange={(v) => {
                                            setEditRouteValue(v);
                                        }} value={editRouteValue} className={styles.remarkInput} size='small' />}
                                </Col>
                                <Col span={8}>
                                    <div style={{ display: 'flex', alignItems: 'center', height: 24 }}>
                                        {index != editIndex ? <Paragraph type='tertiary' ellipsis={{}}>{route.remark}</Paragraph> :
                                            <Input onChange={(e) => { setEditRemarkValue(e); }}
                                                value={editRemarkValue}
                                                className={styles.remarkInput}
                                                size='small' ></Input>}
                                    </div>
                                </Col>
                                <Col span={4} style={{ display: 'flex', justifyContent: 'right', alignItems: 'center' }}>
                                    {!machine || !machine.allowControlAdvertised ?
                                        <></>
                                        : <Space>
                                            {index == editIndex ? <>
                                                <Button size='small' icon={<IconUndo />} onClick={() => {
                                                    setEditIndex(-1);
                                                    setEditRemarkValue('');
                                                    setEditRouteValue('');
                                                }} />
                                                <Button size='small' icon={<IconTick />}
                                                    loading={editLoading}
                                                    onClick={() => {

                                                        const cidrValidateResult = isValidIPRangeOrCIDR(editRouteValue);
                                                        if (cidrValidateResult) {
                                                            Notification.error({ content: cidrValidateResult })
                                                            return;
                                                        }
                                                        if (filteredAdvertisedRoutes[index].route == editRouteValue) {
                                                            handleRouteEdit(index);
                                                        } else {
                                                            Modal.confirm({
                                                                title: formatMessage({ id: 'devices.editRoute.modifyRouteTitle' }),
                                                                content: <>
                                                                    <Paragraph type='warning' className='mb20'>{formatMessage({ id: 'devices.editRoute.modifyRouteWarning' })}
                                                                    </Paragraph>
                                                                    <Paragraph className='mb20'>{formatMessage({ id: 'devices.editRoute.modifyRouteConfirm' })}</Paragraph>
                                                                </>,
                                                                okText: formatMessage({ id: 'components.common.confirm' }),
                                                                cancelText: formatMessage({ id: 'components.common.cancel' }),
                                                                okButtonProps: { type: 'danger' },
                                                                onOk: () => {
                                                                    handleRouteEdit(index);
                                                                }
                                                            })
                                                        }




                                                    }} type='danger' />
                                            </> : <>
                                                <Button
                                                    onClick={() => {
                                                        setEditIndex(index);
                                                        setEditRemarkValue(route.remark);
                                                        setEditRouteValue(route.route);
                                                    }}
                                                    disabled={route.sys}
                                                    size='small' icon={<IconEdit />} />
                                            </>}

                                            <Button
                                                type='danger'
                                                disabled={route.sys}
                                                onClick={() => Modal.confirm({
                                                    title: formatMessage({ id: 'devices.editRoute.deleteRouteTitle' }),
                                                    content: <>
                                                        <Paragraph type='warning' className='mb20'>{formatMessage({ id: 'devices.editRoute.deleteRouteWarning' })}
                                                        </Paragraph>
                                                        <Paragraph className='mb20'>{formatMessage({ id: 'devices.editRoute.deleteRouteConfirm' })}</Paragraph>
                                                    </>,
                                                    okText: formatMessage({ id: 'components.common.delete' }),
                                                    cancelText: formatMessage({ id: 'components.common.cancel' }),
                                                    okButtonProps: { type: 'danger' },
                                                    onOk: () => {
                                                        handleDeleteMachineRoutes(route.id + '')
                                                    }
                                                })} size='small' icon={<IconMinus />}></Button>
                                        </Space>}
                                </Col>
                            </Row>
                        }
                        else return '';
                    })}
                    {filteredAdvertisedRoutes && filteredAdvertisedRoutes.length > pageSize ? <Pagination
                        total={advertisedRoutes.length}
                        style={{ marginBottom: 12 }}
                        pageSize={pageSize}
                        onChange={(page) => {
                            setPage(page)
                        }}
                        currentPage={page} ></Pagination>
                        : ''}
                    {advertisedRoutes && advertisedRoutes.length > 0 && filteredAdvertisedRoutes && filteredAdvertisedRoutes.length == 0 ? <Card>{formatMessage({ id: 'devices.editRoute.noMatchingRoutes' })}</Card> : ''}
                    {!advertisedRoutes || advertisedRoutes.length == 0 ? <Card>{formatMessage({ id: 'devices.editRoute.noAdvertisedRoutes' })}</Card> : ''}
                </TabPane>
                <TabPane tab={formatMessage({ id: 'devices.editRoute.excludeRoutes' })} style={{ paddingTop: 10 }} itemKey="2">
                    {filteredExcludedRoutes.map((route: AdvertisedRoute, index: number) => {
                        if (index >= (excludedPage - 1) * pageSize && index < excludedPage * pageSize) {

                            return <Row key={index} className='mb10' gutter={8} style={{opacity: route.sys ? 0.3 : 1,}}>
                                <Col span={4}><div className={styles.colFormItem}>
                                    <Switch
                                        checked={route.enabled}
                                        loading={loadingRoute == route.id + ''}
                                        onChange={(checked) => {
                                            if(route.sys) {
                                                return;
                                            }
                                            if (checked) {
                                                handleEnableMachineRoutes(route.id + '')
                                            } else {
                                                handleDisableMachineRoutes(route.id + '')
                                            }
                                        }}
                                    /></div></Col>
                                <Col span={8}>
                                    {index != excludedEditIndex ? <Paragraph>{route.route}</Paragraph> :
                                        <Input onChange={(v) => {
                                            setExcludedEditRouteValue(v);
                                        }} value={excludedEditRouteValue} className={styles.remarkInput} size='small' />}
                                </Col>
                                <Col span={8}>
                                    <div style={{ display: 'flex', alignItems: 'center', height: 24 }}>
                                        {index != excludedEditIndex ? <Paragraph type='tertiary' ellipsis={{}}>{route.remark}</Paragraph> :
                                            <Input onChange={(e) => { setExcludedEditRemarkValue(e); }}
                                                value={excludedEditRemarkValue}
                                                className={styles.remarkInput}
                                                size='small' ></Input>}
                                    </div>
                                </Col>
                                <Col span={4} style={{ display: 'flex', justifyContent: 'right', alignItems: 'center' }}>
                                    {!machine || !machine.allowControlAdvertised ?
                                        <></>
                                        :
                                        <Space>
                                            {index == excludedEditIndex ? <>
                                                <Button size='small' icon={<IconUndo />} onClick={() => {
                                                    setExcludedEditIndex(-1);
                                                    setExcludedEditRemarkValue('');
                                                    setExcludedEditRouteValue('');
                                                }} />
                                                <Button size='small' icon={<IconTick />}
                                                    loading={excludedEditLoading}
                                                    onClick={() => {

                                                        const cidrValidateResult = isValidIPRangeOrCIDR(excludedEditRouteValue);
                                                        if (cidrValidateResult) {
                                                            Notification.error({ content: cidrValidateResult })
                                                            return;
                                                        }

                                                        if (filteredExcludedRoutes[index].route == excludedEditRouteValue) {
                                                            handleExcludedRouteEdit(index);
                                                        } else {

                                                            Modal.confirm({
                                                                title: formatMessage({ id: 'devices.editRoute.modifyRouteTitle' }),
                                                                content: <>
                                                                    <Paragraph type='warning' className='mb20'>{formatMessage({ id: 'devices.editRoute.modifyRouteWarning' })}
                                                                    </Paragraph>
                                                                    <Paragraph className='mb20'>{formatMessage({ id: 'devices.editRoute.modifyRouteConfirm' })}</Paragraph>
                                                                </>,
                                                                okText: formatMessage({ id: 'components.common.confirm' }),
                                                                cancelText: formatMessage({ id: 'components.common.cancel' }),
                                                                okButtonProps: { type: 'danger' },
                                                                onOk: () => {
                                                                    handleExcludedRouteEdit(index);
                                                                }
                                                            })
                                                        }

                                                    }
                                                    } type='danger' />
                                            </> : <>
                                                <Button
                                                    onClick={() => {
                                                        setExcludedEditIndex(index);
                                                        setExcludedEditRemarkValue(route.remark);
                                                        setExcludedEditRouteValue(route.route);
                                                    }}
                                                    disabled={route.sys}
                                                    size='small' icon={<IconEdit />} />
                                            </>}
                                            <Button
                                                type='danger'
                                                disabled={route.sys}
                                                onClick={() => Modal.confirm({
                                                    title: formatMessage({ id: 'devices.editRoute.deleteRouteTitle' }),
                                                    content: <>
                                                        <Paragraph type='warning' className='mb20'>{formatMessage({ id: 'devices.editRoute.deleteRouteWarning' })}
                                                        </Paragraph>
                                                        <Paragraph className='mb20'>{formatMessage({ id: 'devices.editRoute.deleteRouteConfirm' })}</Paragraph>
                                                    </>,
                                                    okText: formatMessage({ id: 'components.common.delete' }),
                                                    cancelText: formatMessage({ id: 'components.common.cancel' }),
                                                    okButtonProps: { type: 'danger' },
                                                    onOk: () => {
                                                        handleDeleteMachineRoutes(route.id + '')
                                                    }
                                                })} size='small' icon={<IconMinus />}></Button></Space>}
                                </Col>
                            </Row>
                        }
                        else return '';
                    })}
                    {filteredExcludedRoutes && filteredExcludedRoutes.length > pageSize ? <Pagination
                        total={excludedRoutes.length}
                        style={{ marginBottom: 12 }}
                        pageSize={pageSize}
                        onChange={(page) => {
                            setExcludedPage(page)
                        }}
                        currentPage={excludedPage} ></Pagination>
                        : ''}
                    {excludedRoutes && excludedRoutes.length > 0 && filteredExcludedRoutes && filteredExcludedRoutes.length == 0 ? <Card>{formatMessage({ id: 'devices.editRoute.noMatchingRoutes' })}</Card> : ''}
                    {!excludedRoutes || excludedRoutes.length == 0 ? <Card>{formatMessage({ id: 'devices.editRoute.noExcludedRoutes' })}</Card> : ''}
                </TabPane>
                <TabPane tab={formatMessage({ id: 'devices.editRoute.preview' })} style={{ paddingTop: 10 }} itemKey="3">
                    <div style={{ maxHeight: '690px', overflowY: 'auto', overflowX: 'hidden' }}>{previewRoutes.length > 0 ? <div className='mb20'>{previewRoutes.length > 0 ? <Row gutter={4}>{previewRoutes.map((route, index) => {
                        return <Col xs={24} xl={6} key={index}><Text>{route}</Text></Col>
                    })}</Row> : <Card>{formatMessage({ id: 'devices.editRoute.noAdvertisedRoutes' })}</Card>}</div> : <Card>{formatMessage({ id: 'devices.editRoute.noAdvertisedRoutes' })}</Card>}</div></TabPane>
            </Tabs>

{/* 
            <div className='mb20'></div>
            <Title className='mb10' heading={4}>出口节点</Title>
            <Paragraph className='mb10'>允许网络里的其他设备通过这台设备联接互联网。 <a className='learnMore' target='_blank' href='/docs/start/exitnode'>了解更多<IconChevronRight /></a></Paragraph>
            <Divider className='mb10' />
            <Row className='mb20'>
                <Col span={4}><div className={styles.colFormItem}>
                    <Switch
                        checked={enabledExitNode}
                        loading={exitLoading}
                        disabled={!advertisedExitNode} onChange={(checked) => {
                            if (checked) {
                                handleEnableExitNode()
                            } else {
                                handleDisableExitNode()
                            }
                            setEnabledExitNode(checked)
                        }} /></div></Col>
                <Col span={20}><Paragraph>用于出口节点&nbsp;
                    {!advertisedExitNode ? <Popover
                        content={<div className='p10' style={{ width: 300 }}><span>该设备未将自己宣告为出口节点。 重新运行 <code>flylayer up</code> 命令加上参数 <code >--advertise-exit-node</code> 开启该功能。</span>
                        </div>}><IconInfoCircle style={{ verticalAlign: 'sup' }} />
                    </Popover> : ''}
                </Paragraph></Col>
            </Row> */}
        </Modal>
        {
            showAddRoute && <AddRoute
                advertisedRoutes={advertisedRoutes}
                record={props.record}
                close={() => {
                    setShowAddRoute(false)
                }}
                success={(routes?: MachineRoutes) => {
                    if (routes) {
                        query();
                    }
                    setDataEdited(true);
                    setShowAddRoute(false)
                }}
            ></AddRoute>
        }
        {
            showBatchEditRoute && <BatchEditRoute
                record={props.record}
                advertisedRoutes={allRoutes}
                close={() => {
                    setShowBatchEditRoute(false)
                }}
                success={(routes?: MachineRoutes) => {
                    if (routes) {
                        query();
                    }
                    setDataEdited(true);
                    setShowBatchEditRoute(false)
                }}
            ></BatchEditRoute>

        }
    </>
}
export default Index;
