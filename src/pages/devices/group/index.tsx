import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Divider } from '@douyinfe/semi-ui';
import { getQueryParam } from '@/utils/query';
import { useLocale } from '@/locales';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import GroupAdd from '../group-add';
import GroupEdit from '../group-edit';
import GroupDel from '../group-del';
import GroupDns from '../group-dns';
import useTable from './useTable';
import { GroupFilter } from './useTable';
import SearchFilter, { FilterParam } from '@/components/search-filter';
import { MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';

const { Title } = Typography;

// 根据URL参数设置过滤参数
const getGroupFilter = (location: Location): GroupFilter => {
    const query: string = getQueryParam('query', location) as string;
    return {
        query: query || ''
    }
}

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const initFilter : GroupFilter = getGroupFilter(useLocation())
    const {
        columns,
        loading,
        allGroups: _allGroups,
        groups,
        selectedGroup,
        setSelectedGroup,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        groupDnsVisible,
        setGroupDnsVisible,
        reloadFlag: _reloadFlag,
        setReloadFlag,
        filter,
        setFilter
    } = useTable(initFilter);
    const navigate = useNavigate();
    // 过滤参数改变时跳转路由
    const doNavigate = (param: GroupFilter) => {
        const { query } = param;
        navigate(`${BASE_PATH}/devices/group/?query=${query}`);
    }



    const [createVisible, setCreateVisible] = useState(false);
    
    const [filterParams, setFilterParams] = useState<FilterParam[]>([{
        name: 'query',
        placeholder: formatMessage({ id: 'devices.group.searchPlaceholder' }),
        label: formatMessage({ id: 'devices.group.query' }),
        value: initFilter.query || '',
    }
    
    ]);

    return <><div className='general-page'><Breadcrumb routes={
        [
            {
                path: `${BASE_PATH}/devices`,
                href: `${BASE_PATH}/devices`,
                name: formatMessage({ id: 'devices.allDevices' })
            },
            {
                name: formatMessage({ id: 'devices.deviceGroup' }),
            }
        ]
    }>
    </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>{formatMessage({ id: 'devices.group.title' })}</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>{formatMessage({ id: 'devices.group.createGroup' })}</Button>
                </Space>
            </div></Col>
        </Row>
        <Divider className='mb20'></Divider>
        <SearchFilter onChange={(val: string, filterParam) => {
            setFilter({ ...filter, [filterParam.name]: val })
            doNavigate({ ...filter, [filterParam.name]: val });
            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);

        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
        {/* <div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  设备总数 {services.length}</Tag>} </div> */}
        <Table
            rowKey={(record?: MachineGroup) => record ? record.id + '' : ''}
            expandRowByClick
            // expandAllRows={services.length < 10}
            empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={groups} pagination={false} />
    </div>

        {createVisible && <GroupAdd
            close={() => { setCreateVisible(false); }}
            success={() => {
                setCreateVisible(false)
                setReloadFlag(true)
            }}
        ></GroupAdd>
        }

        {delVisible && selectedGroup && <GroupDel
            close={() => {
                setDelVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setDelVisible(false)
                setSelectedGroup(undefined)
                setReloadFlag(true)
            }}
            record={selectedGroup}
        ></GroupDel>}
        {editVisible && selectedGroup && <GroupEdit
            userGroupId={selectedGroup.id}
            close={() => {
                setEditVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setEditVisible(false)
                setReloadFlag(true)
            }}
        ></GroupEdit>}
        {
            groupDnsVisible && selectedGroup && <GroupDns
                groupId={selectedGroup.id}
                groupName={selectedGroup.alias}
                close={() => {
                    setGroupDnsVisible(false)
                    setSelectedGroup(undefined)
                }}
                success={() => {
                    setSelectedGroup(undefined)
                    setGroupDnsVisible(false)
                    setReloadFlag(true)
                }}
            ></GroupDns>
        }
    </>
}

export default Index;
