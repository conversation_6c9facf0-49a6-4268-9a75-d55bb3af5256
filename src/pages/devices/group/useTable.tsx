import { useEffect, useState, useContext } from 'react';

import { Typography, Dropdown, Button } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';

import { MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import DateFormat from '@/components/date-format';
import { BASE_PATH } from '@/constants/router';
import { useNavigate } from 'react-router-dom';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { caseInsensitiveIncludes } from '@/utils/common';

const { Title, Paragraph, Text } = Typography;
export type GroupFilter = {
    query?: string;
}

const useTable = (filterParam: GroupFilter) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 服务组列表
    const [groups, setGroups] = useState<MachineGroup[]>([]);
    // 全部服务组列表
    const [allGroups, setAllGroups] = useState<MachineGroup[]>([]);

    // 编辑弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 删除弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);

    const [groupDnsVisible, setGroupDnsVisible] = useState(false);

    // 当前菜单选中服务
    const [selectedGroup, setSelectedGroup] = useState<MachineGroup>();

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    // 过滤参数
    const [filter, setFilter] = useState<GroupFilter>(filterParam);

    // 表格列
    const columns = [{
        title: formatMessage({ id: 'devices.group.table.name' }),
        dataIndex: 'name',
        render: (_field: string, record: MachineGroup, _index: number) => {
            return <>
                <div style={{ display: 'inline-flex' }}>
                    <div>
                        <Title heading={6}>
                            <a href={`${BASE_PATH}/devices?group=${record.id}`} onClick={(e) => { e.stopPropagation() }}>
                                {record.alias}
                            </a>
                        </Title>
                        <Paragraph size='small'>{record.name}</Paragraph>
                    </div>
                </div>
            </>
        },
    },
    {
        width: 460,
        title: formatMessage({ id: 'devices.group.table.description' }),
        dataIndex: 'description',
        render: (field: string) => {
            return <Text>{field}</Text>
        },
    },
    {
        width: 160,
        title: formatMessage({ id: 'devices.group.table.type' }),
        dataIndex: 'type',
        render: (_field: string, record: MachineGroup, _index: number) => {

            return <>{record.type === GroupType.GROUP_STATIC && formatMessage({ id: 'devices.group.type.static' })}
                {record.type === GroupType.GROUP_DYNAMIC && formatMessage({ id: 'devices.group.type.dynamic' })}
            </>
        },
    },
    {
        width: 160,
        title: formatMessage({ id: 'devices.group.table.deviceCount' }),
        dataIndex: 'machines',
        render: (_field: string, record: MachineGroup, _index: number) => {
            let count = record.machines.length

            return <>{count}</>
        },
    }, {
        width: 200,
        title: formatMessage({ id: 'devices.group.table.createdTime' }),
        dataIndex: 'createdAt',
        render: (_field: string, record: MachineGroup, _index: number) => {
            return <>
                <DateFormat date={record.createdAt}></DateFormat>

            </>
        }
    },
    {
        width: 100,
        title: '',
        dataIndex: 'operate',
        render: (field: string, record: MachineGroup) => {
            return <div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>
                        <Dropdown.Item onClick={() => {
                            navigate(`${BASE_PATH}/devices?group=${record.id}`)
                        }}>查看设备</Dropdown.Item>
                        <Dropdown.Item
                            onClick={() => {
                                setSelectedGroup(record)
                                setEditVisible(true)
                            }}
                        >编辑设备组</Dropdown.Item>

                        <Dropdown.Item
                            onClick={() => {
                                setSelectedGroup(record)
                                setGroupDnsVisible(true)
                            }}
                        >网络设置</Dropdown.Item>
                        <Dropdown.Divider />

                        <Dropdown.Item type="danger"
                            onClick={() => {
                                setSelectedGroup(record)
                                setDelVisible(true)
                            }}
                        >删除设备组</Dropdown.Item>
                    </Dropdown.Menu>
                }
            >
                <Button><IconMore className='align-v-center' /></Button>
            </Dropdown></div>;
        },
    }];

    // 加载数据
    const query = () => {
        setLoading(true)

        flylayerClient.listMachineGroups({
            flynetId: flynet.id
        }).then((res) => {
            let list = res.groups
            list.sort((a, b) => {
                return a.priority - b.priority
            })
            setGroups(list)
            setAllGroups(list)
        }).finally(() => {
            setLoading(false);
        })
    }

    // 过滤数据
    const doFilter = (src: Array<MachineGroup>, filter: GroupFilter) => {
        if (!src || src.length == 0) {
            return src;
        }

        if (filter.query == '') {
            return src;
        }

        let dst: Array<MachineGroup> = [];

        src.forEach((item) => {
            if (filter.query) {
                if (caseInsensitiveIncludes(item.name, filter.query) || caseInsensitiveIncludes(item.description, filter.query) || caseInsensitiveIncludes(item.alias, filter.query)) {
                    dst.push(item);
                }
            }
        })

        return dst;
    }

    useEffect(() => {
        query()
    }, [])

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    useEffect(() => {
        const res = doFilter(allGroups, filterParam)

        setGroups(res)

    }, [filterParam])

    return {
        columns,
        loading,
        allGroups,
        groups,
        selectedGroup,
        setSelectedGroup,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        groupDnsVisible,
        setGroupDnsVisible,
        reloadFlag,
        setReloadFlag,
        filter,
        setFilter
    }

}

export default useTable;