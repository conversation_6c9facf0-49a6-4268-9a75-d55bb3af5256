import { FC, useEffect, useState } from 'react'
import { Typo<PERSON>, Modal, Switch, Row, Col, Divider, Notification, Card, Popover, Input, Banner, Button, Space, Pagination, Spin, Tabs, TabPane, Tooltip } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { MachineRoutes, AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";

import { flylayerClient } from '@/services/core';
import { IconInfoCircle, IconMinus, IconChevronRight, IconEdit, IconTick, IconSearch } from '@douyinfe/semi-icons';

import AddRoute from '@/pages/devices/add-route-new';
import BatchEditRoute from '@/pages/devices/batch-edit-route';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss';
import { isIPInRange } from '@/utils/format';
import { getMachine } from '@/services/device';
import { caseInsensitiveIncludes } from '@/utils/common';
import { useLocale } from '@/locales';

interface Props {
    close: () => void,
    record: Machine,
    success?: () => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage: _formatMessage } = useLocale();
    // 宣告路由
    const [advertisedRoutes, setAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 宣告路由过滤结果
    const [filteredAdvertisedRoutes, setFilteredAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 排除路由
    const [excludedRoutes, setExcludedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 排除路由过滤结果
    const [filteredExcludedRoutes, setFilteredExcludedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 过滤结果
    const [_filteredRoutes, _setFilteredRoutes] = useState<Array<AdvertisedRoute>>([]);

    const [allRoutes, setAllRoutes] = useState<Array<AdvertisedRoute>>([]);

    const [enableRoutesCount, setEnableRoutesCount] = useState(0);
    // const [enabledRoutes, setEnabledRoutes] = useState<Array<boolean>>([]);
    // const [remarkRoutes, setRemarkRoutes] = useState<Array<string>>([]);

    const [advertisedExitNode, setAdvertisedExitNode] = useState(false);
    const [enabledExitNode, setEnabledExitNode] = useState(false);

    const [ctrlRoutableIpsEnabled, _setCtrlRoutableIpsEnabled] = useState(false);
    const [ctrlRoutableIpsEnabledLoading, setCtrlRoutableIpsEnabledLoading] = useState(false);

    // 正在修改的路由节点
    const [loadingRoute, setLoadingRoute] = useState<string>();
    // 出口路由是否正在修改
    const [exitLoading, setExitLoading] = useState(false);

    // 是否显示新建路由对话框
    const [showAddRoute, setShowAddRoute] = useState(false);

    // 是否显示批量编辑路由对话框
    const [showBatchEditRoute, setShowBatchEditRoute] = useState(false);

    const calEnableRoutesCount = (routes: Array<AdvertisedRoute>) => {
        let count = 0;
        routes.forEach(value => {
            if (value.enabled) {
                count++;
            }
        });
        setEnableRoutesCount(count);
    }

    const [previewRoutes, setPreviewRoutes] = useState<string[]>([]);

    const [machine, setMachine] = useState<Machine>();

    const query = () => {
        flylayerClient.getMachineRoutes({ machineId: props.record.id }).then(res => {
            if (res.routes) {
                const _advertisedRoutes: AdvertisedRoute[] = [];
                const _excludedRoutes: AdvertisedRoute[] = [];
                if (res.routes.advertisedRoutes && res.routes.advertisedRoutes.length > 0) {
                    res.routes.advertisedRoutes.forEach((value, _index) => {
                        if (value.routeType == AdvertisedRoute_RouteType.EXCLUDE) {
                            _excludedRoutes.push(value);
                        }
                        else {
                            _advertisedRoutes.push(value);
                        }
                    });
                    setAllRoutes(res.routes.advertisedRoutes);
                } else {
                    setAllRoutes([]);
                }

                setAdvertisedRoutes(_advertisedRoutes);
                setFilteredAdvertisedRoutes(_advertisedRoutes);
                setExcludedRoutes(_excludedRoutes);
                setFilteredExcludedRoutes(_excludedRoutes);

                // setCtrlRoutableIpsEnabled(res.routes.ctrlRoutableIpsEnabled);

                setAdvertisedExitNode(res.routes.advertisedExitNode);
                setEnabledExitNode(res.routes.enabledExitNode);
                calEnableRoutesCount(res.routes.advertisedRoutes);

                flylayerClient.previewMachineRoutes({
                    machineId: props.record.id,
                    advertisedRoutes: res.routes.advertisedRoutes,
                }).then((res) => {
                    setPreviewRoutes(res.routes);
                }).catch((err) => {
                    console.error(err);
                    Notification.error({ content: "预览子网路由失败，请稍后重试", position: "bottomRight" })
                })
            }
        }, err => {
            console.error(err);
            Notification.error({ content: '获取设备子网路由失败，请稍后重试' })
        })


        getMachine(props.record.ipv4).then(res => {
            setMachine(res);
        })
    };

    useEffect(() => {
        query();
    }, [])

    const handleEnableMachineRoutes = (route: string) => {
        setLoadingRoute(route)
        flylayerClient.enableMachineRoutes({
            machineId: props.record.id,
            routes: [route],
            replace: false
        }).then(() => {
            query()

            setDataEdited(true);

            Notification.success({ content: "更新路由设置成功", position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: "更新路由设置失败，请稍后重试", position: "bottomRight" })
        }).finally(() => {
            setLoadingRoute(undefined)
        })

    }

    const handleDisableMachineRoutes = (route: string) => {
        setLoadingRoute(route)
        flylayerClient.disableMachineRoutes({
            machineId: props.record.id,
            routes: [route],
        }).then(() => {
            query()
            setDataEdited(true);
            Notification.success({ content: "更新路由设置成功", position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: "更新路由设置失败，请稍后重试", position: "bottomRight" })
        }).finally(() => {
            setLoadingRoute(undefined)
        })

    }

    const handleEnableExitNode = () => {
        setExitLoading(true)
        flylayerClient.enableExitNode({
            machineId: props.record.id,
        }).then(() => {
            setDataEdited(true);
            Notification.success({ content: "更新路由设置成功", position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: "更新路由设置失败，请稍后重试", position: "bottomRight" })
        }).finally(() => {
            setExitLoading(false)
        })
    }

    const handleDisableExitNode = () => {
        setExitLoading(true)
        flylayerClient.disableExitNode({
            machineId: props.record.id,
        }).then(() => {
            setDataEdited(true);
            Notification.success({ content: "更新路由设置成功", position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: "更新路由设置失败，请稍后重试", position: "bottomRight" })
        }).finally(() => {
            setExitLoading(false)
        })
    }

    // 添加子网路由
    const handleAddMachineRoutes = () => {
        setShowAddRoute(true);
    }

    // 批量编辑子网路由
    const handleBatchEditMachineRoutes = () => {
        setShowBatchEditRoute(true);
    }

    // 删除子网路由
    const handleDeleteMachineRoutes = (route: string) => {
        flylayerClient.deleteAdvertisedRoutes({
            machineId: props.record.id,
            advertisedRoutes: [route],
        }).then(() => {
            setFilter('');
            setPage(1)
            query();
            setDataEdited(true);
            Notification.success({ content: "更新路由设置成功", position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: "更新路由设置失败，请稍后重试", position: "bottomRight" })
        })
    }


    const handleCtrlRoutableIpsEnabledChange = () => {
        setCtrlRoutableIpsEnabledLoading(true);
        flylayerClient.disableCtrlRoutableIPs({
            machineId: props.record.id
        }).then(() => {
            query();
            Notification.success({ content: "更新路由设置成功", position: "bottomRight" })
        }).catch(err => {
            console.error(err);
            Notification.error({ content: "更新路由设置失败，请稍后重试", position: "bottomRight" })
        }).finally(() => {
            setCtrlRoutableIpsEnabledLoading(false);
        })
    }

    const pageSize = 20;
    const [page, setPage] = useState(1);
    const [excludedPage, setExcludedPage] = useState(1);

    const [editRemarkIndex, setEditRemarkIndex] = useState(-1);
    const [editRemarkLoading, setEditRemarkLoading] = useState(false);
    const [editRemarkValue, setEditRemarkValue] = useState('');

    const [excludedEditRemarkIndex, setExcludedEditRemarkIndex] = useState(-1);
    const [excludedEditRemarkLoading, setExcludedEditRemarkLoading] = useState(false);
    const [excludedEditRemarkValue, setExcludedEditRemarkValue] = useState('');

    const [filter, setFilter] = useState('');
    const handleFilter = (filterTxt: string) => {
        // const filterTxt = filter.trim();
        if (filterTxt == '') {
            setFilteredAdvertisedRoutes(advertisedRoutes);
            setFilteredExcludedRoutes(excludedRoutes);
            return;
        }

        setPage(1);
        setExcludedPage(1);
        const newRoutes: AdvertisedRoute[] = [];
        advertisedRoutes.forEach((value, _index) => {
            if (value.route.indexOf(filterTxt) != -1 || caseInsensitiveIncludes(value.remark, filterTxt) || isIPInRange(filterTxt, value.route)) {
                newRoutes.push(value);
            }
        });
        setFilteredAdvertisedRoutes(newRoutes);

        const newExcludedRoutes: AdvertisedRoute[] = [];
        excludedRoutes.forEach((value, _index) => {
            if (value.route.indexOf(filterTxt) != -1 || caseInsensitiveIncludes(value.remark, filterTxt) || isIPInRange(filterTxt, value.route)) {
                newExcludedRoutes.push(value);
            }
        });
        setFilteredExcludedRoutes(newExcludedRoutes);

    }

    // 数据是否进行了修改
    const [dataEdited, setDataEdited] = useState(false);

    const handleExit = () => {
        if (dataEdited && props.success) {
            props.success()
        }

        props.close();
    }

    return <>
        <Modal
            width={800}
            title={`编辑设备${props.record.givenName}的路由设置`}
            visible={true}
            onOk={handleExit}
            onCancel={handleExit}
            closeOnEsc={true}
            maskClosable={false}
            hasCancel={false}
            okText='关闭'
        >
            {
                !props.record.keyExpiryDisabled ? <> {enableRoutesCount > 0 && advertisedRoutes.length > 0 || advertisedExitNode && enabledExitNode ? <Banner
                    className='mb20'
                    type="warning"
                    fullMode={false}
                    closeIcon={null}
                    title="该设备已开启密钥过期"
                    description="当这台设备的密钥过期后，由该设备中继的流量会被中断，直到您针对该设备重新进行认证。"
                /> : ''}
                </> : ''
            }

            <Row className='mb10'>
                <Col span={20}>
                    <div><Title heading={4} className="mb2">连接器</Title>
                    </div></Col>
                <Col span={4}><div className='btn-right-col'>

                </div></Col>
            </Row>
            <Row className='mb10'>
                <Col><Paragraph>将流量转发到没有安装客户端的设备。<a className='learnMore' target='_blank' href='/docs/start/editnode'>了解更多<IconChevronRight /></a></Paragraph></Col>
                <Col></Col>
            </Row>
            <Row className='mb10'>
                <Col span={12}>
                    <Space>
                        {!machine || !machine.allowControlAdvertised && false ?
                            <>
                                <Tooltip content={<>请在客户端使用下面命令启用:<br />
                                    flylayer set --advertise-routes-from-control</>} >
                                    <Button disabled theme='solid'>添加路由</Button>
                                </Tooltip>
                                <Tooltip content={<>请在客户端使用下面命令启用:<br />
                                    flylayer set --advertise-routes-from-control</>} >
                                    <Button disabled theme='solid'>批量编辑</Button>
                                </Tooltip>
                            </> : <>
                                <Button theme='solid' onClick={handleAddMachineRoutes}>添加路由</Button>
                                <Button theme='solid' onClick={handleBatchEditMachineRoutes}>批量编辑</Button>
                            </>}

                        <Input
                            placeholder="搜索路由"
                            style={{ width: 260 }}
                            suffix={<IconSearch />}
                            value={filter} onChange={(val) => {
                                setFilter(val)
                                handleFilter(val.trim())
                            }}></Input>
                    </Space>
                </Col>
                <Col span={12}>{ctrlRoutableIpsEnabled && <div className={styles.overrideLocalDns}>
                    <Popover content={<div className="p10 mw400">启用该选项后，客户端将忽略本地宣告路由设置。
                        <br />
                        禁用该选项时，客户端使用本地宣告路由设置。</div>}>
                        <IconInfoCircle />
                    </Popover>&nbsp;
                    禁用本地路由宣告&nbsp;<Switch size="small" loading={ctrlRoutableIpsEnabledLoading}
                        onChange={() => Modal.confirm({
                            title: '使用本地宣告路由',
                            content: '确定要使用本地宣告路由，而不是服务端下发的路由吗？',
                            okText: '确定',
                            cancelText: '取消',
                            okButtonProps: { type: 'danger' },
                            onOk: () => {
                                handleCtrlRoutableIpsEnabledChange()
                            }
                        })}
                        checked={true}></Switch>
                </div>}</Col>
            </Row>
            {/* 
            <Divider className='mb10' /> */}

            <Tabs type='card'>
                <TabPane tab="宣告路由" itemKey="1" style={{ paddingTop: 10 }}>
                    {filteredAdvertisedRoutes.map((route: AdvertisedRoute, index: number) => {
                        if (index >= (page - 1) * pageSize && index < page * pageSize) {

                            return <Row key={index} className='mb10'>
                                <Col span={4}><div className={styles.colFormItem}>
                                    <Switch
                                        checked={route.enabled}
                                        loading={loadingRoute == route.id + ''}
                                        onChange={(checked) => {
                                            if (checked) {
                                                handleEnableMachineRoutes(route.id + '')
                                            } else {
                                                handleDisableMachineRoutes(route.id + '')
                                            }
                                        }}
                                    /></div></Col>
                                <Col span={8}><Paragraph>{route.route}</Paragraph></Col>
                                <Col span={10}>
                                    <div style={{ display: 'flex', alignItems: 'center', height: 24 }}>
                                        <IconEdit size='small' style={{ cursor: 'pointer', marginRight: 10 }} onClick={() => {
                                            setEditRemarkValue(route.remark);
                                            setEditRemarkIndex(index)
                                        }} />
                                        {index != editRemarkIndex ? <Paragraph type='tertiary' ellipsis={{}}>{route.remark}</Paragraph> :
                                            <Input
                                                onBlur={() => {
                                                    setEditRemarkIndex(-1);
                                                    setEditRemarkLoading(false);
                                                    setEditRemarkValue('');
                                                }}
                                                onChange={(e) => {
                                                    setEditRemarkValue(e);
                                                }}
                                                suffix={editRemarkLoading ? <Spin style={{ 'transform': 'translateY(4px) translateX(-4px)' }} size='small'></Spin> :
                                                    <IconTick style={{ cursor: 'pointer' }} size='small' onClick={() => {

                                                        setEditRemarkLoading(true);
                                                        const route = filteredAdvertisedRoutes[index];

                                                        const newFilteredRoutes: any[] = filteredAdvertisedRoutes.map((value, _index) => {
                                                            if (value.id == route.id) {
                                                                return { ...value, remark: editRemarkValue }
                                                            }
                                                            return value;
                                                        })
                                                        setFilteredAdvertisedRoutes(newFilteredRoutes);

                                                        const _newRoutes: any[] = advertisedRoutes.map((value, _index) => {
                                                            if (value.id == route.id) {
                                                                return { ...value, remark: editRemarkValue }
                                                            }
                                                            return value;
                                                        })

                                                        flylayerClient.saveMachineRoutes({
                                                            machineId: props.record.id,
                                                            advertisedRoutes: allRoutes.map((value, _index) => {
                                                                if (value.id == route.id) {
                                                                    return { ...value, remark: editRemarkValue }
                                                                }
                                                                return value;
                                                            }),

                                                        }).then((_res) => {

                                                            query();
                                                            setEditRemarkValue('');
                                                            setEditRemarkIndex(-1);
                                                        }).catch((err) => {
                                                            console.error(err);
                                                            Notification.error({ content: "保存子网路由描述失败，请稍后重试", position: "bottomRight" })
                                                        }).finally(() => setEditRemarkLoading(false))
                                                    }} />}
                                                className={styles.remarkInput}
                                                size='small'

                                                value={index === editRemarkIndex ? editRemarkValue : filteredAdvertisedRoutes[index].remark}></Input>}
                                    </div>



                                </Col>
                                <Col span={2} style={{ display: 'flex', justifyContent: 'right', alignItems: 'center' }}>
                                    <Button
                                        type='danger'
                                        onClick={() => Modal.confirm({
                                            title: '删除子网路由',
                                            content: '确定要删除这个子网路由吗？',
                                            okText: '删除',
                                            cancelText: '取消',
                                            okButtonProps: { type: 'danger' },
                                            onOk: () => {
                                                handleDeleteMachineRoutes(route.id + '')
                                            }
                                        })} size='small' icon={<IconMinus />}></Button>
                                </Col>
                            </Row>
                        }
                        else return '';
                    })}
                    {filteredAdvertisedRoutes && filteredAdvertisedRoutes.length > pageSize ? <Pagination
                        total={advertisedRoutes.length}
                        style={{ marginBottom: 12 }}
                        pageSize={pageSize}
                        onChange={(page) => {
                            setPage(page)
                        }}
                        currentPage={page} ></Pagination>
                        : ''}
                    {advertisedRoutes && advertisedRoutes.length > 0 && filteredAdvertisedRoutes && filteredAdvertisedRoutes.length == 0 ? <Card>没有找到匹配的路由</Card> : ''}
                    {!advertisedRoutes || advertisedRoutes.length == 0 ? <Card>这台设备没有宣告任何路由</Card> : ''}
                </TabPane>
                <TabPane tab="排除路由" style={{ paddingTop: 10 }} itemKey="2">
                    {filteredExcludedRoutes.map((route: AdvertisedRoute, index: number) => {
                        if (index >= (excludedPage - 1) * pageSize && index < excludedPage * pageSize) {

                            return <Row key={index} className='mb10'>
                                <Col span={4}><div className={styles.colFormItem}>
                                    <Switch
                                        checked={route.enabled}
                                        loading={loadingRoute == route.id + ''}
                                        onChange={(checked) => {
                                            if (checked) {
                                                handleEnableMachineRoutes(route.id + '')
                                            } else {
                                                handleDisableMachineRoutes(route.id + '')
                                            }
                                        }}
                                    /></div></Col>
                                <Col span={8}><Paragraph>{route.route}</Paragraph></Col>
                                <Col span={10}>
                                    <div style={{ display: 'flex', alignItems: 'center', height: 24 }}>
                                        <IconEdit size='small' style={{ cursor: 'pointer', marginRight: 10 }} onClick={() => {
                                            setExcludedEditRemarkValue(route.remark);
                                            setExcludedEditRemarkIndex(index)
                                        }} />
                                        {index != excludedEditRemarkIndex ? <Paragraph type='tertiary' ellipsis={{}}>{route.remark}</Paragraph> :
                                            <Input
                                                onBlur={() => {
                                                    setExcludedEditRemarkIndex(-1);
                                                    setExcludedEditRemarkLoading(false);
                                                    setExcludedEditRemarkValue('');
                                                }}
                                                onChange={(e) => {
                                                    setExcludedEditRemarkValue(e);
                                                }}
                                                suffix={excludedEditRemarkLoading ? <Spin style={{ 'transform': 'translateY(4px) translateX(-4px)' }} size='small'></Spin> :
                                                    <IconTick style={{ cursor: 'pointer' }} size='small' onClick={() => {

                                                        setExcludedEditRemarkLoading(true);
                                                        const route = filteredExcludedRoutes[index];

                                                        const newFilteredRoutes: any[] = filteredExcludedRoutes.map((value, _index) => {
                                                            if (value.id == route.id) {
                                                                return { ...value, remark: excludedEditRemarkValue }
                                                            }
                                                            return value;
                                                        })
                                                        setFilteredExcludedRoutes(newFilteredRoutes);

                                                        const _newRoutes: any[] = excludedRoutes.map((value, _index) => {
                                                            if (value.route == route.route) {
                                                                return { ...value, remark: excludedEditRemarkValue }
                                                            }
                                                            return value;
                                                        })

                                                        flylayerClient.saveMachineRoutes({
                                                            machineId: props.record.id,
                                                            advertisedRoutes: allRoutes.map((value, _index) => {
                                                                if (value.id == route.id) {
                                                                    return { ...value, remark: excludedEditRemarkValue }
                                                                }
                                                                return value;
                                                            }),

                                                        }).then((_res) => {
                                                            query();
                                                            setExcludedEditRemarkValue('');
                                                            setExcludedEditRemarkIndex(-1);
                                                        }).catch((err) => {
                                                            console.error(err);
                                                            Notification.error({ content: "保存子网路由描述失败，请稍后重试", position: "bottomRight" })
                                                        }).finally(() => setExcludedEditRemarkLoading(false))
                                                    }} />}
                                                className={styles.remarkInput}
                                                size='small'

                                                value={index === excludedEditRemarkIndex ? excludedEditRemarkValue : filteredExcludedRoutes[index].remark}></Input>}
                                    </div>



                                </Col>
                                <Col span={2} style={{ display: 'flex', justifyContent: 'right', alignItems: 'center' }}>
                                    {route.sys ?
                                        <Tooltip content={<>请在客户端使用下面命令启用:<br />
                                            flylayer set --advertise-routes-from-control</>} ><Button
                                                type='danger'
                                                disabled
                                                size='small' icon={<IconMinus />}></Button></Tooltip>
                                        : <Button
                                            type='danger'
                                            onClick={() => Modal.confirm({
                                                title: '删除子网路由',
                                                content: '确定要删除这个子网路由吗？',
                                                okText: '删除',
                                                cancelText: '取消',
                                                okButtonProps: { type: 'danger' },
                                                onOk: () => {
                                                    handleDeleteMachineRoutes(route.id + '')
                                                }
                                            })} size='small' icon={<IconMinus />}></Button>}
                                </Col>
                            </Row>
                        }
                        else return '';
                    })}
                    {filteredExcludedRoutes && filteredExcludedRoutes.length > pageSize ? <Pagination
                        total={excludedRoutes.length}
                        style={{ marginBottom: 12 }}
                        pageSize={pageSize}
                        onChange={(page) => {
                            setExcludedPage(page)
                        }}
                        currentPage={excludedPage} ></Pagination>
                        : ''}
                    {excludedRoutes && excludedRoutes.length > 0 && filteredExcludedRoutes && filteredExcludedRoutes.length == 0 ? <Card>没有找到匹配的路由</Card> : ''}
                    {!excludedRoutes || excludedRoutes.length == 0 ? <Card>这台设备没有任何排除路由</Card> : ''}
                </TabPane>
                <TabPane tab="预览" style={{ paddingTop: 10 }} itemKey="3">
                    <div style={{ maxHeight: '690px', overflowY: 'auto', overflowX: 'hidden' }}>{previewRoutes.length > 0 ? <div className='mb20'>{previewRoutes.length > 0 ? <Row gutter={4}>{previewRoutes.map((route, index) => {
                        return <Col xs={24} xl={6} key={index}><Text>{route}</Text></Col>
                    })}</Row> : <Card>这台设备没有宣告任何路由</Card>}</div> : <Card>这台设备没有宣告任何路由</Card>}</div></TabPane>
            </Tabs>


            <div className='mb20'></div>
            <Title className='mb10' heading={4}>出口节点</Title>
            <Paragraph className='mb10'>允许网络里的其他设备通过这台设备联接互联网。 <a className='learnMore' target='_blank' href='/docs/start/exitnode'>了解更多<IconChevronRight /></a></Paragraph>
            <Divider className='mb10' />
            <Row className='mb20'>
                <Col span={4}><div className={styles.colFormItem}>
                    <Switch
                        checked={enabledExitNode}
                        loading={exitLoading}
                        disabled={!advertisedExitNode} onChange={(checked) => {
                            if (checked) {
                                handleEnableExitNode()
                            } else {
                                handleDisableExitNode()
                            }
                            setEnabledExitNode(checked)
                        }} /></div></Col>
                <Col span={20}><Paragraph>用于出口节点&nbsp;
                    {!advertisedExitNode ? <Popover
                        content={<div className='p10' style={{ width: 300 }}><span>该设备未将自己宣告为出口节点。 重新运行 <code>flylayer up</code> 命令加上参数 <code >--advertise-exit-node</code> 开启该功能。</span>
                        </div>}><IconInfoCircle style={{ verticalAlign: 'sup' }} />
                    </Popover> : ''}
                </Paragraph></Col>
            </Row>
        </Modal>
        {
            showAddRoute && <AddRoute
                advertisedRoutes={advertisedRoutes}
                record={props.record}
                close={() => {
                    setShowAddRoute(false)
                }}
                success={(routes?: MachineRoutes) => {
                    if (routes) {
                        query();
                    }
                    setDataEdited(true);
                    setShowAddRoute(false)
                }}
            ></AddRoute>
        }
        {
            showBatchEditRoute && <BatchEditRoute
                record={props.record}
                advertisedRoutes={allRoutes}

                close={() => {
                    setShowBatchEditRoute(false)
                }}
                success={(routes?: MachineRoutes) => {
                    if (routes) {
                        query();
                    }
                    setDataEdited(true);
                    setShowBatchEditRoute(false)
                }}
            ></BatchEditRoute>

        }
    </>
}
export default Index;
