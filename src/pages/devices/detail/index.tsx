import { FC, useEffect, useState, useContext } from 'react'
import { Breadcrumb, Row, Col, Button, Typography, Tag, Tooltip, Notification, Dropdown, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List, Banner, Tabs, TabPane, Collapse, Modal } from '@douyinfe/semi-ui';
import { IconSetting, IconInfoCircle, IconArrowUpRight, IconGlobeStroke, IconEdit } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { DNSConfig } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb";
import { Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { Machine, MachineGroup, GetMachineDNSConfigResponse } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import EditRejectRoute from '../edit-reject-route';
import EditMeshEnabled from '../edit-mesh-enabled';
import Del from '../del';
import EditRoute from '../edit-route';
import Rename from '../rename';
import EditRelay from '../edit-relay';
import EditGroup from '../edit-group';
import Expiry from '../expiry';

const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'
import { BASE_PATH } from '@/constants/router';
import { useNavigate, useParams } from 'react-router-dom';
import DeviceTag from '@/components/device-tag';
import { getMachine, setMachineKeyExpiry, authorizeMachine, getRelayName, SaveMachineRdpSettings } from '@/services/device';
import { formatDefaultTimestamp, formatIPNVersion } from '@/utils/format';
import { flylayerClient } from '@/services/core';
import { getFlynet } from '@/services/flynet';
import { AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";
import AclTag from '../acl-tag';


import DomainManager from '@/components/domain-manager';
import DomainViewer from '@/components/domain-viewer';
import DnsManager from '@/components/dns-manager';
import DnsViewer from '@/components/dns-viewer';
import RelayMapManager from '@/components/relay-map-manager';
import RelayMapViewer from '@/components/relay-map-viewer';

import { LicenseContext } from '@/hooks/useLicense';
import { getRadioEntitlementVal } from '@/utils/common';

import { RiskLevel } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

interface Props {
}

const Index: FC<Props> = (_props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const license = useContext(LicenseContext);
    const entitlementAllowRdp = getRadioEntitlementVal('rdp', license.entitlements);
    const navigate = useNavigate();
    const params = useParams<{ name: string }>()
    const machineIp = params.name ? params.name : ''

    // 设备加载标识，用于骨架屏
    const [deviceLoading, setDeviceLoading] = useState(false);
    const [device, setDevice] = useState<Machine>();

    const [groups, setGroups] = useState<MachineGroup[]>();

    // 删除设备弹出框是否可见
    const [delVisiable, setDelVisiable] = useState(false);
    // 重命名设备弹出框是否可见
    const [renameVisiable, setRenameVisiable] = useState(false);
    // 编辑路由设置弹出框是否可见
    const [editRouteVisiable, setEditRouteVisiable] = useState(false);

    // 设置拒绝接受路由弹出框是否可见
    const [editRejectRouteVisible, setEditRejectRouteVisible] = useState(false);
    // 编辑中继弹出框是否可见
    const [editRelayVisiable, setEditRelayVisiable] = useState(false);

    // 编辑Mesh模式弹出框是否可见
    const [editMeshEnabledVisible, setEditMeshEnabledVisible] = useState(false);

    // 编辑访问控制标签弹出框是否可见
    const [editAclTagVisible, setEditAclTagVisible] = useState(false);

    // 状态是否为空
    const [isStatusEmpty, setIsStatusEmpty] = useState(true);

    // 宣告路由
    const [advertisedRoutes, setAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 排除路由
    const [excludedRoutes, setExcludedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 预览路由
    const [previewRoutes, setPreviewRoutes] = useState<string[]>([]);

    // 编辑设备组弹出框是否可见
    const [editGroupVisible, setEditGroupVisible] = useState(false);
    // 强制过期弹出框是否可见
    const [expiryVisible, setExpiryVisible] = useState(false);

    const [isExpiry, _setIsExpiry] = useState(false);

    const [flynet, setFlynet] = useState<Flynet>();
    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])




    const connectDetails = [
        {
            key: 'WorkingIcmpV4',
            value: device?.netInfo?.workingIcmpV4 ? 'YES' : 'NO',
            displayName: 'WorkingIcmpV4'
        }, {
            key: 'PreferredRelay',
            value: device?.netInfo?.preferredRelay ? 'YES' : 'NO',
            displayName: 'PreferredRelay'
        }, {
            key: 'Hairpinning',
            value: device?.netInfo?.hairPinning ? 'YES' : 'NO',
            displayName: 'Hairpinning'
        }, {
            key: 'WorkingIpv6',
            value: device?.netInfo?.workingIpv6 ? 'YES' : 'NO',
            displayName: 'WorkingIpv6'
        }, {
            key: 'OsHasIpv6',
            value: device?.netInfo?.osHasIpv6 ? 'YES' : 'NO',
            displayName: 'OsHasIpv6'
        }, {
            key: 'UDP',
            value: device?.netInfo?.workingUdp ? 'YES' : 'NO',
            displayName: 'UDP'
        }, {
            key: 'UPnP',
            value: device?.netInfo?.upnp ? 'YES' : 'NO',
            displayName: 'UPnP'
        }, {
            key: 'PCP',
            value: device?.netInfo?.pcp ? 'YES' : 'NO',
            displayName: 'PCP'
        }, {
            key: 'NAT-PMP',
            value: device?.netInfo?.pmp ? 'YES' : 'NO',
            displayName: 'NAT-PMP'
        }
    ]
    const [preferredRelayName, setPreferredRelayName] = useState('')


    const query = () => {
        setDeviceLoading(true)
        getMachine(machineIp).then((machine: Machine) => {


            setDevice(machine)

            let relayId = -1;
            if (machine && machine.netInfo && machine.netInfo.preferredRelay) {
                relayId = machine.netInfo.preferredRelay
            }
            getRelayName(relayId).then(res => {
                if (res) {
                    setPreferredRelayName(res)
                }
            })

            flylayerClient.getMachineRoutes({ machineId: machine.id }).then(res => {
                if (res.routes && res.routes.advertisedRoutes) {

                    const _advertisedRoutes: AdvertisedRoute[] = [];
                    const _excludedRoutes: AdvertisedRoute[] = [];
                    if (res.routes.advertisedRoutes && res.routes.advertisedRoutes.length > 0) {
                        res.routes.advertisedRoutes.forEach((value, _index) => {
                            if (value.routeType == AdvertisedRoute_RouteType.EXCLUDE) {
                                _excludedRoutes.push(value);
                            }
                            else {
                                _advertisedRoutes.push(value);
                            }
                        });
                    }

                    setAdvertisedRoutes(_advertisedRoutes);
                    setExcludedRoutes(_excludedRoutes);
                    flylayerClient.previewMachineRoutes({
                        machineId: machine.id,
                        advertisedRoutes: res.routes.advertisedRoutes,
                    }).then((res) => {

                        setDeviceLoading(false)
                        setPreviewRoutes(res.routes);
                    }).catch((err) => {

                        setDeviceLoading(false)
                        console.error(err);
                        Notification.error({ content: formatMessage({ id: 'devices.detail.previewSubnetRouteFailed' }), position: "bottomRight" })
                    })
                } else {
                    setAdvertisedRoutes([])
                }
            }).catch(err => {
                setDeviceLoading(false)
                console.error(err)
                Notification.error({ content: formatMessage({ id: 'devices.detail.getDeviceRouteFailed' }), position: "bottomRight" })
            })

            flylayerClient.getMachineGroups({
                machineId: machine.id
            }).then((groupRes) => {
                setGroups(groupRes.groups)
            })

            queryRelayMap(machine.id);
            queryDNSConfig(machine.id);

        }, err => {
            console.error(err)

            setDeviceLoading(false)
            Notification.error({ content: formatMessage({ id: 'devices.detail.getDeviceFailed' }), position: "bottomRight" })
        })
    }

    const [fullDNSConfig, setFullDNSConfig] = useState<GetMachineDNSConfigResponse>();
    const [records, setRecords] = useState<Array<Record>>([]);

    const [domainEditorVisible, setDomainEditorVisible] = useState(false);
    const [dnsEditorVisible, setDnsEditorVisible] = useState(false);
    const [relayMapEditorVisible, setRelayMapEditorVisible] = useState(false);

    const [relayMapSaveLoading, setRelayMapSaveLoading] = useState(false);
    const [machineRelayMapValue, setMachineRelayMapValue] = useState<Uint8Array>();
    const [relayMapValue, setRelayMapValue] = useState<Uint8Array>();

    const [relayMapLabel, setRelayMapLabel] = useState<string>('');
    const [relayMapNav, setRelayMapNav] = useState<string>('');

    const queryRelayMap = (deviceId: bigint) => {
        flylayerClient.getMachineRelayMap({
            flynetId: flynetGeneral.id,
            machineId: deviceId
        }).then((res) => {
            setMachineRelayMapValue(res.value)
            setRelayMapValue(res.finalValue)

            let label = '';
            let nav = '';

            if (res.finalValue == res.value) {
                label = formatMessage({ id: 'devices.detail.currentDevice' });
                nav = '';
            }
            if (label == '' && res.groupValues && res.groupValues.length > 0) {
                res.groupValues.forEach((groupConfig) => {
                    if (groupConfig.value.toString() == res.finalValue.toString()) {
                        let groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;
                        label = formatMessage({ id: 'devices.detail.deviceGroupLabel' }).replace('group', groupDisplay || '');
                        nav = `${BASE_PATH}/devices/group/?query=${groupDisplay}`;
                        return;
                    }
                });
            }
            if (label == '' && res.userValue?.value.toString() == res.finalValue.toString()) {
                label = formatMessage({ id: 'devices.detail.userLabel' }).replace('user', res.userValue?.user?.displayName || '');
                nav = `${BASE_PATH}/users/${res.userValue?.user?.loginName}`;
            }
            if (label == '' && res.userGroupValues && res.userGroupValues.length > 0) {
                res.userGroupValues.forEach((userGroupConfig) => {
                    if (userGroupConfig.value.toString() == res.finalValue.toString()) {
                        let groupDisplay = userGroupConfig.group?.alias ? userGroupConfig.group?.alias : userGroupConfig.group?.name;
                        label = formatMessage({ id: 'devices.detail.userGroupLabel' }).replace('group', groupDisplay || '');
                        nav = `${BASE_PATH}/users/group/?query=${groupDisplay}`;
                        return;
                    }
                });
            }



            setRelayMapLabel(label)
            setRelayMapNav(nav)
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'devices.detail.getRelayRouteFailed' }), position: "bottomRight" })
        });
    }

    const [dnsConfigSaveLoading, setDnsConfigSaveLoading] = useState(false);
    const [dnsConfig, setDnsConfig] = useState<DNSConfig>();
    const queryDNSConfig = (deviceId: bigint) => {
        flylayerClient.getMachineDNSConfig({
            flynetId: flynetGeneral.id,
            machineId: deviceId
        }).then((res) => {
            setFullDNSConfig(res)
            setDnsConfig(res.config)
            setRecords(res.config?.extraRecords || []);
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'devices.detail.getDNSFailed' }), position: "bottomRight" })
        });
    }

    useEffect(() => {
        query()
    }, [])

    // 审批设备
    const handleAuthorizeMachine = () => {
        if (!device) { return }
        authorizeMachine(device?.id).then(() => query())
    }

    // 启用密钥过期
    const handleEnableKeyExpiry = () => {
        if (!device) { return }
        setMachineKeyExpiry(device.id, false).then(() => {
            query()
        })
    }
    // 禁用密钥过期
    const handleDisableKeyExpiry = () => {
        if (!device) { return }
        setMachineKeyExpiry(device.id, true).then(() => {
            query()
        })
    }



    return <><Skeleton placeholder={<div className='general-page'>
        <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>
        <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
        <Skeleton.Image style={{ height: 60 }} className='mb40' />
        <Skeleton.Image style={{ height: 200 }} />
    </div>} loading={deviceLoading}>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/devices`,
                        href: `${BASE_PATH}/devices`,
                        name: formatMessage({ id: 'devices.allDevices' })
                    },
                    {
                        name: device?.ipv4,
                    }
                ]
            }>
            </Breadcrumb>
            <Row className='mb20'>
                <Col span={20}>
                    <Title className={styles.heading} heading={3}>
                        <span>{device?.autoGeneratedName ? device.name : device?.givenName}&nbsp;</span>
                        <Text type='tertiary' size='small'>{device?.description}</Text>
                        {device?.connected ?
                            <Badge className={styles.headingBadge} dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> :
                            <Badge className={styles.headingBadge} dot type='tertiary' />}
                    </Title>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                    <Space>{device && !device.authorized ?
                        <Button theme='solid' type='danger' onClick={handleAuthorizeMachine}>{formatMessage({ id: 'devices.detail.allowAccess' })}</Button> : ''}
                        <Button theme='solid' onClick={() => {
                            navigate(`${BASE_PATH}/devices/${device?.ipv4}/logs`)
                        }}>{formatMessage({ id: 'devices.detail.viewLogs' })}</Button>
                        <Dropdown
                            position='bottomRight'
                            render={
                                <Dropdown.Menu>
                                    <Dropdown.Item onClick={() => setRenameVisiable(true)}>{formatMessage({ id: 'devices.detail.modifyDeviceName' })}</Dropdown.Item>
                                    {device ? device.keyExpiryDisabled ?
                                        <Dropdown.Item onClick={handleEnableKeyExpiry}>{formatMessage({ id: 'devices.detail.enableKeyExpiry' })}</Dropdown.Item> :
                                        <Dropdown.Item onClick={handleDisableKeyExpiry}>{formatMessage({ id: 'devices.detail.disableKeyExpiry' })}</Dropdown.Item> : ''}

                                    <Dropdown.Item onClick={() => {
                                        navigate(`${BASE_PATH}/logs/?target=${device?.name}`)
                                    }}>{formatMessage({ id: 'devices.detail.viewConfigLogs' })}</Dropdown.Item>

                                    <Dropdown.Divider />
                                    <Dropdown.Item onClick={() => setEditRouteVisiable(true)}>{formatMessage({ id: 'devices.detail.editRouteSettings' })}</Dropdown.Item>

                                    <Dropdown.Item
                                        onClick={() => {
                                            setEditRejectRouteVisible(true);
                                        }}>{formatMessage({ id: 'devices.detail.setRejectRoutes' })}</Dropdown.Item>
                                    <Dropdown.Item onClick={() => setEditRelayVisiable(true)}>{formatMessage({ id: 'devices.detail.editRelayNode' })}</Dropdown.Item>
                                    <Dropdown.Item onClick={() => {
                                        setEditMeshEnabledVisible(true);
                                    }}>{formatMessage({ id: 'devices.detail.editMeshMode' })}
                                    </Dropdown.Item>
                                    <Dropdown.Item onClick={() => {
                                        setEditAclTagVisible(true);

                                    }}>{formatMessage({ id: 'devices.detail.editAccessControlTags' })}</Dropdown.Item>
                                    <Dropdown.Divider />
                                    <Dropdown.Item disabled={isExpiry} type="danger" onClick={() => {
                                        setExpiryVisible(true)
                                    }}>{formatMessage({ id: 'devices.detail.forceKeyExpiry' })}</Dropdown.Item>
                                    {entitlementAllowRdp && device && (device.os == 'windows' || device.os == 'macOS') ?
                                        flynet?.rdpEnabled ? device.user?.rdpEnabled ? device.rdpEnabled ? <Dropdown.Item onClick={() => {
                                            SaveMachineRdpSettings(device.id, false).then(() => {
                                                query()
                                            })
                                        }}>{formatMessage({ id: 'devices.menu.disableRemoteDesktop' })}</Dropdown.Item> : <Dropdown.Item onClick={() => {
                                            SaveMachineRdpSettings(device.id, true).then(() => {
                                                query()
                                            })
                                        }}>{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item> : <Popover zIndex={3001} content={<div className='p10'>{formatMessage({ id: 'devices.menu.remoteDesktopUserNotEnabled' })}</div>}>
                                            <Dropdown.Item disabled >{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item>
                                        </Popover> : <Popover zIndex={3001} content={<div className='p10'>{formatMessage({ id: 'devices.menu.remoteDesktopNotEnabled' })}</div>}>
                                            <Dropdown.Item disabled >{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item>
                                        </Popover> : ''}
                                    <Dropdown.Divider />
                                    <Dropdown.Item type="danger" onClick={() => setDelVisiable(true)}>{formatMessage({ id: 'devices.detail.deleteDevice' })}</Dropdown.Item>
                                    <Divider />
                                    <Dropdown.Item
                                        onClick={() => {
                                            setEditGroupVisible(true);
                                        }}
                                    >
                                        {formatMessage({ id: 'devices.detail.editDeviceGroup' })}
                                    </Dropdown.Item>

                                </Dropdown.Menu>
                            }
                        >
                            <Button theme='solid' onClick={() => { }} icon={<IconSetting />}></Button>
                        </Dropdown>
                    </Space>
                </div></Col>
            </Row>

            <Divider style={{ marginBottom: 10 }} />

            <Descriptions className={isStatusEmpty ? styles.generalInfoNoStatus : styles.generalInfo} data={[
                {
                    key: <div className='align-v-center'>{device?.tags && device.tags.length > 0 ? formatMessage({ id: 'devices.detail.tags' }) : formatMessage({ id: 'devices.detail.user' })} &nbsp;
                        <Popover content={<div className="p10">{device?.tags && device.tags.length > 0 ? formatMessage({ id: 'devices.detail.tagsTooltip' }) : formatMessage({ id: 'devices.detail.userTooltip' })}</div>}>
                            <IconInfoCircle size='small' />
                        </Popover></div>, value: device?.tags && device.tags.length > 0 ? <>
                            <div>
                                {device.tags.map((tag, index) => {
                                    return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 15, marginBottom: 0 }}>{tag}</Tag>
                                })}
                            </div>
                        </> : <div className='pt5'>
                            <Avatar
                                size="extra-small"
                                src={device?.user?.avatarUrl}
                            ></Avatar>&nbsp;<Text>{device?.user?.displayName} </Text>
                            <a target='_blank' href={`${BASE_PATH}/users/${device?.user?.loginName}`}><Text type="tertiary" size='small'>{device?.user?.loginName}<IconArrowUpRight size='small' /></Text></a>
                        </div>
                },
                {
                    key: isStatusEmpty ? '' : formatMessage({ id: 'devices.detail.status' }),
                    value: (
                        <>{device ? <div className='pt5'><DeviceTag onLoad={(isEmpty: boolean) => setIsStatusEmpty(isEmpty)} record={device}></DeviceTag></div> : ''}</>
                    ),
                }, {
                    key: <><Space>{formatMessage({ id: 'devices.meshMode' })}
                        <Button size='small' onClick={() => setEditMeshEnabledVisible(true)} icon={<IconEdit />}></Button>
                    </Space></>,

                    value: <>{device && device.meshEnabled ?

                        <Tooltip content={formatMessage({ id: 'devices.table.meshEnabledTooltip' })}>
                            <Tag
                                color='green'

                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}
                            >{formatMessage({ id: 'devices.table.meshEnabled' })}</Tag></Tooltip> :
                        <Tooltip
                            content={formatMessage({ id: 'devices.table.meshDisabledTooltip' })}
                        >
                            <Tag
                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}>{formatMessage({ id: 'devices.table.meshDisabled' })}</Tag>
                        </Tooltip>
                    }
                    </>
                }, {
                    key: groups && groups.length > 0 ? formatMessage({ id: 'devices.detail.deviceGroup' }) : '',
                    value: (<>
                        {groups && groups.length > 0 && <>
                            <Space>
                                {groups.map((item, index) => {
                                    let groupDisplay = item.alias ? item.alias : item.name;
                                    return <a target='_blank' key={index} href={`${BASE_PATH}/devices/group/?query=${groupDisplay}`}><Text type="tertiary" size='small'>{groupDisplay}<IconArrowUpRight size='small' /></Text></a>
                                })}
                            </Space>

                        </>
                        }
                    </>)
                }
            ]} row />

            {device && device.entranceRisks && device.entranceRisks.length > 0 ? <Banner className='mb20' type='warning' title={formatMessage({ id: 'devices.detail.securityRiskTitle' })}>
                <List size='small'>
                    {device.entranceRisks.map((risk, index) => {
                        return <List.Item key={index}>
                            <Space>
                                {risk.riskLevel == RiskLevel.HIGH && <Tag color='red'>高危</Tag>}
                                {risk.riskLevel == RiskLevel.MEDIUM && <Tag color='orange'>中危</Tag>}
                                {risk.riskLevel == RiskLevel.LOW && <Tag color='yellow'>低危</Tag>}
                                {risk.name}
                            </Space>
                        </List.Item>
                    })}
                </List>

            </Banner> : ''}

            <Title heading={4} className='mb2'>{formatMessage({ id: 'devices.detail.deviceDetailTitle' })}</Title>
            <Paragraph className='mb10' type='tertiary'>{formatMessage({ id: 'devices.detail.deviceDetailDesc' })}</Paragraph>
            <Row gutter={16} className='mb20'>
                <Col xs={24} sm={18}><Descriptions className={styles.machineDetail}>
                    {device?.tags && device.tags.length > 0 ? <>
                        <Descriptions.Item key="tags" itemKey={formatMessage({ id: 'devices.detail.accessControlTags' })}>
                            {device.tags.map((tag, index) => {
                                return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 0, marginBottom: 0 }}>{tag}</Tag>
                            })}</Descriptions.Item>
                    </> : null}
                    <Descriptions.Item key="loginName" itemKey={formatMessage({ id: 'devices.detail.creator' })}><Paragraph copyable>{device?.user?.loginName}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="machineName" itemKey={formatMessage({ id: 'devices.detail.deviceName' })}><Paragraph copyable>{device?.givenName}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="osHostname" itemKey={formatMessage({ id: 'devices.detail.hostname' })}><Paragraph copyable>{device?.hostname}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="os" itemKey={formatMessage({ id: 'devices.detail.os' })}><Paragraph copyable>{device?.os} {device?.osVersion ? `(${device.osVersion})` : ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="clientVersion" itemKey={formatMessage({ id: 'devices.detail.version' })}><Paragraph copyable>{formatIPNVersion(device ? device.clientVersion : '')}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="IPv4" itemKey='IPv4'><Paragraph copyable>{device?.ipv4}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="IPv6" itemKey='IPv6'><Paragraph copyable>{device?.ipv6}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="id" itemKey='ID'><Paragraph copyable>{device?.id + ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="created" itemKey={formatMessage({ id: 'devices.detail.createdAt' })}><Paragraph copyable>{device ? formatDefaultTimestamp(device.createdAt) : ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="lastSeen" itemKey={formatMessage({ id: 'devices.detail.lastSeen' })}><Paragraph copyable>{device ? formatDefaultTimestamp(device.lastSeen) : ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="endpoint" itemKey={formatMessage({ id: 'devices.detail.endpoint' })}>
                        <List bordered={false}>
                            {device?.clientConnectivity?.endpoints?.map((endpoint: string, index: number) => {
                                return <List.Item style={{ border: 'none', paddingBottom: '5px', paddingLeft: 0, paddingTop: 2 }} key={index}>{endpoint}</List.Item>
                            })}
                        </List>
                    </Descriptions.Item>
                    {device ? device.keyExpiryDisabled ? '' : <Descriptions.Item key="keyExpiry" itemKey={formatMessage({ id: 'devices.detail.keyExpiry' })}><Paragraph copyable>{formatDefaultTimestamp(device.expiresAt)}</Paragraph></Descriptions.Item> : ''}
                    {preferredRelayName ? <Descriptions.Item key="preferredRelay" itemKey={formatMessage({ id: 'devices.detail.preferredRelay' })}><Paragraph copyable>{preferredRelayName}</Paragraph></Descriptions.Item> : ''}
                    {entitlementAllowRdp && device && (device.os == 'windows' || device.os == 'macOS') && !flynet?.rdpEnabled && <Descriptions.Item key="rdp" itemKey="远程桌面">
                        <Banner
                            type="info"
                            closeIcon={null}
                            description={<div dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.menu.remoteDesktopNotEnabled' }) }} />}
                        />
                    </Descriptions.Item>}
                    {device && (device.os == 'windows' || device.os == 'macOS') && flynet?.rdpEnabled && !device?.user?.rdpEnabled && <Descriptions.Item key="rdp" itemKey={formatMessage({ id: 'devices.detail.remoteDesktop' })}>
                        <Banner
                            type="info"
                            closeIcon={null}
                            description={<div dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.menu.remoteDesktopUserNotEnabled' }) }} />}
                        />
                    </Descriptions.Item>}
                    {device && (device.os == 'windows' || device.os == 'macOS') && flynet?.rdpEnabled && device?.user?.rdpEnabled && <Descriptions.Item key="rdp" itemKey={formatMessage({ id: 'devices.detail.remoteDesktop' })}>{device?.rdpEnabled ? formatMessage({ id: 'components.common.enabled' }) : formatMessage({ id: 'components.common.disabled' })}</Descriptions.Item>}
                </Descriptions></Col>
                <Col xs={24} sm={6}>
                    <Descriptions className={styles.machineDetail}>
                        {connectDetails.map(item => {
                            return <Descriptions.Item key={item.key} itemKey={item.displayName}><Paragraph>{item.value}</Paragraph></Descriptions.Item>
                        })}
                    </Descriptions></Col>
            </Row>


            <Collapse defaultActiveKey={[]} expandIconPosition='left'>
                <Collapse.Panel header={<Title heading={4}>{formatMessage({ id: 'devices.detail.routeSettings' })}</Title>} itemKey="1">
                    <Card className='mb20'>
                        <Row className='mb2'>
                            <Col span={20}>
                                <div><Title heading={4}>{formatMessage({ id: 'devices.detail.connector' })}</Title></div></Col>
                            <Col span={4}><div className='btn-right-col'>
                                <Button theme='solid' onClick={() => setEditRouteVisiable(true)}>{formatMessage({ id: 'devices.detail.setRoute' })}</Button>
                            </div></Col>
                        </Row>
                        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'devices.detail.connectorDesc' })}</Paragraph>

                        <Tabs type="card" className='mb20'>
                            <TabPane tab={formatMessage({ id: 'devices.detail.announceRoute' })} itemKey="1" style={{ paddingTop: 10 }}>
                                {advertisedRoutes.length > 0 ? <div className='mb40'><Row gutter={4}>{advertisedRoutes.map((route, index) => {
                                    return <Col xs={24} sm={12} md={8} lg={6} key={index}><Text title={route.enabled ? '' : formatMessage({ id: 'devices.detail.disabled' })} type={route.enabled ? 'primary' : 'quaternary'}>{route.route} {route.remark}</Text></Col>
                                })}</Row></div> : <Card className='mb40'>{formatMessage({ id: 'devices.detail.noAnnounceRoute' })}</Card>}
                            </TabPane>
                            <TabPane tab={formatMessage({ id: 'devices.detail.excludeRoute' })} itemKey="2" style={{ paddingTop: 10 }}>
                                {excludedRoutes.length > 0 ? <div className='mb40'><Row gutter={4}>{excludedRoutes.map((route, index) => {
                                    return <Col xs={24} sm={12} md={8} lg={6} key={index}><Text title={route.enabled ? '' : formatMessage({ id: 'devices.detail.disabled' })} type={route.enabled ? 'primary' : 'quaternary'}>{route.route} {route.remark}</Text></Col>
                                })}</Row></div> : <Card className='mb40'>{formatMessage({ id: 'devices.detail.noExcludeRoute' })}</Card>}
                            </TabPane>
                            <TabPane tab={formatMessage({ id: 'devices.detail.preview' })} itemKey="3" style={{ paddingTop: 10 }}>
                                {previewRoutes.length > 0 ? <div className='mb40'><Row gutter={4}>{previewRoutes.map((route, index) => {
                                    return <Col xs={12} sm={8} md={6} lg={4} xl={3} key={index}><Text>{route}</Text></Col>
                                })}</Row></div> : <Card className='mb40'>{formatMessage({ id: 'devices.detail.noPreviewRoute' })}</Card>}
                            </TabPane>
                        </Tabs>
                        <Row className='mb2'>
                            <Col span={20}>
                                <div><Title heading={4}>{formatMessage({ id: 'devices.detail.rejectRoute' })}</Title></div></Col>
                            <Col span={4}><div className='btn-right-col'>
                                <Button theme='solid' onClick={() => setEditRejectRouteVisible(true)}>{formatMessage({ id: 'devices.detail.setRejectRoute' })}</Button>
                            </div></Col>
                        </Row>
                        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'devices.detail.rejectRouteDesc' })}</Paragraph>
                        {device?.rejectRoutes && device.rejectRoutes.length > 0 ? <Card className='mb40'><Row gutter={4}>{device.rejectRoutes.map((route, index) => {
                            return <Col xs={24} sm={12} md={8} lg={6} key={index}><Text type={'primary'}>{route}</Text></Col>
                        })}</Row></Card> : <Card className='mb40'>{formatMessage({ id: 'devices.detail.noRejectRoute' })}</Card>}
                    </Card>
                </Collapse.Panel>
                <Collapse.Panel header={
                    <Title heading={4}>{formatMessage({ id: 'devices.detail.networkSettings' })}</Title>
                } itemKey="2">
                    <Paragraph className='mb20' type="tertiary">
                        {formatMessage({ id: 'devices.detail.networkSettingsDesc' })}
                    </Paragraph>
                    <Card>
                        {device && fullDNSConfig && <DomainViewer
                            fullDNSConfig={fullDNSConfig}
                            flynetId={flynetGeneral.id}
                            onEdit={() => setDomainEditorVisible(true)}
                        />}
                        {
                            fullDNSConfig && <DnsViewer
                                flynetId={flynetGeneral.id}
                                fullDNSConfig={fullDNSConfig}
                                onEdit={() => setDnsEditorVisible(true)}
                            />
                        }
                        {device && relayMapValue && machineRelayMapValue && <>
                            <RelayMapViewer
                                flynetId={flynetGeneral.id}
                                value={relayMapValue}
                                currentValue={machineRelayMapValue}
                                label={relayMapLabel}
                                nav={relayMapNav}
                                className='mb40'
                                onEdit={() => setRelayMapEditorVisible(true)}
                            ></RelayMapViewer>
                        </>
                        }
                    </Card>
                </Collapse.Panel>
            </Collapse>

            <div style={{ height: 100 }}></div>
        </div>
    </Skeleton>
        {delVisiable && device ?
            <Del record={device} success={() => { navigate(`${BASE_PATH}/devices`) }} close={() => { setDelVisiable(false) }} /> : null
        }
        {
            editRouteVisiable && device ?
                <EditRoute record={device} success={() => { query(); }} close={() => { setEditRouteVisiable(false) }} /> : null
        }
        {
            renameVisiable && device ?
                <Rename record={device} success={() => { query(); setRenameVisiable(false) }} close={() => { setRenameVisiable(false) }} /> : null
        }
        {
            editRelayVisiable && device ?
                <EditRelay record={device} success={() => { query(); setEditRelayVisiable(false) }} close={() => { setEditRelayVisiable(false) }} /> : null
        }
        {
            editRejectRouteVisible && device ?
                <EditRejectRoute record={device} success={() => { query(); setEditRejectRouteVisible(false) }} close={() => { setEditRejectRouteVisible(false) }} /> : null
        }
        {
            editMeshEnabledVisible && device ?
                <EditMeshEnabled record={device} success={() => { query(); setEditMeshEnabledVisible(false) }} close={() => { setEditMeshEnabledVisible(false) }} ></EditMeshEnabled> : null
        }
        {editAclTagVisible && device ?
            <AclTag
                success={() => {
                    setEditAclTagVisible(false);
                    query();
                }}
                record={device} close={() => { query(); setEditAclTagVisible(false) }}></AclTag>
            : null}
        {
            domainEditorVisible && device &&
            <Modal
                width={500}
                footer={null}
                onCancel={() => setDomainEditorVisible(false)}
                closeOnEsc={true}
                maskClosable={false}

                title={formatMessage({ id: 'devices.detail.editDomainTitle' })} visible={true}>
                <DomainManager records={records} flynetId={flynetGeneral.id} onSave={(records) => {
                    let config = new DNSConfig({
                        ...dnsConfig,
                        extraRecords: records
                    });
                    setDnsConfig(config);
                    flylayerClient.setMachineDNSConfig({
                        flynetId: flynetGeneral.id,
                        machineId: device.id,
                        config: config
                    }).then(() => {
                        queryDNSConfig(device.id)
                        setDomainEditorVisible(false)
                        Notification.success({ content: formatMessage({ id: 'devices.detail.saveDnsSuccess' }), position: "bottomRight" })
                    }).catch((err) => {
                        console.error(err)
                        Notification.error({ content: formatMessage({ id: 'devices.detail.saveDnsFailed' }), position: "bottomRight" })
                    });
                }} />
            </Modal>
        }
        {dnsEditorVisible && device && dnsConfig && <Modal
            width={500}
            footer={null}
            onCancel={() => setDnsEditorVisible(false)}
            closeOnEsc={true}
            maskClosable={false}

            title={formatMessage({ id: 'devices.detail.editDnsTitle' })} visible={true}
        >
            <DnsManager
                dnsConfig={dnsConfig}
                saveLoading={dnsConfigSaveLoading}
                onSave={(dnsConfig) => {
                    setDnsConfigSaveLoading(true)
                    flylayerClient.setMachineDNSConfig({
                        flynetId: flynetGeneral.id,
                        machineId: device.id,
                        config: dnsConfig
                    }).then(() => {
                        queryDNSConfig(device.id)
                        Notification.success({ content: formatMessage({ id: 'devices.detail.saveDnsSuccess' }), position: "bottomRight" })
                    }).catch((err) => {
                        console.error(err)
                        Notification.error({ content: formatMessage({ id: 'devices.detail.saveDnsFailed' }), position: "bottomRight" })
                    }).finally(() => {
                        setDnsConfigSaveLoading(false)
                    });
                }}
                flynetId={flynetGeneral.id} />
        </Modal>}
        {expiryVisible && device ?
            <Expiry record={device} success={() => {
                setExpiryVisible(false)
                query()
            }} close={() => { setExpiryVisible(false) }} /> : null}
        {device && editGroupVisible && <EditGroup
            close={() => {

                setEditGroupVisible(false)
            }}
            record={device}
            success={() => {
                setEditGroupVisible(false)
                query()
            }}
        ></EditGroup>
        }
        {relayMapEditorVisible && device && <Modal
            width={800}
            footer={null}
            onCancel={() => setRelayMapEditorVisible(false)}
            closeOnEsc={true}
            maskClosable={false}

            title={formatMessage({ id: 'devices.detail.editRelayMapTitle' })} visible={true}
        >
            {device && <RelayMapManager
                className='mb40'
                flynetId={flynetGeneral.id}
                value={machineRelayMapValue || new Uint8Array()}
                saveLoading={relayMapSaveLoading}
                onSave={(value) => {
                    setRelayMapSaveLoading(true)
                    flylayerClient.setMachineRelayMap({
                        flynetId: flynetGeneral.id,
                        machineId: device.id,
                        value: value
                    }).then(() => {
                        Notification.success({ content: formatMessage({ id: 'devices.detail.saveRelayMapSuccess' }), position: "bottomRight" })

                        queryRelayMap(device.id)
                        setRelayMapEditorVisible(false)
                    }).catch((err) => {
                        console.error(err)
                        Notification.error({
                            title: formatMessage({ id: 'devices.detail.saveRelayMapFailed' }),
                            content: err.message,
                            position: "bottomRight"
                        })
                    }).finally(() => {
                        setRelayMapSaveLoading(false)
                    });
                }} />}

        </Modal>}
    </>
}

export default Index