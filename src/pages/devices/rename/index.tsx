import { FC, useState } from 'react'
import { Typography, Modal, Form, Row, Col, Notification } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Paragraph } = Typography;

const { Switch, Input, TextArea } = Form
import styles from './index.module.scss';
import { isValidDeviceName } from '@/utils/validators';


interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi<{
        newName: string,
        autoGeneratedName: boolean
        description: string
    }>>()
    const [cacheName, setCacheName] = useState(props.record.autoGeneratedName ? props.record.name : props.record.givenName);

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const handleOk = () => {
        if (!formApi) {
            return
        }
        let err = formApi.getError('newName');
        if (err) {
            return;
        }
        setLoading(true)
        const { newName, autoGeneratedName, description } = formApi.getValues();
        flylayerClient.renameMachine({
            machineId: props.record.id,
            autoGeneratedName,
            newName,
            description
        }).then((_res) => {
            props.success && props.success();
            Notification.success({content: formatMessage({ id: 'devices.rename.success' }),position: "bottomRight"})
        }).catch((err) => {
            console.error(err);
            Notification.error({
                title: formatMessage({ id: 'devices.rename.failed' }),
                content: err.message,position: "bottomRight"})
            
        }).finally(() => setLoading(false))
    };
    const handleCancel = () => {
        props.close();
    };
    const handleAutogenerateNameChange = (checked: boolean) => {
        if (checked) {
            formApi?.setValue("newName", props.record.name)
        } else {
            formApi?.setValue("newName", cacheName);
        }
    }
    return <>
        <Modal
            width={500}
            title={`${formatMessage({ id: 'devices.rename.title' })}${props.record.givenName}`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okButtonProps={{ loading }}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'devices.rename.description' })}</Paragraph>
            <Form initValues={{ 
                newName: props.record.autoGeneratedName ? props.record.name : props.record.givenName, 
                autoGeneratedName: props.record.autoGeneratedName,
                description: props.record.description
                 }} getFormApi={SetFormApi} render={({ formState }) => (<>
                <Row>
                    <Col span={4}><div className={styles.colFormItem}><Switch onChange={handleAutogenerateNameChange} field='autoGeneratedName' noLabel /></div></Col>
                    <Col span={20}><Paragraph>{formatMessage({ id: 'devices.rename.autoGenerate' })}</Paragraph></Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Input field='newName' validate={isValidDeviceName} disabled={formState.values.autoGeneratedName} onChange={(value) => setCacheName(value)} label={formatMessage({ id: 'devices.rename.deviceName' })}></Input>

                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <TextArea
                        maxLength={200}
                        label={formatMessage({ id: 'devices.rename.deviceDescription' })}
                        field='description'
                        />
                    </Col>
                </Row>
                {/* <Paragraph>这台机器将可以作为 {formState.values.name} 使用 MagicDNS 访问。 
                {formState.values.fromHostname ? '' : `${preName}将不再指向这台机器。` } </Paragraph> */}

            </>)} />
        </Modal></>
}
export default Index;
