import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Notification, Select } from '@douyinfe/semi-ui';
import { Machine, MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { useLocale } from '@/locales';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';

import { getSimpleMachineName } from '@/utils/machine';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
const { Paragraph } = Typography;
interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 设备组是否正在加载中
    const [_loadingGroups, setLoadingGroups] = useState(true);
    // 设备组列表
    const [groups, setGroups] = useState<MachineGroup[]>([]);

    const [groupIds, setGroupIds] = useState<string[]>(props.record.machineGroups.map((item) => {
        return item.id + '';
    }));

    const machineName = getSimpleMachineName(props.record);


    // 加载数据
    const query = () => {
        setLoadingGroups(true)

        flylayerClient.listMachineGroups({
            flynetId: flynet.id
        }).then((res) => {
            setGroups(res.groups)
        }).finally(() => {
            setLoadingGroups(false);
        })
    }

    useEffect(() => {
        query();
    }, []);

    const handleSubmit = () => {
        setLoading(true);

        flylayerClient.editMachineGroups({
            flynetId: flynet.id,
            machineId: props.record.id,
            groupIds: groupIds.map((item) => {
                return BigInt(item);
            })
        }).then((_res) => {
            Notification.success({
                title: formatMessage({ id: 'devices.editGroup.updateSuccess' })
            })
            props.success && props.success();
        }).catch(() => {
            Notification.error({ content: formatMessage({ id: 'devices.editGroup.updateFailed' }), position: "bottomRight" })

        }).finally(() => {
            setLoading(false);
        })

    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'devices.editGroup.title' })}
            visible={true}
            onOk={handleSubmit}

            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'devices.editGroup.description' }, { name: machineName })}</Paragraph>
            <div style={{ minHeight: 200 }}>
                {groups && groups.length > 0 &&
                    <Select size='large' style={{ width: '100%' }}
                        multiple
                        filter
                        optionList={groups.map((item) => {
                            return {
                                label: `${item.alias}(${item.name})`,
                                value: item.id + '',
                                disabled: item.type == GroupType.GROUP_DYNAMIC
                            }
                        })}
                        value={groupIds}
                        onChange={(value) => {
                            setGroupIds(value as any);
                        }}
                        placeholder={formatMessage({ id: 'devices.editGroup.placeholder' })}
                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    ></Select>
                }
            </div>
        </Modal></>
}
export default Index;
