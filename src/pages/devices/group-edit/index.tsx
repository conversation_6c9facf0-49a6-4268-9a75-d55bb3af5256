import React, { useEffect, useState, useContext } from 'react'
import { Typography, Modal, Form, Notification, Skeleton, Popover, Button, Row, Col, Divider, Space, Card } from '@douyinfe/semi-ui';
import { getFlynet } from '@/services/flynet';
import { useLocale } from '@/locales';
import { Machine, MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import CodeEditor from '@/components/code-editor';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import Expressions from '@/components/expressions';

import UserModalSelector from '@/components/machine-selector';
import { BASE_PATH } from '@/constants/router';
import { AttributeTemplate } from '@/interface/attribute-template';
import { validateParamCombo } from '@/utils/common';
const { Paragraph, Text } = Typography;

const { Input } = Form

interface Props {
    close: () => void,
    success?: (userGroup?: MachineGroup) => void
    userGroupId: bigint
}

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
        otherUsers: Array<string>,
        expressionsCombo: string,
        advancedDynamicMode: boolean,
        priority: number,
    }>>()

    const [userGroup, setUserGroup] = useState<MachineGroup>();

    const [machines, setMachines] = useState<Machine[]>([]);
    const [userSelectorVisible, setUserSelectorVisible] = useState(false);
    const [type, setType] = useState<GroupType>(GroupType.GROUP_STATIC);


    const [attributeTemplate, setAttributeTemplate] = useState<AttributeTemplate>();
    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet && res.flynet.attributeTemplate) {
            const json = JSON.parse(res.flynet.attributeTemplate);
            const userProperties = json.properties.input.properties.Device;

            let attributeTemplate: AttributeTemplate = {
                type: 'object',
                title: 'properties',
                description: 'properties',
                properties: {
                    'input': {
                        type: 'object',
                        description: '输入',
                        title: '输入',
                        properties: {
                            Device: userProperties
                        }
                    }
                }

            }
            setAttributeTemplate(attributeTemplate);
        }
    }
    useEffect(() => {
        queryFlynet();
    }, [])

    const [expressions, setExpressions] = useState<Array<Expression>>([]);
    // const [advancedDynamicMode, setAdvancedDynamicMode] = useState(false);
    const [expressionAdvanced, setExpressionAdvanced] = useState('');

    const [expressionsError, setExpressionsError] = useState(false);

    const [groupLoading, setGroupLoading] = useState(false);

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleSubmit = async () => {
        await formApi?.validate();

        const values = formApi?.getValues();
        if (!values) {
            return;
        }


        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';

        const expressionsCombo = values.expressionsCombo;
        const advancedDynamicMode = values.advancedDynamicMode;
        const priority = values.priority;

        if (type == GroupType.GROUP_DYNAMIC) {
            if (advancedDynamicMode) {
                if (expressionAdvanced.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            } else {
                if (expressionsError || expressions.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            }
        }

        setLoading(true);

        flylayerClient.updateMachineGroup({
            flynetId: flynet.id,
            groupId: props.userGroupId,
            name: name,
            description: description,
            alias: alias,
            type: type,
            machines: machines,
            priority: priority,
            attrs: advancedDynamicMode ? {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_ADVANCED,
                expressionAdvanced: expressionAdvanced
            } : {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_STANDARD,
                expressions: expressions,
                expressionsCombo: expressionsCombo
            }
        }).then(() => {
            Notification.success({
                title: formatMessage({ id: 'devices.groupEdit.updateSuccess' })
            });
            props.success && props.success();
            props.close();
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'devices.groupEdit.updateFailed' }),
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        })
    }

    useEffect(() => {
        setGroupLoading(true);
        flylayerClient.getMachineGroup({
            groupId: props.userGroupId
        }).then(res => {
            setUserGroup(res.group);
            if (res.group) {
                setType(res.group.type);
            }
            if (res.group && res.group.machines) {
                setMachines(res.group?.machines);
                setExpressions(res.group.attrs?.expressions ? res.group.attrs.expressions : []);
                setExpressionAdvanced(res.group.attrs?.expressionAdvanced ? res.group.attrs.expressionAdvanced : '');
            }

            setGroupLoading(false);
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'devices.groupEdit.updateFailed' }),
                content: err.message
            });
            setGroupLoading(false);
        })
    }, [])

    return <>
        <Modal
            width={800}
            title={formatMessage({ id: 'devices.groupEdit.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >
            <Skeleton loading={groupLoading} placeholder={
                <>
                    <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title>
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />
                </>
            }>
                {userGroup && <div className={styles.addService}>

                    <Form getFormApi={SetFormApi}
                        allowEmpty
                        initValues={
                            {
                                name: userGroup.name,
                                description: userGroup.description,
                                alias: userGroup.alias,
                                type: userGroup.type,
                                advancedDynamicMode: userGroup.attrs?.dynamicGroupMode == DynamicGroupMode.DYNAMIC_GROUP_ADVANCED ? true : false,
                                expressionsCombo: userGroup.attrs && userGroup.attrs.expressionsCombo ? userGroup.attrs.expressionsCombo : '',
                                priority: userGroup.priority,
                            }
                        }
                    >
                        {({ values }) => (<>

                            <Row gutter={12}>
                                <Col span={12}>
                                    <Input field='alias' label={formatMessage({ id: 'devices.groupAdd.name' })} validate={value => {
                                        if (!value) {
                                            return formatMessage({ id: 'devices.groupAdd.nameRequired' });
                                        }
                                        return '';
                                    }} />
                                </Col>
                                <Col span={12}>
                                    <Input field='name'
                                        label={<>{formatMessage({ id: 'devices.groupAdd.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'devices.groupAdd.codeTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                                        trigger={'blur'} readonly validate={value => {
                                            if (!value) {
                                                return formatMessage({ id: 'devices.groupAdd.codeRequired' });
                                            }
                                            // 编码不能以-开头
                                            if (value.trim().startsWith('-')) {
                                                return formatMessage({ id: 'devices.groupAdd.codeCannotStartWithDash' })
                                            }
                                            if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                                return formatMessage({ id: 'devices.groupAdd.codeInvalidFormat' });
                                            }
                                            return '';
                                        }}
                                        required />
                                </Col>
                                {/* <Col span={6}>
                                    <InputNumber field="priority" min={1} max={100} step={1} label={<>优先级&nbsp;<Popover content={<div className='p10'>优先级范围为1-100，默认为1，即最高优先级。</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}></InputNumber>
                                </Col> */}
                            </Row>
                            <Row>
                                <Col span={24}>
                                    <Input field='description' label={formatMessage({ id: 'devices.groupAdd.remarks' })} />
                                </Col>
                            </Row>
                            <Divider></Divider>
                            <Row>
                                <Col span={16} style={{ paddingTop: 12 }}>
                                    <Space>
                                        <Text type='tertiary' style={{ lineHeight: '32px' }}>类型</Text>
                                        {type == GroupType.GROUP_STATIC && <Text style={{ lineHeight: '32px' }}>静态设备组</Text>}
                                        {type == GroupType.GROUP_DYNAMIC && <Text style={{ lineHeight: '32px' }}>动态设备组</Text>}
                                    </Space>
                                </Col>

                                {/* <Col span={8} className={styles.rightColumn}>
                                    <span style={{ display: type == GroupType.GROUP_DYNAMIC ? '' : 'none' }}>
                                        <Switch field='advancedDynamicMode' label="专家模式" labelPosition='left' disabled={type == GroupType.GROUP_STATIC}></Switch>
                                    </span>
                                </Col> */}
                            </Row>
                            {values.type == GroupType.GROUP_STATIC && <>
                                <Row className="mb20">
                                    <Col span={20}>
                                        <Text type='tertiary'>设备</Text>
                                    </Col>
                                    <Col span={4} className={styles.rightColumn}>
                                        <Button
                                            onClick={() => {
                                                setUserSelectorVisible(true);
                                            }}
                                            icon={<IconPlus></IconPlus>}></Button>
                                    </Col>
                                </Row>
                                {
                                    machines.length == 0 ? <TableEmpty loading={false}></TableEmpty> :
                                        <>
                                            {machines.map((item, index) => {
                                                return <Row className="mb10" key={index}>
                                                    <Col span={20}>
                                                        {item.givenName ? item.givenName : item.name}({item.ipv4})
                                                    </Col>
                                                    <Col span={4} className={styles.rightColumn}>
                                                        <Button
                                                            type='danger'
                                                            onClick={() => {
                                                                let newUsers = machines.filter((_item, i) => i != index);
                                                                setMachines(newUsers);
                                                            }}
                                                            icon={<IconMinusCircle></IconMinusCircle>}></Button>
                                                    </Col>
                                                </Row>
                                            })}
                                        </>
                                }
                            </>}
                            {values.type == GroupType.GROUP_DYNAMIC && attributeTemplate && <>
                                {
                                    values.advancedDynamicMode ? <>
                                        <CodeEditor value={expressionAdvanced} height='280px' onChange={(value) => setExpressionAdvanced(value || '')} language='systemverilog'></CodeEditor>
                                        {expressionsError && <Paragraph type='danger'>表达式不能为空</Paragraph>}
                                    </> : <>
                                        <Expressions
                                            expressions={expressions}
                                            onChange={(expressions: Array<Expression>) => {
                                                setExpressions(expressions);
                                                setExpressionsError(false);
                                            }}
                                            onError={() => {
                                                setExpressionsError(true);
                                            }}
                                            attributeTemplate={attributeTemplate}
                                        ></Expressions>
                                        {expressionsError && <Paragraph type='danger'>触发参数错误</Paragraph>}
                                        <Row>
                                            <Col span={24}>
                                                <Input
                                                    validate={ value => (validateParamCombo(value, expressions.length))}
                                                    extraText={formatMessage({ id: 'devices.groupAdd.paramCombinationHelp' })}
                                                    field='expressionsCombo' label={formatMessage({ id: 'devices.groupAdd.paramCombination' })} />
                                            </Col>
                                        </Row>
                                    </>
                                }
                            </>}
                            {type == GroupType.GROUP_DYNAMIC && !attributeTemplate && <Card>
                                <Paragraph style={{ textAlign: 'center' }}>动态设备组属性为空, 请前往
                                    <a className='link-external' target='_blank' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>
                                        设置<IconArrowUpRight />
                                    </a>

                                </Paragraph>

                            </Card>}
                        </>)}

                    </Form>
                </div>}
            </Skeleton>

        </Modal>
        {
            userSelectorVisible && <UserModalSelector
                multi={true}
                value={machines}
                onChange={(value: Machine | Machine[]) => {
                    setUserSelectorVisible(false)

                    let newUsers = machines.filter(() => true);
                    if (value instanceof Array) {
                        value.forEach((item) => {
                            if (!newUsers.some(u => u.id == item.id)) {
                                newUsers.push(item);
                            }
                        })
                    } else {
                        if (!newUsers.some(u => u.id == value.id)) {
                            newUsers.push(value);
                        }
                    }
                    setMachines(newUsers);
                }}
                close={() => setUserSelectorVisible(false)}
            ></UserModalSelector>
        }
    </>
}

export default Index;
