import { FC, useState } from 'react'
import { Typography, Modal, Notification } from '@douyinfe/semi-ui';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Machine
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    return <>
        <Modal
            width={400}
            title={formatMessage({ id: 'devices.expiry.title' })}
            visible={true}
            okButtonProps={{ loading, type: 'danger' }}
            onOk={() => {
                setLoading(true)
                flylayerClient.expireMachine({
                    machineId: props.record.id
                }).then(() => {
                    Notification.success({ content: formatMessage({ id: 'devices.expiry.success' }), position: "bottomRight" })
                    if (props.success) {
                        props.success();
                    }
                }).catch((err) => {
                    console.error(err);
                    Notification.error({ content: formatMessage({ id: 'devices.expiry.failed' }), position: "bottomRight" })
                }).finally(() => setLoading(false))
            }}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph>{formatMessage({ id: 'devices.expiry.description' })}</Paragraph>
        </Modal></>
}
export default Index;
