import { FC, useContext, useState } from 'react';
import { Button, Typography, List, Toast, Tabs, TabPane, Modal, Space, Upload } from '@douyinfe/semi-ui';
import styles from './index.module.scss'

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { IconPaperclip } from '@douyinfe/semi-icons';
import Papa from 'papaparse';
import TableEmpty from '@/components/table-empty';
import { flylayerClient } from '@/services/core';
import { uploadFileWithProgress } from '@/services/file';
import { ImportError } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';
import { useLocale } from '@/locales';

const { Text, Paragraph } = Typography;

interface Props {
    close: () => void;
    success?: () => void;
}

const MachineOverview: FC<{ machine: Machine }> = ({ machine }) => {
    return (
        <List.Item>{machine.name}</List.Item>
    )
}


const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const rootPath = 'device';


    const [isPreviewStep, setIsPreviewStep] = useState(false);

    const [updateMachines, setUpdateMachines] = useState<Machine[]>([]);
    const [importErrors, setImportErrors] = useState<ImportError[]>([]);

    const fields = [
        formatMessage({ id: 'devices.dataImportDetail.fields.id' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.deviceName' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.customDeviceName' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.autoNaming' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.description' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.deviceGroup' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.disableKeyExpiry' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.keyExpiryTime' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.deviceRoutes' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.rejectRoutes' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.relayNode' }),
        formatMessage({ id: 'devices.dataImportDetail.fields.meshMode' })]

    const [importId, setImportId] = useState<bigint>();

    const flynet = useContext(FlynetGeneralContext);

    const [saveLoading, setSaveLoading] = useState(false);

    // 下载模板
    const handleTemplateDown = () => {
        const csv = Papa.unparse([fields])
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `${formatMessage({ id: 'devices.dataImportDetail.templateDownload' })}.csv`;
        a.click();
    }

    const checkFileType = (file: any) => {
        if (file.name.startsWith('.')) {
            return true;
        }

        return file.name.endsWith('.csv');
    }


    // 文件
    const [files, setFiles] = useState<FileItem[]>();

    const handleSubmit = async () => {
        setSaveLoading(true);

        flylayerClient.importMachines({
            id: importId
        }).then(() => {
            setSaveLoading(false);
            props.success && props.success();
            Toast.success(formatMessage({ id: 'devices.dataImportDetail.importSuccess' }));
        }
        ).catch(() => {
            setSaveLoading(false);
            Toast.error(formatMessage({ id: 'devices.dataImportDetail.importFailed' }));
        });
    }

    return <>
        <Modal
            width={960}
            title={formatMessage({ id: 'devices.dataImportDetail.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okText={formatMessage({ id: 'devices.dataImportDetail.import' })}
            okButtonProps={{ loading: saveLoading, disabled: isPreviewStep ? false : true }}
            className='semi-modal'
            maskClosable={false}
        >
            {isPreviewStep ?
                <>
                    <Tabs type='line'>
                        <TabPane tab={formatMessage({ id: 'devices.dataImportDetail.updatedDevices' })} itemKey='1' className={styles.tabPane}>
                            {updateMachines && updateMachines.length > 0 ?
                                <List grid={{ gutter: 12, span: 6 }} dataSource={updateMachines}
                                    renderItem={(item, index) => {
                                        return <MachineOverview key={index} machine={item} />
                                    }}
                                /> : <TableEmpty loading={false} />
                            }
                        </TabPane>
                        <TabPane tab={formatMessage({ id: 'devices.dataImportDetail.errorRows' })} itemKey="2" className={styles.tabPane}>
                            <div className={styles.errorWrap}>
                                <List layout="horizontal" className={styles.errorList} style={{ width: (fields.length + 2) * 100 }}>
                                    <List.Item className={styles.errorItem}>
                                        <Text type='tertiary'>{formatMessage({ id: 'devices.dataImportDetail.lineNumber' })}</Text>
                                    </List.Item>
                                    {fields.map((item, index) => {
                                        return (
                                            <List.Item key={index} className={styles.errorItem}>
                                                <Text type='tertiary'>{item}</Text>
                                            </List.Item>
                                        );
                                    })}
                                    <List.Item className={styles.errorItem}>
                                        <Text type='tertiary'>{formatMessage({ id: 'devices.dataImportDetail.errorMessage' })}</Text>
                                    </List.Item>
                                </List>

                                {importErrors && importErrors.length > 0 ? <>

                                    {importErrors.map((item, index) => {
                                        return (
                                            <List layout="horizontal" key={index} className={styles.errorList} style={{ width: (fields.length + 2) * 100 }}>
                                                <List.Item className={styles.errorItem}>
                                                    {item.line}
                                                </List.Item>
                                                {item.data.map((dataItem, dataIndex) => {
                                                    return (
                                                        <List.Item key={dataIndex} className={styles.errorItem}>
                                                            <Text ellipsis>{dataItem}</Text>
                                                        </List.Item>
                                                    );
                                                })}
                                                <List.Item className={styles.errorItem}>
                                                    {item.message}
                                                </List.Item>
                                            </List>
                                        );
                                    })}

                                </> : <Paragraph>{formatMessage({ id: 'devices.dataImportDetail.noErrorRows' })}</Paragraph>
                                }
                            </div>
                        </TabPane>
                    </Tabs>
                </> :
                <>

                    <Space style={{ alignItems: 'flex-start' }}>
                        <Upload
                            beforeUpload={(prop) => {
                                if (!checkFileType(prop.file)) {
                                    Toast.error(formatMessage({ id: 'devices.dataImportDetail.uploadError' }));
                                    return false;
                                }
                                return true;
                            }}
                            style={{ width: 740 }}
                            showUploadList={false}
                            action=''
                            draggable
                            dragMainText={formatMessage({ id: 'devices.dataImportDetail.uploadText' })}
                            dragSubText={formatMessage({ id: 'devices.dataImportDetail.uploadSubText' })}
                            fileList={files}
                            onChange={({ fileList }) => setFiles(fileList)}
                            customRequest={(options) => {
                                let ext = options.file.name.split('.').pop();
                                if (!ext) return;
                                let fileName = options.file.name.split('.').slice(0, -1).join('.');
                                if (fileName.length > 50) {
                                    fileName = fileName.slice(0, 50);
                                }
                                fileName = `${fileName}-${new Date().getTime()}.${ext}`;

                                // 生成文件名加上时间戳
                                let uploadName = `${rootPath}/${fileName}`;

                                flylayerClient.getUploadUrlWithPathName({
                                    path: rootPath,
                                    name: fileName,
                                }).then((res) => {
                                    uploadFileWithProgress(res.uploadUrl, options.fileInstance, (total, loaded) => {
                                        options.onProgress && options.onProgress({ total, loaded });
                                    }).then(() => {
                                        flylayerClient.importMachinesPreview({
                                            flynetId: flynet.id,
                                            fileName: uploadName,
                                            fileUrl: res.accessUrl,
                                        }).then((res) => {

                                            setUpdateMachines(res.updateMachines);
                                            setImportErrors(res.errors);
                                            setImportId(res.importRecordId);
                                            setIsPreviewStep(true);
                                        });
                                    }).catch((err) => {
                                        options.onError(err);
                                    });
                                }).catch((err) => {
                                    options.onError(err);
                                });
                            }}
                            uploadTrigger='auto'
                            limit={1}
                            accept='.csv'
                        ></Upload>
                        <Button size='large' type='primary' theme='solid' icon={<IconPaperclip />} onClick={handleTemplateDown}>{formatMessage({ id: 'devices.dataImportDetail.templateDownload' })}</Button>
                    </Space>
                </>}
        </Modal>
    </>
}

export default Index;