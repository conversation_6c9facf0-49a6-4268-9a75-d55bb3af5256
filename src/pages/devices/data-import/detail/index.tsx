import { FC, useEffect, useState } from 'react';
import { Typography, Table, Skeleton, Breadcrumb, Tag, Divider, Descriptions } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { useParams } from 'react-router-dom';
import { ImportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { flylayerClient } from '@/services/core';
import TableEmpty from '@/components/table-empty';
import DateFormat from '@/components/date-format';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;

const Index: FC = () => {
    const { formatMessage: _formatMessage } = useLocale();
    const params = useParams<{ id: string }>()
    const idStr = params.id || '';

    const [loading, setLoading] = useState(true);
    const [record, setRecord] = useState<ImportRecord>();

    useEffect(() => {
        setLoading(true);
        flylayerClient.getImportRecord({
            id: BigInt(idStr)
        }).then((res) => {
            setRecord(res.record);
            setLoading(false);
        }
        ).catch((err) => {
            console.error('获取导入记录失败', err);
            setLoading(false);
        }
        ).finally(() => {
            setLoading(false);
        });
    }, [idStr]);

    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/devices`,
                        href: `${BASE_PATH}/devices`,
                        name: '所有设备'
                    }, {
                        path: `${BASE_PATH}/devices/import`,
                        href: `${BASE_PATH}/devices/import`,
                        name: '数据导入',
                    }, {
                        name: '导入详情'
                    }
                ]
            }>
            </Breadcrumb>
            <Title heading={3} className='mb20'>导入记录详情</Title>
            <Divider className='mb10' />
            <Skeleton loading={loading} >
                <Descriptions className='mb40'>
                    <Descriptions.Item key={'fileName'} itemKey='文件名'>{record?.fileName}</Descriptions.Item>
                    <Descriptions.Item key={'fileUrl'} itemKey='文件地址'>
                        <a className='link-external' href={record?.fileUrl} target='_blank'>{record?.fileUrl}</a>
                    </Descriptions.Item>
                    <Descriptions.Item key={'totalLine'} itemKey='数据行数'>{record?.totalLine}</Descriptions.Item>
                    <Descriptions.Item key={'errorLine'} itemKey='错误行数'>{record?.errorLine}</Descriptions.Item>
                    <Descriptions.Item key={'createLine'} itemKey='创建行数'>{record?.createLine}</Descriptions.Item>
                    <Descriptions.Item key={'updateLine'} itemKey='更新行数'>{record?.updateLine}</Descriptions.Item>
                    <Descriptions.Item key={'finish'} itemKey='是否完成'>{record?.finish ? '是': '否'}</Descriptions.Item>
                    <Descriptions.Item key={'user'} itemKey='操作人'>{record?.user?.displayName}({record?.user?.loginName})</Descriptions.Item>
                    <Descriptions.Item key={'createdAt'} itemKey='导入时间'><DateFormat date={record?.createdAt} /></Descriptions.Item>
                </Descriptions>


                <Title heading={4} className='mb2'>错误行</Title>
                <Paragraph className='mb10' type='tertiary'>设备导入的错误行</Paragraph>
                {record?.errors && record.errors.length > 0 ? (
                    <Table
                        dataSource={record.errors}
                        rowKey='line'
                        pagination={false}
                        columns={[
                            {
                                title: '行号',
                                dataIndex: 'line',
                                width: 100,
                            },
                            {
                                title: '错误信息',
                                dataIndex: 'message',
                            }
                        ]}
                    />
                ) : (
                    <TableEmpty loading={false}></TableEmpty>
                )}
                <div style={{ height: 40 }}></div>


                <Title heading={4} className='mb2'>导入详情</Title>
                <Paragraph className='mb10' type='tertiary'>设备导入每条数据的详情</Paragraph>
                {record?.importItems && record.importItems.length > 0 ? (
                    <Table
                        dataSource={record.importItems}
                        rowKey='line'
                        pagination={false}
                        columns={[
                            {
                                title: '行号',
                                dataIndex: 'lineNumber',
                                width: 100,
                            },
                            {
                                title: '是否成功',
                                dataIndex: 'success',
                                render: (value: boolean) => {
                                    return <Tag color={value  ? 'green' : 'red'}>{value}</Tag>;
                                }
                            },
                            {
                                title: '是否完成',
                                dataIndex: 'finish',
                                render: (value: boolean) => {
                                    return <Tag color={value ? 'green' : 'red'}>{value ? '是' : '否'}</Tag>;
                                }
                            },
                            {
                                title: '信息',
                                dataIndex: 'errorMessage',
                            }
                        ]}
                    />
                ) : (
                    <TableEmpty loading={false}></TableEmpty>
                    
                )}
                <div style={{ height: 40 }}></div>
            </Skeleton>

        </div>
    </>
}

export default Index;