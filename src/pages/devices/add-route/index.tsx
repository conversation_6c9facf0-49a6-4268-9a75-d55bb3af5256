import { FC, useState } from 'react'
import { Modal, Form, Notification } from '@douyinfe/semi-ui';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { MachineRoutes, AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss';

import { isValidIPRangeOrCIDR } from '@/utils/validators';
import { useLocale } from '@/locales';

const { Switch, Input, RadioGroup, Radio } = Form

interface Props {
    close: () => void,
    record: Machine,
    advertisedRoutes: AdvertisedRoute[],
    success?: (routes?: MachineRoutes) => void
}

// 校验重重复的路由

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi<{ value: string, enable: boolean, remark: string, routeType: AdvertisedRoute_RouteType }>>()

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    // 点击确定按钮
    const handleOk = async () => {
        if (!formApi) {
            return
        }
        await formApi.validate();
        let err = formApi.getError('value');
        
        if (err) {
            return;
        }
        let { value, remark, enable, routeType } = formApi.getValues();
        value = value.trim();
        remark = remark ? remark.trim() : '';
        let find = false;
        props.advertisedRoutes.forEach((route) => {
            if (route.route === value) {
                find = true;
            }
        })

        if(find) {
            Notification.error({ content: `添加子网路由失败，子网路由${value}已经存在`, position: "bottomRight" })
            return;
        }

        setLoading(true);

        flylayerClient.createAdvertisedRoutes({
            machineId: props.record.id,
            advertisedRoutes: [new AdvertisedRoute({
                route: value,
                enabled: enable,
                remark: remark,
                routeType: routeType == AdvertisedRoute_RouteType.ADVERTISED ? AdvertisedRoute_RouteType.ADVERTISED : AdvertisedRoute_RouteType.EXCLUDE
            })]
            
        }).then((res) => {
            props.success && props.success(res.routes);
            Notification.success({ content: formatMessage({ id: 'devices.addRoute.addSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'devices.addRoute.addFailed' }), position: "bottomRight" })

        }).finally(() => setLoading(false))
    }
    const handleCancel = () => {
        props.close();
    };

    const handleNameChange = () => {
    }

    return <>
        <Modal
            width={500}
            title={`添加设备${props.record.givenName}的子网路由`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
            okText={formatMessage({ id: 'devices.addRoute.confirm' })}
            okButtonProps={{ loading }}
        >
            <div className={styles.addRoute}>
                <Form getFormApi={SetFormApi} initValues={{
                    routeType: AdvertisedRoute_RouteType.ADVERTISED,
                    enable: true,
                }} render={() => (<>
                
                    <RadioGroup label={formatMessage({ id: 'devices.addRoute.type' })} field='routeType' name='routeType' >
                        <Radio value={AdvertisedRoute_RouteType.ADVERTISED} checked>{formatMessage({ id: 'devices.addRoute.advertisedRoute' })}</Radio>
                        <Radio value={AdvertisedRoute_RouteType.EXCLUDE}>{formatMessage({ id: 'devices.addRoute.excludeRoute' })}</Radio>
                    </RadioGroup>
                    <Input field='value' validate={isValidIPRangeOrCIDR} placeholder={formatMessage({ id: 'devices.addRoute.placeholder' })} label={formatMessage({ id: 'devices.addRoute.subnetRoute' })}></Input>
                    <Input field='remark' maxLength={64} label={formatMessage({ id: 'devices.addRoute.remark' })}></Input>
                    <Switch onChange={handleNameChange} field='enable' label={formatMessage({ id: 'devices.addRoute.enableImmediately' })} />
                </>)} />
            </div>
        </Modal>
    </>
}

export default Index;