import { useState, useContext } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';
import { MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';
import { useLocale } from '@/locales';

import { flylayerClient } from '@/services/core';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: MachineGroup
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={`${formatMessage({ id: 'devices.groupDel.title' })}${props.record.name}`}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

            flylayerClient.deleteMachineGroup({
                groupId: props.record.id,
                flynetId: flynet?.id
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'devices.groupDel.deleteSuccess' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'devices.groupDel.deleteFailed' }), position: "bottomRight" })
            }).finally(() => setLoading(false))

        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'>{formatMessage({ id: 'devices.groupDel.description' })}
        </Paragraph>
        <Paragraph className='mb20'>{formatMessage({ id: 'devices.groupDel.confirmText' })} <b>{props.record.name}</b> {formatMessage({ id: 'devices.groupDel.confirmSuffix' })}
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;