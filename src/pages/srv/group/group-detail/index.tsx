import React, { FC, useEffect, useState } from 'react'
import { Breadcrumb, Row, Col, Button, Typography, Tag, Notification, Dropdown, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List } from '@douyinfe/semi-ui';

import { BASE_PATH } from '@/constants/router';

import { Service, ServiceGroup, ServiceType, ServiceOrigin, ServiceProto, ServiceRouteMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import styles from './index.module.scss';
const { Title, Paragraph, Text } = Typography;

const Index: FC = () => {

    // 详情加载标识，用于骨架屏
    const [detailLoading, setDetailLoading] = useState(false);

    // 详情数据
    const [service, setService] = useState<Service>();


    return (
        <><Skeleton placeholder={<div className='general-page'>
            <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>

            <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
            <Skeleton.Image style={{ height: 60 }} className='mb40' />
            <Skeleton.Image style={{ height: 200 }} />
        </div>} loading={detailLoading}>
            <div className='general-page'>
                <Breadcrumb routes={
                    [
                        
                {
                    path: `${BASE_PATH}/srv/routing`,
                    href: `${BASE_PATH}/srv/routing`,
                    name: '网络服务'
                },{
                            path: `${BASE_PATH}/srv/routing/group`,
                            href: `${BASE_PATH}/srv/routing/group`,
                            name: '服务组'
                        },
                        {
                            name: service?.name,
                        }
                    ]
                }>
                </Breadcrumb>
                <Title  className={styles.heading} heading={3}>
                    <span>{service?.name}</span>
                    {/* {service?.status === 'running' ?
                            <Badge className={styles.headingBadge} dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> :
                            <Badge className={styles.headingBadge} dot type='tertiary' />} */}
                    </Title>

                <Divider style={{ marginBottom: 10 }} />
                <div className={styles.detailTable}>
                <Descriptions row={true}>
                    <Descriptions.Item key="remark" itemKey="服务名称">OA服务</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务类型">HTTP</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务来源">自动发现</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务状态">在线</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="加入网络">默认网络</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="所属站点">上海站</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务协议">TCP</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务端口">443</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务访问地址">**********</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务物理地址">***************</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务创建时间">2020-11-11 23:30:20</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务发现时间">2020-11-11 23:30:20</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="服务运行所在设备设备组">默认设备组</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey="自定义标签">标签</Descriptions.Item>
                </Descriptions>
                </div>
            </div>
        </Skeleton>
        </>
    )
}

export default Index;