import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input, TabPane } from '@douyinfe/semi-ui';

import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';
import { Service, ServiceGroup, ServiceType, ServiceOrigin, ServiceProto, ServiceRouteMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: ServiceGroup
}

const Index: FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <>
        <Modal
            width={500}
            title={`删除服务组${props.record.name}`}
            visible={true}
            okButtonProps={{ 
                disabled: props.record.name !== confirmVal,
                loading, 
                type: 'danger'
             }}
            onOk={() => {
                setLoading(true)

                flylayerClient.deleteServiceGroups({
                    serviceGroupId: props.record.id,
                    flynetId: flynet?.id
                }).then(() => {
                    Notification.success({ content: "删除服务组成功", position: "bottomRight" })
                    if (props.success) {
                        props.success();
                    }
                }).catch((err) => {
                    console.error(err);
                    Notification.error({ content: "删除服务组失败，请稍后重试", position: "bottomRight" })
                }).finally(() => setLoading(false))
                
            }}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'> 服务组将被删除，删除后将无法访问服务组下的服务。
            </Paragraph>
            <Paragraph className='mb20'> 输入 <b>{props.record.name}</b> 以确认删除
            </Paragraph>
            <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
