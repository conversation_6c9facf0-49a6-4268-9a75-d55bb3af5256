import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Divider } from '@douyinfe/semi-ui';
import useTable, { ServicesFilter } from './useTable';
import { getQueryParam } from '@/utils/query';
import { ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { Location, useLocation, useNavigate } from 'react-router-dom';
import GroupAdd from './group-add';
import TableEmpty from '@/components/table-empty';

import SearchFilter, { FilterParam } from '@/components/search-filter';

import GroupEdit from './group-edit';
import GroupDel from './group-del';

const { Title } = Typography;

// 根据URL参数设置过滤参数
const getServicesFilter = (location: Location): ServicesFilter => {
    const query: string = getQueryParam('query', location) as string;
    return {
        query: query || ''
    }
}

const Index: React.FC = () => {

    const initFilter: ServicesFilter = getServicesFilter(useLocation());
    const { columns, loading, allGroups: allServices, groups: services, selectedGroup, setSelectedGroup,
        editVisible, setEditVisible,
        serviceType,
        delVisible, setDelVisible,
        reloadFlag, setReloadFlag, filter, setFilter } = useTable(initFilter);

    const navigate = useNavigate();
    
    const location = useLocation();
    

    // 过滤参数改变时跳转路由
    const doNavigate = (param: ServicesFilter) => {
        const { query } = param;
        navigate(`${location.pathname}/?query=${query}`);
    }
    const handleQueryChange = (value: string) => {
        setFilter({ ...filter, query: value })
        doNavigate({ ...filter, query: value });
    }

    const [createVisible, setCreateVisible] = useState(false);
    const [groupVisible, setGroupVisible] = useState(false);

    const [filterParams, setFilterParams] = useState<FilterParam[]>([{
        name: 'query',
        placeholder: '根据服务组名称或描述查询',
        label: '查询',
        value: initFilter.query || '',
    }
        // , {
        //     name: 'type',
        //     placeholder: '服务类型',
        //     label: '服务类型',
        //     value: initFilter.type || '',
        //     filterComponent: TypeSelector,
        //     funGetDisplayValue: getTypeDisplayValue,

        // }, {
        //     name: 'source',
        //     placeholder: '来源',
        //     label: '来源',
        //     value: initFilter.source || '',
        //     filterComponent: SourceSelector,
        //     funGetDisplayValue: getSourceDisplayValue
        // }, {
        //     name: 'lastSeen',
        //     placeholder: '最近在线时间',
        //     label: '最近在线时间',
        //     value: initFilter.lastSeen || '',
        //     filterComponent: LastSeenSelector,
        //     funGetDisplayValue: getLastSeenDisplayValue
        // }
    ]);

    let servicePath = location.pathname.replace('/group', '');
    let serviceName = '';
    if(location.pathname.indexOf('networking') > -1) {
        serviceName = '网络服务';
    } else if(location.pathname.indexOf('app') > -1) {
        serviceName = '应用服务';
    } else if(location.pathname.indexOf('system') > -1) {
        serviceName = '系统服务';
    }

    return <><div className='general-page'><Breadcrumb routes={
        [
            {
                path: servicePath,
                href: servicePath,
                name: serviceName
            }, 
            {
                name: '服务组',
            }
        ]
    }>
    </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>服务组列表</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>新建服务组</Button>
                </Space>
            </div></Col>
        </Row>
        <Divider className='mb20'></Divider>
        <SearchFilter onChange={(val: string, filterParam) => {
            setFilter({ ...filter, [filterParam.name]: val })
            doNavigate({ ...filter, [filterParam.name]: val });
            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);

        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
        {/* <div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  服务总数 {services.length}</Tag>} </div> */}
        <Table
            rowKey={(record?: ServiceGroup) => record ? record.id + '' : ''}
            expandRowByClick
            // expandAllRows={services.length < 10}
            empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={services} pagination={false} />
    </div>

        {createVisible && <GroupAdd
            close={() => { setCreateVisible(false); }}
            serviceType={serviceType}
            success={() => {
                setCreateVisible(false)
                setReloadFlag(true)
            }}
        ></GroupAdd>
        }

        {delVisible && selectedGroup && <GroupDel
            close={() => {
                setDelVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setDelVisible(false)
                setSelectedGroup(undefined)
                setReloadFlag(true)
            }}
            record={selectedGroup}
        ></GroupDel>}
        {editVisible && selectedGroup && <GroupEdit
            serviceType={serviceType}
            servicesGroupId={selectedGroup.id}
            close={() => {
                setEditVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setEditVisible(false)
                setReloadFlag(true)
            }}
        ></GroupEdit>}
    </>
}

export default Index;