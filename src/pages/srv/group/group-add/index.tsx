import { FC, useState, useContext } from 'react'
import { Typo<PERSON>, Modal, Form, Notification, Button, Row, Col, Divider, Popover } from '@douyinfe/semi-ui';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import pinyin from 'tiny-pinyin';

import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ServiceGroup, ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';

import { flylayerClient } from '@/services/core';

import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons';
import ServicesModalSelector from '@/components/network-service-modal-selector';
import useServicesGroup from '../useServicesGroup';
import { sanitizeLabel } from '@/utils/common';
import { getSimpleNetworkServiceName, getSimpleServiceName } from '@/utils/service';
const { Title } = Typography;
interface Props {
    close: () => void,
    serviceType: ServiceType,
    success?: (ServicesGroup?: ServiceGroup) => void
}
const Index: FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        parentId: bigint,
        alias: string,
        type: GroupType
    }>>()

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    // 服务
    const [service, setService] = useState<NetworkService>()

    const [services, setServices] = useState<NetworkService[]>([])

    const [servicesSelectorVisible, setServicesSelectorVisible] = useState(false);

    const { serviceGroupTreeData, getMapTreeData } = useServicesGroup();

    const handleChange = (values: any) => {
        console.log(values)
    }

    const handleSubmit = async () => {
        await formApi?.validate();
        setLoading(true);
        let values = formApi?.getValues();
        if (values) {
            let parentId = values.parentId && values.parentId > 0 ? values.parentId : BigInt(0);
            const mapTreeData = getMapTreeData();
            const name = values.name.trim();
            let fullName = name;
            if (mapTreeData) {
                let parent = mapTreeData.get(parentId + '');
                if (parent) {
                    fullName = parent.fullName + '/' + fullName;
                }
            }
            let servicesGroup = new ServiceGroup({
                name: name,
                fullName: fullName,
                alias: values.alias ? values.alias.trim() : '',
                type: GroupType.GROUP_STATIC,
                description: values.description ? values.description.trim() : '',
                serviceCount: services.length,
                services: services,
                parentId: values.parentId && values.parentId > 0 ? values.parentId : BigInt(0),
                serviceType: props.serviceType,
            });
            setLoading(true);
            flylayerClient.createServiceGroups({
                serviceGroup: servicesGroup,
                flynetId: flynet.id,

            }).then((res) => {
                Notification.success({
                    title: '创建服务组成功',
                    content: '创建服务组成功'
                });
                props.success && props.success(servicesGroup);
            }).catch((err) => {
                Notification.error({
                    title: '创建服务组失败',
                    content: err.message
                })
            }).finally(() => {
                setLoading(false);
            })



        }
        setLoading(false);
    }

    const { Switch, Input, Select } = Form
    return <>
        <Modal
            width={600}
            title='新建服务组'
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >
            <div className={styles.addService}>
                <Form getFormApi={SetFormApi}
                    onValueChange={(values, changedValue) => {
                        if(changedValue.hasOwnProperty('alias')){
                            formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                        }
                    }}
                    allowEmpty
                    initValues={
                        service
                    }

                >
                    <>
                        <Row style={{ display: 'none' }}>
                            <Col span={24}>
                                <Form.TreeSelect
                                    style={{ width: '100%' }}
                                    placeholder='请选择服务组'
                                    expandAll
                                    showClear
                                    treeData={
                                        serviceGroupTreeData
                                    }
                                    field='parentId' label='上级服务组'></Form.TreeSelect>

                            </Col>
                        </Row>
                        <Row gutter={12}>
                            <Col span={12}>
                                <Input field='alias' 
                                label='名称'
                                trigger={'blur'} validate={value => {
                                    if (!value) {
                                        return '名称不能为空';
                                    }
                                    return '';
                                }} />
                            </Col>
                            <Col span={12}>
                                <Input field='name'
                                 label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{fontSize:14, verticalAlign: 'text-top'}}/></Popover></>} 
                                  trigger={'blur'} validate={value => {
                                    if (!value) {
                                        return '编码不能为空';
                                    }
                                    // 编码不能以-开头
                                    if (value.trim().startsWith('-')) {
                                        return '编码不能以-开头'
                                    }
                                    if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                        return "编码只能包含字母、数字和'-'";
                                    }
                                    return '';
                                }}
                                    required />
                            </Col>
                        </Row>
                        <Row>
                            <Col span={24}>
                                <Input field='description' label='备注' />

                            </Col>
                        </Row>
                        <Divider className='mb20'></Divider>

                        <Row className="mb20">
                            <Col span={20}>
                                <Title heading={6} className="mb10">服务</Title>
                            </Col>
                            <Col span={4} className={styles.rightColumn}>

                                <Button
                                    onClick={() => {
                                        setServicesSelectorVisible(true);
                                    }}
                                    icon={<IconPlus></IconPlus>}></Button>

                            </Col>
                        </Row>
                        {
                            services.length == 0 ? <TableEmpty loading={false}></TableEmpty> :

                                <>
                                    {services.map((item, index) => {

                                        return <Row className="mb10" key={index}>
                                            <Col span={20}>
                                                {getSimpleNetworkServiceName(item)}
                                            </Col>
                                            <Col span={4} className={styles.rightColumn}>

                                                <Button
                                                    type='danger'
                                                    onClick={() => {
                                                        let newServices = services.filter((item, i) => i != index);
                                                        setServices(newServices);
                                                    }}
                                                    icon={<IconMinusCircle></IconMinusCircle>}></Button>

                                            </Col>
                                        </Row>
                                    })}
                                </>
                        }

                    </>

                </Form>
            </div>

        </Modal>
        {
            servicesSelectorVisible && <ServicesModalSelector
                multi={true}
                value={services}
                serviceType={ServiceType.ROUTING}
                onChange={(value) => {
                    setServicesSelectorVisible(false)

                    let newServices = services.filter((item) => true);

                    if (value instanceof Array) {

                        value.forEach((item) => {
                            let find = false;
                            newServices.forEach((s) => {
                                if (s.id == item.id) {
                                    find = true;
                                }
                            })
                            if (!find) {
                                newServices.push(item);
                            }
                        })
                    } else {

                        let find = false;
                        newServices.forEach((s) => {
                            if (s.id == value.id) {
                                find = true;
                            }
                        })
                        if (!find) {
                            newServices.push(value);
                        }
                    }


                    setServices(newServices);
                }}
                close={() => {
                    setServicesSelectorVisible(false);
                }}
            ></ServicesModalSelector>
        }
    </>
}

export default Index