import React, { FC, useEffect, useState, useContext } from 'react'
import { Typography, Modal, Form, Notification, Skeleton, Popover, Button, Row, Col, Spin, List, Divider } from '@douyinfe/semi-ui';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Service, ServiceGroup, ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons';
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { SystemService } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/system_services_pb';   

import NetworkServicesSelector from '@/components/network-service-modal-selector';
const { Title, Paragraph, Text } = Typography;

const { Switch, Input, Select } = Form
import useServicesGroup from '../useServicesGroup';
import { getSimpleNetworkServiceName, getSimpleServiceName, getSimpleSystemServiceName } from '@/utils/service';

interface Props {
    close: () => void,
    success?: (service?: ServiceGroup) => void
    servicesGroupId: bigint,
    serviceType: ServiceType
}
const Index: FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        parentId: bigint,
        alias: string,
        type: GroupType,
    }>>()

    const [servicesGroup, setServicesGroup] = useState<ServiceGroup>()

    const { serviceGroupTreeData, getMapTreeData } = useServicesGroup();

    const [services, setServices] = useState<Service[]>()
    const [networkServicesSelectorVisible, setNetworkServicesSelectorVisible] = useState(false);

    const [networkServices, setNetworkServices] = useState<NetworkService[]>([]);
    
    const [systemServices, setSystemServices] = useState<SystemService[]>([]);

    const [groupLoading, setGroupLoading] = useState(false);

    useEffect(() => {
        setGroupLoading(true);

        if (props.serviceType == ServiceType.ROUTING) {
            flylayerClient.getNetworkServiceGroup({
                serviceGroupId: props.servicesGroupId,
            }).then((res) => {
                setServicesGroup(res.serviceGroup);
                setNetworkServices(res.services)
            }).catch((err) => {
                Notification.error({
                    title: '获取服务组失败',
                    content: err.message
                });
            }).finally(() => {
                setGroupLoading(false);
            })
        } else if (props.serviceType == ServiceType.SYSTEM_DAEMON) {
            flylayerClient.getSystemServiceGroup({
                serviceGroupId: props.servicesGroupId,
            }).then((res) => {
                setServicesGroup(res.serviceGroup);
                setSystemServices(res.services)
            }).catch((err) => {
                Notification.error({
                    title: '获取服务组失败',
                    content: err.message
                });
            }).finally(() => {
                setGroupLoading(false);
            })
        }
        else {
            flylayerClient.getServiceGroup({
                serviceGroupId: props.servicesGroupId,
            }).then((res) => {
                setServicesGroup(res.serviceGroup);
                setServices(res.serviceGroup?.services)
            }).catch((err) => {
                Notification.error({
                    title: '获取服务组失败',
                    content: err.message
                });
            }).finally(() => {
                setGroupLoading(false);
            })
        }
    }, [])


    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleChange = (values: any) => {

    }

    const handleSubmit = async () => {
        await formApi?.validate();
        setLoading(true);
        let values = formApi?.getValues();
        if (values) {
            let parentId = values.parentId && values.parentId > 0 ? values.parentId : BigInt(0);
            const name = values.name.trim();
            let fullName = name;
            const mapTreeData = getMapTreeData();
            if (mapTreeData) {
                let parent = mapTreeData.get(parentId + '');
                if (parent) {
                    fullName = parent.fullName + '/' + fullName;
                }
            }


            let _servicesGroup = {
                ...servicesGroup, ...{
                    name: name,
                    fullName: fullName,
                    alias: values.alias.trim(),
                    type: GroupType.GROUP_STATIC,
                    description: values.description.trim(),
                    serviceCount: services ? services.length : 0,
                    parentId: values.parentId
                        ? values.parentId : BigInt(0),
                }
            };

            if (props.serviceType == ServiceType.ROUTING) {
                _servicesGroup.serviceCount = networkServices.length;
                flylayerClient.updateNetworkServiceGroup({
                    serviceGroup: _servicesGroup,
                    services: networkServices,
                }).then((res) => {
                    Notification.success({
                        title: '更新服务组成功',
                        content: '更新服务组成功'
                    });
                    props.success && props.success();
                }).catch((err) => {
                    Notification.error({
                        title: '更新服务组失败',
                        content: err.message
                    });
                }).finally(() => {
                    setLoading(false);
                })
            } else if (props.serviceType == ServiceType.SYSTEM_DAEMON) {
                _servicesGroup.serviceCount = systemServices.length;
                flylayerClient.updateSystemServiceGroup({
                    serviceGroup: _servicesGroup,
                    services: systemServices,
                }).then((res) => {
                    Notification.success({
                        title: '更新服务组成功',
                        content: '更新服务组成功'
                    });
                    props.success && props.success();
                }).catch((err) => {
                    Notification.error({
                        title: '更新服务组失败',
                        content: err.message
                    });
                }).finally(() => {
                    setLoading(false);
                })
            } else {

                _servicesGroup.services = systemServices.map((item) => {
                    return new Service({
                        id: item.id,
                    });
                })

                flylayerClient.updateServiceGroups({
                    serviceGroup: _servicesGroup,
                    flynetId: flynet.id,
                }).then((res) => {
                    Notification.success({
                        title: '更新服务组成功',
                        content: '更新服务组成功'
                    });
                    props.success && props.success();
                }).catch((err) => {
                    Notification.error({
                        title: '更新服务组失败',
                        content: err.message
                    });
                }).finally(() => {
                    setLoading(false);
                })
            }

        }
    }


    return <>
        <Modal
            width={600}
            title='编辑服务组'
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >
            <Skeleton loading={groupLoading} placeholder={
                <>
                    <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title>
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />

                </>
            }>
                {servicesGroup && <div className={styles.addService}>

                    <Form getFormApi={SetFormApi}
                        onValueChange={handleChange}
                        allowEmpty
                        initValues={
                            {
                                name: servicesGroup.name,
                                description: servicesGroup.description,
                                alias: servicesGroup.alias,
                                parentId: servicesGroup.parentId,
                            }
                        }
                    >
                        <>

                            <Row gutter={12}>
                                <Col span={12}>
                                    <Input field='alias' label='名称' validate={value => {
                                        if (!value) {
                                            return '名称不能为空';
                                        }
                                        return '';
                                    }} />
                                </Col>
                                <Col span={12}>
                                    <Input field='name'
                                        label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}

                                        trigger={'blur'} readonly validate={value => {
                                            if (!value) {
                                                return '编码不能为空';
                                            }
                                            // 编码不能以-开头
                                            if (value.trim().startsWith('-')) {
                                                return '编码不能以-开头'
                                            }
                                            if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                                return "编码只能包含字母、数字和'-'";
                                            }
                                            return '';
                                        }}
                                        required />
                                </Col>
                            </Row>
                            <Row>
                                <Col span={24}>
                                    <Input field='description' label='备注' />

                                </Col>
                            </Row>
                            <Divider className='mb20'></Divider>

                            <Row className="mb20">
                                <Col span={20}>
                                    <Title heading={6} className="mb10">服务</Title>
                                </Col>
                                <Col span={4} className={styles.rightColumn}>

                                    <Button
                                        onClick={() => {
                                            setNetworkServicesSelectorVisible(true);
                                        }}
                                        icon={<IconPlus></IconPlus>}></Button>

                                </Col>
                            </Row>
                            {props.serviceType == ServiceType.ROUTING && <>
                                {
                                    !networkServices || networkServices.length == 0 ? <TableEmpty loading={false}></TableEmpty> :
                                        <>
                                            {networkServices && networkServices.map((item, index) => {
                                                return <Row className="mb10" key={index}>
                                                    <Col span={20}>
                                                        {getSimpleNetworkServiceName(item)}
                                                    </Col>
                                                    <Col span={4} className={styles.rightColumn}>

                                                        <Button
                                                            type='danger'
                                                            onClick={() => {
                                                                let newServices = networkServices.filter((item, i) => i != index);
                                                                setNetworkServices(newServices);
                                                            }}
                                                            icon={<IconMinusCircle></IconMinusCircle>}></Button>

                                                    </Col>
                                                </Row>
                                            })}
                                        </>
                                }
                            </>}
                            {props.serviceType == ServiceType.SYSTEM_DAEMON && <>
                                { !systemServices || systemServices.length == 0 ? <TableEmpty loading={false}></TableEmpty> :
                                    <>
                                        {systemServices && systemServices.map((item, index) => {
                                            return <Row className="mb10" key={index}>
                                                <Col span={20}>
                                                    {getSimpleSystemServiceName(item)}
                                                </Col>
                                                <Col span={4} className={styles.rightColumn}>

                                                    <Button
                                                        type='danger'
                                                        onClick={() => {
                                                            let newServices = systemServices.filter((item, i) => i != index);
                                                            setSystemServices(newServices);
                                                        }}
                                                        icon={<IconMinusCircle></IconMinusCircle>}></Button>

                                                </Col>
                                            </Row>
                                        })}
                                    </>
                                }
                            </>}


                        </>

                    </Form>
                </div>}
            </Skeleton>

        </Modal>
        {
            networkServicesSelectorVisible && <NetworkServicesSelector
                serviceType={ServiceType.ROUTING}
                multi={true}
                value={networkServices}
                onChange={(value) => {
                    setNetworkServicesSelectorVisible(false)
                    if (props.serviceType == ServiceType.ROUTING) {

                        let newServices = networkServices ? networkServices.filter((item) => true) : [];
                        if (value instanceof Array) {
                            value.forEach((item) => {
                                let find = false;
                                newServices.forEach((s) => {
                                    if (s.id == item.id) {
                                        find = true;
                                    }
                                })
                                if (!find) {
                                    newServices.push(new NetworkService({
                                        id: item.id,
                                        name: item.name,
                                        description: item.description,

                                    }))
                                }
                            })
                        } else {
                            let find = false;
                            newServices.forEach((s) => {
                                if (s.id == value.id) {
                                    find = true;
                                }
                            })
                            if (!find) {
                                newServices.push(new NetworkService({
                                    id: value.id,
                                    name: value.name,
                                    description: value.description,

                                })
                                );
                            }
                        }
                        setNetworkServices(newServices);
                        return;
                    }


                }}
                close={() => {
                    setNetworkServicesSelectorVisible(false);
                }}
            ></NetworkServicesSelector>
        }
    </>
}

export default Index