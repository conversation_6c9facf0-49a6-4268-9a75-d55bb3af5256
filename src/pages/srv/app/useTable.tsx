import { useState, useContext, useEffect } from 'react';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ServiceGroup, ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';

import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { FilterParam } from '@/components/search-filter';

import { Typography, Dropdown, Button, Divider, Popover, Row, Col, Tag, Space, Select, RadioGroup, Radio } from '@douyinfe/semi-ui';

import { AppService } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/app_services_pb';
import { caseInsensitiveIncludes } from '@/utils/common';

const { Title, Paragraph, Text } = Typography;

export type RoutingServiceFilter = {
    query: string;
    group?: string;
    status: 'enable' | 'disable' | ''
}

const useTable = (initFilter: RoutingServiceFilter) => {
    const navigate = useNavigate();
    const flynet = useContext(FlynetGeneralContext);

    // 服务组列表
    const [groups, setGroups] = useState<ServiceGroup[]>([]);
    // 当前选中的服务组
    const [curGroup, setCurGroup] = useState<ServiceGroup>();

    // 是否正在加载中
    const [loading, setLoading] = useState(false);
    const [filter, setFilter] = useState<RoutingServiceFilter>(initFilter);
    const [total, setTotal] = useState<number>(0);
    // 当前页码
    const [page, setPage] = useState(1);
    const pageSize = 20;

    const [routingServices, setRoutingServices] = useState<AppService[]>([]);
    const [allRoutingServices, setAllRoutingServices] = useState<AppService[]>([]);

    const [editVisible, setEditVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    const [selectedRoutingService, setSelectedRoutingService] = useState<AppService>();

    const [connectionDelVisible, setConnectionDelVisible] = useState(false);
    const [selectedConnection, setSelectedConnection] = useState<Machine>();
    const [connectionSelectVisible, setConnectionSelectVisible] = useState(false);

    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);

    const initFilterParams = (serviceGroups: ServiceGroup[]) => {
        setFilterParams([{
            name: 'query',
            placeholder: '根据服务名称或描述搜索',
            label: '查询',
            value: initFilter.query || '',
        }, {
            name: 'status',
            placeholder: '请选择状态',
            label: '状态',
            value: initFilter.status || '',
            filterComponent: ({ value, onChange }) => {
                const [val, setVal] = useState(value);
                return <><RadioGroup
                    value={val}
                    onChange={(val) => {
                        setVal(val.target.value);
                    }}
                    style={{ width: 180 }}
                    className='mb10'
                >
                    <Radio value={'enable'}>启用</Radio>
                    <Radio value={'disable'}>禁用</Radio>
                </RadioGroup>
                    <Divider className='mb10' />
                    <Button block onClick={() => {
                        onChange(val)
                    }}>应用</Button>

                </>
            },
            funGetDisplayValue: (val: string) => {
                if (val == 'enable') {
                    return '启用';
                } else if (val == 'disable') {
                    return '禁用';
                }
                return '';
            }
        }, {
            name: 'group',
            placeholder: '服务组',
            label: '服务组',
            value: initFilter.group || '',
            filterComponent: ({ value, onChange }) => {
                const [val, setVal] = useState(value);

                return <><Select
                    style={{ width: 180 }}
                    className='mb10'
                    value={val}
                    onChange={val => setVal(val)}
                    placeholder='选择服务组'
                    optionList={[{ value: '', label: '全部' }, ...serviceGroups.map((item) => { return { value: item.name, label: item.alias || item.name } })]}
                ></Select>
                    <Divider />
                    <Button block onClick={() => {
                        onChange(val)
                    }}>应用</Button>

                </>
            },
            funGetDisplayValue: (val: string) => {
                if (val) {
                    let group = serviceGroups.find((item) => item.name == val);
                    if (group) {
                        return group.alias || group.name;
                    }
                }
                return '';
            }
        }]);
    };

    const query = async () => {

        const resGroup = await flylayerClient.listServiceGroups({
            flynetId: flynet.id,
            serviceType: ServiceType.WEB_APP
        });

        let curGroup: ServiceGroup | undefined;
        setGroups(resGroup.serviceGroups);
        initFilterParams(resGroup.serviceGroups);
        if (resGroup.serviceGroups) {
            resGroup.serviceGroups.forEach((item) => {
                if (item.name == filter.group) {
                    curGroup = item;
                }
            })
        }
        setCurGroup(curGroup);

        flylayerClient.listAppServices({
            flynetId: flynet.id
        }).then((res) => {
            setAllRoutingServices(res.services);
            setRoutingServices(doFilter(1, res.services, filter, resGroup.serviceGroups));
        }).catch((err) => {
            console.error(err);
        }).finally(() => {
            setLoading(false);
        })

    }



    useEffect(() => {
        setPage(1)
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    useEffect(() => {
        query();
    }, []);

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            render: (field: string, record: AppService, index: number) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                        <div>
                            <Title heading={6}>
                                {record.name}
                            </Title>
                            <Paragraph size='small'>{record.description}</Paragraph>
                        </div>
                    </div>
                </>
            }
        },
        {
            title: '所属服务组',
            dataIndex: 'serviceGroups',
            render: (_: any, record: AppService) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{record.serviceGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space></div>
            }
        },
        // {
        //     title: '状态',
        //     dataIndex: 'disabled',
        //     key: 'disabled',
        //     width: 100,
        //     render: (fieldd: string, acl: AppService, index: number) => {
        //         return <>
        //             {acl.disabled ? <Tag color='red'>禁用</Tag> : <Tag color='green'>启用</Tag>}
        //         </>;
        //     }
        // }, 
        {
            title: '',
            dataIndex: 'operation',
            key: 'operation',
            width: 100,
            render: (text: string, record: AppService) => {
                return <><div className='table-last-col' onClick={(e) => {
                    e.nativeEvent.preventDefault();
                    e.nativeEvent.stopPropagation();
                }}><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.preventDefault();
                                    e.nativeEvent.stopPropagation();
                                    setEditVisible(true);
                                    setSelectedRoutingService(record);
                                }}
                            >编辑服务</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.stopPropagation();
                                    e.nativeEvent.stopPropagation();
                                    setDelVisible(true);
                                    setSelectedRoutingService(record);
                                }}
                                type='danger'>删除服务</Dropdown.Item>

                        </Dropdown.Menu>}><Button onClick={(e) => { e.nativeEvent.stopPropagation() }}><IconMore className='align-v-center' /></Button>
                    </Dropdown>
                </div>
                </>;
            }
        }
    ];

    // 过滤数据
    const doFilter = (page: number, src: Array<AppService>, routingPoolFilter: RoutingServiceFilter, serviceGroups?: ServiceGroup[]) => {
        if (!src || src.length == 0) {
            setTotal(0);
            return src;
        }

        if ((!routingPoolFilter.query || routingPoolFilter.query == '') && routingPoolFilter.group == '' && !routingPoolFilter.status) {
            setTotal(src.length);
            return src.slice(0, pageSize * page);
        }

        const group = serviceGroups ? serviceGroups.find((item) => item.name == filter.group) : groups.find((item) => item.name == filter.group);

        const filteredList = src.filter((item) => {
            let isPassName = false;
            let isPassEnabled = false;
            let isPassGroup = false;

            if (routingPoolFilter.query) {
                if (caseInsensitiveIncludes(item.name, routingPoolFilter.query) || caseInsensitiveIncludes(item.name, routingPoolFilter.query) || caseInsensitiveIncludes(item.description, routingPoolFilter.query)) {
                    isPassName = true;
                }

            } else {
                isPassName = true;
            }


            // if (routingPoolFilter.status == 'enable') {
            //     if (item.disabled) {
            //         isPassEnabled = false;
            //     } else {
            //         isPassEnabled = true;
            //     }
            // } else if (routingPoolFilter.status == 'disable') {
            //     if (item.disabled) {
            //         isPassEnabled = true;
            //     } else {
            //         isPassEnabled = false;
            //     }
            // } else {
            //     isPassEnabled = true
            // }
            isPassEnabled = true;

            if (group) {
                item.serviceGroups.forEach((val) => {
                    if (group?.id == val.id) {
                        isPassGroup = true;
                    }
                });
            } else {
                isPassGroup = true;
            }



            if (isPassName && isPassEnabled && isPassGroup) {
                return true;
            } else {
                return false;
            }


            return true;
        })

        setTotal(filteredList.length);
        return filteredList;
    }

    const handleFilterChange = (newFilter: RoutingServiceFilter) => {
        setFilter(newFilter)
        setRoutingServices(doFilter(1, allRoutingServices, newFilter))
    }

    const addPage = () => {

        setRoutingServices(doFilter(page + 1, allRoutingServices, initFilter));
        setPage(page + 1)

    }

    const handleGroupChange = (group?: ServiceGroup) => {
        const list = doFilter(1, allRoutingServices, filter);
        setRoutingServices(list);
        setCurGroup(group);
    };

    return {
        loading,
        routingPools: routingServices,
        total,
        columns,
        filter,
        setFilter,
        filterParams,
        setFilterParams,
        handleFilterChange,
        reloadFlag,
        setReloadFlag,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedRoutingService,
        setSelectedRoutingService,
        groups,
        curGroup, setCurGroup,
        addPage,
        handleGroupChange,
        page, pageSize,
        connectionDelVisible, setConnectionDelVisible,
        selectedConnection, setSelectedConnection,
        connectionSelectVisible, setConnectionSelectVisible
    }
}

export default useTable;
