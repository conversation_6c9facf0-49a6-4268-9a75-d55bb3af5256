import React, { useState, useContext } from "react";
import { Typo<PERSON>, Modal, Form, Row, Col, Notification, Divider, Popover, ArrayField, Button, Collapse } from "@douyinfe/semi-ui";
import { IconHelpCircle } from '@douyinfe/semi-icons';
import { ServiceGroup, ServiceType, ServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';

import { AppService, AppServiceUpstream, AppServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/app_services_pb';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from "@/services/core";
import useServicesGroup from '../../group/useServicesGroup';
import FormArrayInput from "@/components/array-input";

const { Text } = Typography;

interface Props {
  close: () => void,
  success?: () => void,
}

const Index: React.FC<Props> = (props) => {
  const flynet = useContext(FlynetGeneralContext);
  const [formApi, setFormApi] = useState<FormApi<{
    name: string;
    description: string;
    priority: number;
    host: string;
    hosts: string[];
    uri: string;
    uris: string[];
    remoteAddr: string;
    remoteAddrs: string[];
    methods: string[];
    serviceProtocol: string;
    enableWebsocket: boolean;
    script: string;
    timeout: {
      connect: number;
      send: number;
      read: number;
    };
    upstream: {
      nodes: Array<{
        ip: string;
        port: number;
        weight: number;
        priority: number;
      }>;
      retries: number;
      timeout: {
        connect: number;
        send: number;
        read: number;
      };
      type: string;
      checks: { [key: string]: string };
      hashOn: string;
      key: string;
      scheme: string;
      discoveryType: string;
      discoveryArgs: { [key: string]: string };
      passHost: string;
      upstreamHost: string;
      name: string;
      serviceName: string;
      labels: { [key: string]: string };
      tls: {
        clientCert: string;
        clientKey: string;
      };
      keepalivePool: {
        idleTimeout: number;
        requests: number;
        size: number;
      };
      retryTimeout: number;
    };
    serviceGroups: Array<string>;
  }>>();

  const [gatewayIp, setGatewayIp] = useState('');


  const [gatewayNodeViewerVisible, setGatewayNodeViewerVisible] = useState(false);

  // 网关节点选择
  const [gatewaySelectorVisible, setGatewaySelectorVisible] = useState(false);
  // 网关节点
  const [gatewayNodes, setGatewayNodes] = useState<Array<ServiceNode>>([])

  // 从设备添加网关结点
  const addGatewayNodeFromMachine = () => {
    setGatewaySelectorVisible(true)
  }

  // 保存按钮loading
  const [saveLoading, setSaveLoading] = useState(false);

  const { serviceGroupTreeData } = useServicesGroup(ServiceType.ROUTING);

  const [nodeSelectorVisible, setNodeSelectorVisible] = useState(false);

  const handleSubmit = async () => {
    await formApi?.validate();
    const values = formApi?.getValues();
    if (!values) {
      return;
    }

    let name = values.name ? values.name.trim() : '';
    let description = values.description ? values.description.trim() : '';
    let priority = values.priority;
    let hosts = values.hosts;
    let uris = values.uris;
    let remoteAddrs = values.remoteAddrs;
    let methods = values.methods;
    let serviceProtocol = values.serviceProtocol;
    let enableWebsocket = values.enableWebsocket;
    let script = values.script;
    let timeout = values.timeout;
    let upstream = values.upstream;


    let serviceGroups: Array<ServiceGroup> = []
    if (values.serviceGroups) {
      serviceGroups = values.serviceGroups.map((id: string) => {
        let serviceGroup = new ServiceGroup({ id: BigInt(id) });
        return serviceGroup;
      });
    }


    setSaveLoading(true);

    let service = new AppService({
      name: name,
      description: description,
      priority: priority,
      hosts: hosts,
      uris: uris,
      remoteAddrs: remoteAddrs,
      methods: methods,
      serviceProtocol: serviceProtocol,
      enableWebsocket: enableWebsocket,
      script: script,
      timeout: {
        connect: timeout.connect,
        send: timeout.send,
        read: timeout.read
      },
      upstream: new AppServiceUpstream({
        nodes: upstream.nodes.map((node) => {
          return new AppServiceNode({
            ip: node.ip,
            port: node.port,
            weight: node.weight,
            priority: node.priority
          });
        }
        ),
        retries: upstream.retries,
        timeout: {
          connect: upstream.timeout.connect,
          send: upstream.timeout.send,
          read: upstream.timeout.read
        },
        type: upstream.type,
        checks: upstream.checks,
        hashOn: upstream.hashOn,
        key: upstream.key,
        scheme: upstream.scheme,
        discoveryType: upstream.discoveryType,
        discoveryArgs: upstream.discoveryArgs,
        passHost: upstream.passHost,
        upstreamHost: upstream.upstreamHost,
        name: upstream.name,
        serviceName: upstream.serviceName,
        labels: upstream.labels,
        tls: {
          clientCert: upstream.tls.clientCert,
          clientKey: upstream.tls.clientKey
        },
        keepalivePool: {
          idleTimeout: upstream.keepalivePool.idleTimeout,
          requests: upstream.keepalivePool.requests,
          size: upstream.keepalivePool.size
        },
        retryTimeout: upstream.retryTimeout
      }),
      serviceGroups: serviceGroups
    });


    flylayerClient.createAppService({
      flynetId: flynet.id,
      service: service
    }).then((res) => {
      Notification.success({
        title: '创建成功',
        content: '网络服务创建成功'
      });
      props.success?.();
      props.close();
    }).catch((err) => {
      Notification.error({
        title: '创建失败',
        content: err.message
      });
    }).finally(() => {
      setSaveLoading(false);
    });

  }

  return <><Modal
    title="添加应用服务"
    visible={true}
    onOk={handleSubmit}
    onCancel={props.close}
    width={860}
    okButtonProps={{ loading: saveLoading }}
    closeOnEsc={true}
    maskClosable={false}
  >
    <Form getFormApi={setFormApi}
    >
      {({ values }) => (<>
        <Form.Section text="服务" >
          <Collapse className="mb20" defaultActiveKey={['0']}>
            <Collapse.Panel header="基础信息" itemKey={'0'}>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Input field='name'
                    label={<>名称 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                    trigger={'blur'} validate={value => {
                      if (!value) {
                        return '名称不能为空';
                      }
                      // 编码不能以-开头
                      if (value.trim().startsWith('-')) {
                        return '名称不能以-开头'
                      }
                      if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                        return "名称只能包含字母、数字和'-'";
                      }
                      return '';
                    }}
                    required />
                </Col>
                <Col span={16}>
                  <Form.Input field="description" label="描述" />
                </Col>
              </Row>


              <Row gutter={24}>
                <Col span={24}>
                  <Form.TreeSelect style={{ width: '100%' }}
                    multiple
                    expandAll
                    checkRelation='unRelated'
                    treeData={
                      serviceGroupTreeData
                    }
                    field='serviceGroups' label='服务组'
                    filterTreeNode
                    showFilteredOnly
                    dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                  ></Form.TreeSelect>
                </Col>
              </Row>
            </Collapse.Panel>
            <Collapse.Panel header="匹配条件" itemKey={'1'}>

              <Row gutter={24}>
                <Col span={8}>
                  <Form.Select style={{ width: '100%' }} label="HTTP方法" field="methods" multiple>
                    <Form.Select.Option value="GET">GET</Form.Select.Option>
                    <Form.Select.Option value="POST">POST</Form.Select.Option>
                    <Form.Select.Option value="PUT">PUT</Form.Select.Option>
                    <Form.Select.Option value="DELETE">DELETE</Form.Select.Option>
                    <Form.Select.Option value="PATCH">PATCH</Form.Select.Option>
                    <Form.Select.Option value="OPTIONS">OPTIONS</Form.Select.Option>
                    <Form.Select.Option value="HEAD">HEAD</Form.Select.Option>
                    <Form.Select.Option value="TRACE">TRACE</Form.Select.Option>
                    <Form.Select.Option value="CONNECT">CONNECT</Form.Select.Option>
                  </Form.Select>
                </Col>

                <Col span={8}>
                  <Form.InputNumber field="priority" label="优先级" />
                </Col>
                <Col span={8}>
                  <Form.Switch field="enableWebsocket" label="启用WebSocket" />
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  {/* 当前请求域名，比如 foo.com；也支持泛域名，比如 *.foo.com。 */}
                  <FormArrayInput field="hosts" label="域名" />
                </Col>
              </Row>

              <Row>
                <Col span={24}>
                  {/* 除了如 /foo/bar、/foo/gloo 这种全量匹配外，使用不同 Router 还允许更高级匹配，更多信息请参考 Router。 */}
                  <FormArrayInput field="uris" label="路径" />
                </Col>
              </Row>

              <Row>
                <Col span={24}>
                  {/* 客户端请求的 IP 地址。支持 IPv4 地址，如：************* 以及 CIDR 格式的支持 ***********/24；支持 IPv6 地址匹配，如 ::1，fe80::1，fe80::1/64 等。 */}
                  <FormArrayInput field="remoteAddrs" label="客户端地址" />
                </Col>
              </Row>

            </Collapse.Panel>
            <Collapse.Panel header="超时配置" itemKey="2">

              <Row gutter={24}>
                <Col span={8}>
                  <Form.InputNumber style={{ width: '100%' }} field="timeout.connect" label="连接超时" />
                </Col>
                <Col span={8}>
                  <Form.InputNumber style={{ width: '100%' }} field="timeout.send" label="发送超时" />
                </Col>
                <Col span={8}>
                  <Form.InputNumber style={{ width: '100%' }} field="timeout.read" label="读取超时" />
                </Col>
              </Row>

            </Collapse.Panel>
            <Collapse.Panel header="其他配置" itemKey="3">
              <Row>
                <Col span={24}>
                  <Form.TextArea field="script" label="路由脚本配置" style={{ width: '100%' }} />
                </Col>
              </Row>
            </Collapse.Panel>
          </Collapse>
        </Form.Section>


        <Form.Section text="上游" >
          <Collapse className="mb20" defaultActiveKey={['1']}>
            <Collapse.Panel header="基础信息" itemKey="1">
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Input field="upstream.name" label="名称" />
                </Col>
                <Col span={12}>
                  <Form.Input field="upstream.discoveryType" label="服务发现类型" />
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Select style={{ width: '100%' }} field="upstream.passHost" label="Host请求头">
                    <Form.Select.Option value="pass">pass</Form.Select.Option>
                    <Form.Select.Option value="node">节点</Form.Select.Option>
                    <Form.Select.Option value="rewrite">rewrite</Form.Select.Option>
                  </Form.Select>
                </Col>
                <Col span={16}>
                  <Form.Input field="upstream.upstreamHost" label="上游主机" />
                </Col>

              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Select style={{ width: '100%' }} label="负载均衡算法" field="upstream.type" >
                    <Form.Select.Option value="roundrobin">轮询</Form.Select.Option>
                    <Form.Select.Option value="leastconn">最少连接</Form.Select.Option>
                    <Form.Select.Option value="iphash">IP哈希</Form.Select.Option>
                  </Form.Select>
                </Col>
                <Col span={8}>
                  <Form.Select style={{ width: '100%' }} label="协议" field="upstream.scheme" >
                    <Form.Select.Option value="http">HTTP</Form.Select.Option>
                    <Form.Select.Option value="https">HTTPS</Form.Select.Option>
                    <Form.Select.Option value="grpc">GRPC</Form.Select.Option>
                    <Form.Select.Option value="grpcs">GRPCS</Form.Select.Option>
                  </Form.Select>
                </Col>
                <Col span={8}>
                  <Form.Select style={{ width: '100%' }} field="upstream.hashOn" label="Host请求头" >
                    <Form.Select.Option value="vars">vars</Form.Select.Option>
                    <Form.Select.Option value="header">自定义header</Form.Select.Option>
                    <Form.Select.Option value="cookie">cookie</Form.Select.Option>
                    <Form.Select.Option value="consumer">consumer</Form.Select.Option>
                  </Form.Select>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.InputNumber field="upstream.retries" label="重试次数" />
                </Col>
                <Col span={12}>
                  <Form.InputNumber field="upstream.retryTimeout" label="重试超时时间" />
                </Col>
              </Row>
            </Collapse.Panel>
            <Collapse.Panel header="超时配置" itemKey="2">
              <Row gutter={24}>
                <Col span={8}>
                  <Form.InputNumber field="upstream.timeout.connect" label="连接超时" />
                </Col>
                <Col span={8}>
                  <Form.InputNumber field="upstream.timeout.send" label="发送超时" />
                </Col>
                <Col span={8}>
                  <Form.InputNumber field="upstream.timeout.read" label="读取超时" />
                </Col>
              </Row>
            </Collapse.Panel>
            <Collapse.Panel header="证书配置" itemKey="3">
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Input field="upstream.tls.clientCert" label="HTTPS证书" />
                </Col>
                <Col span={12}>
                  <Form.Input field="upstream.tls.clientKey" label="HTTPS证书密钥" />
                </Col>
              </Row>
            </Collapse.Panel>
            <Collapse.Panel header="连接池" itemKey="4">
              <Row gutter={24}>
                <Col span={8}>
                  <Form.InputNumber field="upstream.keepalivePool.idleTimeout" label="空闲超时时间" />
                </Col>
                <Col span={8}>
                  <Form.InputNumber field="upstream.keepalivePool.requests" label="请求数量" />
                </Col>
                <Col span={8}>
                  <Form.InputNumber field="upstream.keepalivePool.size" label="容量" />
                </Col>
              </Row>
            </Collapse.Panel>
            <Collapse.Panel header="目标节点" itemKey="5">

              <Row>
                <Col span={24}>
                  <ArrayField field="upstream.nodes">
                    {({ add, arrayFields }) => {
                      return <>
                        <Row gutter={24}>
                          <Col span={5}>
                            <Text>IP</Text>
                          </Col>
                          <Col span={5}>端口</Col>
                          <Col span={5}>权重</Col>
                          <Col span={5}>优先级</Col>
                          <Col span={4} className='btn-right-col'>
                            <Button onClick={() => add()} icon={<IconPlusCircle />}></Button>
                          </Col>
                        </Row>
                        {arrayFields.map(({ field, key, remove }, i) => {
                          return <Row gutter={24} key={key}>
                            <Col span={5}>
                              <Form.Input field={`${field}.ip`} noLabel />
                            </Col>
                            <Col span={5}>
                              <Form.InputNumber field={`${field}.port`} noLabel />
                            </Col>
                            <Col span={5}>
                              <Form.InputNumber field={`${field}.weight`} noLabel />
                            </Col>
                            <Col span={5}>
                              <Form.InputNumber field={`${field}.priority`} noLabel />
                            </Col>
                            <Col span={4} className='btn-right-col'>
                              <Button onClick={() => remove()} type="danger" theme="borderless" icon={<IconMinusCircle />}
                                style={{ marginTop: 12 }}
                              ></Button>
                            </Col>
                          </Row>
                        })}
                      </>
                    }}
                  </ArrayField>

                </Col>
              </Row>
            </Collapse.Panel>

            <Collapse.Panel header="其他配置" itemKey="6">

              <Row>
                <Col span={24}>
                  <Form.Input field="upstream.key" label="Key" />
                </Col>
              </Row>
            </Collapse.Panel>
          </Collapse>






        </Form.Section>

      </>)}
    </Form>
  </Modal>

  </>
}

export default Index;