import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Layout, Input, Select, Tabs, TabPane, List } from '@douyinfe/semi-ui';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";
import { IconInfoCircle } from '@douyinfe/semi-icons';
import styles from './index.module.scss'
import useTable from './useTable';

const { Title, Paragraph, Text } = Typography;
const Index: React.FC = () => {
    const navigate = useNavigate();

    const { columns, devices } = useTable({
        query: ''
    });

    // 宣告路由
    const [advertisedRoutes, setAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 排除路由
    const [excludedRoutes, setExcludedRoutes] = useState<Array<AdvertisedRoute>>([]);

    const listData = [{
        route: '***************/32',
        routeName: '办公一区路由',
        deviceName: 'desktop-0av24o5',
        deviceDescription: '开发部办公路由',
        confict: true,
        confictData: [{
            route: '**********/12',
            routeName: '',
            deviceName: '',
            deviceDescription: '',
        }]
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'desktop-ghp9nct',
        deviceDescription: '开发部办公路由',
        confict: true,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'flylayer-6448c47479-44v55',
        deviceDescription: '开发部办公路由',
        confict: false,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'flylayer-7b5b5dccbb-9t22v',
        deviceDescription: '开发部办公路由',
        confict: false,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'flylayer-7b5b5dccbb-bkkx8',
        deviceDescription: '开发部办公路由',
        confict: true,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'flylayer-7b5b5dccbb-gm4v5',
        deviceDescription: '开发部办公路由',
        confict: false,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'flylayer-7b5b5dccbb-k5nj8',
        deviceDescription: '开发部办公路由',
        confict: true,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'flylayer-7b5b5dccbb-mxgt7',
        deviceDescription: '开发部办公路由',
        confict: true,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'flylayer-ubuntu',
        deviceDescription: '开发部办公路由',
        confict: false,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'macbook-air-2',
        deviceDescription: '开发部办公路由',
        confict: true,
    }, {
        route: '**********/12',
        routeName: '办公一区路由',
        deviceName: 'meetaidemac-mini  ',
        deviceDescription: '开发部办公路由',
        confict: false,
    }]

    return <>
        <div className='general-page'>
            <Breadcrumb className='mb10' routes={
                [
                    {
                        path: `${BASE_PATH}/srv/networking`,
                        href: `${BASE_PATH}/srv/networking`,
                        name: '网络服务'
                    },
                    {
                        name: '路由表',
                    }
                ]
            }>
            </Breadcrumb>
            <Tabs>
                <TabPane tab="全部路由" itemKey='General'>
                    <div style={{ paddingTop: 10 }}>
                        <List
                            bordered
                            dataSource={listData}

                            renderItem={(item) => (<List.Item
                                className={item.confict ? styles.confict : ''}
                                header={
                                    <div style={{width: 60}}>{ item.confict && <Space><IconInfoCircle className={styles.confictText}/>
                                    <Text type='danger'>冲突</Text></Space>}</div>}
                                main={
                                    <div>
                                        <Title heading={6} type='secondary'>{item.route}</Title>
                                        <Paragraph type='tertiary'>{item.routeName}</Paragraph>
                                    </div>
                                }
                                extra={item.deviceName}
                            >
                            </List.Item>)}
                        ></List>
                    </div>

                </TabPane>
                <TabPane tab="最终生效路由" itemKey='Final'>
                    <div style={{ paddingTop: 10 }}>
                        <List
                            bordered
                            dataSource={listData}
                            renderItem={(item) => (<List.Item
                                main={
                                    <div>
                                        <Title heading={6} type='secondary'>{item.route}</Title>
                                        <Paragraph type='tertiary'>{item.routeName}</Paragraph>
                                    </div>
                                }
                                extra={item.deviceName}
                            >
                            </List.Item>)}
                        ></List>
                    </div>
                </TabPane>
            </Tabs>
            {/* <div className='mb20'>
                <Table
                    rowKey={(record?: any) => record ? record.id + '' : ''}
                    columns={columns}
                    dataSource={devices}
                />
            </div> */}

        </div>
    </>
}

export default Index;
