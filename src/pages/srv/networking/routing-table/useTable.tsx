import { useState, useContext, useEffect } from 'react';
import { listMachines } from '@/services/device';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { render } from 'react-dom';

export type RoutingTableFilter = {
    query: string;
}

const useTable = (initFilter: RoutingTableFilter) => {
    const flynet = useContext(FlynetGeneralContext);

    // 设备列表
    const [devices, setDevices] = useState<Machine[]>([]);
    const columns = [{
        width: 200,
        title: '名称',
        dataIndex: 'name',
    }, {
        title: '路由',
        dataIndex: 'advertisedRoutes',
        render: (field: string[], record: Machine, index: number) => {
            return field.join(', ');
        }
    } ];

    const query = () => {
        listMachines(flynet.id).then((res) => {
            const machines = res.machines.filter((machine) => {
                if (machine.advertisedRoutes && machine.advertisedRoutes.length > 0) {
                    return true;
                } else {
                    return false;
                }
            });

            setDevices(machines);
        }).catch((err) => {
            console.error(err);
        })
    }

    useEffect(() => {
        query();
    }, []);

    return { columns, devices };
}

export default useTable;