import React, { useState, useContext } from "react";
import { Typography, Modal, Input, Space, Row, Col, Button, Collapse, Tag, Notification, Collapsible, TreeSelect, Checkbox, Card, Select, Divider, Spin } from "@douyinfe/semi-ui";
import { IconPlus, IconSearch, IconMinus, IconChevronDown, IconChevronRight } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss';
import { ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { NetworkService, DistributionInfo, DistributeRange } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { ServiceRange } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/service_range_pb';
import { User, UserGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { Machine, MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import UserModalSelector from '@/components/user-modal-selector';
import MachineSelector from '@/components/machine-selector'
import useServicesGroup from '../../group/useServicesGroup';

import { flylayerClient } from "@/services/core";
import useData from "./useData";
import useRangeData from "./useRangeData";
const { Title, Text, Paragraph } = Typography;

interface Props {
    close: () => void;
    success: () => void;
    service: NetworkService;
}

const Index: React.FC<Props> = ({ close, success, service }) => {

    const flynet = useContext(FlynetGeneralContext);

    const { serviceGroupTreeData } = useServicesGroup(ServiceType.ROUTING);

    const { ranges } = useData();

    const [serviceGroups, setServiceGroups] = useState<string[]>(service.serviceGroups.map(g => g.id + ''));

    const {
        userGroups,
        loadingUserGroups,
        machineGroups,
        loadingMachineGroups,
        connectGroups,
        loadingConnectGroups
    } = useRangeData();

    // 选中的范围
    const [selectedRanges, setSelectedRanges] = useState<ServiceRange[]>();

    const [saveLoading, setSaveLoading] = useState(false);

    // 选中的用户组
    const [selectedUserGroups, setSelectedUserGroups] = useState<UserGroup[]>([]);
    // 选中的设备组
    const [selectedMachineGroups, setSelectedMachineGroups] = useState<MachineGroup[]>([]);
    // 用户
    const [users, setUsers] = useState<User[]>([]);
    // 设备
    const [machines, setMachines] = useState<Machine[]>([]);
    const [userModalVisible, setUserModalVisible] = useState(false);
    const [machineModalVisible, setMachineModalVisible] = useState(false);

    const handleSubmit = () => {
        let range = new DistributeRange({
            serviceRanges: selectedRanges,
            userGroups: selectedUserGroups,
            users: users,
            machineGroups: selectedMachineGroups,
            machines: machines
        })
        const distributeInfo = new DistributionInfo({
            ranges: [range],
            excludeRanges: [],
        });
        flylayerClient.distributeNetworkService({
            serviceId: service.id,
            distributionInfo: distributeInfo
        }).then(() => {
            Notification.success({
                title: '发布成功'
            });
            success();
        }).finally(() => {
            setSaveLoading(false);
        });

    };

    const handleSelectRangeChange = (range: ServiceRange, selected: boolean) => {
        let newSelectedRanges = selectedRanges ? [...selectedRanges] : [];
        if (selected) {
            newSelectedRanges.push(range);
        } else {
            newSelectedRanges = newSelectedRanges.filter(r => r.id !== range.id);
        }
        setSelectedRanges(newSelectedRanges);
    }

    const [moreVisible, setMoreVisible] = useState(false);


    return <>
        <Modal
            title={`发布网络服务 ${service.description}(${service.name})`}
            visible={true}
            onOk={handleSubmit}
            onCancel={close}
            width={800}

            okButtonProps={{ loading: saveLoading }}
            closeOnEsc={true}
            maskClosable={false}
        >
            {serviceGroupTreeData.length === 0 ? <div className={styles.loading}><Spin /></div> :
            <>
            <Title className="mb10" heading={6} type="tertiary">服务组</Title>
            <TreeSelect className="mb20" style={{ width: '100%' }}
                multiple
                expandAll
                checkRelation='unRelated'
                treeData={
                    serviceGroupTreeData
                }
                value={ serviceGroups }
                filterTreeNode
                showFilteredOnly
                dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
            ></TreeSelect>
            <Divider className="mb20"/>
            <Title className="mb10" heading={6} type="tertiary">连接器组</Title>
            <Select className="mb20"
                style={{ width: '100%' }}
                multiple
                dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                optionList={connectGroups.map((g) => {
                    return {
                        label: `${g.name}(${g.alias}) - ${g.description}`,
                        value: g.name
                    }
                })}
            ></Select>
            <Divider className="mb20"/>
            <Title className="mb10" heading={6} type="tertiary">可见范围</Title>
            <Card className="mb20">
                <div>
                    <Row gutter={20}>
                        <Col span={12} style={{ borderRight: '1px solid #f0f0f0' }}>
                            <Input placeholder="请输入关键字搜索" className="mb20" suffix={<IconSearch />} />

                            {ranges.map(range => {
                                let selected = false;
                                selectedRanges?.forEach(r => {
                                    if (r.id === range.id) {
                                        selected = true;
                                    }
                                });
                                if (selected) {
                                    return <Checkbox className="mb10" value={range.id} checked onChange={() => handleSelectRangeChange(range, false)}>{range.alias}({range.name})</Checkbox>
                                } else {
                                    return <Checkbox className="mb10" value={range.id} onChange={() => handleSelectRangeChange(range, true)}>{range.alias}({range.name})</Checkbox>
                                }
                            })}

                        </Col>
                        <Col span={12}>
                            <Row>
                                <Col span={16}>
                                    <Paragraph className="mb20" style={{ lineHeight: '32px' }}>已选择发布范围:{selectedRanges?.length}个</Paragraph>
                                </Col>
                                <Col span={8} className="btn-right-col">
                                    <Button onClick={() => {
                                        setSelectedRanges([]);
                                    }}>清空</Button>
                                </Col>
                            </Row>
                            {selectedRanges?.map(range => {
                                return <Row className="mb10">
                                    <Col span={20}>
                                        <Text>{range.alias}({range.name})</Text>
                                    </Col>
                                    <Col span={4} className="btn-right-col">
                                        <Button size="small" icon={<IconMinus />} onClick={() => handleSelectRangeChange(range, false)}></Button>
                                    </Col>
                                </Row>
                            })}
                        </Col>
                    </Row>
                </div>
            </Card>
            <Button className="mb10" onClick={() => setMoreVisible(!moreVisible)} iconPosition="right" icon={moreVisible ? <IconChevronDown /> : <IconChevronRight />}>自定义</Button>
            <Collapsible isOpen={moreVisible}>
                <Collapse>
                    <Collapse.Panel header="用户组" itemKey="2">
                        <Card>
                            <div>
                                <Row gutter={20}>
                                    <Col span={12} style={{ borderRight: '1px solid #f0f0f0' }}>
                                        <Input placeholder="请输入关键字搜索" className="mb20" suffix={<IconSearch />} />
                                        {userGroups.map(group => {
                                            return <Checkbox className="mb10" value={group.id} onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedUserGroups([...selectedUserGroups, group]);
                                                } else {
                                                    setSelectedUserGroups(selectedUserGroups.filter(g => g.id !== group.id));
                                                }
                                            }}>{group.alias}({group.name})</Checkbox>
                                        })}
                                    </Col>
                                    <Col span={12}>
                                        <Row>
                                            <Col span={16}>
                                                <Paragraph className="mb20" style={{ lineHeight: '32px' }}>已选择用户组:{selectedUserGroups.length}个</Paragraph>
                                            </Col>
                                            <Col span={8} className="btn-right-col">
                                                <Button onClick={() => setSelectedUserGroups([])}>清空</Button>
                                            </Col>
                                        </Row>
                                        {selectedUserGroups.map(group => {
                                            return <Row className="mb10">
                                                <Col span={20}>
                                                    <Text>{group.alias}({group.name})</Text>
                                                </Col>
                                                <Col span={4} className="btn-right-col">
                                                    <Button size="small" icon={<IconMinus />} onClick={() => setSelectedUserGroups(selectedUserGroups.filter(g => g.id !== group.id))}></Button>
                                                </Col>
                                            </Row>
                                        })}
                                    </Col>
                                </Row>
                            </div>
                        </Card>
                    </Collapse.Panel>
                    <Collapse.Panel header="用户" itemKey="3">
                        {/* <Card>
                            <div style={{ minHeight: 100, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>

                                <Button size="large" onClick={()=> setUserModalVisible(true)} icon={<IconPlusCircle />} type="primary"></Button>
                            </div>
                        </Card> */}
                        <div style={{ paddingTop: 20, paddingBottom: 20 }}>
                            <Space className="mt20">
                                {users.map((user) => {
                                    return <Tag key={user.id + ''} closable size='large' onClose={() => {
                                        setUsers(users.filter(u => u.id !== user.id))
                                    }}>{user.displayName}</Tag>
                                })}
                                <Button size='small' onClick={() => setUserModalVisible(true)} icon={<IconPlus />} ></Button>
                            </Space>
                        </div>
                    </Collapse.Panel>

                    <Collapse.Panel header="设备组" itemKey="4">
                        <Card>
                            <div>
                                <Row gutter={20}>
                                    <Col span={12} style={{ borderRight: '1px solid #f0f0f0' }}>
                                        <Input placeholder="请输入关键字搜索" className="mb20" suffix={<IconSearch />} />
                                        {machineGroups.map(group => {
                                            return <Checkbox className="mb10" value={group.id} onChange={(e) => {
                                                if (e.target.checked) {
                                                    setSelectedMachineGroups([...selectedMachineGroups, group]);
                                                } else {
                                                    setSelectedMachineGroups(selectedMachineGroups.filter(g => g.id !== group.id));
                                                }
                                            }}>{group.alias}({group.name})</Checkbox>
                                        })}
                                    </Col>
                                    <Col span={12}>
                                        <Row>
                                            <Col span={16}>
                                                <Paragraph className="mb20" style={{ lineHeight: '32px' }}>已选择用户组:2个</Paragraph>
                                            </Col>
                                            <Col span={8} className="btn-right-col">
                                                <Button onClick={() => setSelectedMachineGroups([])}>清空</Button>
                                            </Col>
                                        </Row>
                                        {selectedMachineGroups.map(group => {
                                            return <Row className="mb10">
                                                <Col span={20}>
                                                    <Text>{group.alias}({group.name})</Text>
                                                </Col>
                                                <Col span={4} className="btn-right-col">
                                                    <Button size="small" icon={<IconMinus />} onClick={() => setSelectedMachineGroups(selectedMachineGroups.filter(g => g.id !== group.id))}></Button>
                                                </Col>
                                            </Row>
                                        })}
                                    </Col>
                                </Row>
                            </div>
                        </Card>
                    </Collapse.Panel>
                    <Collapse.Panel header="设备" itemKey="3">
                        {/* <Card>
                            <div style={{ minHeight: 100, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                <Button size="large" icon={<IconPlusCircle />} type="primary" onClick={() => setMachineModalVisible(true)}></Button>
                            </div>
                        </Card> */}
                        <div style={{ paddingTop: 20, paddingBottom: 20 }}>
                            <Space className="mt20">
                                {machines.map((machine) => {
                                    return <Tag key={machine.id + ''} closable size='large' onClose={() => {
                                        setMachines(machines.filter(m => m.id !== machine.id))
                                    }}>{machine.givenName ? machine.givenName : machine.name}</Tag>
                                })}
                                <Button size='small' onClick={() => setMachineModalVisible(true)} icon={<IconPlus />} ></Button>
                            </Space>
                        </div>
                    </Collapse.Panel>
                </Collapse>
            </Collapsible>
            </>
            }

        </Modal>
        {
            userModalVisible && <UserModalSelector
                multi
                close={() => setUserModalVisible(false)}
                onChange={(users) => {
                    setUsers(users as User[]);
                    setUserModalVisible(false);
                }}
                value={users}
            />
        }
        {
            machineModalVisible && <MachineSelector
                multi
                close={() => setMachineModalVisible(false)}
                onChange={(machines) => {
                    setMachines(machines as Machine[]);
                    setMachineModalVisible(false);
                }}
                value={machines}
            />
        }
    </>
};

export default Index;
