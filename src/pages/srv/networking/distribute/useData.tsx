import { useEffect, useState, useContext } from 'react';
import { flylayerClient } from '@/services/core';
import { ServiceRange } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/service_range_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';


const useData = () => {

    const flynet = useContext(FlynetGeneralContext);
    
    // 范围列表
    const [ranges, setRanges] = useState<ServiceRange[]>([]);

    const [rangesLoading, setRangesLoading] = useState(true);

    const queryRanges = () => {
        setRangesLoading(true);
        flylayerClient.listServiceRanges({
            flynetId: flynet.id
        }).then(res => {
            setRanges(res.serviceRanges);
        }).finally(() => {
            setRangesLoading(false);
        });
    }

    useEffect(() => {
        queryRanges();
    }, []);

    return {
        ranges,
        rangesLoading
    }
}

export default useData;