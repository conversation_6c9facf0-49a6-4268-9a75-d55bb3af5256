import { useEffect, useState, useContext } from 'react';
import { User, UserGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ConnectorGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";

const useRangeData = () => {

    const flynetGeneral = useContext(FlynetGeneralContext);

    // 用户组是否正在加载中
    const [loadingUserGroups, setLoadingUserGroups] = useState(true);
    // 用户组列表
    const [userGroups, setUserGroups] = useState<UserGroup[]>([]);

    // 设备组是否正在加载中
    const [loadingMachineGroups, setLoadingMachineGroups] = useState(true);
    // 设备组列表
    const [machineGroups, setMachineGroups] = useState<MachineGroup[]>([]);
    // 连接器组列表
    const [connectGroups, setConnectGroups] = useState<ConnectorGroup[]>([]);
    // 连接器组是否正在加载中
    const [loadingConnectGroups, setLoadingConnectGroups] = useState(true);
    

    // 加载用户组数据
    const queryUserGroups = () => {
        setLoadingUserGroups(true)

        flylayerClient.listUserGroups({
            flynetId: flynetGeneral.id
        }).then((res) => {
            setUserGroups(res.groups)
        }).finally(() => {
            setLoadingUserGroups(false);
        })
    }

    // 加载设备组数据
    const queryMachineGroups = () => {
        setLoadingMachineGroups(true)

        flylayerClient.listMachineGroups({
            flynetId: flynetGeneral.id
        }).then((res) => {
            setMachineGroups(res.groups)
        }).finally(() => {
            setLoadingMachineGroups(false);
        })
    }

    // 加载连接器组数据
    const queryConnectGroups = () => {
        setLoadingConnectGroups(true);

        flylayerClient.listConnectorGroups({
            flynetId: flynetGeneral.id
        }).then((res) => {
            setConnectGroups(res.connectorGroups)
        }).finally(() => {
            setLoadingConnectGroups(false);
        })
    }

    useEffect(() => {
        queryUserGroups();
        queryMachineGroups();
        queryConnectGroups();
    }, []);

    return {
        userGroups,
        loadingUserGroups,
        machineGroups,
        loadingMachineGroups,
        connectGroups,
        loadingConnectGroups
    }

}

export default useRangeData;