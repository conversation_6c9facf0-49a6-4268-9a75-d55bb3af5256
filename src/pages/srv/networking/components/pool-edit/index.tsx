import React, { useState, useContext } from "react";

import { Typo<PERSON>, Modal, Form, Row, Col, Button, Notification, Divider, Popover, Radio, ArrayField } from "@douyinfe/semi-ui";
import { IconHelpCircle, IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import FormIpRangeInput from '@/components/ip-range-input';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from "@/services/core";
import { isValidIPOrIpRangeOrCIDRByType } from "@/utils/validators";
import { RoutingPool, RoutingPoolType, RoutingPoolNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

const { Title, Text } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: RoutingPool,
}

const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        alias: string;
        description: string;
        disabled: number;
        nodes: Array<{
            id: bigint,
            name: string,
            ip: string,
            description: string,
            mask: string,
            disabled: number
        }>;
        type: RoutingPoolType;
    }>>();

    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const handleSubmit = async () => {
        const result = await formApi?.validate();
        const values = formApi?.getValues();
        if (!values) {
            return;
        }
        const name = values.name.trim();
        const alias = values.alias.trim();
        const description = values.description ? values.description.trim() : '';
        const type = values.type;
        const disabled = values.disabled;
        const nodes: RoutingPoolNode[] = values.nodes.map((node, index) => {
            return new RoutingPoolNode({
                id: node.id,
                name: node.name.trim(),
                description: node.description ? node.description.trim() : '',
                ip: node.ip.trim(),
                disabled: node.disabled ? true : false,
                type: type,
                rank: index,
            });
        });
        let routingPool: RoutingPool = new RoutingPool({
            id: props.record.id,
            name,
            description,
            alias,
            type,
            disabled: disabled ? true : false,
            nodes
        });

        setSaveLoading(true);
        flylayerClient.updateRoutingPool({
            flynetId: flynet.id,
            routingPool: routingPool
        }).then(() => {
            Notification.success({ title: '保存成功' });
            props.success && props.success();
        }).catch((err) => {
            Notification.error({ title: '保存失败', content: err.message });
        }).finally(() => {
            setSaveLoading(false);
        })
    }

    return <><Modal
        title="编辑网络规划"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={760}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Form getFormApi={setFormApi}
            initValues={{
                name: props.record.name,
                alias: props.record.alias,
                description: props.record.description,
                disabled: props.record.disabled ? 1 : 0,
                nodes: props.record.nodes.map(node => {
                    return {
                        id: node.id,
                        name: node.name,
                        ip: node.ip,
                        description: node.description,
                        disabled: node.disabled ? 1 : 0
                    }
                }),
                type: props.record.type,
            }}
        >
            {({ values }) => (<>
                <Row gutter={24}>
                    <Col span={8}>
                        <Form.Input field='alias' label='名称' trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={8}><Form.Input field='name'
                        label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                        readonly
                        required />
                    </Col>
                    <Col span={8}>
                        <Form.RadioGroup field="disabled" label="状态">
                            <Form.Radio value={0}>
                                <Text>启用</Text>
                            </Form.Radio>
                            <Form.Radio value={1}>
                                <Text>禁用</Text>
                            </Form.Radio>
                        </Form.RadioGroup>
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>

                        <Form.Input field="description" label="备注" />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Form.RadioGroup field="type" label="类型">
                            <Radio value={RoutingPoolType.VIRTUAL_IP}>
                                <Text>零信任组网</Text>
                            </Radio>
                            <Radio value={RoutingPoolType.LOCAL_IP}>
                                <Text>本地网络</Text>
                            </Radio>
                            <Radio value={RoutingPoolType.PUBLIC_IP}>
                                <Text>公共网络</Text>
                            </Radio>
                        </Form.RadioGroup>
                    </Col>
                </Row>
                <Divider className="mb10" />
                <Title heading={6} className="mb10">路由节点</Title>
                <ArrayField field="nodes" >
                    {({ add, arrayFields }) => <>
                        <Row className="tableTitle">

                            <Col span={12}>IP或IP段&nbsp;<Popover zIndex={3000} content={<div className='p10'>
                                支持以下格式：
                                <div>单个IP：**********</div>
                                <div>CIDR格式：**********/24</div>
                                <div>IP范围：**********-************或**********-255</div>
                            </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></Col>
                            <Col span={4}>物理位置</Col>
                            <Col span={5}>备注</Col>
                            <Col span={3} className="btn-right-col">
                                <Button icon={<IconPlusCircle />} onClick={add}></Button>
                            </Col>
                        </Row>
                        {arrayFields.map(({ field, remove }, i) => {
                            return <Row key={i} className='tableBody'>
                                <Col span={12}>
                                    <FormIpRangeInput
                                        validate={
                                            (value: string) => {
                                                if (!value) {
                                                    return 'IP不能为空';
                                                }
                                                return isValidIPOrIpRangeOrCIDRByType(value, values.type);
                                            }
                                        } field={`${field}.ip`} noLabel
                                    />
                                </Col>
                                <Col span={4}>
                                    <Form.Input
                                        validate={value => {
                                            if (!value) {
                                                return '物理位置不能为空';
                                            }
                                            return '';
                                        }}
                                        placeholder={'请输入物理位置'}
                                        field={`${field}.description`} noLabel />
                                </Col>
                                <Col span={5}>
                                    <Form.Input
                                        validate={value => {
                                            if (!value) {
                                                return '备注不能为空';
                                            }
                                            return '';
                                        }}
                                        placeholder={'请输入备注'}
                                        field={`${field}.name`} noLabel />
                                </Col>
                                <Col span={3} className="btn-right-col" style={{ paddingTop: 12 }}>
                                    <Button type="danger" onClick={remove} icon={<IconMinusCircle />}></Button>
                                </Col>
                            </Row>
                        })}
                    </>}
                </ArrayField>

            </>)}
        </Form>
    </Modal></>
}

export default Index;