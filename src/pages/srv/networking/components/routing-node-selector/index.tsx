import React, { useState, useContext, useEffect } from "react";

import { Typo<PERSON>, Modal, Form, Row, Col, Button, Notification, Divider, Popover, Space, Input, ArrayField, Skeleton, Tag, Checkbox } from "@douyinfe/semi-ui";

import { IconMinusCircle, IconEdit } from '@douyinfe/semi-icons';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { RoutingPool, RoutingPoolType, ServiceNodeType, RoutingPoolNode, Service, ServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import New from '../pool-new';
import Edit from '../pool-edit';
import Del from '../pool-del';
import { flylayerClient } from "@/services/core";
const { Title, Text, Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: (nodes: RoutingPoolNode[]) => void,
}

const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);


    const [routingPoolsLoading, setRoutingPoolsLoading] = useState(false);
    const [routingPools, setRoutingPools] = useState<RoutingPool[]>([]);

    const [createVisible, setCreateVisible] = useState(false);
    const [editVisible, setEditVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);

    const [selectedRoutingPool, setSelectedRoutingPool] = useState<RoutingPool>();

    const query = () => {
        setRoutingPoolsLoading(true);
        flylayerClient.listRoutingPools({
            flynetId: flynet.id
        }).then((res) => {
            setRoutingPools(res.routingPools);
        }).catch((err) => {
            console.error(err);
        }).finally(() => {
            setRoutingPoolsLoading(false);
        })
    }

    const [routingNodes, setRoutingNodes] = useState<RoutingPoolNode[]>([]);


    useEffect(() => {
        query();
    }, []);

    const handleSubmit = async () => {
        
        props.success?.(routingNodes);
        props.close();
    }

    return <>
        <Modal
            title="选择网络规划项"
            visible={true}
            onOk={handleSubmit}
            onCancel={props.close}
            width={760}

            closeOnEsc={true}
            maskClosable={false}
        >
            <Skeleton loading={routingPoolsLoading} placeholder={<Skeleton.Image style={{ width: '100%', height: 500 }}></Skeleton.Image>}>
                <div style={{ minHeight: 500 }}>
                    <Row className="mb20">
                        <Col span={12}><Paragraph type='tertiary'>从下列网络规划项中选择路由节点</Paragraph></Col>
                        <Col span={12} className="btn-right-col">
                            <Button onClick={() => setCreateVisible(true)}>新建网络规划项</Button>
                        </Col>
                    </Row>


                    {
                        routingPools.map((pool, index) => {
                            return <div key={index} className="mb40">
                                <Row>
                                    <Col span={18}>
                                        <Title heading={5}>{pool.alias} <Text type='tertiary'>{pool.name}</Text></Title>
                                    </Col>
                                    <Col span={6} className="btn-right-col">
                                        <Space>
                                            <Button size="small" onClick={() => {
                                                setSelectedRoutingPool(pool);
                                                setEditVisible(true);
                                            }} icon={<IconEdit></IconEdit>}></Button>
                                            <Button size="small" type="danger" onClick={() => {
                                                setSelectedRoutingPool(pool);
                                                setDelVisible(true);
                                            }} icon={<IconMinusCircle></IconMinusCircle>}></Button>
                                        </Space>
                                    </Col>
                                </Row>

                                <Divider className="mb10"></Divider>
                                <Space>
                                    {
                                    pool.nodes.map((node, index) => {
                                        return <Space key={index} style={{marginRight:20}}>
                                            <Checkbox value={node.ip} 
                                                onChange={(e) => {
                                                    const checked = e.target.checked;
                                                    if (checked) {
                                                        setRoutingNodes([...routingNodes, node]);
                                                    } else {
                                                        setRoutingNodes(routingNodes.filter(item => (item.ip !== node.ip)));
                                                    }

                                                }}
                                            >{node.name}&nbsp;<Text type="tertiary">{node.ip}</Text></Checkbox>
                                        </Space>
                                    })
                                }
                                </Space>

                            </div>
                        })
                    }
                </div>
            </Skeleton>
        </Modal>
        {
            createVisible && <New close={() => setCreateVisible(false)} success={() => query()}></New>
        }
        {
            editVisible && selectedRoutingPool && <Edit close={() => setEditVisible(false)} success={() => query()} record={selectedRoutingPool}></Edit>
        }
        {
            delVisible && selectedRoutingPool && <Del close={() => setDelVisible(false)} success={() => query()} record={selectedRoutingPool}></Del>
        }
    </>
}

export default Index;