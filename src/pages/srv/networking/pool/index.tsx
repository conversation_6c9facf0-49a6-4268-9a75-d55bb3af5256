import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Layout, Input, Select } from '@douyinfe/semi-ui';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import New from '../components/pool-new';
import Edit from '../components/pool-edit';
import Del from '../components/pool-del';
import styles from './index.module.scss'
import useTable, { RoutingPoolFilter } from './useTable';
import { getQueryParam } from '@/utils/query';

import { RoutingPool, RoutingPoolType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import TableEmpty from '@/components/table-empty';
import qs from 'query-string';

const { Title } = Typography;

// 根据URL参数设置过滤参数
const getInitFilter = (location: Location): RoutingPoolFilter => {
    const query: string = getQueryParam('query', location) as string;
    const enabled: string = getQueryParam('enabled', location) as string;

    return {
        query: query || '',
        enabled: enabled == 'enable' || enabled == 'disable' ? enabled : '',
    };
}

const { Sider, Content } = Layout;

const Index: React.FC = () => {
    const navigate = useNavigate();
    const [createVisible, setCreateVisible] = useState(false);

    const initFilter = getInitFilter(useLocation());

    const {
        loading,
        routingPools,
        total,
        columns,
        filterParam,
        setFilterParam,
        reload,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedRoutingPool,
        handleFilterChange
    } = useTable(initFilter);

    const doNavigate = (params: RoutingPoolFilter) => {
        let query = '';
        if (params.query || params.enabled) {
            query = qs.stringify(params, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/srv/networking/pool?${query}`);
        } else {
            navigate(`${BASE_PATH}/srv/networking/pool`);
        }
    }

    const handleQueryChange = (value: string) => {
        handleFilterChange({ ...filterParam, query: value })
        doNavigate({ ...filterParam, query: value });
    }

    const handleEnableChange = (value: any) => {
        handleFilterChange({ ...filterParam, enabled: value })
        doNavigate({ ...filterParam, enabled: value });
    }

    return <><div className='general-page'>
        <Breadcrumb routes={
            [
                {
                    path: `${BASE_PATH}/srv/networking`,
                    href: `${BASE_PATH}/srv/networking`,
                    name: '网络服务'
                },
                {
                    name: '网络规划',
                }
            ]
        }>
        </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>网络规划</Title>
            </Col>
            <Col span={4}>
                <div className='btn-right-col'>
                    <Space><Button type='primary' onClick={() => setCreateVisible(true)}>创建项目</Button></Space>
                </div>
            </Col>
        </Row>
        <Layout className='mb20 search-bar'>
            <Content className='pr10'>
                <Input
                    value={filterParam.query}
                    onChange={handleQueryChange}
                    style={{ width: '100%' }}
                    placeholder='根据名称、备注、节点名、IP等搜索'
                />
            </Content>
            <Sider>
                <Space>
                    <Select style={{ width: 200 }}
                        optionList={[
                            { value: '', label: '全部' },
                            { value: 'enable', label: '启用' },
                            { value: 'disable', label: '禁用' }
                        ]}
                        insetLabel="状态"
                        onChange={handleEnableChange}
                        value={filterParam.enabled}></Select>
                </Space>
            </Sider>
        </Layout>

        <Table
            rowKey={(record?: RoutingPool) => record ? record.id + '' : ''}
            columns={columns}
            dataSource={routingPools}
            loading={loading}
            pagination={false}
            empty={<TableEmpty loading={loading}></TableEmpty>}
        />
    </div>
        {createVisible && <New
            close={() => setCreateVisible(false)}
            success={() => {
                setCreateVisible(false);
                reload();
            }}
        />}

        {editVisible && selectedRoutingPool && <Edit
            close={() => setEditVisible(false)}
            success={() => {
                reload();
                setEditVisible(false);
            }}
            record={selectedRoutingPool}
        />
        }
        {delVisible && selectedRoutingPool && <Del
            close={() => setDelVisible(false)}
            success={() => {
                reload();
                setDelVisible(false);
            }}
            record={selectedRoutingPool}
        />
        }

    </>
}

export default Index
