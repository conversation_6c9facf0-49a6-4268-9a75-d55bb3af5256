import { useState, useContext, useEffect } from 'react';

import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

import { Typography, Dropdown, Button, Divider, Popover, Row, Col, Tag, Space } from '@douyinfe/semi-ui';
import { RoutingPool, RoutingPoolType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';
import { caseInsensitiveIncludes } from '@/utils/common';

const { Title, Paragraph, Text } = Typography;

export type RoutingPoolFilter = {
    query: string;
    enabled: 'enable' | 'disable' | '';
}

const useTable = (initFilter: RoutingPoolFilter) => {
    const navigate = useNavigate();
    const flynet = useContext(FlynetGeneralContext);

    // 是否正在加载中
    const [loading, setLoading] = useState(false);
    const [filterParam, setFilterParam] = useState<RoutingPoolFilter>(initFilter);
    const [total, setTotal] = useState<number>(0);

    const [routingPools, setRoutingPools] = useState<RoutingPool[]>([]);
    const [allRoutingPools, setAllRoutingPools] = useState<RoutingPool[]>([]);

    const [editVisible, setEditVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);

    const [selectedRoutingPool, setSelectedRoutingPool] = useState<RoutingPool>();

    const query = () => {
        setLoading(true);

        flylayerClient.listRoutingPools({
            flynetId: flynet.id,
        }).then((res) => {
            setAllRoutingPools(res.routingPools);
            setRoutingPools(doFilter(res.routingPools, filterParam));

        }).catch((err) => {
            console.error(err);
        }).finally(() => {
            setLoading(false);
        })

    }


    // 重新加载数据
    const reload = () => {
        query();
    }

    useEffect(() => {
        query();
    }, []);

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            render: (field: string, record: RoutingPool, index: number) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                        <div>
                            <Title heading={6}>
                                <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/policies/entrance/baseline-check/detail/${record.id}`), 10)}>
                                    {record.alias}
                                </a>
                                {record.description &&
                                    <Popover content={<div className='p10'>{record.description}</div>}>
                                        <IconArticle style={{
                                            fontSize: 14,
                                            marginLeft: '4px',
                                            color: '#999'
                                        }} />
                                    </Popover>}
                            </Title>
                            <Paragraph size='small'>{record.name}</Paragraph>
                        </div>
                    </div>
                </>
            }
        }, {
            title: '网络类型',
            dataIndex: 'type',
            render: (field: string, record: RoutingPool) => {
                switch (record.type) {
                    case RoutingPoolType.PUBLIC_IP:
                        return <Tag color="green">公共网络</Tag>
                    case RoutingPoolType.VIRTUAL_IP:
                        return <Tag color="blue">零信任组网</Tag>
                    case RoutingPoolType.LOCAL_IP:
                        return <Tag color="orange">本地网络</Tag>
                }
                return <></>
            }
        }, {
            title: '节点',
            dataIndex: 'nodes',
            key: 'nodes',
            render: (field: string, record: RoutingPool, index: number) => {
                return <>
                    <Row className='tableTitle'>
                        <Col span={6}>IP或IP段</Col>
                        <Col span={6}>物理位置</Col>
                        <Col span={6}>备注</Col>
                        {/* <Col span={6}>状态</Col> */}
                    </Row>
                    {record.nodes.map((node, index) => {
                        return <Row key={index} className='tableBody'>
                            <Col span={6}>{node.ip}</Col>
                            <Col span={6}>{node.description}</Col>
                            <Col span={6}>{node.name}</Col>
                            {/* <Col span={6}>{node.disabled ? <Tag color='red'>禁用</Tag> : <Tag color='green'>启用</Tag>}</Col> */}
                        </Row>
                    })}

                </>
            }

        }, {
            title: '状态',
            dataIndex: 'disabled',
            key: 'disabled',
            width: 100,
            render: (fieldd: string, acl: RoutingPool, index: number) => {
                return <>
                    {acl.disabled ? <Tag color='red'>禁用</Tag> : <Tag color='green'>启用</Tag>}
                </>;
            }
        }, {
            title: '',
            dataIndex: 'operation',
            key: 'operation',
            width: 100,
            render: (text: string, record: RoutingPool) => {
                return <><div className='table-last-col' onClick={(e) => {
                    e.nativeEvent.preventDefault();
                    e.nativeEvent.stopPropagation();
                }}><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.preventDefault();
                                    e.nativeEvent.stopPropagation();
                                    setEditVisible(true);
                                    setSelectedRoutingPool(record);
                                }}
                            >编辑网络规划项</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.stopPropagation();
                                    e.nativeEvent.stopPropagation();
                                    setDelVisible(true);
                                    setSelectedRoutingPool(record);
                                }}
                                type='danger'>删除网络规划项</Dropdown.Item>

                        </Dropdown.Menu>}><Button onClick={(e) => { e.nativeEvent.stopPropagation() }}><IconMore className='align-v-center' /></Button>
                    </Dropdown>
                </div>
                </>;
            }
        }
    ];


    // 过滤数据
    const doFilter = (src: Array<RoutingPool>, routingPoolFilter: RoutingPoolFilter) => {

        if (!src || src.length == 0) {
            setTotal(0);
            return src;
        }

        if ((!routingPoolFilter.query || routingPoolFilter.query == '') && !routingPoolFilter.enabled) {
            setTotal(src.length);
            return src;
        }

        const filteredList = src.filter((item) => {
            let isPassName = false;
            let isPassEnabled = true;

            if (caseInsensitiveIncludes(item.name, routingPoolFilter.query) || caseInsensitiveIncludes(item.description, routingPoolFilter.query) || caseInsensitiveIncludes(item.alias, routingPoolFilter.query)) {
                isPassName = true;
            }
            


            if (routingPoolFilter.enabled == 'enable') {
                if (item.disabled) {
                    isPassEnabled = false;
                } else {
                    isPassEnabled = true;
                }
            } else if (routingPoolFilter.enabled == 'disable') {
                if (item.disabled) {
                    isPassEnabled = true;
                } else {
                    isPassEnabled = false;
                }
            } else {
                isPassEnabled = true
            }


            if (isPassName && isPassEnabled) {
                return true;
            } else {
                return false;
            }
        })
        setTotal(filteredList.length);
        return filteredList;
    }

    const handleFilterChange = (newFilter: RoutingPoolFilter) => {
        setFilterParam(newFilter)
        setRoutingPools(doFilter(allRoutingPools, newFilter));
    }


    return {
        loading,
        routingPools,
        total,
        columns,
        filterParam,
        setFilterParam,
        reload,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedRoutingPool,
        setSelectedRoutingPool,
        handleFilterChange
    }
}

export default useTable;