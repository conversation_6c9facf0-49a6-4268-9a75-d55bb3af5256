import { FC, useState, useContext } from 'react'
import { Typography, Modal, List, Notification, TabPane, Input } from '@douyinfe/semi-ui';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { RoutingPool, RoutingPoolType, Service, ServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';


import styles from './index.module.scss';

interface Props {
    close: () => void,
    success?: () => void,
    device: Machine,
    routingNode: NetworkServiceNode
}
const { Paragraph, Title } = Typography;


const Index: FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');

    const handleSubmit = () => {
        setLoading(true);
        flylayerClient.deleteRoutingConnection({
            flynetId: flynet.id,
            serviceNodeId: props.routingNode.id,
            machineIds: [props.device.id]
        }).then(() => {
            Notification.success({
                title: '删除成功',
                content: `路由节点${props.device.name}已从连接器中删除`
            })
            props.success && props.success();
        }).catch((err) => {
            Notification.error({
                title: '删除失败',
                content: err.message
            })
        }).finally(() => {
            setLoading(false);
        });
    }
    return <>
        <Modal
            width={600}
            title={`从连接器中删除路由节点${props.device.name}`}
            visible={true}
            onOk={handleSubmit}

            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                disabled: props.device.name != confirmVal,
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >

            <Paragraph className='mb20'> 删除路由节点 <b>{props.device.name}</b> 后，该网络服务将不再生效。
            </Paragraph>

            <Paragraph className='mb20'> 输入 <b>{props.device.name}</b> 以确认删除
            </Paragraph>
            <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
        </Modal></>
}

export default Index;