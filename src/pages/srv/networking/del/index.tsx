import { FC, useState, useContext } from 'react'
import { Typography, Modal, List, Notification, TabPane, Input } from '@douyinfe/semi-ui';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';

import { flylayerClient } from '@/services/core';
import { ACLPolicy, AclOrigin, SSHRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import styles from './index.module.scss';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { RoutingPool, RoutingPoolType, Service, ServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: NetworkService,
}

const Index: FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');


    const handleSubmit = () => {
        
        
        setLoading(true);

        // flylayerClient.setACLPolicy({
        //     flynetId: flynet.id,
        //     policy: policy
        // }).then(() => {
        //     props.success && props.success(policy);
        //     Notification.success({ content: '删除网络服务成功', position: "bottomRight" })
        // }).catch((err) => {
        //     console.error(err);
        //     Notification.error({ content: '删除网络服务失败, 请稍后重试', position: "bottomRight" })
        // }).finally(() => {
        //     setLoading(false);
        // })


    }

    return <>
        <Modal
            width={600}
            title={`删除网络服务${props.record.name}`}
            visible={true}
            onOk={handleSubmit}

            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                disabled: props.record.name != confirmVal,
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'> 删除网络服务 <b>{props.record.name}</b> 后，该网络服务将不再生效。
            </Paragraph>

            <Paragraph className='mb20'> 输入 <b>{props.record.name}</b> 以确认删除
            </Paragraph>
            <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
