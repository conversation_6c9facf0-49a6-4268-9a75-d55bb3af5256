import React, { useState, useContext, useEffect } from "react";

import { Typo<PERSON>, Modal, Form, Row, Col, Button, Notification, Divider, Popover, ArrayField, Spin } from "@douyinfe/semi-ui";
import { IconHelpCircle, IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { ConnectorGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";

import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { ServiceStatus, ServiceType, ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';


import { sanitizeLabel } from '@/utils/common';
import pinyin from 'tiny-pinyin';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from "@/services/core";
import RoutingNodeSelector from '../components/routing-node-selector';
import { isValidIPOrIpRangeOrCIDR } from "@/utils/validators";
import useServicesGroup from '../../group/useServicesGroup';

const { Title, Text } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
}

const Index: React.FC<Props> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        description: string;
        stataus: ServiceStatus;
        serviceGroups: Array<string>;
        connectorGroups: Array<string>;
        nodes: Array<{
            id: bigint,
            name: string,
            remark: string,
            description: string,
            ip: string,
            rank: number
        }>;
    }>>();

    const [connectGroups, setConnectGroups] = useState<ConnectorGroup[]>([]);

    useEffect(() => {
        flylayerClient.listConnectorGroups({
            flynetId: flynetGeneral.id
        }).then((res) => {
            setConnectGroups(res.connectorGroups);
        }).catch((err) => {
            Notification.error({
                title: '获取连接器组失败',
                content: err.message
            });
        });
    }, []);

    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const { serviceGroupTreeData } = useServicesGroup(ServiceType.ROUTING);

    const [nodeSelectorVisible, setNodeSelectorVisible] = useState(false);

    const handleSubmit = async () => {
        await formApi?.validate();
        const values = formApi?.getValues();
        if (!values) {
            return;
        }
        let name = values.name ? values.name.trim() : '';
        let description = values.description ? values.description.trim() : '';
        let status = values.stataus;
        const connectorGroups = values.connectorGroups.map((name: string) => {
            let connectorGroup = new ConnectorGroup({
                name: name
            });

            return connectorGroup;
        });

        let serviceGroups: Array<ServiceGroup> = []
        if (values.serviceGroups) {
            serviceGroups = values.serviceGroups.map((id: string) => {
                let serviceGroup = new ServiceGroup({ id: BigInt(id) });
                return serviceGroup;
            });
        }

        let nodes: Array<NetworkServiceNode> = values.nodes.map((node: {
            id: bigint,
            name: string,
            remark: string,
            description: string,
            ip: string,
            rank: number
        }, index) => {
            let serviceNode = new NetworkServiceNode({
                name: node.name,
                remark: node.remark,
                rank: index,
                ip: node.ip,
                description: node.description
            })
            return serviceNode;
        });

        setSaveLoading(true);

        let service = new NetworkService({
            name: name,
            description: description,
            status: ServiceStatus.INITIAL,
            serviceGroups: serviceGroups,
            connectorGroups: connectorGroups,
            nodes: nodes
        });


        flylayerClient.createNetworkService({
            flynetId: flynetGeneral.id,
            service: service
        }).then((res) => {
            Notification.success({
                title: '创建成功',
                content: '网络服务创建成功'
            });
            props.success?.();
            props.close();
        }).catch((err) => {
            Notification.error({
                title: '创建失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        });

    }

    return <><Modal
        title="添加网络服务"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={760}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        {serviceGroupTreeData.length === 0 ? <div className={styles.loading}><Spin /></div> :
            <Form 
            getFormApi={setFormApi}
            initValues={{
                serviceGroups:  [serviceGroupTreeData[0].key]
            }}
            >
                {({ values }) => (<>
                    <Row gutter={24}>
                        <Col span={8}><Form.Input field='name'
                            label={<>名称 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '名称不能为空';
                                }
                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '名称不能以-开头'
                                }
                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "名称只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            required />
                        </Col>
                        <Col span={16}>
                            <Form.Input field="description" label="备注" />
                        </Col>
                        
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Form.TreeSelect style={{ width: '100%' }}
                                multiple
                                expandAll
                                checkRelation='unRelated'
                                treeData={
                                    serviceGroupTreeData
                                }
                                field='serviceGroups' label='服务组'
                                filterTreeNode
                                showFilteredOnly
                                dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                            ></Form.TreeSelect>
                        </Col>
                    </Row>
                    <Divider className="mb10" />
                    {/* <Form.Select
                        style={{ width: '100%' }}
                        multiple
                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                        optionList={connectGroups.map((g) => {
                            return {
                                label: `${g.name}(${g.alias}) - ${g.description}`,
                                value: g.name
                            }
                        })}
                        label="连接器组" field="connectorGroups"></Form.Select>

                    <Divider className="mb10" /> */}
                    <Title heading={6} className="mb10">服务节点</Title>
                    <ArrayField field="nodes" >
                        {({ add, arrayFields }) => <>
                            <Row className="tableTitle">
                                {/* <Col span={6}>名称</Col> */}

                                <Col span={9}>IP地址&nbsp;<Popover zIndex={3000} content={<div className='p10'>
                                    支持以下格式：
                                    <div>单个IP：**********</div>
                                    <div>CIDR格式：**********/24</div>
                                    <div>IP范围：**********-************或**********-255</div>
                                </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></Col>
                                <Col span={6}>物理位置</Col>
                                <Col span={6}>备注</Col>
                                <Col span={3} className="btn-right-col">
                                    <Button icon={<IconPlusCircle />} onClick={() => setNodeSelectorVisible(true)}></Button>
                                    {/* <Button icon={<IconPlusCircle />} onClick={add}></Button> */}
                                </Col>
                            </Row>
                            {arrayFields.map(({ field, remove }, i) => {
                                return <Row key={i} className='tableBody'>
                                    {/* <Col span={6}>
                                    <Form.Input
                                        readonly
                                        validate={value => {
                                            if (!value) {
                                                return '名称不能为空';
                                            }
                                            return '';
                                        }}
                                        placeholder={'请输入名称'}
                                        field={`${field}.name`} noLabel />
                                </Col> */}

                                    <Col span={9}>
                                        <Form.Input readonly validate={
                                            (value) => {
                                                if (!value) {
                                                    return 'ip不能为空';
                                                }

                                                return isValidIPOrIpRangeOrCIDR(value)
                                            }
                                        } placeholder={'请输入IP，CIDR格式或IP段'} field={`${field}.ip`} noLabel />
                                    </Col>
                                    <Col span={6}>
                                        <Form.Input
                                            validate={value => {
                                                if (!value) {
                                                    return '物理位置不能为空';
                                                }
                                                return '';
                                            }}
                                            placeholder={'请输入物理位置'}
                                            field={`${field}.description`} noLabel />
                                    </Col>
                                    <Col span={6}>
                                        <Form.Input
                                            validate={value => {
                                                if (!value) {
                                                    return '备注不能为空';
                                                }
                                                return '';
                                            }}
                                            placeholder={'请输入备注'}
                                            field={`${field}.name`} noLabel />
                                    </Col>
                                    <Col span={3} className="btn-right-col" style={{ paddingTop: 12 }}>
                                        <Button type="danger" onClick={remove} icon={<IconMinusCircle />}></Button>
                                    </Col>
                                </Row>
                            })}

                        </>}
                    </ArrayField>

                </>)}
            </Form>}
    </Modal>
        {nodeSelectorVisible && <RoutingNodeSelector close={() => setNodeSelectorVisible(false)}
            success={(nodes) => {
                let existNodes = formApi?.getValue('nodes') || [];
                nodes.forEach(node => {
                    if (!existNodes.find(n => n.ip === node.ip)) {
                        existNodes.push({
                            id: BigInt(0),
                            name: node.name,
                            remark: node.name,
                            ip: node.ip,
                            description: node.description,
                            rank: existNodes.length
                        });
                    }
                });
                formApi?.setValue('nodes', existNodes);
                setNodeSelectorVisible(false);
            }}
        ></RoutingNodeSelector>}
    </>
}

export default Index;