import React, { useState, useContext, useEffect } from "react";
import { Typo<PERSON>, Modal, Form, Row, Col, Button, Divider, Popover, Notification, ArrayField } from "@douyinfe/semi-ui";
import { IconHelpCircle, IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { BASE_PATH } from '@/constants/router';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { RoutingPool, RoutingPoolType, ServiceOrigin, ServiceRouteMode, ServiceNodeType, RoutingPoolNode, Service, ServiceGroup, ServiceType, ServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { ServiceStatus } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { ConnectorGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";

import RoutingNodeSelector from '../components/routing-node-selector';
import { isValidIPOrIpRangeOrCIDR } from "@/utils/validators";

import useServicesGroup from '../../group/useServicesGroup';
import { flylayerClient } from "@/services/core";
const { Title, Text } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: NetworkService
}

const Index: React.FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        description: string;
        stataus: ServiceStatus;
        serviceGroups: Array<string>;
        connectorGroups: Array<string>;
        nodes: Array<{
            id: bigint,
            name: string,
            description: string,
            remark: string,
            ip: string,
            rank: number
        }>;
    }>>();


    const [connectGroups, setConnectGroups] = useState<ConnectorGroup[]>([]);

    useEffect(() => {
        flylayerClient.listConnectorGroups({
            flynetId: flynet.id
        }).then((res) => {
            setConnectGroups(res.connectorGroups);
        }).catch((err) => {
            Notification.error({
                title: '获取连接器组失败',
                content: err.message
            });
        });
    }, []);

    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);


    const { serviceGroupTreeData } = useServicesGroup(ServiceType.ROUTING);

    const [nodeSelectorVisible, setNodeSelectorVisible] = useState(false);

    const handleSubmit = async () => {
        await formApi?.validate();
        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        let name = values.name ? values.name.trim() : '';
        let description = values.description ? values.description.trim() : '';
        let status = values.stataus;

        const connectorGroups = values.connectorGroups.map((name: string) => {
            let connectorGroup = new ConnectorGroup({
                name: name
            });

            return connectorGroup;
        });
        let serviceGroups: Array<ServiceGroup> = []
        if (values.serviceGroups) {
            serviceGroups = values.serviceGroups.map((id: string) => {
                let serviceGroup = new ServiceGroup({ id: BigInt(id) });
                return serviceGroup;
            });
        }

        let nodes: Array<NetworkServiceNode> = values.nodes.map((node: {
            id: bigint,
            name: string,
            remark: string,
            description: string,
            ip: string,
            rank: number
        }, index) => {
            let serviceNode = new NetworkServiceNode({
                name: node.name,
                remark: node.remark,
                rank: index,
                ip: node.ip,
                description: node.description,
            })
            return serviceNode;
        });


        setSaveLoading(true);

        let service = new NetworkService({
            id: props.record.id,
            name: name,
            description: description,
            status: status,
            serviceGroups: serviceGroups,
            nodes: nodes,
            connectorGroups
        });

        flylayerClient.updateNetworkService({
            flynetId: flynet.id,
            service: service
        }).then(() => {
            setSaveLoading(false);
            props.success && props.success();
            props.close();
        }).catch((err) => {
            setSaveLoading(false);
            console.log(err);
        })
    }

    return <><Modal
        title="编辑网络服务"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={800}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Form getFormApi={setFormApi}
            initValues={{
                name: props.record.name,
                description: props.record.description,
                status: props.record.status,
                serviceGroups: props.record.serviceGroups.map(group => group.id),
                nodes: props.record.nodes.map(node => {
                    return {
                        id: node.id,
                        name: node.name,
                        remark: node.remark,
                        ip: node.ip,
                        rank: node.rank,
                        description: node.description
                    }
                }),
                connectorGroups: props.record.connectorGroups.map((g) => g.name)
            }}
        >
            {({ values }) => (<>
                <Row gutter={24}>
                    <Col span={8}>
                        <Form.Input field='name' label='名称' trigger={'blur'} validate={value => {

                            if (!value) {
                                return '名称不能为空';
                            }
                            // 编码不能以-开头
                            if (value.trim().startsWith('-')) {
                                return '名称不能以-开头'
                            }
                            if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                return "名称只能包含字母、数字和'-'";
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={16}>
                        <Form.Input field="description" label="备注" />
                        {/* <Form.RadioGroup field="stataus" label="状态">
                            <Form.Radio value={ServiceStatus.INITIAL}>
                                <Text>未发布</Text>
                            </Form.Radio>
                            <Form.Radio value={ServiceStatus.PUBLISHED}>
                                <Text>已发布</Text>
                            </Form.Radio>
                        </Form.RadioGroup> */}
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Form.TreeSelect style={{ width: '100%' }}
                            multiple
                            expandAll
                            checkRelation='unRelated'
                            treeData={
                                serviceGroupTreeData
                            }
                            field='serviceGroups' label='服务组'
                            filterTreeNode
                            showFilteredOnly
                            dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                        ></Form.TreeSelect>
                    </Col>
                </Row>
                <Divider className="mb10" />
                {/* <Form.Select
                    style={{ width: '100%' }}
                    multiple
                    dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    optionList={connectGroups.map((g) => {
                        return {
                            label: `${g.name}(${g.alias}) - ${g.description}`,
                            value: g.name
                        }
                    })}
                    label="连接器组" field="connectorGroups"></Form.Select>
                <Divider className="mb10" /> */}
                <Title heading={6} className="mb10">服务节点</Title>
                <ArrayField field="nodes" >
                    {({ add, arrayFields }) => <>
                        <Row className="tableTitle">
                            {/* <Col span={6}>名称</Col> */}

                            <Col span={9}>IP地址&nbsp;<Popover zIndex={3000} content={<div className='p10'>
                                支持以下格式：
                                <div>单个IP：**********</div>
                                <div>CIDR格式：**********/24</div>
                                <div>IP范围：**********-************或**********-255</div>
                            </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></Col>
                            <Col span={6}>物理位置</Col>
                            <Col span={6}>备注</Col>
                            <Col span={3} className="btn-right-col">
                                <Button icon={<IconPlusCircle />} onClick={() => setNodeSelectorVisible(true)}></Button>
                                {/* <Button icon={<IconPlusCircle />} onClick={add}></Button> */}
                            </Col>
                        </Row>
                        {arrayFields.map(({ field, remove }, i) => {
                            return <Row key={i} className='tableBody'>
                                {/* <Col span={6}>
                                    <Form.Input
                                        readonly
                                        validate={value => {
                                            if (!value) {
                                                return '名称不能为空';
                                            }
                                            return '';
                                        }}
                                        placeholder={'请输入名称'}
                                        field={`${field}.name`} noLabel />
                                </Col> */}

                                <Col span={9}>
                                    <Form.Input readonly validate={
                                        (value) => {
                                            if (!value) {
                                                return 'IP不能为空';
                                            }

                                            return isValidIPOrIpRangeOrCIDR(value)
                                        }
                                    } placeholder={'请输入IP，CIDR格式或IP段'} field={`${field}.ip`} noLabel />
                                </Col>

                                <Col span={6}>
                                    <Form.Input
                                        validate={value => {
                                            if (!value) {
                                                return '物理位置不能为空';
                                            }
                                            return '';
                                        }}
                                        placeholder={'请输入物理位置'}
                                        field={`${field}.description`} noLabel />
                                </Col>
                                <Col span={6}>
                                    <Form.Input
                                        validate={value => {
                                            if (!value) {
                                                return '备注不能为空';
                                            }
                                            return '';
                                        }}
                                        placeholder={'请输入备注'}
                                        field={`${field}.name`} noLabel />
                                </Col>
                                <Col span={3} className="btn-right-col" style={{ paddingTop: 12 }}>

                                    <Button type="danger" onClick={remove} icon={<IconMinusCircle />}></Button>
                                </Col>
                            </Row>
                        })}

                    </>}
                </ArrayField>

            </>)}
        </Form>
    </Modal>
        {nodeSelectorVisible && <RoutingNodeSelector close={() => setNodeSelectorVisible(false)}
            success={(nodes) => {
                let existNodes = formApi?.getValue('nodes') || [];
                nodes.forEach(node => {
                    if (!existNodes.find(n => n.ip === node.ip)) {
                        existNodes.push({
                            id: BigInt(0),
                            name: node.name,
                            remark: node.name,
                            ip: node.ip,
                            rank: existNodes.length,
                            description: node.description,
                        });
                    }
                });
                formApi?.setValue('nodes', existNodes);
                setNodeSelectorVisible(false);

            }}
        ></RoutingNodeSelector>}
    </>
}

export default Index;