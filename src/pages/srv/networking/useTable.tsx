import { useState, useContext, useEffect } from 'react';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { IconMore } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ServiceGroup, ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';

import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { FilterParam } from '@/components/search-filter';

import { Typography, Dropdown, Button, Divider, Row, Col, Tag, Space, Select, RadioGroup, Radio } from '@douyinfe/semi-ui';

import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { ServiceStatus } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { caseInsensitiveIncludes } from '@/utils/common';

const { Title, Paragraph } = Typography;

export type RoutingServiceFilter = {
    query: string;
    group?: string;
    status: 'enable' | 'disable' | ''
}

const useTable = (initFilter: RoutingServiceFilter) => {
    const navigate = useNavigate();
    const flynet = useContext(FlynetGeneralContext);

    // 服务组列表
    const [groups, setGroups] = useState<ServiceGroup[]>([]);
    // 当前选中的服务组
    const [curGroup, setCurGroup] = useState<ServiceGroup>();

    // 是否正在加载中
    const [loading, setLoading] = useState(false);
    const [filter, setFilter] = useState<RoutingServiceFilter>(initFilter);
    const [total, setTotal] = useState<number>(0);
    // 当前页码
    const [page, setPage] = useState(1);
    const pageSize = 20;

    const [routingServices, setRoutingServices] = useState<NetworkService[]>([]);
    const [allRoutingServices, setAllRoutingServices] = useState<NetworkService[]>([]);

    const [editVisible, setEditVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);
    const [distributeVisible, setDistributeVisible] = useState(false);

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    const [selectedRoutingService, setSelectedRoutingService] = useState<NetworkService>();

    const [connectionDelVisible, setConnectionDelVisible] = useState(false);
    const [selectedConnection, setSelectedConnection] = useState<Machine>();
    const [connectionSelectVisible, setConnectionSelectVisible] = useState(false);
    const [selectedRoutingNode, setSelectedRoutingNode] = useState<NetworkServiceNode>();

    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);

    const initFilterParams = (serviceGroups: ServiceGroup[]) => {
        setFilterParams([{
            name: 'query',
            placeholder: '根据服务名称或备注搜索',
            label: '查询',
            value: initFilter.query || '',
        }, {
            name: 'status',
            placeholder: '请选择状态',
            label: '状态',
            value: initFilter.status || '',
            filterComponent: ({ value, onChange }) => {
                const [val, setVal] = useState(value);
                return <><RadioGroup
                    value={val}
                    onChange={(val) => {
                        setVal(val.target.value);
                    }}
                    style={{ width: 180 }}
                    className='mb10'
                >
                    <Radio value={'enable'}>启用</Radio>
                    <Radio value={'disable'}>禁用</Radio>
                </RadioGroup>
                    <Divider className='mb10' />
                    <Button block onClick={() => {
                        onChange(val)
                    }}>应用</Button>

                </>
            },
            funGetDisplayValue: (val: string) => {
                if (val == 'enable') {
                    return '启用';
                } else if (val == 'disable') {
                    return '禁用';
                }
                return '';
            }
        }, {
            name: 'group',
            placeholder: '服务组',
            label: '服务组',
            value: initFilter.group || '',
            filterComponent: ({ value, onChange }) => {
                const [val, setVal] = useState(value);

                return <><Select
                    style={{ width: 180 }}
                    className='mb10'
                    value={val}
                    onChange={val => setVal(val)}
                    placeholder='选择服务组'
                    optionList={[{ value: '', label: '全部' }, ...serviceGroups.map((item) => { return { value: item.name, label: item.alias || item.name } })]}
                ></Select>
                    <Divider />
                    <Button block onClick={() => {
                        onChange(val)
                    }}>应用</Button>

                </>
            },
            funGetDisplayValue: (val: string) => {
                if (val) {
                    let group = serviceGroups.find((item) => item.name == val);
                    if (group) {
                        return group.alias || group.name;
                    }
                }
                return '';
            }
        }]);
    };

    const query = async () => {
        setLoading(true);
        const resGroup = await flylayerClient.listServiceGroups({
            flynetId: flynet.id,
            serviceType: ServiceType.ROUTING
        });

        let curGroup: ServiceGroup | undefined;
        setGroups(resGroup.serviceGroups);
        initFilterParams(resGroup.serviceGroups);
        if (resGroup.serviceGroups) {
            resGroup.serviceGroups.forEach((item) => {
                if (item.name == filter.group) {
                    curGroup = item;
                }
            })
        }
        setCurGroup(curGroup);

        flylayerClient.listNetworkServices({
            flynetId: flynet.id
        }).then((res) => {
            setAllRoutingServices(res.services);
            setRoutingServices(doFilter(1, res.services, filter, resGroup.serviceGroups));
        }).catch((err) => {
            console.error(err);
        }).finally(() => {
            setLoading(false);
        })

    }



    useEffect(() => {
        setPage(1)
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    useEffect(() => {
        query();
    }, []);

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (field: string, record: NetworkService, index: number) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                        <div>
                            <Title heading={6}>
                                {record.name}
                                {/* <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/srv/networking/detail/${record.id}`), 10)}>
                                    {record.name}
                                </a> */}
                                {/* {record.description &&
                                    <Popover content={<div className='p10'>{record.description}</div>}>
                                        <IconArticle style={{
                                            fontSize: 14,
                                            marginLeft: '4px',
                                            color: '#999'
                                        }} />
                                    </Popover>} */}
                            </Title>
                            <Paragraph size='small'>{record.description}</Paragraph>
                        </div>
                    </div>
                </>
            }
        },
        {
            title: '所属服务组',
            width: 200,
            dataIndex: 'serviceGroups',
            render: (_: any, record: NetworkService) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{record.serviceGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space></div>
            }
        }, {
            title: '网络服务',
            dataIndex: 'nodes',
            key: 'nodes',
            width: 400,
            render: (field: string, record: NetworkService, index: number) => {
                return <>
                    <Row className='tableTitle'>
                        <Col span={8}>IP或IP段</Col>
                        <Col span={8}>物理位置</Col>
                        {/* <Col span={3}>协议</Col> */}
                        <Col span={8}>备注</Col>
                        {/* <Col span={3}>状态</Col> */}
                        {/* <Col span={10}>连接器</Col> */}
                    </Row>
                    {record.nodes.map((node, index) => {
                        return <Row key={index} className='tableBody'>

                            <Col span={8}>{node.ip}</Col>
                            <Col span={8}>{node.description}</Col>    
                            <Col span={8}>{node.name}</Col>

                        </Row>
                    })}

                </>
            }

        },
        {
            title: '连接器组',
            width: 200,
            dataIndex: 'connectorGroups',
            key: 'connectorGroups',
            render: (_: any, record: NetworkService) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{record.connectorGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space></div>
            }
        }
        , {
            title: '状态',
            dataIndex: 'stataus',
            key: 'stataus',
            width: 100,
            render: (fieldd: string, acl: NetworkService, index: number) => {
                return <>
                    {acl.status == ServiceStatus.INITIAL && <Tag color='red'>末发布</Tag>}
                    {acl.status == ServiceStatus.PUBLISHED && <Tag color='green'>已发布</Tag>}
                </>;
            }
        }, {
            title: '',
            dataIndex: 'operation',
            key: 'operation',
            width: 100,
            render: (text: string, record: NetworkService) => {
                return <><div className='table-last-col' onClick={(e) => {
                    e.nativeEvent.preventDefault();
                    e.nativeEvent.stopPropagation();
                }}><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            {record.status == ServiceStatus.INITIAL && <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.preventDefault();
                                    e.nativeEvent.stopPropagation();
                                    setDistributeVisible(true);
                                    setSelectedRoutingService(record);
                                }}
                            >发布服务</Dropdown.Item>}
                            {record.status == ServiceStatus.PUBLISHED && <Dropdown.Item disabled>发布服务</Dropdown.Item>}

                            <Divider />
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.preventDefault();
                                    e.nativeEvent.stopPropagation();
                                    setEditVisible(true);
                                    setSelectedRoutingService(record);
                                }}
                            >编辑服务</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.stopPropagation();
                                    e.nativeEvent.stopPropagation();
                                    setDelVisible(true);
                                    setSelectedRoutingService(record);
                                }}
                                type='danger'>删除服务</Dropdown.Item>

                        </Dropdown.Menu>}><Button onClick={(e) => { e.nativeEvent.stopPropagation() }}><IconMore className='align-v-center' /></Button>
                    </Dropdown>
                </div>
                </>;
            }
        }
    ];

    // 过滤数据
    const doFilter = (page: number, src: Array<NetworkService>, routingPoolFilter: RoutingServiceFilter, serviceGroups?: ServiceGroup[]) => {
        if (!src || src.length == 0) {
            setTotal(0);
            return src;
        }

        if ((!routingPoolFilter.query || routingPoolFilter.query == '') && routingPoolFilter.group == '' && !routingPoolFilter.status) {
            setTotal(src.length);
            return src.slice(0, pageSize * page);
        }

        const group = serviceGroups ? serviceGroups.find((item) => item.name == filter.group) : groups.find((item) => item.name == filter.group);

        const filteredList = src.filter((item) => {
            let isPassName = false;
            let isPassEnabled = false;
            let isPassGroup = false;

            if (routingPoolFilter.query) {
                if (caseInsensitiveIncludes(item.name, routingPoolFilter.query) || caseInsensitiveIncludes(item.name, routingPoolFilter.query) || caseInsensitiveIncludes(item.description, routingPoolFilter.query)) {
                    isPassName = true;
                }

                if (!isPassName) {

                    item.nodes.forEach(node => {
                        if (node.ip.indexOf(routingPoolFilter.query) >= 0 || caseInsensitiveIncludes(node.name, routingPoolFilter.query)) {
                            isPassName = true;
                        }
                    })
                }
            } else {
                isPassName = true;
            }


            if (routingPoolFilter.status == 'enable') {
                if (item.status == ServiceStatus.INITIAL) {
                    isPassEnabled = false;
                } else {
                    isPassEnabled = true;
                }
            } else if (routingPoolFilter.status == 'disable') {
                if (item.status == ServiceStatus.INITIAL) {
                    isPassEnabled = true;
                } else {
                    isPassEnabled = false;
                }
            } else {
                isPassEnabled = true
            }


            if (group) {
                item.serviceGroups.forEach((val) => {
                    if (group?.id == val.id) {
                        isPassGroup = true;
                    }
                });
            } else {
                isPassGroup = true;
            }



            if (isPassName && isPassEnabled && isPassGroup) {
                return true;
            } else {
                return false;
            }


            return true;
        })

        setTotal(filteredList.length);
        return filteredList;
    }

    const handleFilterChange = (newFilter: RoutingServiceFilter) => {
        setFilter(newFilter)
        setRoutingServices(doFilter(1, allRoutingServices, newFilter))
    }

    const addPage = () => {

        setRoutingServices(doFilter(page + 1, allRoutingServices, initFilter));
        setPage(page + 1)

    }

    const handleGroupChange = (group?: ServiceGroup) => {
        const list = doFilter(1, allRoutingServices, filter);
        setRoutingServices(list);
        setCurGroup(group);
    };

    return {
        loading,
        routingPools: routingServices,
        total,
        columns,
        filter,
        setFilter,
        filterParams,
        setFilterParams,
        handleFilterChange,
        reloadFlag,
        setReloadFlag,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedRoutingService,
        setSelectedRoutingService,
        groups,
        curGroup, setCurGroup,
        addPage,
        handleGroupChange,
        page, pageSize,
        connectionDelVisible, setConnectionDelVisible,
        selectedConnection, setSelectedConnection,
        connectionSelectVisible, setConnectionSelectVisible,
        selectedRoutingNode, setSelectedRoutingNode,
        distributeVisible, setDistributeVisible
    }
}

export default useTable;
