import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, But<PERSON> } from '@douyinfe/semi-ui';
import { useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import Routing from './networking';
import App from './app';
import System from './system';

const Index: React.FC = () => {
    const location = useLocation();
    let type = 'networking';
    if (location.pathname.includes('app')) {
        type = 'app';
    } else if (location.pathname.includes('networking')) {
        type = 'networking';
    } else if (location.pathname.includes('system')) {
        type = 'system';
    }
    const [activeKey, setActiveKey] = useState(type);
    const navigate = useNavigate();
    const [appParams, setAppParams] = useState({});
    const [routingParams, setRouterParams] = useState({});
    const [systemParams, setSystemParams] = useState({});
    return <>
        <div className='general-page'>
            <Tabs type="line" activeKey={activeKey} onChange={(key) => {
                let params = {};
                if (key === 'app') {
                    params = appParams;
                } else if (key === 'networking') {
                    params = routingParams;
                } else if (key === 'system') {
                    params = systemParams;
                }
                navigate(`${BASE_PATH}/srv/${key}`);
                if (activeKey === 'app') {
                    setAppParams(params);
                } else if (activeKey === 'networking') {
                    setRouterParams(params);
                } else if (activeKey === 'system') {
                    setSystemParams(params);
                }
                setActiveKey(key);
            }}

            >
                <TabPane tab="网络服务" itemKey="networking">
                    <Routing />
                </TabPane>
                <TabPane tab="系统服务" itemKey="system">
                    <System />
                </TabPane>
                <TabPane tab="应用服务" itemKey="app">
                    <App />
                </TabPane>
            </Tabs>


        </div>
    </>
}

export default Index