.addService {

}
.tableBody {
    >div {
        padding-right: 8px;
    }
}

.guideWrap {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 60px);
}
.guideCard {
    width: 900px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor:default;
}
.listItem {
    width: 100%;
}
.listItem:hover {
    background-color: var(--semi-color-fill-1);
}

.typeItemContent {
    position: relative;
    text-align: center;
    width: 200px;
    h6, p {
        text-align: center;
    }
}
.iconTick {
    position: absolute;
    right: -12px;
    top: 0;
    padding: 5px;
    border-radius: 20px;
    background-color: var(--semi-color-text-2);
    color: #FFF;
}
.typeItem {
    cursor: pointer;
    img {
        -webkit-user-drag: none;
    }
}
.typeItem:last-of-type {
    border-top-right-radius: var(--semi-border-radius-large);
    border-bottom-right-radius: var(--semi-border-radius-large);
}
.typeItem:first-of-type {
    border-top-left-radius: var(--semi-border-radius-large);
    border-bottom-left-radius: var(--semi-border-radius-large);
}
.typeItemActive {
    background-color: var(--semi-color-fill-0);
}
.typeItem:hover {
    background-color: var(--semi-color-fill-1);
}
.typeItemDisabled {
    cursor: not-allowed;
    background-color: var(--semi-color-disabled-bg);
    opacity: 0.5;
    border-top-right-radius: var(--semi-border-radius-large);
    border-bottom-right-radius: var(--semi-border-radius-large);
    img {
        cursor: not-allowed;
    }
}

.rdpContainer {
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}
.daemonContainer {
    min-height: 250px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin-bottom: 20px;
    width: 100%;
}

.rightColumn {
    display: flex!important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0!important;
}

.addCenter {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: 80px;
}

.addServiceModal {
    div[id="semi-modal-body"] {
        padding: 20px!important;
        // background-color: var(--semi-color-fill-0);
        margin-bottom: 20px;
        margin-top: 20px;
        border-radius: var(--semi-border-radius-medium);
    }
}