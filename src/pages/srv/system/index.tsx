import React, { useState, useContext } from 'react'
import { Typo<PERSON>, Tabs, Table, Row, Col, Button, Space, TabPane, Popover, Layout, Input, Tag, BackTop, Select, Notification } from '@douyinfe/semi-ui';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { SystemService } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/system_services_pb';

import InfiniteScroll from 'react-infinite-scroll-component';
import qs from 'query-string';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';
import MachineSelector from '@/components/machine-selector';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import useTable, { RoutingServiceFilter } from './useTable';

import New from './new';
import Edit from './edit';
import Del from './del';
import SearchFilter from '@/components/search-filter';
const { Paragraph } = Typography;
const { Sider, Content } = Layout;
// 根据URL参数设置过滤参数
const getInitFilter = (location: Location): RoutingServiceFilter => {
    const query: string = getQueryParam('query', location) as string;
    const group: string = getQueryParam('group', location) as string;
    const status: string = getQueryParam('status', location) as string;

    return {
        query: query || '',
        group: group || '',
        status: status == 'enable' || status == 'disable' ? status : '',
    };
}
const Index: React.FC = () => {

    const navigate = useNavigate();

    const flynet = useContext(FlynetGeneralContext);
    const [createVisible, setCreateVisible] = useState(false);

    const initFilter = getInitFilter(useLocation());

    const {
        loading,
        routingPools,
        total,
        columns,
        filter,
        setFilter,
        filterParams,
        setFilterParams,
        handleFilterChange,
        setReloadFlag,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedRoutingService,
        groups,
        curGroup, setCurGroup,
        addPage,
        handleGroupChange,
        pageSize,
        connectionDelVisible, setConnectionDelVisible,
        selectedConnection,
        connectionSelectVisible, setConnectionSelectVisible,
        selectedRoutingNode
    } = useTable(initFilter);

    const doNavigate = (params: RoutingServiceFilter) => {
        let query = '';

        if (params.query || params.group || params.status) {
            query = qs.stringify(params, {
                skipEmptyString: true,
            });
        }

        if (query) {
            navigate(`${BASE_PATH}/srv/routing?${query}`);
        } else {
            navigate(`${BASE_PATH}/srv/routing`);
        }
    }

    const handleQueryChange = (value: string) => {
        handleFilterChange({ ...filter, query: value })
        doNavigate({ ...filter, query: value });
    }

    const handleEnableChange = (value: any) => {
        handleFilterChange({ ...filter, status: value })
        doNavigate({ ...filter, status: value });
    }

    return <><div style={{ paddingTop: 20 }}>
        <Row className='mb10'>
            <Col span={20}>
                <Paragraph type='tertiary'>
                    配置系统的网络服务，以便配置对服务的策略
                </Paragraph>
            </Col>
            <Col span={4}>
                <div className='btn-right-col'>
                    <Space>
                        <Button onClick={() => navigate(`${BASE_PATH}/srv/system/group`)}>服务组</Button>
                        <Button type='primary' theme='solid' onClick={() => setCreateVisible(true)}>创建服务</Button></Space>
                </div>
            </Col>
        </Row>
        <SearchFilter onChange={(val: string, filterParam) => {
            handleFilterChange({ ...filter, [filterParam.name]: val })
            doNavigate({ ...filter, [filterParam.name]: val });
            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);
        }}
            filterParams={filterParams}
            className='mb20 search-bar'
        />

        {groups.map((group, index) => {
            return <TabPane key={index}
                tab={group.alias ?
                    <Popover position='top' content={<div className='p10'>{group.description ? group.description : group.name}</div>}>{group.alias}</Popover>
                    : <span>{group.name}</span>}
                itemKey={group.name}></TabPane>
        })}
        <div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  服务总数 {total}</Tag>} </div>
        <InfiniteScroll
            dataLength={routingPools.length}
            next={addPage}
            hasMore={routingPools.length < total}
            loader={<div><TableEmpty loading={true} /></div>}
            endMessage={
                <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                    {routingPools.length > pageSize && <Paragraph type='tertiary'>---- 到底了 ----</Paragraph>}
                </div>
            }
        >
            <Table
                rowKey={(record?: SystemService) => record ? record.id + '' : ''}
                columns={columns}
                dataSource={routingPools}
                loading={loading}
                pagination={false}
                empty={<TableEmpty loading={loading}></TableEmpty>}
            />
        </InfiniteScroll>
        <BackTop style={{ right: 10 }} />
    </div>
        {createVisible && <New
            close={() => setCreateVisible(false)}
            success={() => {
                setReloadFlag(true);
                setCreateVisible(false)
            }}
        />}
        {editVisible && selectedRoutingService && <Edit
            close={() => setEditVisible(false)}
            success={() => {
                setReloadFlag(true);
                setEditVisible(false)
            }}
            record={selectedRoutingService}
        />}
        {delVisible && selectedRoutingService && <Del
            close={() => setDelVisible(false)}
            success={() => {
                setReloadFlag(true);
                setDelVisible(false)
            }}
            record={selectedRoutingService} />
        }
        {connectionSelectVisible && selectedRoutingNode && <MachineSelector
            multi={true}
            onChange={(machines) => {
                let machineIds = [];
                if (Array.isArray(machines)) {
                    machineIds = machines.map(m => m.id);
                } else {
                    machineIds = [machines.id];
                }
                flylayerClient.saveRoutingConnection({
                    flynetId: flynet.id,
                    serviceNodeId: selectedRoutingNode.id,
                    machineIds: machineIds
                }).then(() => {
                    Notification.success({
                        title: '成功',
                        content: '添加成功'
                    });
                    setReloadFlag(true);
                    setConnectionSelectVisible(false);
                });
            }}
            gateway={true}
            close={() => setConnectionSelectVisible(false)}></MachineSelector>}
    </>
}

export default Index;