import React, { useState, useContext } from "react";
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'
import MachineViewer from '@/components/machine-viewer';
import { Typography, Modal, Form, Row, Col, Button, Divider, Notification, ArrayField, Space, RadioGroup, Radio, Popover } from "@douyinfe/semi-ui";
import { IconArrowUpRight, IconPlus, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { SystemService, SystemServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/system_services_pb';
import { ServiceGroup, ServiceStatus, ServiceOrigin, ServicePort, ServiceProto, ServiceRouteMode, ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import MachineSelector from '@/components/machine-selector'
import FormNetworkServiceSelector from '@/components/network-service-selector';
import FormServiceIpInput from "@/components/service-ip-input";


import useServicesGroup from '../../group/useServicesGroup';
import { flylayerClient } from "@/services/core";
const { Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: SystemService
}

const Index: React.FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        description: string;
        stataus: ServiceStatus;
        routeMode: ServiceRouteMode;
        serviceGroups: Array<string>;
        directNodes: Array<SystemServiceNode>;
        forwordNodes: Array<SystemServiceNode>;
        gatewayNodes: Array<SystemServiceNode>;
        networkServiceId: string;
    }>>();
    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    // 系统服务模式
    const [routeMode, setRouteMode] = useState<ServiceRouteMode>(props.record.routeMode)

    const [gatewayIp, setGatewayIp] = useState('');

    // 直连节点
    const [directNodes, setDirectNodes] = useState<Array<SystemServiceNode>>([]);
    // 转发节点
    const [forwordNodes, setForwordNodes] = useState<Array<SystemServiceNode>>([]);

    // 网关节点
    const [gatewayNodes, setGatewayNodes] = useState<Array<SystemServiceNode>>([]);
    const { serviceGroupTreeData } = useServicesGroup(ServiceType.SYSTEM_DAEMON);

    const [directNodeViewerVisible, setDirectNodeViewerVisible] = useState(false);
    const viewDirectNode = (index: number) => {
        var subnetNode = formApi?.getValues().directNodes[index];
        if (subnetNode) {
            if (!subnetNode.ip) {
                Notification.error({ content: 'IP地址为空' })
                return;
            } else {
                setGatewayIp(subnetNode.ip);
                setDirectNodeViewerVisible(true)
            }
        }
    }

    // 直连节点选择
    const [directSelectorVisible, setDirectSelectorVisible] = useState(false);
    // 从设备添加子网结点
    const addSubnetNodeFromMachine = () => {
        setDirectSelectorVisible(true)
    }


    // 网关节点选择
    const [gatewaySelectorVisible, setGatewaySelectorVisible] = useState(false);
    // 从设备添加网关结点
    const addGatewayNodeFromMachine = () => {
        setGatewaySelectorVisible(true)
    }

    const handleSubmit = async () => {
        await formApi?.validate();
        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        let name = values.name ? values.name.trim() : '';
        let description = values.description ? values.description.trim() : '';

        let serviceGroups: Array<ServiceGroup> = []
        if (values.serviceGroups) {
            serviceGroups = values.serviceGroups.map((id: string) => {
                let serviceGroup = new ServiceGroup({ id: BigInt(id) });
                return serviceGroup;
            });
        }



        let nodes: Array<SystemServiceNode> = [];

        if (values.routeMode == ServiceRouteMode.DIRECT) {
            nodes = values.directNodes.map((node, index) => {
                let serviceNode = new SystemServiceNode({
                    ip: node.ip,
                    domain: node.domain,
                    name: node.name,
                    description: node.description,
                    remark: node.remark,
                    proto: node.proto,
                    port: Number(node.port),
                    rank: index + 1
                })
                return serviceNode;
            });
        } else {
            nodes = values.forwordNodes.map((node, index) => {
                let serviceNode = new SystemServiceNode({
                    ip: node.ip,
                    name: node.name,
                    domain: node.domain,
                    description: node.description,
                    remark: node.remark,
                    proto: node.proto,
                    port: Number(node.port),
                    rank: index + 1
                })
                return serviceNode;
            });
        }



        setSaveLoading(true);

        let service = new SystemService({
            id: props.record.id,
            name: name,
            description: description,
            status: ServiceStatus.INITIAL,
            serviceGroups: serviceGroups,
            routeMode: routeMode,
            nodes: nodes
        });


        flylayerClient.updateSystemService({
            flynetId: flynet.id,
            service: service
        }).then(() => {
            Notification.success({
                title: '更新成功',
                content: '系统服务更新成功'
            });

            setSaveLoading(false);
            props.success && props.success();
            props.close();
        }).catch((err) => {
            Notification.error({
                title: '系统服务更新失败',
                content: err.message
            });
            setSaveLoading(false);
            console.log(err);
        })
    }

    return <><Modal
        title="编辑系统服务"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={960}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Form getFormApi={setFormApi}
            initValues={{
                name: props.record.name,
                description: props.record.description,
                routeMode: props.record.routeMode,
                serviceGroups: props.record.serviceGroups.map(group => group.id),
                directNodes: props.record.routeMode == ServiceRouteMode.DIRECT ? props.record.nodes : [],
                forwordNodes: props.record.routeMode == ServiceRouteMode.FORWARD ? props.record.nodes : [],
                networkServiceId: props.record.networkService ? props.record.networkService.id : ''
            }}
        >
            {({ values }) => (<>
                <Row gutter={24}>
                    <Col span={8}><Form.Input field='name'
                        label={<>名称 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                        trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            // 编码不能以-开头
                            if (value.trim().startsWith('-')) {
                                return '名称不能以-开头'
                            }
                            if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                return "名称只能包含字母、数字和'-'";
                            }
                            return '';
                        }}
                        required />
                    </Col>
                    <Col span={16}>
                        <Form.Input field="description" label="描述" />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Form.TreeSelect style={{ width: '100%' }}
                            multiple
                            expandAll
                            checkRelation='unRelated'
                            treeData={
                                serviceGroupTreeData
                            }
                            field='serviceGroups' label='服务组'
                            filterTreeNode
                            showFilteredOnly
                            dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                        ></Form.TreeSelect>
                    </Col>
                </Row>

                <Row gutter={24}>
                    <Col span={24}>
                        <FormNetworkServiceSelector field='networkServiceId' label='网络服务' />
                    </Col>
                </Row>
                <Space className="mb10">
                    <RadioGroup name="mode" value={routeMode} onChange={(e) => {
                        let newDaemonMode: ServiceRouteMode = e.target.value;
                        if (routeMode == ServiceRouteMode.DIRECT && newDaemonMode == ServiceRouteMode.FORWARD) {
                            setDirectNodes(formApi?.getValue('directNodes') as any)
                            formApi?.setValue('forwordNodes', forwordNodes)
                        } else if (routeMode == ServiceRouteMode.FORWARD && newDaemonMode == ServiceRouteMode.DIRECT) {
                            formApi?.setValue('directNodes', directNodes)
                            setForwordNodes(formApi?.getValue('forwordNodes') as any)
                        }
                        setRouteMode(newDaemonMode)
                    }} direction="horizontal">
                        <Radio checked={routeMode == ServiceRouteMode.DIRECT} value={ServiceRouteMode.DIRECT}>直连模式</Radio>
                        <Radio checked={routeMode == ServiceRouteMode.FORWARD} value={ServiceRouteMode.FORWARD}>负载模式</Radio>
                    </RadioGroup>
                </Space>
                <Divider className="mb10" />
                {routeMode == ServiceRouteMode.FORWARD && <>
                    <ArrayField field='gatewayNodes'>
                        {({
                            arrayFields
                        }) => <React.Fragment>

                                <Row>
                                    <Col span={20}>
                                        <Title heading={6} className="mb10">负载均衡</Title>
                                    </Col>
                                </Row>
                                {arrayFields.length > 0 ? <>
                                    <Row className={styles.tableTitle} >
                                        <Col span={7}>IP地址</Col>
                                        <Col span={3}>协议</Col>
                                        <Col span={2}>端口</Col>
                                        <Col span={10}>备注</Col>
                                        <Col span={2}>&nbsp;</Col>
                                    </Row>
                                    {arrayFields.map(({ field, key, remove }, i) => (
                                        <Row className={styles.tableBody} key={key} >
                                            <Col xs={24} sm={7}>

                                                <Form.Input
                                                    field={`${field}[ip]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '地址不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                />

                                            </Col>

                                            <Col xs={24} sm={3}>
                                                <Form.Select
                                                    field={`${field}[proto]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '协议不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                    style={{ width: '100%' }}
                                                >
                                                    <Form.Select.Option value={ServiceProto.TCP}>TCP</Form.Select.Option>
                                                    <Form.Select.Option value={ServiceProto.UDP}>UDP</Form.Select.Option>
                                                </Form.Select>
                                            </Col>
                                            <Col xs={24} sm={2}>
                                                <Form.Input
                                                    field={`${field}[port]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '端口不能为空';
                                                        }
                                                        // 只能为端口号
                                                        if (!/^\d+$/.test(value)) {
                                                            return '端口号只能为数字';
                                                        }
                                                        return '';
                                                    }}
                                                >
                                                </Form.Input>
                                            </Col>
                                            <Col xs={24} sm={10}>
                                                <Form.Input
                                                    validate={value => {
                                                        if (!value) {
                                                            return '备注不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                    placeholder={'备注'}
                                                    field={`${field}.remark`} noLabel />
                                            </Col>
                                            <Col xs={24} sm={2} className={styles.rightColumn}>
                                                <Button style={{ marginTop: 12 }}
                                                    type='danger'
                                                    theme='borderless'
                                                    icon={<IconMinusCircle />}
                                                    onClick={() => {
                                                        remove()
                                                        setGatewayNodes(formApi?.getValue('gatewayNodes') as any)

                                                    }}

                                                />

                                            </Col>

                                        </Row>
                                    ))}
                                </> :
                                    <div className={styles.addCenter}>
                                        <Button
                                            size='large'
                                            onClick={addGatewayNodeFromMachine}
                                            icon={<IconPlus />} />
                                    </div>

                                }
                            </React.Fragment>}
                    </ArrayField>
                    <ArrayField field='forwordNodes'>
                        {({
                            add,
                            arrayFields
                        }) => <React.Fragment>

                                <Row className="mb20">
                                    <Col span={20}>
                                        <Title heading={6} className="mb10">节点</Title>
                                    </Col>
                                    <Col span={4} className={styles.rightColumn}>
                                        <Button
                                            onClick={add}
                                            icon={<IconPlus />} />
                                    </Col>
                                </Row>
                                {arrayFields.length > 0 ? <>
                                    <Row className={styles.tableTitle} >
                                        <Col span={10}>IP地址</Col>
                                        <Col span={3}>协议</Col>
                                        <Col span={2}>端口</Col>
                                        <Col span={7}>备注</Col>
                                        <Col span={2}>&nbsp;</Col>
                                    </Row>
                                    {arrayFields.map(({ field, key, remove }, i) => (
                                        <Row className={styles.tableBody} key={key} >
                                            <Col xs={24} sm={10}>
                                                <FormServiceIpInput
                                                    field={`${field}[ip]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '地址不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                />

                                            </Col>

                                            <Col xs={24} sm={3}>
                                                <Form.Select
                                                    field={`${field}[proto]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '协议不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                    style={{ width: '100%' }}
                                                >
                                                    <Form.Select.Option value={ServiceProto.TCP}>TCP</Form.Select.Option>
                                                    <Form.Select.Option value={ServiceProto.UDP}>UDP</Form.Select.Option>
                                                </Form.Select>
                                            </Col>
                                            <Col xs={24} sm={2}>
                                                <Form.Input
                                                    field={`${field}[port]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '端口不能为空';
                                                        }
                                                        // 只能为端口号
                                                        if (!/^\d+$/.test(value)) {
                                                            return '端口号只能为数字';
                                                        }
                                                        return '';
                                                    }}
                                                >
                                                </Form.Input>
                                            </Col>
                                            <Col xs={24} sm={7}>
                                                <Form.Input
                                                    validate={value => {
                                                        if (!value) {
                                                            return '备注不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                    placeholder={'备注'}
                                                    field={`${field}.remark`} noLabel />
                                            </Col>
                                            <Col xs={24} sm={2} className={styles.rightColumn}>
                                                <Button style={{ marginTop: 12 }}
                                                    disabled={arrayFields.length == 1}
                                                    type='danger'
                                                    theme='borderless'
                                                    icon={<IconMinusCircle />}
                                                    onClick={() => {
                                                        remove()
                                                        setForwordNodes(formApi?.getValue('forwordNodes') as any)
                                                    }}

                                                />

                                            </Col>

                                        </Row>
                                    ))}
                                </> :
                                    // <div className={styles.addCenter}>
                                    //     <Button
                                    //         size='large'
                                    //         onClick={add}
                                    //         icon={<IconPlus />} />
                                    // </div>
                                    <></>
                                }
                            </React.Fragment>}
                    </ArrayField>
                </>}
                {routeMode == ServiceRouteMode.DIRECT && <>
                    <ArrayField field='directNodes'>
                        {({
                            arrayFields
                        }) => <React.Fragment>
                                {/* 
                                <Row className="mb20">
                                    <Col span={20}>
                                        <Title heading={6} className="mb10">节点</Title>
                                    </Col>
                                </Row> */}
                                {arrayFields.length > 0 ? <>
                                    <Row className={styles.tableTitle} >
                                        <Col span={10}>IP</Col>
                                        <Col span={4}>协议</Col>
                                        <Col span={3}>端口</Col>
                                        <Col span={7}>备注</Col>
                                        {/* <Col span={2}>&nbsp;</Col> */}
                                    </Row>
                                    {arrayFields.map(({ field, key, remove }, i) => (
                                        <Row className={styles.tableBody} key={key} >
                                            <Col xs={24} sm={10}>

                                                <FormServiceIpInput
                                                    field={`${field}[ip]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '地址不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                />


                                            </Col>

                                            <Col xs={24} sm={4}>
                                                <Form.Select
                                                    field={`${field}[proto]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '协议不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                    style={{ width: '100%' }}
                                                >
                                                    <Form.Select.Option value={ServiceProto.TCP}>TCP</Form.Select.Option>
                                                    <Form.Select.Option value={ServiceProto.UDP}>UDP</Form.Select.Option>
                                                </Form.Select>
                                            </Col>
                                            <Col xs={24} sm={3}>
                                                <Form.Input
                                                    field={`${field}[port]`}
                                                    noLabel
                                                    validate={value => {
                                                        if (!value) {
                                                            return '端口不能为空';
                                                        }
                                                        // 只能为端口号
                                                        if (!/^\d+$/.test(value)) {
                                                            return '端口号只能为数字';
                                                        }
                                                        return '';
                                                    }}
                                                >
                                                </Form.Input>
                                            </Col>
                                            <Col xs={24} sm={7}>
                                                <Form.Input
                                                    validate={value => {
                                                        if (!value) {
                                                            return '备注不能为空';
                                                        }
                                                        return '';
                                                    }}
                                                    placeholder={'备注'}
                                                    field={`${field}.remark`} noLabel />
                                            </Col>
                                            {/* <Col xs={24} sm={2} className={styles.rightColumn}>
                                                <Button style={{ marginTop: 12 }}
                                                    type='danger'
                                                    theme='borderless'
                                                    icon={<IconMinusCircle />}
                                                    onClick={() => {
                                                        remove()
                                                        setDirectNodes(formApi?.getValue('directNodes') as any)
                                                    }}
                                                />
                                            </Col> */}

                                        </Row>
                                    ))}
                                </> :
                                    <div className={styles.addCenter}>
                                        <Button
                                            size='large'
                                            onClick={addSubnetNodeFromMachine}
                                            icon={<IconPlus />} />
                                    </div>}
                            </React.Fragment>}
                    </ArrayField>
                </>}

            </>)}
        </Form>
    </Modal>
        {directSelectorVisible && <MachineSelector
            multi={false}
            value={directNodes.map(node => node.ip)}
            onChange={(value => {
                let newList: Array<SystemServiceNode> = []
                let nodes = formApi?.getValue('directNodes');
                nodes?.forEach((item: SystemServiceNode) => {
                    newList.push(item)
                })
                if (value instanceof Machine) {
                    let exist = false;
                    nodes?.forEach(node => {
                        if (node.ip == value?.ipv4) {
                            exist = true;
                        }
                    })

                    if (!exist) {
                        console.log(value)
                        newList.push(new SystemServiceNode({
                            ip: value.ipv4,
                            name: value.name,
                            description: '',
                            remark: value.name,
                            proto: ServiceProto.TCP,
                            port: 80
                        }))

                    }
                }
                formApi?.setValue('directNodes', newList);
                setDirectNodes(newList)
                setDirectSelectorVisible(false)
            })}
            close={() => setDirectSelectorVisible(false)}
        ></MachineSelector>}

        {gatewaySelectorVisible && <MachineSelector
            multi={false}
            gateway={true}
            value={gatewayNodes.map(node => node.ip)}
            onChange={(value => {
                let newList: Array<SystemServiceNode> = []
                let nodes = formApi?.getValue('gatewayNodes');
                nodes?.forEach((item: SystemServiceNode) => {
                    newList.push(item)
                })
                if (value instanceof Machine) {
                    let exist = false;
                    nodes?.forEach(node => {
                        if (node.ip == value?.ipv4) {
                            exist = true;
                        }
                    })

                    if (!exist) {
                        console.log(value)
                        newList.push(new SystemServiceNode({
                            ip: value.ipv4,
                            name: value.name,
                            description: '',
                            remark: value.name,
                            proto: ServiceProto.TCP,
                            port: 80
                        }))

                    }
                }
                formApi?.setValue('gatewayNodes', newList);
                setGatewayNodes(newList)
                setGatewaySelectorVisible(false)
            })}
            close={() => setGatewaySelectorVisible(false)}
        ></MachineSelector>
        }
        {directNodeViewerVisible && gatewayIp && <MachineViewer ip={gatewayIp} close={() => setDirectNodeViewerVisible(false)}></MachineViewer>}

    </>
}

export default Index;