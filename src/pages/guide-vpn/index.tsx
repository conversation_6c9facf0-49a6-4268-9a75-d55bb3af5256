import React, { useState, useContext, useEffect, useRef } from 'react'
import { Typography, Steps, Badge, Tag, RadioGroup, Row, Col, Button, Card, Space, Divider } from '@douyinfe/semi-ui';
import GuideDownload from '@/components/guide-download';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { IconChevronDown, IconChevronRight } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig'

import styles from './index.module.scss'
import { Spin } from '@douyinfe/semi-ui';
import { listMachines, getMachine } from '@/services/device';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

import { Platform_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/install_pb';
import EditRoute from '../devices/edit-route';
import { getMockCidrIp } from '@/utils/common';
import {
    isMobile,
    isAndroid,
    isIOS
} from 'react-device-detect';
const { Title, Text, Paragraph } = Typography;

const Index: React.FC = () => {
    const navigate = useNavigate();

    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);
    // 步骤
    const [step, setStep] = useState(0);
    // refStep
    const refStep = useRef(0);

    const userProfile = useContext(UserProfileContext)

    const flynet = useContext(FlynetGeneralContext);
    // 第一台设备
    const [machine1, setMachine1] = useState<Machine>();
    // 第二台设备
    const [machine2, setMachine2] = useState<Machine>();

    // 平台类型
    const [platformType, setPlatformType] = useState<Platform_Type>(Platform_Type.LINUX);

    const queryMachines = () => {

        listMachines(flynet.id, 'limit=2&offset=0').then(res => {
            let m1: Machine | undefined = undefined;
            let m2: Machine | undefined = undefined;
            res.machines.forEach(m => {
                if (m.advertisedRoutes && m.advertisedRoutes.length > 0) {
                    if (!machine2) {
                        m2 = m;
                    }
                } else if (!m1) {
                    m1 = m;
                }

            })
            setMachine1(m1);

            setMachine2(m2);
            if (m1 != undefined && m2 == undefined) {
                refStep.current = 1;
                setStep(1)
            } else if (m1 != undefined && m2 != undefined) {
                setStep(2)
                refStep.current = 2;
            }
        })
    };

    const updateMachine2 = (m: Machine) => {
        getMachine(m.ipv4).then(res => {
            setMachine2(res)
        })
    }

    // 定时轮询函数
    const polling = () => {
        queryMachines()

        if (refStep.current < 2) {
            setTimeout(() => {
                polling()
            }, 3000)
        }
    }

    // 定时轮询
    useEffect(() => {
        polling()
    }, [])


    // 编辑路由设置弹出框是否可见
    const [editRouteVisiable, setEditRouteVisiable] = useState(false);

    // 子网路由是否展开
    const [subnetRouteExpand, setSubnetRouteExpand] = useState(false);


    return <><div className={styles.guideWrap}>
        <div className={styles.guideRow}>

            <div className={styles.cardWrap}>
                <Card bordered={false} className={styles.guideCard}>
                    {
                        step === 0 && <div className={styles.previewContent}>
                            <Spin />
                            <Paragraph style={{ marginTop: 20 }} type='tertiary'>等待您的第一台设备接入</Paragraph>
                        </div>
                    }
                    {
                        step === 1 && <div className={styles.previewContent}>
                            <Tag size='large' className={styles.machineTag} style={{ marginBottom: 40, padding: 20 }}><span style={{ flexGrow: 1 }}><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {machine1?.givenName} </span><span>{machine1?.ipv4}</span></Tag>

                            <Spin />
                            <Paragraph style={{ marginTop: 20 }} type='tertiary'>等待您的第二台设备接入</Paragraph>
                        </div>
                    }
                    {
                        (step === 2 || step === 3) && <div className={styles.previewContent}>
                            <Tag size='large' className={styles.machineTag} style={{ marginBottom: 20, padding: 20 }}><span style={{ flexGrow: 1 }}><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {machine1?.givenName} </span><span>{machine1?.ipv4}</span></Tag>

                            <Tag size='large' className={styles.machineRouteTag} style={{ marginBottom: 20, padding: 12 }}>
                                <span style={{ flexGrow: 1 }}><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {machine2?.givenName} </span>
                                <span>
                                    <span onClick={()=>{setSubnetRouteExpand(!subnetRouteExpand)}} style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>{subnetRouteExpand ? <IconChevronDown size='small' /> : <IconChevronRight  size='small'/>}  {machine2?.ipv4}</span>
                                    {subnetRouteExpand && <span>
                                        {machine2?.advertisedRoutes.map((route: string, index: number) => {
                                            return <Paragraph className={styles.subnet} key={index} type={machine2?.enabledRoutes.indexOf(route) >= 0 ? 'primary' : 'quaternary'}>
                                                {route}
                                            </Paragraph>;
                                        })}
                                    </span>}
                                    
                                </span></Tag>

                        </div>
                    }
                </Card>
            </div>
            <div className={styles.cardWrap}>
                <Card bordered={false} className={styles.guideCard}>
                    <Steps className='mb40' type="basic" current={step}  direction={isMobile || isAndroid || isIOS ? 'vertical' : 'horizontal'}>
                        <Steps.Step title='添加设备' description="接入第一台设备" />
                        <Steps.Step title='部署网关' description="接入网关设备" />
                        <Steps.Step title='测试' description="联通性验证" />
                        <Steps.Step title='结束' description="零信任SDP构建成功" />
                    </Steps>
                    {step === 0 &&
                        <div className={styles.guideContent}>
                            {/* <Title heading={4} className='mb20'>欢迎您使用越云，现在让我们开始添加第一台设备</Title> */}
                            <Paragraph className='mb20'>安装{globalConfig.name}并以 {userProfile.identity?.traits?.email} 登录。 一旦您在设备上登录，它就会自动添加到您的{globalConfig.name}网络中。您可以通过以下方式安装{globalConfig.name}：</Paragraph>
                            <GuideDownload></GuideDownload>
                        </div>}
                    {step === 1 &&
                        <div className={styles.guideContent}>
                            {/* <Title heading={4} className='mb20'>第二步，添加网关设备</Title> */}
                            <Paragraph className='mb20'>您的第一台设备（{machine1?.givenName}）已经加入网络！</Paragraph>
                            <Paragraph className='mb20'>现在需要添加一台网关设备作为连接器, 请遵循以下步骤：</Paragraph>
                            <Title heading={6} className='mb20'>1.  安装{globalConfig.name}：</Title>
                            <GuideDownload
                                currentTab={Platform_Type.LINUX}
                                hideTabs={[Platform_Type.ANDROID, Platform_Type.IOS]}
                                onTabChange={(tab) => { setPlatformType(tab) }}
                            ></GuideDownload>
                            {platformType == Platform_Type.LINUX ? <><Paragraph className='mb20'>Linux系统下需要启用ip转发,如果您的 Linux 系统有 /etc/sysctl.d 目录，请使用下面命令：</Paragraph>
                                <div className={styles.codeWrap}>
                                    <code>echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.d/99-flylayer.conf</code><br/>
                                    <code>echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.d/99-flylayer.conf</code><br/>
                                    <code>sudo sysctl -p /etc/sysctl.d/99-flylayer.conf</code>
                                </div>
                                <Paragraph className='mb10'>如果没有，请使用下面命令：</Paragraph>
                                <div className={styles.codeWrap}>
                                    <code>echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf</code><br/>
                                    <code>echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.conf</code><br/>
                                    <code>sudo sysctl -p /etc/sysctl.conf</code>
                                </div>
                                <Paragraph className='mb10'>如果您的 Linux 节点使用 firewalld,您可能需要启用masquerading，请使用下面命令：</Paragraph>

                                <div className={styles.codeWrap}>
                                    <code>sudo firewall-cmd --add-masquerade --permanent</code><br/>
                                    <code>sudo firewall-cmd --reload</code>
                                </div></> : ''}



                            <Title heading={6} className='mb20'>2.在终端中运行以下命令来宣告路由：</Title>
                            <div className={styles.codeWrap}>
                                <code>flylayer up --advertise-routes=10.0.0.0/24,********/24</code>
                            </div>
                            <Paragraph className='mb20'>
                                将上面示例中的子网10.0.0.0/24,********/24替换为适合您网络的子网, 子网支持 IPv4 和 IPv6 。
                            </Paragraph>
                        </div>}
                    {step === 2 &&
                        <div className={styles.guideContent}>
                            {/* <Title heading={4} className='mb20'>第三步，测试第一台设备和子网设备之间的网络连接</Title> */}
                            {/* <Paragraph className='mb20'></Paragraph> */}

                            <Title heading={6} className='mb20'>1. 开启子网路由权限</Title>
                            <Paragraph className='mb20'>默认情况下，子网路由权限没有开启，点击下面按钮启用子网路由</Paragraph>
                            <Button className='mb20' onClick={() => setEditRouteVisiable(true)}>编辑路由设置</Button>
                            <Title heading={6} className='mb20'>2. 测试</Title>
                            <Paragraph className='mb20'>在您的第一台设备上，打开终端并运行以下命令(把{getMockCidrIp(machine2?.advertisedRoutes[0])}替换成真实子网ip)：</Paragraph>
                            <div className={styles.codeWrap}>
                                <code>ping <span className={styles.codeText}>
                                    {getMockCidrIp(machine2?.advertisedRoutes[0])}
                                </span>
                                </code>
                            </div>
                            <Paragraph className='mb20'>您应该看到类似以下内容的输出：</Paragraph>
                            <div className={styles.codeWrap}>
                                <code>64 bytes from {machine1?.ipv4}: icmp_seq=1 ttl=64 time=0.032 ms</code>
                            </div>
                            <Paragraph className='mb20'>如果您看到这样的输出，这意味着您的设备已经成功连接到子网！</Paragraph>

                            <Button type='primary' size='large' theme='solid' onClick={() => { setStep(3) }}>测试完成，继续</Button>
                        </div>}
                    {step === 3 &&
                        <div className={styles.guideContent}>
                            <Title heading={4} className='mb20'>您已经成功创建了您的第一个网络！</Title>
                            <Paragraph className='mb20'>您可以通过以下方式继续学习：</Paragraph>
                            <Paragraph><a>了解如何设置DNS</a></Paragraph>
                            <Paragraph><a>了解如何进行访问控制</a></Paragraph>
                            <Paragraph><a>了解如何设置DNS</a></Paragraph>
                            <Paragraph className='mb20'><a>了解如何邀请其他团队成员</a></Paragraph>
                            <Paragraph className='mb20'>或者您还可以直接进入控制台进行设置：</Paragraph>
                            <Button type='primary' size='large' theme='solid' onClick={() => { navigate(`${BASE_PATH}`) }}>进入控制台</Button>

                        </div>}
                </Card>
            </div>
        </div>
    </div>
        {editRouteVisiable && machine2 ?
            <EditRoute record={machine2} success={() => { updateMachine2(machine2) }} close={() => { setEditRouteVisiable(false) }} /> : null}
    </>
}

export default Index;