import { AdmissionCondition, AdmissionConditionValue } from "../admission-policy";

import { Struct, Value, ListValue } from '@bufbuild/protobuf';

interface ExpressionItem {
    name: string;
    operator: string;
    value: Expression | string | number | boolean;
}

interface Expression {
    types: string[];
    children: ExpressionItem[];
}

function parseExpression(expression: string): Expression {
    const types: string[] = [];
    const items: ExpressionItem[] = [];

    expression = expression.trim();
    // 把expression的 多个连续空格替换成一个空格，同时把Tab替换成空格
    expression = expression.replace(/\s+/g, ' ');


    const orSplit = splitByTopLevelOrOperator(expression, 'or');
    if (orSplit.length > 1) {
        orSplit.forEach((subExpr, index) => {


            subExpr = subExpr.trim();
            if (subExpr.startsWith('(') && subExpr.endsWith(')')) {
                subExpr = subExpr.substring(1);
                subExpr = subExpr.substring(0, subExpr.length - 1);
            }



            if (subExpr.startsWith('(')) {
                subExpr = subExpr.substring(1);
            }

            subExpr = subExpr.trim();
            if (subExpr.startsWith('or ')) {
                subExpr = subExpr.substring(3);
                subExpr = subExpr.trim();
                if (subExpr.startsWith('(')) {
                    subExpr = subExpr.substring(1);
                }
            }

            subExpr = subExpr.trim();

            if (index > 0) {
                types.push('or');
            }

            const andSplit = splitByTopLevelAndOperator(subExpr, 'and');
            if (andSplit.length > 1) {
                andSplit.forEach((subAndExpr, index) => {
                    subAndExpr = subAndExpr.trim();
                    if (subAndExpr.startsWith('and ')) {
                        subAndExpr = subAndExpr.substring(4);
                    }
                    subAndExpr = subAndExpr.trim();
                    if (subAndExpr.startsWith('(') && subAndExpr.endsWith(')')) {
                        subAndExpr = subAndExpr.substring(1);
                        subAndExpr = subAndExpr.substring(0, subAndExpr.length - 1);
                    }

                    if (index > 0) {
                        types.push('and');
                    }

                    const item = parseExpressionItem(subAndExpr);
                    items.push(item);
                });
            } else {
                const item = parseExpressionItem(subExpr);
                items.push(item);
            }

        });

        return {
            types,
            children: items,
        };
    }


    if (expression.startsWith('(') && expression.endsWith(')')) {
        expression = expression.substring(1);
        expression = expression.substring(0, expression.length - 1);
    }

    const andSplit = splitByTopLevelAndOperator(expression, 'and');

    if (andSplit.length > 1) {
        andSplit.forEach((subExpr, index) => {
            subExpr = subExpr.trim();
            if (subExpr.startsWith('and ')) {
                subExpr = subExpr.substring(4);
            }
            subExpr = subExpr.trim();
            if (subExpr.startsWith('(') && subExpr.endsWith(')')) {
                subExpr = subExpr.substring(1);
                subExpr = subExpr.substring(0, subExpr.length - 1);
            }

            if (index > 0) {
                types.push('and');
            }

            const item = parseExpressionItem(subExpr);
            items.push(item);
        });

        return {
            types,
            children: items,
        };
    }

    return {
        types: [],
        children: [parseExpressionItem(expression)],
    };

}

function splitByTopLevelOrOperator(expression: string, operator: 'and' | 'or'): string[] {

    if (expression.startsWith('(') && expression.endsWith(')')) {
        expression = expression.substring(1);
        expression = expression.substring(0, expression.length - 1);
    }

    const operatorPattern = new RegExp(`\\b${operator}\\b`);

    if (!expression.match(operatorPattern)) {
        return [];
    }

    let level = 0;
    const splitParts: string[] = [];
    let currentPart: string = '';

    let i = 0;

    while (i < expression.length) {
        const char = expression[i];

        if (char === '(') {
            level++;
        } else if (char === ')') {
            level--;
        }



        // Check for the operator only if we're at the top level of parentheses
        if (level === 0) {
            const match = expression.slice(i).match(operatorPattern);
            if (match) {

                if (match.index === operator.length + 1 || match.index === 0) {


                    if (currentPart && currentPart.trim() != '') {

                        let str = currentPart.trim();
                        if (str.startsWith('(') && str.endsWith(')')) {
                            str = str.substring(1);
                            str = str.substring(0, str.length - 1);
                        }
                        splitParts.push(str);

                    }
                    currentPart = '';
                    i += operator.length; // Skip over the operator
                    continue;
                }
            }
        }

        currentPart += char;
        i++;
    }


    if (currentPart && currentPart.trim() != '') {
        let str = currentPart.trim();
        if (level == 1 && str.startsWith('(')) {
            str = str.substring(1);
        }
        splitParts.push(str);
    }


    return splitParts;
}


function splitByTopLevelAndOperator(expression: string, operator: 'and' | 'or'): string[] {


    const operatorPattern = new RegExp(`\\b${operator}\\b`);

    if (!expression.match(operatorPattern)) {
        return [];
    }

    let level = 0;
    const splitParts: string[] = [];
    let currentPart: string = '';

    let i = 0;

    while (i < expression.length) {
        const char = expression[i];

        if (char === '(') {
            level++;
        } else if (char === ')') {
            level--;
        }



        // Check for the operator only if we're at the top level of parentheses
        if (level === 0) {
            const match = expression.slice(i).match(operatorPattern);

            if (operator == 'and' && match && match.index === 0) {

                if (currentPart && currentPart.trim() != '') {
                    splitParts.push(currentPart.trim());
                }
                currentPart = '';
                i += operator.length; // Skip over the operator
                continue;
            } else if (operator == 'or' && match) {

                if (match.index === operator.length + 1 || match.index === 0) {


                    if (currentPart && currentPart.trim() != '') {
                        splitParts.push(currentPart.trim());
                    }
                    currentPart = '';
                    i += operator.length; // Skip over the operator
                    continue;
                }
            }
        }

        currentPart += char;
        i++;
    }

    if (currentPart && currentPart.trim() != '') {
        splitParts.push(currentPart.trim());
    }


    return splitParts;
}


function parseExpressionItem(condition: string): ExpressionItem {
    // 可能的操作符
    const operators = ['!=', '==', '<=', '>=', '<', '>', ' in ', ' contains '];

    // 在condition中找到第一个操作符
    let operatorIndex = -1;
    let op = '';
    for (const operator of operators) {
        const index = condition.indexOf(operator);
        if (index !== -1 && (operatorIndex === -1 || index < operatorIndex)) {
            operatorIndex = index;
            op = operator;
        }
    }

    if (operatorIndex === -1) {
        throw new Error(`Unsupported condition format: ${condition}`);
    }

    const name = condition.substring(0, operatorIndex).trim();
    const value = parseValueOrExpression(condition.substring(operatorIndex + op.length).trim());

    return {
        name,
        operator: op,
        value,
    };

}


function parseValueOrExpression(value: string): Expression | string | number | boolean {
    if (value.startsWith('(')) {
        return parseExpression(value);
    }
    return parseValue(value);
}

function parseValue(value: string): string | number | boolean {
    if (value.startsWith('"') && value.endsWith('"')) {
        return value.slice(1, -1);
    }
    if (!isNaN(Number(value))) {
        return Number(value);
    }
    if (value === 'true' || value === 'false') {
        return value === 'true';
    }
    return value; // Default return for any unquoted string
}

export function expressionToCondition(expression: string): [AdmissionCondition[], string[]] {
    const conditions: AdmissionCondition[] = [];


    const parsedExpression = parseExpression(expression);
    const operators: string[] = parsedExpression.types;
    if (parsedExpression) {

        parsedExpression.children.forEach((element, index) => {
            let val = element.value;

            let admissionCondition = new AdmissionCondition({
                name: element.name,
                op: element.operator.trim(),

            });

            if (typeof val === 'object') {
                let dynamicConditions: AdmissionCondition[] = [];

                let dataSource = '';
                let dataField = '';
                val.children.forEach((child, index) => {
                    let name = child.name.trim();
                    if (name.startsWith('data.')) {
                        name = name.substring(5);
                    }

                    // dataSource 为第一个.之前的字符串
                    const dotIndex = name.indexOf('.');
                    if (dotIndex > 0) {
                        dataSource = name.substring(0, dotIndex);
                        name = name.substring(dotIndex + 1);
                    }

                    // dataField 为第一个.之后,第二个.之前的字符串
                    const secondDotIndex = name.indexOf('.');
                    if (secondDotIndex > 0) {
                        dataField = name.substring(0, secondDotIndex);
                        name = name.substring(secondDotIndex + 1);
                    }

                    let dynamicCondition: AdmissionCondition = new AdmissionCondition({
                        name: name,
                        op: child.operator.trim(),
                    });
                    if (typeof child.value === 'string') {
                        // 检测child.value是否是一个数组
                        if (child.value.startsWith('[') && child.value.endsWith(']')) {
                            child.value = child.value.substring(1, child.value.length - 1);
                            dynamicCondition.value = new AdmissionConditionValue({
                                dynamic: false,
                                dynamicDataSource: dataSource,
                                dynamicDataField: dataField,
                                inputData: new Value({
                                    kind: {
                                        case: 'listValue',
                                        value: new ListValue({
                                            values: child.value.split(',').map((v) => {
                                                v = v.trim();
                                                if (v.startsWith('"') && v.endsWith('"')) {
                                                    v = v.substring(1, v.length - 1);
                                                }
                                                return new Value({ kind: { value: v, case: "stringValue" } })
                                            })
                                        })
                                    }
                                })
                            });

                        } else {
                            dynamicCondition.value = new AdmissionConditionValue({
                                dynamic: false,
                                dynamicDataSource: dataSource,
                                dynamicDataField: dataField,
                                inputData: new Value({
                                    kind: { value: child.value, case: "stringValue" }
                                })
                            });
                        }


                    } else if (typeof child.value === 'number') {
                        dynamicCondition.value = new AdmissionConditionValue({
                            dynamic: false,
                            dynamicDataSource: dataSource,
                            dynamicDataField: dataField,
                            inputData: new Value({
                                kind: { value: child.value, case: "numberValue" }
                            })
                        });
                    } else if (typeof child.value === 'boolean') {
                        dynamicCondition.value = new AdmissionConditionValue({
                            dynamic: false,
                            dynamicDataSource: dataSource,
                            dynamicDataField: dataField,
                            inputData: new Value({
                                kind: { value: child.value, case: "boolValue" }
                            })
                        });
                    }

                    dynamicConditions.push(dynamicCondition);
                });
                admissionCondition.value = new AdmissionConditionValue({
                    dynamic: true,
                    dynamicDataSource: dataSource,
                    dynamicDataField: dataField,
                    dynamicConditions: dynamicConditions,
                    inputData: new Value()
                });
            } else {
                if (typeof val === 'string') {
                    if (val.startsWith('[') && val.endsWith(']')) {
                        val = val.substring(1, val.length - 1);
                        admissionCondition.value = new AdmissionConditionValue({
                            dynamic: false,
                            inputData: new Value({
                                kind: {
                                    case: 'listValue',
                                    value: new ListValue({
                                        values: val.split(',').map((v) => {
                                            v = v.trim();
                                            if (v.startsWith('"') && v.endsWith('"')) {
                                                v = v.substring(1, v.length - 1);
                                            }
                                            return new Value({ kind: { value: v, case: "stringValue" } })
                                        })
                                    })
                                }
                            })
                        });
                    } else {

                        admissionCondition.value = new AdmissionConditionValue({
                            dynamic: false,
                            inputData: new Value({
                                kind: { value: val, case: "stringValue" }
                            })
                        });
                    }
                } else if (typeof val === 'number') {
                    admissionCondition.value = new AdmissionConditionValue({
                        dynamic: false,
                        inputData: new Value({
                            kind: { value: val, case: "numberValue" }
                        })
                    });
                } else if (typeof val === 'boolean') {
                    admissionCondition.value = new AdmissionConditionValue({
                        dynamic: false,
                        inputData: new Value({
                            kind: { value: val, case: "boolValue" }
                        })
                    });
                }
            }

            conditions.push(admissionCondition);
        });

    }
    return [conditions, operators];
}


