import { AdmissionCondition, AdmissionConditionValue } from "../admission-policy";

import { Struct, Value, ListValue } from '@bufbuild/protobuf';

function getValueString(value: Value): string {
    let val = '';
    if (value) {
        if (value.kind) {
            switch (value.kind.case) {
                case 'numberValue':
                    val = '' + value.kind.value;
                    break;
                case 'stringValue':
                    val = `"${value.kind.value}"`;
                    break;
                case 'boolValue':
                    val = value.kind.value ? 'true' : 'false';
                    break;
                case 'structValue':
                    val = getStructValue(value.kind.value);
                    break;
                case 'listValue':
                    val = getListValue(value.kind.value);
                    break;
                default:
                    break;
            }
        }
    }
    return val;
}

function getListValue(value: ListValue): string {
    let val = '[';
    if (value) {
        value.values.forEach((v, index) => {
            val += getValueString(v);
            if (index < value.values.length - 1) {
                val += ', ';
            }
        });
    }
    val += ']';
    return val;
}

function getStructValue(val: Struct): string {
    let value = '{';
    if (val) {
        Object.keys(val.fields).forEach(key => {
            let field = val.fields[key];
            let fieldVal = '';
            if (field.kind) {
                switch (field.kind.case) {
                    case 'numberValue':
                        fieldVal = '' + field.kind.value;
                        break;
                    case 'stringValue':
                        fieldVal = field.kind.value;
                        break;
                    case 'boolValue':
                        fieldVal = field.kind.value ? 'true' : 'false';
                        break;
                    case 'structValue':
                        fieldVal = getStructValue(field.kind.value);
                        break;
                    case 'listValue':
                        fieldVal = getListValue(field.kind.value);
                        break;
                    default:
                        break;
                }
            }
            value += `${key}: ${fieldVal}, `;
        });
    }
    value += '}';
    return value;
}

function getConditionValue(cond?: AdmissionConditionValue): string {
    
    let value = '';

    if (cond) {
        if (cond.dynamic) {
            let dynamicValue = '';

            let mockOperators: string[] = [];
            cond.dynamicConditions.forEach((c, index) => {
                if (index > 0) {
                    mockOperators.push('and');
                }
            })

            dynamicValue = getConditionExpression(cond.dynamicConditions, mockOperators, `data.${cond.dynamicDataSource}.${cond.dynamicDataField}.`);

            value = `${dynamicValue}`;
        } else if (cond.inputData) {
            value = getValueString(cond.inputData);
        }
    }

    return value;
}

export function getConditionExpression(conditions: AdmissionCondition[], operators: string[], prefix?: string): string {

    let nTag = prefix ? '' : '\n';
    let tTag = prefix ? ' ' : '\t';

    let code = '';
    if (
        conditions.length > 0
        // && conditions.length === operators.length + 1
        // || conditions.length === 1
    ) {
        // 为了保证条件表达式的正确性，需要保证条件数量比操作符数量多1
        // 且第一个条件不需要添加操作符
        // 从第二个条件开始，添加对应的操作符
        // 例如：(条件1 and 条件2) or 条件3 or (条件4 and 条件5), operators = ['and', 'or', 'and']
        // let startOr = true;
        for (let i = 0; i < conditions.length; i++) {
            let lastNTag = prefix || i == conditions.length - 1 ? '' : '\n'; 
            
            let operator = '';
            if(i === 0) {
                code += `(${nTag}`;
            }
            if (i > 0) {
                operator = operators[i - 1];
                if (operator === 'or') {
                    code += ')\n';
                    code += `${tTag}${operator} \n(\n`;
                } else {
                    code += `${tTag}${operator}`;
                }

            }


            let name = prefix ? `${prefix}${conditions[i].name}` : conditions[i].name;
            let op = conditions[i].op;
            let value = getConditionValue(conditions[i].value);

            code += `${tTag}${name} ${op} ${value}${lastNTag} `;
        }



    }

    code += `${nTag})`;

    return code;
}
