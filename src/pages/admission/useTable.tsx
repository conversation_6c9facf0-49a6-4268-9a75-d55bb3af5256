import { useState, useEffect, useContext } from 'react'
import AdmissionPolicyOrigin from './admission-policy'
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { Typography, Notification, Dropdown, Button, Divider, Tag, Popover } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { caseInsensitiveIncludes } from '@/utils/common';
const { Title, Paragraph } = Typography;

export type AdmissionFilter = {
    query?: string;
    disabled: string;
}

const useTable = (filterParam: AdmissionFilter) => {
    const flynet = useContext(FlynetGeneralContext);
    
    const [admissionPolicies, setAdmissionPolicies] = useState<Array<AdmissionPolicyOrigin>>([]);
    const [allAdmissionPolicies, setAllAdmissionPolicies] = useState<Array<AdmissionPolicyOrigin>>([]);

    const [loading, setLoading] = useState(true);


    const [editVisible, setEditVisible] = useState(false);

    const [delVisible, setDelVisible] = useState(false);

    const [selectedAdmission, setSelectedAdmission] = useState<AdmissionPolicyOrigin>();

    // 过滤参数
    const [filter, setFilter] = useState<AdmissionFilter>(filterParam);
    const query = () => {
        setLoading(true);

        flylayerClient.listAdmissionPolicyOrigin({
            flynetId: flynet.id
        }).then(res => {
            setAllAdmissionPolicies(res.admissionPolicyOrigins);
            const list = doFilter(res.admissionPolicyOrigins, filterParam);
            setAdmissionPolicies(list);
        }, err => {
            Notification.error({
                title: '获取准入策略列表失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        })
    }

    useEffect(() => {
        query()
    }, []);

    // 重新加载数据
    const reload = () => {
        query();
    }

    const columns = [{
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        render: (field: string, record: AdmissionPolicyOrigin, index: number) => {
            return <>
                <div style={{ display: 'inline-flex' }}>
                    <div>
                        <Title heading={6}>
                            {record.alias}{record.description &&
                                <Popover content={<div className='p10'>{record.description}</div>}>
                                    <IconArticle style={{
                                        fontSize: 14,
                                        marginLeft: '4px',
                                        color: '#999'
                                    }} />
                                </Popover>}
                        </Title>
                        <Paragraph size='small'>{record.name}</Paragraph>
                    </div>
                </div>
            </>
        },
    }, {
        title: '状态',
        dataIndex: 'disabled',
        key: 'disabled',
        width: 100,
        render: (disabled: boolean) => {
            return <>
                {disabled ? <Tag color='red'>禁用</Tag> : <Tag color='green'>启用</Tag>}
            </>;
        }
    }, {
        title: '',
        dataIndex: 'operation',
        key: 'operation',
        width: 100,
        render: (text: string, record: AdmissionPolicyOrigin) => {
            return <><div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>

                        <Dropdown.Item
                            onClick={() => {
                                setEditVisible(true);
                                setSelectedAdmission(record);
                            }}
                        >编辑策略</Dropdown.Item>
                        <Divider />
                        <Dropdown.Item
                            onClick={() => {
                                setDelVisible(true);
                                setSelectedAdmission(record);
                            }}
                            type='danger'>删除策略</Dropdown.Item>

                    </Dropdown.Menu>}><Button><IconMore className='align-v-center' /></Button>
            </Dropdown>
            </div>
            </>;
        }
    }];

    // 过滤数据
    const doFilter = (src: Array<AdmissionPolicyOrigin>, admissionFilter: AdmissionFilter) => {
        let result = src;
        if (admissionFilter.query || admissionFilter.disabled) {
            const queryStr = admissionFilter.query ? admissionFilter.query.trim() : '';
            result = result.filter((item) => {

                let passName = false;
                if (caseInsensitiveIncludes(item.name, queryStr)) {
                    passName = true;
                }
                if (caseInsensitiveIncludes(item.alias, queryStr)) {
                    passName = true;
                }
                if (caseInsensitiveIncludes(item.description, queryStr)) {
                    passName = true;
                }

                let passDisabled = true;
                if (admissionFilter.disabled) {
                    passDisabled = item.disabled == (admissionFilter.disabled === 'true');
                }


                return passName && passDisabled;

            });

        }
        return result;
    }

    // 过滤参数改变时重新过滤
    useEffect(() => {
        setAdmissionPolicies(doFilter(allAdmissionPolicies, filter));
    }, [filter]);

    return {
        admissionPolicies,
        columns,
        loading,
        reload,
        editVisible,
        delVisible,
        setEditVisible,
        setDelVisible,
        selectedAdmission,
        setSelectedAdmission,
        filter,
        setFilter,
        doFilter,
        allAdmissionPolicies
    }
}

export default useTable