import React, { useState, useContext, useEffect } from 'react'
import { Typography, Row, Col, Button, Table, Input, Space, Layout, Select, Banner } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import TableEmpty from '@/components/table-empty';
import { IconSearch } from '@douyinfe/semi-icons';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { AdmissionMode } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/admission_pb";
import { getFlynet } from '@/services/flynet';

import qs from 'query-string';
import New from './new';
import Edit from './edit';
import Del from './del';
import AdmissionModeEdit from './admission-mode-edit';

import styles from './index.module.scss'
import useTable, { AdmissionFilter } from './useTable';

const { Text } = Typography;
const { Sider, Content } = Layout;
// 根据URL参数设置过滤参数
const getInitFilter = (location: Location): AdmissionFilter => {
    const query: string = getQueryParam('query', location) as string;
    const disabled: string = getQueryParam('disabled', location) as string;
    return {
        query: query || '',
        disabled: disabled || ''
    }
}

const Index: React.FC = () => {
    const flynetGeneral = useContext(FlynetGeneralContext);

    const initFilter: AdmissionFilter = getInitFilter(useLocation());

    // 新建弹窗是否显示
    const [newVisible, setNewVisible] = useState(false);

    // 准入模式弹框是否显示
    const [admissionModeVisible, setAdmissionModeVisible] = useState(false);

    const {
        admissionPolicies,
        columns,
        loading,
        reload,
        editVisible,
        delVisible,
        setEditVisible,
        setDelVisible,
        selectedAdmission,
        setSelectedAdmission,
        filter,
        setFilter,
        doFilter,
        allAdmissionPolicies
    } = useTable(initFilter);
    const [admissionMode, setAdmissionMode] = useState<AdmissionMode>();
    const [admissionModeLoaded, setAdmissionModeLoaded] = useState(false);

    const queryFlynet = async () => {
        getFlynet(flynetGeneral.id).then(res => {
            if (res && res.flynet) {
                setAdmissionModeLoaded(true);
                setAdmissionMode(res.flynet.admissionMode);
            }
        }).finally(() => {
            setAdmissionModeLoaded(true);
        });
    }

    useEffect(() => {
        queryFlynet();
    }, [])

    const navigate = useNavigate();
    // 过滤参数改变时跳转路由
    const doNavigate = (param: AdmissionFilter) => {
        let query = '';
        if (param.query || param.disabled) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/policies/admission/?${query}`);
        } else {
            navigate(`${BASE_PATH}/policies/admission`);
        }
    }

    return <>
        <div style={{ paddingTop: 10 }}>
            <Row>
                <Col span={20}>

                    <Layout className='mb20 search-bar' >
                        <Layout>
                            <Content className='pr10'>
                                <Input
                                    value={filter.query}
                                    onChange={(e) => {
                                        setFilter({ ...filter, query: e })
                                        doNavigate({ ...filter, query: e });
                                    }}
                                    style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={'根据名称、编码搜索'}></Input>
                            </Content>
                            <Sider> <Space>
                                <Select style={{ width: 120 }}
                                    optionList={[
                                        { value: '', label: '全部' },
                                        { value: 'true', label: '禁用' },
                                        { value: 'false', label: '启用' }
                                    ]}
                                    insetLabel="状态"
                                    onChange={(value) => {
                                        const val = value as string;
                                        setFilter({ ...filter, disabled: val })
                                        doNavigate({ ...filter, disabled: val })
                                    }}
                                    value={filter.disabled}></Select>
                            </Space></Sider>
                        </Layout>
                    </Layout>
                </Col>
                <Col span={4}>
                    <div className='btn-right-col'>
                        <Space>
                            <Button
                                theme='solid'
                                type='primary'
                                onClick={() => setNewVisible(true)}
                            >添加策略</Button>


                        </Space>
                    </div>
                </Col>
            </Row>


            {admissionMode === AdmissionMode.BLACK && <Banner
                className='mb20'
                type="info"
                description={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'right' }}>
                        <Space>
                            <Text>当前准入模式：</Text>
                            <Text strong>黑名单</Text>
                            <Text>该模式下，默认允许接入网络，如命中任意一条规则并通过，则禁止接入网络
                            </Text>
                            <Button type='warning' theme='solid' onClick={() => setAdmissionModeVisible(true)}>编辑准入模式</Button>
                        </Space>
                    </div>}
            >
            </Banner>}
            {admissionMode === AdmissionMode.WHITE && <Banner
                className='mb20'
                type="info"
                description={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'right' }}>
                        <Space>
                            <Text>当前准入模式：</Text>
                            <Text strong>白名单</Text>
                            <Text>该模式下，默认禁止接入网络，如命中任意一条规则并通过，则允许接入网络
                            </Text>
                            <Button type='warning' theme='solid' onClick={() => setAdmissionModeVisible(true)}>编辑准入模式</Button>
                        </Space>
                    </div>}
            >
            </Banner>}

            <Table
                loading={loading}
                columns={columns}
                dataSource={admissionPolicies}
                pagination={false}
                empty={<TableEmpty loading={loading}></TableEmpty>}
            ></Table>
        </div>
        {newVisible && <New
            close={() => {
                setNewVisible(false);
            }}
            success={() => {
                setNewVisible(false);
                reload();
            }}
        ></New>}
        {
            editVisible && selectedAdmission && <Edit
                admissionId={selectedAdmission.id}
                close={() => {
                    setEditVisible(false);
                    setSelectedAdmission(undefined);
                }}
                success={() => {
                    setEditVisible(false);
                    setSelectedAdmission(undefined);
                    reload();
                }}
            ></Edit>
        }
        {
            delVisible && selectedAdmission && <Del
                record={selectedAdmission}
                close={() => {
                    setDelVisible(false);
                    setSelectedAdmission(undefined);
                }}
                success={() => {
                    setDelVisible(false);
                    setSelectedAdmission(undefined);
                    reload();
                }}
            ></Del>
        }
        {
            admissionModeVisible && <AdmissionModeEdit
                close={() => {
                    setAdmissionModeVisible(false);
                }}
                success={() => {
                    setAdmissionModeVisible(false);
                    queryFlynet();
                }}
            ></AdmissionModeEdit>
        }

    </>
}

export default Index;
