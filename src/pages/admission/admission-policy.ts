import { AdmissionPolicy, AdmissionCondition, AdmissionConditionValue, AdmissionPolicyOrigin } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/admission_pb';

// // 准入条件数据
// interface AdmissionConditionValue {
//     inputData: string[],
//     dynamic: boolean, // 是否存在动态数据
//     dynamicDataSource: string, // 动态数据源
//     dynamicDataField: string, // 动态数据匹配字段
//     dynamicConditions: AdmissionCondition[],
// }
// // 准入条件
// interface AdmissionCondition {
//     name: string;
//     op: string;
//     value: AdmissionConditionValue;
// }

// // 准入策略
// interface AdmissionPolicyOrigin {
//     id: bigint;
//     name: string;
//     alias: string;
//     description: string;
//     disabled: boolean;
//     conditions: AdmissionCondition[];
//     scopes: string[]; // 适用范围 user: XXX, group: XXX, role: XXX
// }



export default AdmissionPolicyOrigin;
export { AdmissionPolicy, AdmissionCondition, AdmissionConditionValue };