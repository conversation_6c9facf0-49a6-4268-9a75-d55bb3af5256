import React, { useState, useContext, useEffect } from "react";
import { AttributeTemplate } from '@/interface/attribute-template';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';


const useExpressionsTemplate = () => {
    const flynet = useContext(FlynetGeneralContext);

    const [attrTreeData, setAttrTreeData] = useState<TreeNodeData[]>();

    const [expressionsTemplate, setExpressionsTemplate] = useState<AttributeTemplate>();

    const buildTreeData = (key: string, expressionsTemplate: AttributeTemplate) => {
        let treeData: TreeNodeData = {
            label: expressionsTemplate.title,
            value: key,
            key: key,
            children: []
        }
        if (!expressionsTemplate.properties) {
            return treeData;
        }
        Object.keys(expressionsTemplate.properties).forEach((childKey) => {
            let child = buildTreeData(key + '.' + childKey, expressionsTemplate.properties[childKey]);
            treeData.children?.push(child);
        })

        return treeData;
    }


    useEffect(() => {
        getFlynet(flynet.id).then(res => {


            if (res && res.flynet && res.flynet.attributeTemplate) {

                const template = res.flynet.attributeTemplate;
                if (template) {
                    const json: AttributeTemplate = JSON.parse(template);
                    const requestProperties = json.properties.input.properties.Request;
                    const userProperties = json.properties.input.properties.User;
                    const deviceProperties = json.properties.input.properties.Device;

                    let expressionsAttrs: AttributeTemplate = {
                        type: 'object',
                        title: 'properties',
                        description: 'properties',
                        properties: {
                            'input': {

                                type: 'object',
                                description: '输入',
                                title: '输入',
                                properties: {
                                    User: userProperties,
                                    Request: requestProperties,
                                    Device: deviceProperties
                                }
                            }
                        }

                    }
                    setExpressionsTemplate(expressionsAttrs);
                    const initTreeData: TreeNodeData[] = [];
                    Object.keys(expressionsAttrs.properties).forEach((key) => {
                        let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
                        initTreeData.push(treeDataItem);
                    })
                    setAttrTreeData(initTreeData);
                }

            }
        })

    }, [])

    return {
        attrTreeData,
        expressionsTemplate
    }
}

export default useExpressionsTemplate;