import React, { useState, useContext } from "react";
import { IconHelpCircle } from '@douyinfe/semi-icons';
import pinyin from 'tiny-pinyin';

import { sanitizeLabel } from '@/utils/common';
import { Value } from '@bufbuild/protobuf';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Typography, Button, Form, Row, Col, Notification, Divider, Popover, Space, Skeleton, Modal, Banner } from "@douyinfe/semi-ui";
import { IconAlertCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import AdmissionConditionRow from "../admission-condition-row";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import AdmissionPolicyOrigin, { AdmissionCondition, AdmissionConditionValue } from "../admission-policy";
import AdmissionScopes from "../admission-scopes";

import styles from './index.module.scss';
import useDataSource from "../useDataSource";
import { flylayerClient } from "@/services/core";
import useExpressionsTemplate from "../useExpressionsTemplate";
import ExpressionCombo from "@/components/expressions-combo";
import ExpressionsPreview from "../expressions-preview";
import ExpressionsEdit from "../expressions-edit";
import { BASE_PATH } from "@/constants/router";

const { Input, RadioGroup, Radio, TextArea } = Form
const { Title, Paragraph } = Typography;
interface Props {
    close: () => void,
    success?: () => void,
}

const Index: React.FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        alias: string,
        name: string,
        disabled: number,
        description: string,
        conditions: Array<{
            name: string,
            op: string,
            value: {
                inputData?: Value,
                dynamic: boolean,
                dynamicDataSource: string,
                dynamicDataField: string,
                dynamicConditions: Array<{
                    name: string,
                    op: string,
                    value: {
                        inputData?: Value,
                        dynamic: boolean,
                        dynamicDataSource: string,
                        dynamicDataField: string,
                    }
                }>
            }
        }>,
        scopes: Array<string>
    }>>()

    // 数据源
    const { dataSources } = useDataSource();

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [admissionItems, setAdmissionItems] = useState<AdmissionCondition[]>([new AdmissionCondition({
        name: '',
        op: '',
        value: new AdmissionConditionValue({
            inputData: new Value({
                kind: { value: "", case: "stringValue" }
            }),
            dynamic: false,
            dynamicDataSource: '',
            dynamicDataField: '',
            dynamicConditions: []
        })
    })]);
    const [operators, setOperators] = useState<string[]>([]);

    const [isExpressionsEdit, setIsExpressionsEdit] = useState(false);

    const [itemsError, setItemsError] = useState(false);

    const [scopes, setScopes] = useState<string[]>([]);

    const { attrTreeData, expressionsTemplate } = useExpressionsTemplate();

    const [validateItemFlag, setValidateItemFlag] = useState(false);

    const validateItems = async () => {
        let hasError = false;
        if (!admissionItems || admissionItems.length === 0) {
            setItemsError(true);
            return true;
        } else {
            setItemsError(false);
        }
        for (let i = 0; i < admissionItems.length; i++) {
            let item = admissionItems[i];
            if (!item.name || !item.op || !item.value) {
                hasError = true;
                break;
            }

            if (item.value.dynamic) {
                if (!item.value.dynamicDataSource || !item.value.dynamicDataField || item.value.dynamicConditions.length === 0) {
                    hasError = true;
                    break;
                }
                item.value.dynamicConditions.forEach((condition) => {
                    if (!condition.name || !condition.op || !condition.value || !condition.value.inputData) {
                        hasError = true;
                        return;
                    }
                    if (!condition.value.inputData.kind.case
                        || condition.value.inputData.kind.value == undefined
                        || (condition.value.inputData.kind.case == 'stringValue' && condition.value.inputData.kind.value == '')
                        || (condition.value.inputData.kind.case == 'numberValue' && isNaN(condition.value.inputData.kind.value))
                    ) {
                        hasError = true;
                        return;
                    }
                });
                if (hasError) {
                    break;
                }
            } else {
                if (!item.value.inputData
                    || !item.value.inputData.kind
                    || !item.value.inputData.kind.case
                    || item.value.inputData.kind.value == undefined
                    || (item.value.inputData.kind.case == 'stringValue' && item.value.inputData.kind.value == '')
                    || (item.value.inputData.kind.case == 'numberValue' && isNaN(item.value.inputData.kind.value))
                ) {
                    hasError = true;
                    break;
                }
            }
        }

        return hasError;
    }

    const handleSubmit = async () => {
        if (!formApi) {
            return;
        }


        let hasError = await validateItems();

        if (hasError) {
            setValidateItemFlag(true);
            setTimeout(() => {
                setValidateItemFlag(false);
            }, 300);

            await formApi.validate();

            return;
        } else {
            setValidateItemFlag(false);
            await formApi.validate();
        }



        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';
        const disabled = values.disabled ? true : false;

        const parseAdmissionCondition = (src?: AdmissionCondition): AdmissionCondition => {
            if (!src) {
                return new AdmissionCondition({
                    name: '',
                    op: '',
                    value: new AdmissionConditionValue({
                        inputData: new Value({
                            kind: { value: "", case: "stringValue" }
                        }),
                        dynamic: false,
                        dynamicDataSource: '',
                        dynamicDataField: '',
                        dynamicConditions: []
                    })
                })
            }
            let value = parseAdmissionValue(src.value);
            return new AdmissionCondition({
                name: src.name,
                op: src.op,
                value: value
            })
        }

        const parseAdmissionValue = (src?: AdmissionConditionValue): AdmissionConditionValue => {
            if (!src) {
                return new AdmissionConditionValue()
            }

            var dynamicConditions: AdmissionCondition[] = [];
            if (src.dynamicConditions) {
                src.dynamicConditions.forEach((item) => {
                    dynamicConditions.push(parseAdmissionCondition(item));
                })
            }

            let value = new AdmissionConditionValue({
                inputData: src.inputData ? new Value({
                    kind: src.inputData.kind
                }) : undefined,
                dynamic: src.dynamic,
                dynamicDataSource: src.dynamicDataSource,
                dynamicDataField: src.dynamicDataField,
                dynamicConditions: dynamicConditions
            });
            return value
        }

        const conditions: AdmissionCondition[] = admissionItems.map((item) => {
            let value = parseAdmissionValue(item.value);
            return new AdmissionCondition({
                name: item.name,
                op: item.op,
                value: value
            })
        })

        setLoading(true);
        flylayerClient.createAdmissionPolicyOrigin({
            flynetId: flynet.id,
            admissionPolicyOrigin: new AdmissionPolicyOrigin({
                name,
                description,
                alias,
                disabled,
                conditions: conditions,
                scopes: scopes
            })
        }).then((res) => {

            Notification.success({
                title: '添加准入策略成功',
            });
            props.close();
            props.success && props.success();

        }).catch((err) => {
            console.error(err);
            Notification.error({
                title: '添加准入策略失败',
                content: err.message,
            });
        }).finally(() => {
            setLoading(false);
        })
    }
    return <>
    <Modal
            title="添加准入策略"
            visible={true}
            onOk={handleSubmit}
            onCancel={props.close}
            width={1200}
            okButtonProps={{ loading }}
            closeOnEsc={true}
            maskClosable={false}
        >
    
        {!expressionsTemplate && <Banner title="未设置表达式模板">
            <Paragraph>
                请前往 <a href={`${BASE_PATH}/settings/schema`} target="_blank">设置/数据设置/属性/网络准入策略</a>处添加表达式模板 后再添加准入策略
            </Paragraph>
        </Banner>}
        <Skeleton loading={!expressionsTemplate || !attrTreeData || !dataSources} placeholder={
            <>
                <Row gutter={20}>
                    <Col span={9}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />

                    </Col>
                    <Col span={9}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />

                    </Col>
                    <Col span={6}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />

                    </Col>
                </Row>

                <Row>
                    <Col span={24}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />

                    </Col>
                </Row>


                <Row>
                    <Col span={24}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 50, marginBottom: 0 }} />

                    </Col>
                </Row>

                <Skeleton.Image style={{ height: 105, marginBottom: 20 }} />

                <Skeleton.Image style={{ height: 100, marginBottom: 20 }} />
            </>
        }>
            <Form
                getFormApi={setFormApi}
                initValues={{
                    disabled: 0
                }}
                onValueChange={(values, changedValue) => {
                    if (changedValue.hasOwnProperty('alias')) {
                        formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                    }
                }}
            >
                <Row gutter={20}>
                    <Col span={9}>
                        <Input field='alias' placeholder={'请输入名称'} label='名称' trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={9}>
                        <Input field='name'
                            placeholder={'请输入编码'}
                            label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '编码不能为空';
                                }
                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '编码不能以-开头'
                                }
                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "编码只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            required />
                    </Col>
                    <Col span={6}>
                        <RadioGroup field="disabled" label="状态">
                            <Radio checked value={0}>启用</Radio>
                            <Radio value={1}>禁用</Radio>
                        </RadioGroup>
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <TextArea field="description" autosize rows={1} placeholder={'请输入描述'} label="策略描述" />
                    </Col>
                </Row>
                <Divider className="mb10" />

                <Title heading={6} type="tertiary" className="mb10">策略规则</Title>

                {isExpressionsEdit ?
                    <ExpressionsEdit
                        onToggle={() => setIsExpressionsEdit(false)}
                        conditions={admissionItems} operators={operators}
                        onchange={(conditions, operators) => {
                            setAdmissionItems(conditions);
                            setOperators(operators);
                        }}
                    ></ExpressionsEdit>
                    : <div style={{ minHeight: '113px' }} className="mb20">
                        {expressionsTemplate && attrTreeData && dataSources &&
                            <>
                                <Row className={styles.tableTitle} >
                                    <Col span={1}>序号</Col>
                                    <Col span={21}>
                                        <Row>
                                            <Col span={3}>
                                                属性 <Popover content={<div className='p10'><a className='link-external' target='_blank' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>编辑属性<IconArrowUpRight />
                                                </a></div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover>
                                            </Col>
                                            <Col span={3}>
                                                操作
                                            </Col>
                                            <Col span={18}>
                                                值
                                            </Col>
                                        </Row>
                                    </Col>
                                    <Col span={2} className='btn-right-col'></Col>
                                </Row>
                                <ExpressionCombo
                                    onAdd={(index: number) => {
                                        let items = [...admissionItems];
                                        let ac: AdmissionCondition = new AdmissionCondition({
                                            name: '',
                                            op: '',
                                            value: new AdmissionConditionValue({
                                                inputData: new Value({
                                                    kind: { value: "", case: "stringValue" }
                                                }),
                                                dynamic: false,
                                                dynamicDataSource: '',
                                                dynamicDataField: '',
                                                dynamicConditions: []
                                            })
                                        });
                                        items.splice(index + 1, 0, ac);
                                        setAdmissionItems(items);
                                    }}
                                    onDel={(index) => {
                                        let items = [...admissionItems];
                                        items.splice(index, 1);
                                        setAdmissionItems(items);
                                    }}
                                    operators={operators}
                                    setOperators={setOperators}
                                >
                                    {admissionItems.map((item, index) => {
                                        return <AdmissionConditionRow
                                            key={index}
                                            index={index}
                                            item={item}
                                            validateItemFlag={validateItemFlag}
                                            attrTreeData={attrTreeData}
                                            expressionsTemplate={expressionsTemplate}
                                            admissionItems={admissionItems}
                                            setAdmissionItems={setAdmissionItems}
                                            dataSources={dataSources}
                                        ></AdmissionConditionRow>
                                    })}
                                </ExpressionCombo>
                                <div style={{ height: 20 }}></div>
                                <ExpressionsPreview onToggle={() => setIsExpressionsEdit(true)} conditions={admissionItems} operators={operators}></ExpressionsPreview>
                            </>
                        }
                        {itemsError && <Typography.Paragraph type="danger" className="mt10"><Space><IconAlertCircle />请至少添加一条规则</Space></Typography.Paragraph>}
                    </div>}
                <Divider className="mb10" />
                <AdmissionScopes
                    value={scopes}
                    onChange={(value) => {
                        setScopes(value);
                    }}
                ></AdmissionScopes>
            </Form>
        </Skeleton>

        </Modal></>
}

export default Index;
