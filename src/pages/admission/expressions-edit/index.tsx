import { FC, useEffect, useState } from 'react';
import { AdmissionCondition } from "../admission-policy";
import CodeEditor from '@/components/code-editor';
import { getConditionExpression } from '../util/cal-condition-expression';

import { Typography, Row, Col, Button, Space } from "@douyinfe/semi-ui";
import { expressionToCondition } from '../util/expression-to-condition';

const { Text } = Typography;

interface Props {
    conditions: AdmissionCondition[];
    operators: string[];
    onToggle: () => void;
    onchange: (conditions: AdmissionCondition[], operators: string[]) => void;
}

const Index: FC<Props> = (props) => {
    const { conditions, operators, onToggle } = props;

    const [code, setCode] = useState<string>(getConditionExpression(conditions, operators));



    return (
        <div>
            <Row className='mb10'>
                <Col span={12}><Text type='tertiary'>表达式编辑</Text></Col>
                <Col span={12} className='btn-right-col'>
                    <Space>
                        <Button onClick={onToggle}>使用表达式生成器</Button>
                        <Button theme='solid' onClick={() => {
                            const [conditions, operators] = expressionToCondition(code);
                            props.onchange(conditions, operators);
                            onToggle();
                        }}>应用</Button>
                    </Space>
                </Col>
            </Row>
            <CodeEditor
                height='400px'
                value={code}
                onChange={(val) => {
                    setCode(val);
                }}
                language='lua' />
            <Row>
                <Col span={42} className='btn-right-col'>
                </Col>

            </Row>
        </div>
    )
}

export default Index;
