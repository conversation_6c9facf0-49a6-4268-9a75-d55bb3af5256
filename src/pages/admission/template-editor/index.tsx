import React, { useState, useRef, useContext, useEffect } from 'react'
import { Modal, Notification } from "@douyinfe/semi-ui";
import styles from './index.module.scss'
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { flylayerClient } from '@/services/core';

import CodeEditor from '@/components/code-editor';

import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

const Index: React.FC<{
    close: () => void,
    success: (template: string) => void,
}> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [flynet, setFlynet] = useState<Flynet>(); // flynetGeneral.flynet
    const [loading, setLoading] = useState(false);

    const [templateLoaded, setTemplateLoaded] = useState(false);

    const handleOk = () => {

        try {
            JSON.parse(template);

        } catch (e) {
            Notification.error({
                title: '策略组模板不是合法的 JSON',
                content: (e as any).message,
                duration: 6000,
            });
            return;
        }


        setLoading(true);
        flylayerClient.saveAclGroupTemplate({
            flynetId: flynet?.id,
            aclGroupTemplate: template,
        }).then(res => {

            props.success(template);
            Notification.success({
                content: '保存成功',
            });
        }).catch(err => {
            Notification.error({
                content: '保存失败',
            });
        }).finally(() => {
            setLoading(false);
        })
    }


    const [template, setTemplate] = useState<string>('');

    const queryFlynet = async () => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet);
            if (res && res.flynet && res.flynet.aclGroupTemplate) {
                setTemplateLoaded(true);
                if (res.flynet.aclGroupTemplate) {
                    setTemplate(res.flynet.aclGroupTemplate);
                }
            }
        }).finally(() => {
            setTemplateLoaded(true);
        });


    }

    useEffect(() => {
        queryFlynet();
    }, [])

    return <>
        <Modal
            title="编辑策略组模板"
            visible={true}
            onCancel={props.close}
            onOk={handleOk}
            width={1080}
            height={620}
            okButtonProps={{ loading }}
            closeOnEsc={true}
            maskClosable={false}
        >
            {templateLoaded && <CodeEditor value={template} onChange={(t) => {
                setTemplate(t)
            }}></CodeEditor>}
        </Modal>
    </>
}

export default Index;