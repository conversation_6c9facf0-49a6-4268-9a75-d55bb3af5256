import React, { useState, useEffect } from "react";
import { Typo<PERSON>, Row, Col, Button, Tag, TreeSelect, Select, Space } from "@douyinfe/semi-ui";
import { AdmissionCondition, AdmissionConditionValue } from "../admission-policy";
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { Value } from '@bufbuild/protobuf';
import ItemValueEditor from "../item-value-editor";
import { DataSource } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/datasource_pb"
import { IconAlertCircle } from '@douyinfe/semi-icons';

import { getAttributeNode, getDataType } from "@/utils/expression";
import { AttributeTemplate } from '@/interface/attribute-template';
import styles from './index.module.scss'

const { Paragraph } = Typography;

interface Props {
    index: number;
    item: AdmissionCondition;
    attrTreeData: TreeNodeData[];
    expressionsTemplate: AttributeTemplate;
    admissionItems: AdmissionCondition[];
    setAdmissionItems: (items: AdmissionCondition[]) => void;
    dataSources: DataSource[];
    validateItemFlag: boolean;
}
const Index: React.FC<Props> = (props) => {

    const getNode = (expressionsTemplate: AttributeTemplate, attr: string,) => {
        let nodes = Object.keys(expressionsTemplate.properties);
        for (let i = 0; i < nodes.length; i++) {
            let node = getAttributeNode(attr, nodes[i], expressionsTemplate.properties[nodes[i]])
            if (node) {
                return node;
            }
        }
        return undefined;
    }

    const isOneOfChar = 'in';

    const { index, attrTreeData, expressionsTemplate, admissionItems, setAdmissionItems, dataSources } = props;

    const [item, setItem] = useState(props.item);

    let initNode = getNode(expressionsTemplate, props.item.name);

    const [inputDataType, setInputDataType] = useState<string>(initNode ? getDataType(initNode) : '');
    const [enumValues, setEnumValues] = useState<string[]>(initNode ? initNode.enum || [] : []);

    useEffect(() => {
        setItem(props.item);
    }, [props.item])

    const [validateItemFlag, setValidateItemFlag] = useState(props.validateItemFlag);
    useEffect(() => {
        setValidateItemFlag(props.validateItemFlag);
        if (validateItemFlag) {
            if (!item.name) {
                setNameError(true);
            } else {
                setNameError(false);
            }
            if (!item.op) {
                setOpError(true);
            } else {
                setOpError(false);
            }

        }
    }, [props.validateItemFlag])

    const [nameError, setNameError] = useState(false);
    const [opError, setOpError] = useState(false);

    {/**
    json schema 属性类型：
    type: number, string, boolean, object, array
    
    枚举属性enum：
        当有枚举属性时，type为Array，界面为下拉多选，否则为TagInput
        当有枚举属性时，type为String，界面为下拉单选，否则为Input
    format: date-time
    当format为date-time,同时type为object，界面为时间选择器
    操作符逻辑：
    大于、小于、大于等于、小于等于： type=="number" || type="object" && format == "date-time"
    等于、不等于： type!="array"
    包含(模糊匹配)、包含(精确匹配)： type=="array" || type=="string"
*/}
    return <Row key={index}>
        <Col span={1}>
            <Tag style={{ height: 32, width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'} >{index + 1}</Tag>
        </Col>
        <Col span={3}>
            <TreeSelect
                expandAll
                value={item.name}
                treeData={attrTreeData}
                placeholder='请选择'
                onChange={(val) => {
                    if (!val) {
                        setNameError(true);
                    } else {
                        setNameError(false);
                    }
                    const attr = val as string;

                    let _inputDataType = '';
                    let _enumValues: string[] = [];

                    let node = getNode(expressionsTemplate, attr);
                    if (node) {
                        _inputDataType = getDataType(node);
                        if (node.enum) {
                            _enumValues = node.enum;
                        }
                    }

                    setInputDataType(_inputDataType);
                    setEnumValues(_enumValues);

                    let items = [...admissionItems];

                    items[index].name = attr;
                    items[index].op = '';
                    items[index].value = new AdmissionConditionValue({
                        inputData: new Value({
                            kind: { value: "", case: "stringValue" }
                        }),
                        dynamic: false,
                        dynamicDataSource: '',
                        dynamicDataField: '',
                        dynamicConditions: []
                    })
                    setAdmissionItems(items);
                }}
                style={{ width: 115 }}
                dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}></TreeSelect>
            {nameError && <Paragraph type="danger"><Space><IconAlertCircle />属性不能为空</Space></Paragraph>}
        </Col>
        <Col span={3}>
            <Select
                value={item.op}
                placeholder='请选择'
                onChange={(val) => {
                    if (!val) {
                        setOpError(true);
                    } else {
                        setOpError(false);
                    }
                    let items = [...admissionItems];
                    items[index].op = val as string;

                    items[index].value = new AdmissionConditionValue({
                        inputData: new Value({
                            kind: { value: "", case: "stringValue" }
                        }),
                        dynamic: false,
                        dynamicDataSource: '',
                        dynamicDataField: '',
                        dynamicConditions: []
                    })

                    setAdmissionItems(items);
                }}
                style={{ width: 115 }}
                dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}>
                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'>'}>大于</Select.Option>}
                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'<'}>小于</Select.Option>}
                {inputDataType != 'array' && <Select.Option value={'=='}>等于</Select.Option>}
                {inputDataType != 'array' && <Select.Option value={'!='}>不等于</Select.Option>}
                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'>='}>大于等于</Select.Option>}
                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'<='}>小于等于</Select.Option>}
                {(inputDataType == 'array' || inputDataType == 'string') && <Select.Option value={'contains'}>包含(模糊匹配)</Select.Option>}
                {(inputDataType == 'array' || inputDataType == 'string') && <Select.Option value={isOneOfChar}>包含(精确匹配)</Select.Option>}
            </Select>
            {opError && <Paragraph type="danger"><Space><IconAlertCircle />操作不能为空</Space></Paragraph>}
        </Col>
        <Col span={17}>
            {item.value && dataSources && <ItemValueEditor
                validateItemFlag={validateItemFlag}
                value={item.value}
                op={item.op}
                inputDataType={inputDataType}
                enumValues={enumValues}
                onChange={(val: AdmissionConditionValue) => {
                    let items = [...admissionItems];
                    items[index].value = val;
                    setAdmissionItems(items);
                }}
                dataSources={dataSources}
                onError={() => {
                    console.error('item value editor error')
                }}
            ></ItemValueEditor>}

        </Col>
        <Col span={1} className='btn-right-col'>
        </Col>
        
    </Row>
}

export default Index;
