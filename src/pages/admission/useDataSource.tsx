import { useState, useEffect, useContext } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { DataSource } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/datasource_pb"
import { flylayerClient } from '@/services/core';
import { Notification } from '@douyinfe/semi-ui';

const useDataSource = () => {
    const flynet = useContext(FlynetGeneralContext);

    // 数据源列表
    const [dataSources, setDataSources] = useState<Array<DataSource>>();

    // 数据源加载状态
    const [dataSourceloading, setDataSourceLoading] = useState(true);
    // 加载数据
    const query = () => {
        setDataSourceLoading(true);

        flylayerClient.listDataSources({
            flynetId: flynet.id
        }).then(res => {
            setDataSources(res.dataSources);
        }, err => {
            Notification.error({
                title: '获取数据源列表失败',
                content: err.message
            });
        }).finally(() => {
            setDataSourceLoading(false);
        })

    }

    useEffect(() => {
        query()
    }, []);


    return {
        dataSourceloading,
        dataSources,
    }
}

export default useDataSource;