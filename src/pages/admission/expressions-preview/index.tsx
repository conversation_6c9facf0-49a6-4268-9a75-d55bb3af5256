import { FC, useEffect, useState } from 'react';
import { AdmissionCondition } from "../admission-policy";
import { Typography, Row, Col, Button } from "@douyinfe/semi-ui";

import CodeViewer from '@/components/code-viewer';
import { getConditionExpression } from '../util/cal-condition-expression';

const { Text } = Typography;

interface Props {
    conditions: AdmissionCondition[];
    operators: string[];
    onToggle: () => void;
}

const Index: FC<Props> = (props) => {
    const { conditions, operators, onToggle } = props;

    const [code, setCode] = useState<string>(getConditionExpression(conditions, operators));

    useEffect(() => {
        setCode(getConditionExpression(conditions, operators));
    }, [conditions, operators]);

    return (
        <div>
            <Row className='mb10'>
                <Col span={12}><Text type='tertiary'>表达式预览</Text></Col>
                <Col span={12} className='btn-right-col'>
                    <Button onClick={onToggle} >编辑表达式</Button>
                </Col>
            </Row>
            <CodeViewer height='200px' value={code} language='lua' />
        </div>
    )
}

export default Index;
