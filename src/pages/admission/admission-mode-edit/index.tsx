import React, { useState, useRef, useContext, useEffect } from 'react'
import { Typography, Modal, Radio, RadioGroup, Notification } from '@douyinfe/semi-ui';

import { RadioChangeEvent } from '@douyinfe/semi-ui/lib/es/radio';
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { flylayerClient } from '@/services/core';
import { AdmissionMode } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/admission_pb";

const { Paragraph, Title } = Typography;
const Index: React.FC<{
    close: () => void,
    success: () => void,
}> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [loading, setLoading] = useState(false);
    const [saveLoading, setSaveLoading] = useState(false);

    const handleOk = () => {
        setSaveLoading(true);

        flylayerClient.saveAdmissionMode({
            flynetId: flynetGeneral.id,
            admissionMode: admissionMode
        }).then(res => {
            props.success();
            Notification.success({
                content: '保存成功',
            });
        }).catch(err => {
            Notification.error({
                content: '保存失败',
            });
        }).finally(() => {
            setLoading(false);
        })
    }

    const [admissionModeLoaded, setAdmissionModeLoaded] = useState(false);
    const [admissionMode, setAdmissionMode] = useState<AdmissionMode>();

    const queryFlynet = async () => {
        getFlynet(flynetGeneral.id).then(res => {
            if (res && res.flynet) {
                setAdmissionModeLoaded(true);
                setAdmissionMode(res.flynet.admissionMode);
            }
        }).finally(() => {
            setAdmissionModeLoaded(true);
        });
    }

    useEffect(() => {
        queryFlynet();
    }, [])
    const onChange = (e: RadioChangeEvent) => {
        setAdmissionMode(e.target.value);
    };
    return <><Modal
        title="编辑网络准入模式"
        visible={true}
        onCancel={props.close}
        onOk={handleOk}
        width={500}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'>
        请选择用于控制网络接入的策略
        </Paragraph>
        <RadioGroup direction="vertical" onChange={onChange} value={admissionMode} >
                <Radio value={AdmissionMode.BLACK}
                    checked={admissionMode === AdmissionMode.BLACK}
                    extra="黑名单模式下，默认允许接入网络，如命中任意一条规则并通过，则禁止接入网络" aria-label="黑名单" name="role">
                    黑名单
                </Radio>
                <Radio value={AdmissionMode.WHITE}
                    checked={admissionMode === AdmissionMode.WHITE}
                    extra="白名单模式下，默认禁止接入网络，如命中任意一条规则并通过，则允许接入网络" aria-label="白名单" name="role">
                    白名单
                </Radio>
            </RadioGroup>
    </Modal></>
}

export default Index;
