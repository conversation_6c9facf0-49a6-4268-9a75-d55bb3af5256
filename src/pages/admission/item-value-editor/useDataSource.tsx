import React, { useState, useEffect, useContext } from 'react'
import { DataSource } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/datasource_pb"
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { AttributeTemplate } from '@/interface/attribute-template';


const useDataSource = (dataSources: Array<DataSource>) => {

    // 选中的数据源
    const [selectedDataSource, setSelectedDataSource] = useState<DataSource>();

    const [attrTreeData, setAttrTreeData] = useState<TreeNodeData[]>();

    const [expressionsTemplate, setExpressionsTemplate] = useState<AttributeTemplate>();


    const buildTreeData = (key: string, expressionsTemplate: AttributeTemplate) => {
        let treeData: TreeNodeData = {
            label: expressionsTemplate.title,
            value: key,
            key: key,
            children: []
        }
        if (!expressionsTemplate.properties) {
            return treeData;
        }
        Object.keys(expressionsTemplate.properties).forEach((childKey) => {
            let child = buildTreeData(key + '.' + childKey, expressionsTemplate.properties[childKey]);
            treeData.children?.push(child);
        })

        return treeData;
    }

    const doSelectDataSource = (name: string) => {

        dataSources.forEach(item => {
            if (item.name == name) {
                setSelectedDataSource(item);
                
                let jsonStr = item.value?.toJsonString();
                if(!jsonStr) {
                    return;
                }
                
                const expressionsStr = (JSON.parse(jsonStr). admission) as string;

                const expressionsAttrs: AttributeTemplate = JSON.parse(expressionsStr);
                
                if (expressionsAttrs) {

                    setExpressionsTemplate(expressionsAttrs);

                    const initTreeData: TreeNodeData[] = [];
                    Object.keys(expressionsAttrs.properties).forEach((key) => {
                        let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
                        initTreeData.push(treeDataItem);
                    })
                    setAttrTreeData(initTreeData);
                }
            }
        })
    };

    return {
        selectedDataSource,
        setSelectedDataSource,
        doSelectDataSource,
        attrTreeData,
        expressionsTemplate,
    }
}

export default useDataSource;