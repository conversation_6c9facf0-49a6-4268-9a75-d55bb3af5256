import React, { useState, useEffect } from "react";
import { AdmissionConditionValue, AdmissionCondition } from "../admission-policy";
import { Typography, TagInput, Row, Col, Button, Switch, Select, Space, DatePicker, Input, TreeSelect, InputNumber, Popover } from "@douyinfe/semi-ui";
import { IconPlus, IconAlertCircle, IconHelpCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import { Value, ListValue } from '@bufbuild/protobuf';
import { DataSource } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/datasource_pb"
import useDataSource from "./useDataSource";
import styles from './index.module.scss'
import DynamicDataItemEditor from "./dynamic-data-item-editor";
import { BASE_PATH } from "@/constants/router";

interface Props {
    op: string,            // 操作符
    inputDataType: string, // 输入数据类型
    enumValues: string[],  // 枚举值
    value: AdmissionConditionValue,
    onChange: ((val: AdmissionConditionValue) => void),
    onError: () => void,
    dataSources: Array<DataSource>,
    validateItemFlag: boolean;
}
const { Text, Paragraph } = Typography;

const Index: React.FC<Props> = (props) => {
    const {
        doSelectDataSource,
        attrTreeData,
        expressionsTemplate
    } = useDataSource(props.dataSources);

    const { onChange, onError } = props;
    const isOneOfChar = 'in';
    const [value, setValue] = useState(props.value);
    useEffect(() => {
        setValue(props.value);
        if (props.value.dynamicDataSource) {
            doSelectDataSource(props.value.dynamicDataSource)
        }
    }, [props.value]);

    const [op, setOp] = useState(props.op);
    useEffect(() => {
        setOp(props.op);
    }, [props.op]);


    const [inputDataType, setInputDataType] = useState(props.inputDataType);
    useEffect(() => {
        setInputDataType(props.inputDataType);
    }, [props.inputDataType]);
    const [enumValues, setEnumValues] = useState(props.enumValues);
    useEffect(() => {
        setEnumValues(props.enumValues);
    }, [props.enumValues]);

    const handleHasDynamicDataChange = (checked: boolean) => {
        let newValue = {
            ...value,
            dynamic: checked,
        }
        props.onChange(newValue as any);
    }

    const getArrInputValue = (val: Value) => {
        if (val.kind.case == 'stringValue') {
            if (val.kind.value == '') {
                return [];
            }
            return val.kind.value.split(',');
        } else if (val.kind.case == 'listValue') {
            return val.kind.value.values.map((item) => {
                return item.kind.value as string;
            });
        }
        return [];
    }
    const getBoolInputValue = (val: Value) => {
        if (val.kind.case == 'stringValue') {
            if (val.kind.value == 'true') {
                return true;
            }
        }
        return false;
    }

    // 输入值
    const [inputVaule, setInputValue] = useState(value.inputData?.kind.value as string);
    useEffect(() => {
        setInputValue(value.inputData?.kind.value as string);
    }, [value.inputData?.kind.value])

    const handleInputValueChange = (val: string) => {
        let inputData = new Value({
            kind: {
                case: 'stringValue',
                value: val as any,
            },
        })

        let newValue = {
            ...value,
            inputData: inputData,
        }
        props.onChange(newValue as any);
    }


    const [validateItemFlag, setValidateItemFlag] = useState(props.validateItemFlag);
    useEffect(() => {
        setValidateItemFlag(props.validateItemFlag);
        if (validateItemFlag) {
            if (value.dynamic) {
                if (!value.dynamicDataSource) {
                    setDatasourceError(true);
                }
                if (!value.dynamicDataField) {
                    setDynamicDataFieldError(true);
                }
                if (value.dynamicConditions.length == 0) {
                    setDynamicConditionsError(true);
                }
            } else {
                if (!value.inputData || value.inputData.kind.case == undefined || !value.inputData.kind.value || value.inputData.kind.value == undefined || value.inputData.kind.value == '') {
                    setValueError(true);
                }
            }
        }
    }, [props.validateItemFlag])

    const [valueError, setValueError] = useState(false);
    const [datasourceError, setDatasourceError] = useState(false);
    const [dynamicDataFieldError, setDynamicDataFieldError] = useState(false);
    const [dynamicConditionsError, setDynamicConditionsError] = useState(false);

    return <div>
        <div className={styles.outerRow}>

            <div className={styles.operator}>
                <Space>
                    &nbsp;
                    <Text type='tertiary' >动态数据</Text>
                    <Switch checked={value.dynamic} onChange={handleHasDynamicDataChange}></Switch>
                </Space>
            </div>
            <div className={styles.content}>

                {value.dynamic ?
                    <Space style={{ alignItems: 'normal' }}>
                        <div style={{}}>
                            <Space>
                                <Text type='tertiary' >数据源 <Popover content={<div className='p10'><a className='link-external' target='_blank' href={`${BASE_PATH}/settings/datasource`} onClick={(e) => { e.stopPropagation() }}>编辑数据源与字段<IconArrowUpRight />
                                </a></div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></Text>
                                <Select value={value.dynamicDataSource}
                                    onChange={(val) => {
                                        let newValue = {
                                            ...value,
                                            dynamicDataSource: val,
                                        }

                                        if (val == undefined || val == '') {
                                            setDatasourceError(true);
                                        } else {
                                            setDatasourceError(false);
                                        }

                                        doSelectDataSource(val as string);
                                        props.onChange(newValue as any);
                                    }}
                                    placeholder='请选择' style={{ width: 150 }}>
                                    {props.dataSources.map(item => {
                                        return <Select.Option key={item.name} value={item.name}>{item.alias}</Select.Option>
                                    })}
                                </Select>
                            </Space>
                            {datasourceError && <Paragraph type="danger"><Space><IconAlertCircle />数据源不能为空</Space></Paragraph>}
                        </div>
                        <div style={{}}>
                            <Space>
                                <Text type='tertiary' >字段</Text>
                                <TreeSelect
                                    expandAll
                                    value={value.dynamicDataField}
                                    treeData={attrTreeData}
                                    placeholder='请选择'
                                    style={{ width: 150 }}
                                    onChange={(val) => {
                                        if (val == undefined || val == '') {
                                            setDynamicDataFieldError(true);
                                        } else {
                                            setDynamicDataFieldError(false);
                                        }
                                        let newValue = {
                                            ...value,
                                            dynamicDataField: val,
                                        }
                                        props.onChange(newValue as any);
                                    }}
                                    dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}
                                ></TreeSelect>
                            </Space>
                            {dynamicDataFieldError && <Paragraph type="danger"><Space><IconAlertCircle />字段不能为空</Space></Paragraph>}
                        </div>



                        {value.dynamic && value.dynamicDataField && value.dynamicDataSource &&
                            <>
                                <Popover
                                    content={
                                        <div className="p10" style={{ width: 760 }}>
                                            <Row className='tableTitle' >
                                                <Col span={2}>序号</Col>
                                                <Col span={4}>属性</Col>
                                                <Col span={4}>操作</Col>
                                                <Col span={12}>值</Col>
                                                <Col span={2} className='btn-right-col'><Button onClick={() => {
                                                    let items = [...value.dynamicConditions];
                                                    let cond = new AdmissionCondition({
                                                        name: '',
                                                        op: '',
                                                        value: new AdmissionConditionValue({
                                                            inputData: new Value({
                                                                kind: {
                                                                    case: 'stringValue',
                                                                    value: '',
                                                                },
                                                            }),
                                                            dynamic: false,
                                                            dynamicDataSource: '',
                                                            dynamicDataField: '',
                                                            dynamicConditions: [],
                                                        }),
                                                    });
                                                    items.push(cond);
                                                    let newValue = {
                                                        ...value,
                                                        dynamicConditions: items,
                                                    }
                                                    setDynamicConditionsError(false);
                                                    props.onChange(newValue as any);
                                                }} icon={<IconPlus />} /></Col>
                                            </Row>
                                            {value.dynamicConditions.map((item, index) => {
                                                if (!attrTreeData) {
                                                    return null;
                                                }
                                                if (!expressionsTemplate) {
                                                    return null;
                                                }

                                                return <DynamicDataItemEditor
                                                    attrTreeData={attrTreeData}
                                                    key={index}
                                                    index={index}
                                                    value={item}
                                                    validateItemFlag={validateItemFlag}
                                                    expressionsTemplate={expressionsTemplate}
                                                    onDel={() => {
                                                        let items = [...value.dynamicConditions];
                                                        items.splice(index, 1);
                                                        let newValue = {
                                                            ...value,
                                                            dynamicConditions: items,
                                                        }
                                                        props.onChange(newValue as any);
                                                    }}
                                                    onChange={(val) => {
                                                        let items = [...value.dynamicConditions];
                                                        items[index] = val;
                                                        let newValue = {
                                                            ...value,
                                                            dynamicConditions: items,
                                                        }
                                                        props.onChange(newValue as any);
                                                    }}
                                                    onError={() => {
                                                        onError();
                                                    }}
                                                ></DynamicDataItemEditor>
                                            })}
                                        </div>
                                    }>
                                    <Button>维护动态数据</Button>
                                </Popover>
                            </>
                        }
                    </Space>
                    :

                    <div>
                        {op == isOneOfChar || op == 'contains' || inputDataType == 'array' ? <>  {
                            enumValues.length > 0 ? <Select
                                placeholder='请选择'
                                value={
                                    value.inputData ? getArrInputValue(value.inputData) : []
                                }
                                optionList={enumValues.map((val) => {
                                    return {
                                        label: val,
                                        value: val
                                    }
                                })}
                                multiple
                                onChange={(val) => {
                                    let arr = val as string[]
                                    if (!arr || arr.length == 0) {
                                        props.onError();
                                        setValueError(true);
                                        return;
                                    } else {
                                        setValueError(false);
                                    }

                                    let inputData = new Value({
                                        kind: {
                                            case: 'listValue',
                                            value: {
                                                values: arr.map((item) => {
                                                    return new Value({
                                                        kind: {
                                                            case: 'stringValue',
                                                            value: item,
                                                        },
                                                    })
                                                })
                                            },
                                        },
                                    })
                                    let newValue = {
                                        ...value,
                                        inputData: inputData,
                                    }
                                    props.onChange(newValue as any);
                                }}
                                style={{ width: '100%' }}></Select> : <TagInput
                                    placeholder="请输入"
                                    value={
                                        value.inputData ? getArrInputValue(value.inputData) : []
                                    }
                                    onChange={(val) => {

                                        let arr = val as string[]
                                        if (!arr || arr.length == 0) {
                                            props.onError();
                                            setValueError(true);
                                            return;
                                        } else {
                                            setValueError(false);
                                        }
                                        const listValue = new ListValue({
                                            values: arr.map((item) => {
                                                return new Value({
                                                    kind: {
                                                        case: 'stringValue',
                                                        value: item,
                                                    },
                                                })
                                            })
                                        });
                                        let inputData = new Value({
                                            kind: {
                                                case: 'listValue',
                                                value: listValue,
                                            },
                                        })
                                        let newValue = {
                                            ...value,
                                            inputData: inputData,

                                        }
                                        props.onChange(newValue as any);
                                    }}
                                    addOnBlur
                                    style={{ width: '100%' }}
                                ></TagInput>
                        }


                        </> : inputDataType == 'boolean' ? <Switch
                            checked={value.inputData?.kind.value as boolean}
                            checkedText={"是"}
                            uncheckedText={"否"}
                            onChange={(val) => {
                                let inputData = new Value({
                                    kind: {
                                        case: 'boolValue',
                                        value: val,
                                    },
                                })
                                let newValue = {
                                    ...value,
                                    inputData: inputData,
                                }
                                props.onChange(newValue as any);
                            }}
                            style={{ marginTop: 4 }}
                        ></Switch> : inputDataType == 'datetime' ?
                            <DatePicker
                                placeholder={'请选择'}
                                type='dateTime'
                                value={value.inputData?.kind.value as string}
                                format='yyyy-MM-dd HH:mm:ss'
                                onChange={(val) => {

                                    if (val) {

                                        let date = new Date(val as string);
                                        let datestr = date.toISOString();

                                        let inputData = new Value({
                                            kind: {
                                                case: 'stringValue',
                                                value: datestr,
                                            },
                                        })
                                        let newValue = {
                                            ...value,
                                            inputData: inputData,
                                        }
                                        props.onChange(newValue as any);
                                        setValueError(false);
                                    } else {
                                        props.onError();
                                        setValueError(true);
                                    }
                                }}
                                style={{ marginRight: 0 }}
                            ></DatePicker> : enumValues.length > 0 ? <Select
                                placeholder='请选择'
                                value={value.inputData?.kind.value as string}
                                optionList={enumValues.map((val) => {
                                    return {
                                        label: val,
                                        value: val
                                    }
                                })}
                                onChange={(val) => {
                                    if (!val) {
                                        props.onError();
                                        setValueError(true);
                                        return;
                                    }
                                    let inputData = new Value({
                                        kind: {
                                            case: 'stringValue',
                                            value: val as any,
                                        },
                                    })

                                    let newValue = {
                                        ...value,
                                        inputData: inputData,
                                    }
                                    props.onChange(newValue as any);
                                    setValueError(false);
                                }}
                                style={{ width: '100%' }}></Select> :
                                inputDataType == 'number' ? <InputNumber
                                    placeholder={'请输入'}
                                    value={value.inputData?.kind.value as number}
                                    onChange={(val) => {
                                        if (val != undefined) {
                                            let inputData = new Value({
                                                kind: {
                                                    case: 'numberValue',
                                                    value: val as number,
                                                },
                                            })
                                            let newValue = {
                                                ...value,
                                                inputData: inputData,
                                            }
                                            props.onChange(newValue as any);
                                            setValueError(false);
                                        } else {
                                            props.onError();
                                            setValueError(true);
                                        }
                                    }} style={{ width: '100%' }}
                                ></InputNumber> :
                                    <Input value={inputVaule}
                                        placeholder={'请输入'}
                                        onChange={(val) => {
                                            if (val) {
                                                setValueError(false);
                                            } else {
                                                setValueError(true);
                                            }
                                            setInputValue(val)
                                        }}
                                        onEnterPress={() => handleInputValueChange(inputVaule)}
                                        onBlur={() => handleInputValueChange(inputVaule)}
                                        style={{ width: '100%' }}></Input>}
                        {valueError && <Paragraph type="danger"><Space><IconAlertCircle />值不能为空</Space></Paragraph>}
                    </div>
                }
                {dynamicConditionsError && <Paragraph type="danger"><Space><IconAlertCircle />动态数据条件不能为空</Space></Paragraph>}

            </div>
        </div>






    </div>;
}

export default Index;

