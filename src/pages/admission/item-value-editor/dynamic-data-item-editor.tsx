import React, { useState, useEffect } from "react";
import { Typography, TagInput, DatePicker, Input, Row, Col, Button, Tag, TreeSelect, Switch, Select, InputNumber, Space } from "@douyinfe/semi-ui";
import { AdmissionCondition, AdmissionConditionValue } from "../admission-policy";
import { AttributeTemplate } from '@/interface/attribute-template';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { IconMinusCircle, IconAlertCircle } from '@douyinfe/semi-icons';
import { getAttributeNode, getDataType } from "@/utils/expression";
import { Value } from '@bufbuild/protobuf';

import styles from './index.module.scss'
import { set } from "lodash";

const { Paragraph } = Typography;

interface Props {
    index: number,
    value: AdmissionCondition,
    onChange: ((val: AdmissionCondition) => void),
    onError: () => void,
    onDel: () => void,
    attrTreeData: TreeNodeData[],
    expressionsTemplate: AttributeTemplate,
    validateItemFlag: boolean
}

const Index: React.FC<Props> = (props) => {


    const getNode = (expressionsTemplate: AttributeTemplate, attr: string,) => {
        let nodes = Object.keys(expressionsTemplate.properties);
        for (let i = 0; i < nodes.length; i++) {
            let node = getAttributeNode(attr, nodes[i], expressionsTemplate.properties[nodes[i]])
            if (node) {
                return node;
            }
        }
        return undefined;
    }

    const isOneOfChar = 'in';
    const { onChange, onError } = props;

    let initNode = getNode(props.expressionsTemplate, props.value.name);


    const [inputDataType, setInputDataType] = useState<string>(initNode ? getDataType(initNode) : '');
    const [enumValues, setEnumValues] = useState<string[]>(initNode ? initNode.enum || [] : []);

    const [value, setValue] = useState(props.value);
    useEffect(() => {
        setValue(props.value);
    }, [props.value]);


    const getArrInputValue = (val: Value) => {
        if (val.kind.case == 'stringValue') {
            if (val.kind.value == '') {
                return [];
            }
            return val.kind.value.split(',');
        } else if (val.kind.case == 'listValue') {
            return val.kind.value.values.map((item) => {
                return item.kind.value as string;
            });
        }
        return [];
    }

    // 输入值
    const [inputVaule, setInputValue] = useState(value.value?.inputData?.kind.value as string);
    useEffect(() => {
        setInputValue(value.value?.inputData?.kind.value as string);
    }, [value.value?.inputData?.kind.value]);
    const handleInputValueChange = (val: string) => {

        if (val) {
            let inputData = new Value({
                kind: {
                    case: 'stringValue',
                    value: val as any,
                },
            })

            let newValue = {
                ...value,
                value: {
                    inputData: inputData,
                    dynamic: false,
                    dynamicDataSource: '',
                    dynamicDataField: '',
                    dynamicConditions: []
                },
            }
            props.onChange(newValue as any);
        } else {
            props.onError();
        }
    }


    const [validateItemFlag, setValidateItemFlag] = useState(props.validateItemFlag);
    useEffect(() => {
        setValidateItemFlag(props.validateItemFlag);
        if (validateItemFlag) {
            if (value.name == '') {
                setNameError(true);
            } else {
                setNameError(false);
            }
            if (value.op == '') {
                setOpError(true);
            } else {
                setOpError(false);
            }
            if (!value.value
                || !value.value.inputData
                || !value.value.inputData.kind
                || !value.value.inputData.kind.case
                || value.value.inputData.kind.value == undefined
                || (value.value.inputData.kind.case == 'stringValue' && value.value.inputData.kind.value == '')
            ) {
                setValueError(true);
            } else {
                setValueError(false);
            }
        }
    }, [props.validateItemFlag])


    const [nameError, setNameError] = useState(false);
    const [opError, setOpError] = useState(false);
    const [valueError, setValueError] = useState(false);

    return <Row className='tableBody'>
        <Col span={2}>
            <Tag style={{ height: 32, width: 32 }} color={props.index % 2 == 0 ? 'grey' : 'white'} >{props.index + 1}</Tag>
        </Col>
        <Col span={4}>
            <TreeSelect
                expandAll
                value={value.name}
                treeData={props.attrTreeData}
                placeholder='请选择'
                onChange={(val) => {
                    if (!val) {
                        setNameError(true);
                    } else {
                        setNameError(false);
                    }
                    const attr = val as string;
                    let newValue = {
                        ...value,
                        name: val as string,
                        op: '',
                        value: new AdmissionConditionValue({
                            inputData: new Value({
                                kind: { value: "", case: "stringValue" }
                            }),
                            dynamic: false,
                            dynamicDataSource: '',
                            dynamicDataField: '',
                            dynamicConditions: []
                        })
                    }

                    props.onChange(newValue as any);


                    let expressionsTemplate = props.expressionsTemplate;

                    if (expressionsTemplate) {
                        let _inputDataType = '';
                        let _enumValues: string[] = [];

                        let node = getNode(expressionsTemplate, attr);
                        if (node) {
                            _inputDataType = getDataType(node);
                            if (node.enum) {
                                _enumValues = node.enum;
                            }
                        }

                        setInputDataType(_inputDataType);
                        setEnumValues(_enumValues);
                    }

                }}
                style={{ width: 110 }}
                dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}></TreeSelect>
            {nameError && <Paragraph type="danger"><Space><IconAlertCircle />属性不能为空</Space></Paragraph>}
        </Col>
        <Col span={4}>
            <Select
                value={value.op}
                placeholder='请选择'
                onChange={(val) => {
                    if (!val) {
                        setOpError(true);
                    } else {
                        setOpError(false);
                    }
                    let newValue = {
                        ...value,
                        op: val as string,
                        value: new AdmissionConditionValue({
                            inputData: new Value({
                                kind: { value: "", case: "stringValue" }
                            }),
                            dynamic: false,
                            dynamicDataSource: '',
                            dynamicDataField: '',
                            dynamicConditions: []
                        })
                    }

                    props.onChange(newValue as any);
                }}
                style={{ width: 110 }}
                dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}>

                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'>'}>大于</Select.Option>}
                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'<'}>小于</Select.Option>}
                {inputDataType != 'array' && <Select.Option value={'=='}>等于</Select.Option>}
                {inputDataType != 'array' && <Select.Option value={'!='}>不等于</Select.Option>}

                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'>='}>大于等于</Select.Option>}
                {(inputDataType == 'number' || inputDataType == 'datetime') && <Select.Option value={'<='}>小于等于</Select.Option>}
                {(inputDataType == 'array' || inputDataType == 'string') && <Select.Option value={'contains'}>包含(模糊匹配)</Select.Option>}
                {(inputDataType == 'array' || inputDataType == 'string') && <Select.Option value={isOneOfChar}>包含(精确匹配)</Select.Option>}
            </Select>
            {opError && <Paragraph type="danger"><Space><IconAlertCircle />操作不能为空</Space></Paragraph>}
        </Col>
        <Col span={12}>
            {value.op == isOneOfChar || value.op == 'contains' || inputDataType == 'array' ? <>  {
                enumValues.length > 0 ? <Select
                    placeholder='请选择'
                    value={
                        value.value?.inputData ? getArrInputValue(value.value?.inputData) : []
                    }
                    optionList={enumValues.map((val) => {
                        return {
                            label: val,
                            value: val
                        }
                    })}
                    multiple
                    onChange={(val) => {
                        
                        let arr = val as string[]

                        if(!arr || arr.length == 0){
                            props.onError();
                            setValueError(true);
                            return;
                        } else {
                            setValueError(false);
                        }

                        let inputData = new Value({
                            kind: {
                                case: 'listValue',
                                value: {
                                    values: arr.map((item) => {
                                        return new Value({
                                            kind: {
                                                case: 'stringValue',
                                                value: item,
                                            },
                                        })
                                    })
                                },
                            },
                        })
                        let newValue = {
                            ...value,
                            value: {
                                inputData: inputData,
                                dynamic: false,
                                dynamicDataSource: '',
                                dynamicDataField: '',
                                dynamicConditions: []
                            },
                        }
                        props.onChange(newValue as any);
                    }}
                    style={{ width: '100%' }}></Select> : <TagInput
                        placeholder='请输入'
                        value={
                            value.value?.inputData ? getArrInputValue(value.value?.inputData) : []
                        }
                        onChange={(val) => {
                            let arr = val as string[]

                            if(!arr || arr.length == 0){
                                props.onError();
                                setValueError(true);
                                return;
                            } else {
                                setValueError(false);
                            }

                            let inputData = new Value({
                                kind: {
                                    case: 'listValue',
                                    value: {
                                        values: arr.map((item) => {
                                            return new Value({
                                                kind: {
                                                    case: 'stringValue',
                                                    value: item,
                                                },
                                            })
                                        })
                                    },
                                },
                            })
                            let newValue = {
                                ...value,
                                value: {
                                    inputData: inputData,
                                    dynamic: false,
                                    dynamicDataSource: '',
                                    dynamicDataField: '',
                                    dynamicConditions: []
                                },
                            }
                            props.onChange(newValue as any);
                        }}
                        addOnBlur
                        style={{ width: '100%' }}
                    ></TagInput>
            }


            </> : inputDataType == 'boolean' ? <Switch
                checked={value.value?.inputData ? value.value.inputData.kind.value as boolean : false}
                checkedText={"是"}
                uncheckedText={"否"}
                onChange={(val) => {
                    let inputData = new Value({
                        kind: {
                            case: 'boolValue',
                            value: val,
                        },
                    })
                    let newValue = {
                        ...value,
                        value: {
                            inputData: inputData,
                            dynamic: false,
                            dynamicDataSource: '',
                            dynamicDataField: '',
                            dynamicConditions: []
                        },
                    }
                    props.onChange(newValue as any);
                }}
                style={{ marginTop: 4 }}
            ></Switch> : inputDataType == 'datetime' ?
                <DatePicker
                    placeholder={'请选择'}
                    type='dateTime'
                    value={value.value?.inputData?.kind.value as string}
                    format='yyyy-MM-dd HH:mm:ss'
                    onChange={(val) => {

                        if (val) {

                            let date = new Date(val as string);
                            let datestr = date.toISOString();

                            let inputData = new Value({
                                kind: {
                                    case: 'stringValue',
                                    value: datestr,
                                },
                            })
                            let newValue = {
                                ...value,
                                value: {
                                    inputData: inputData,
                                    dynamic: false,
                                    dynamicDataSource: '',
                                    dynamicDataField: '',
                                    dynamicConditions: []
                                },
                            }
                            props.onChange(newValue as any);
                            setValueError(false);
                        } else {
                            setValueError(true);
                            props.onError();
                        }
                    }}
                    style={{ marginRight: 0 }}
                ></DatePicker> : enumValues.length > 0 ? <Select
                    placeholder='请选择'
                    value={value.value?.inputData?.kind.value as string}
                    optionList={enumValues.map((val) => {
                        return {
                            label: val,
                            value: val
                        }
                    })}
                    onChange={(val) => {
                        if (!val) {
                            props.onError();
                            setValueError(true);
                            return;
                        }
                        setValueError(false);
                        let inputData = new Value({
                            kind: {
                                case: 'stringValue',
                                value: val as any,
                            },
                        })

                        let newValue = {
                            ...value,
                            value: {
                                inputData: inputData,
                                dynamic: false,
                                dynamicDataSource: '',
                                dynamicDataField: '',
                                dynamicConditions: []
                            },
                        }
                        props.onChange(newValue as any);
                    }}
                    style={{ width: '100%' }}></Select> : inputDataType == 'number' ?
                    <InputNumber
                        placeholder={'请输入'}
                        value={value.value?.inputData?.kind.value as number}
                        onChange={(val) => {
                            if (val != '' && val != undefined && val != null && !isNaN(val as number)) {
                                setValueError(false);
                                let inputData = new Value({
                                    kind: {
                                        case: 'numberValue',
                                        value: val as number,
                                    },
                                })
                                let newValue = {
                                    ...value,
                                    value: {
                                        inputData: inputData,
                                        dynamic: false,
                                        dynamicDataSource: '',
                                        dynamicDataField: '',
                                        dynamicConditions: []
                                    },
                                }
                                props.onChange(newValue as any);
                            } else {
                                setValueError(true);
                                props.onError();
                            }
                        }} style={{ width: '100%' }}
                    ></InputNumber> :
                    <Input value={inputVaule}
                        placeholder={'请输入'}
                        onChange={(val) => {
                            if(val){
                                setValueError(false);
                            } else {
                                setValueError(true);
                            }
                            setInputValue(val)
                        }}
                        onEnterPress={() => handleInputValueChange(inputVaule)}
                        onBlur={() => handleInputValueChange(inputVaule)}
                        style={{ width: '100%' }}></Input>}
            {valueError && <Paragraph type="danger"><Space><IconAlertCircle />值不能为空或格式不对</Space></Paragraph>}


        </Col>
        <Col span={2} className='btn-right-col'>
            <Button type="danger" onClick={() => {
                props.onDel();
            }} icon={<IconMinusCircle />}></Button>
        </Col>
    </Row>
}

export default Index;
