.guideWrap {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 20px;
    height: calc(100vh - 100px);
}
.guideRow {
    flex-grow: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cardWrap {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-grow: 0;
    padding: 10px;
    width: 50%;
}

// .cardWrap:last-of-type {
//     padding-left: 20px;
// }
// .cardWrap:first-of-type {
//     padding-right: 20px;
// }
// 媒体查询
@media screen and (max-width: 1200px) {
    .guideRow {
        flex-direction: column;
    }
    .cardWrap {
        width: 100%;
    }

}

.guideCard {
    width: 100%;
    height: 100%;
    cursor:default;
    >div {
        height: 100%;
    }
}

.previewContent {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;height: 100%;
}
.machineTag {
    width: 300px;
    display: flex;
}

.codeWrap {
    font-family: 'SFMono-Regular', <PERSON>sol<PERSON>, 'Liberation Mono', <PERSON><PERSON>, Courier, monospace;
    background-color: var(--semi-color-black);
    padding: 10px;
    border-radius: 4px;
    color: var(--semi-color-white);
    .codeText {
        color: var(--semi-color-success);
    }
    margin-bottom: 20px;
}