import React, { useState, useContext, useEffect, useRef } from 'react'
import { Typography, Steps, Badge, Tag, <PERSON><PERSON>, Card } from '@douyinfe/semi-ui';
import GuideDownload from '@/components/guide-download';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { UserProfileContext } from '@/hooks/useUserProfile';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import  { GlobalConfigContext } from '@/hooks/useGlobalConfig'

import styles from './index.module.scss'
import { Spin } from '@douyinfe/semi-ui';
const { Title, Paragraph } = Typography;
import { listMachines } from '@/services/device';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import {
    isMobile,
    isAndroid,
    isIOS
} from 'react-device-detect';
const Index: React.FC = () => {

    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);
    const navigate = useNavigate();

    // 步骤
    const [step, setStep] = useState(0);
    // refStep
    const refStep = useRef(0);

    const flynet = useContext(FlynetGeneralContext);
    // 第一台设备
    const [machine1, setMachine1] = useState<Machine>();
    // 第二台设备
    const [machine2, setMachine2] = useState<Machine>();

    const userProfile = useContext(UserProfileContext)
    const queryMachines = () => {

        listMachines(flynet.id, 'limit=2&offset=0').then(res => {
            let m1: Machine | undefined = undefined;
            let m2: Machine | undefined = undefined;
            res.machines.forEach(m => {
                if (!m1) {
                    m1 = m;
                } else if (!machine2) {
                    m2 = m;
                }
            })
            setMachine1(m1);

            setMachine2(m2);
            if (m1 != undefined && m2 == undefined) {
                refStep.current = 1;
                setStep(1)
            } else if (m1 != undefined && m2 != undefined) {
                setStep(2)
                refStep.current = 2;
            }
        })
    };

    // 定时轮询函数
    const polling = () => {
        queryMachines()

        if (refStep.current < 2) {
            setTimeout(() => {
                polling()
            }, 3000)
        }
    }

    // 定时轮询
    useEffect(() => {
        polling()
    }, [])


    return <div className={styles.guideWrap}>
        <div className={styles.guideRow}>

            <div className={styles.cardWrap}>
                <Card bordered={false} className={styles.guideCard}>
                    {
                        step === 0 && <div className={styles.previewContent}>
                            <Spin />
                            <Paragraph style={{ marginTop: 20 }} type='tertiary'>等待您的第一台设备接入</Paragraph>
                        </div>
                    }
                    {
                        step === 1 && <div className={styles.previewContent}>
                            <Tag size='large' className={styles.machineTag} style={{ marginBottom: 20, padding: 20 }}><span style={{ flexGrow: 1 }}><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {machine1?.givenName} </span><span>{machine1?.ipv4}</span></Tag>

                            <Spin />
                            <Paragraph style={{ marginTop: 20 }} type='tertiary'>等待您的第二台设备接入</Paragraph>
                        </div>
                    }
                    {
                        (step === 2 || step === 3) && <div className={styles.previewContent}>
                            <Tag size='large' className={styles.machineTag} style={{ marginBottom: 20, padding: 20 }}><span style={{ flexGrow: 1 }}><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {machine1?.givenName} </span><span>{machine1?.ipv4}</span></Tag>

                            <Tag size='large' className={styles.machineTag} style={{ marginBottom: 20, padding: 20 }}><span style={{ flexGrow: 1 }}><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {machine2?.givenName} </span><span>{machine2?.ipv4}</span></Tag>

                        </div>
                    }
                </Card>
            </div>
            <div className={styles.cardWrap}>
                <Card bordered={false} className={styles.guideCard}>
                    <Steps className='mb40' type="basic" current={step} direction={isMobile || isAndroid || isIOS ? 'vertical' : 'horizontal'}>
                        <Steps.Step title='添加设备' description="接入第一台设备" />
                        <Steps.Step title='添加设备' description="接入第二台设备" />
                        <Steps.Step title='测试' description="联通性验证" />
                        <Steps.Step title='结束' description="Mesh网络创建成功" />
                    </Steps>
                    {step === 0 &&
                        <div className={styles.guideContent}>
                            {/* <Title heading={4} className='mb20'>欢迎您使用越云，现在让我们开始添加第一台设备</Title> */}
                            <Paragraph className='mb20'>安装{globalConfig.name}并以 {userProfile.identity?.traits?.email} 登录。 一旦您在设备上登录，它就会自动添加到您的{globalConfig.name}网络中。您可以通过以下方式安装{globalConfig.name}：</Paragraph>
                            <GuideDownload></GuideDownload>
                        </div>}
                    {step === 1 &&
                        <div className={styles.guideContent}>
                            {/* <Title heading={4} className='mb20'>第二步，添加第二台设备</Title> */}
                            <Paragraph className='mb20'>您的第一台设备（{machine1?.name}）已经加入网络！</Paragraph>
                            <Paragraph className='mb20'>{globalConfig.name}将多个设备连接在一起，因此您需要将它安装在多个设备上。 在第二台设备上以同样的方式也下载{globalConfig.name}。</Paragraph>
                            <GuideDownload></GuideDownload>
                        </div>}
                    {step === 2 &&
                        <div className={styles.guideContent}>
                            {/* <Title heading={6} className='mb20'>第三步，测试两台设备之间的网络连接</Title> */}
                            <Paragraph className='mb20'>{globalConfig.name}网络中的每个设备都有一个私有的100.x.y.z IP, 设备之间可以使用各种协议连接，例如SSH，RDP， HTTP等</Paragraph>
                            <Paragraph className='mb20'>在您的第一台设备上，打开终端并运行以下命令：</Paragraph>
                            <div className={styles.codeWrap}>
                                <code>ping <span className={styles.codeText}>
                                    {machine2?.ipv4}
                                </span>
                                </code>
                            </div>
                            <Paragraph className='mb20'>您应该看到类似以下内容的输出：</Paragraph>
                            <div className={styles.codeWrap}>
                                <code>64 bytes from {machine1?.ipv4}: icmp_seq=1 ttl=64 time=0.032 ms</code>
                            </div>
                            <Paragraph className='mb20'>如果您看到这样的输出，这意味着您的设备已经成功连接到网络！</Paragraph>

                            <Paragraph className='mb20'>现在，您可以在第二台设备上运行相同的命令，以确保两台设备之间的网络连接正常。</Paragraph>

                            <Button type='primary' theme='solid' size='large' onClick={() => { setStep(3) }}>测试完成，继续</Button>
                        </div>}
                    {step === 3 &&
                        <div className={styles.guideContent}>
                            <Title heading={4} className='mb20'>您已经成功创建了您的第一个网络！</Title>
                            <Paragraph className='mb20'>您可以通过以下方式继续学习：</Paragraph>
                            <Paragraph><a>了解如何设置DNS</a></Paragraph>
                            <Paragraph><a>了解如何进行访问控制</a></Paragraph>
                            <Paragraph><a>了解如何设置DNS</a></Paragraph>
                            <Paragraph className='mb20'><a>了解如何邀请其他团队成员</a></Paragraph>
                            <Paragraph className='mb20'>或者您可以直接进入控制台进行设置：</Paragraph>
                            <Button theme='solid' size='large' onClick={() => { navigate(`${BASE_PATH}`) }}>进入控制台</Button>

                        </div>}
                </Card>
            </div>
        </div>
    </div>
}

export default Index;