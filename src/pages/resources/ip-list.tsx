import React, { useState, useEffect } from 'react'
import { IconCopyAdd, IconHelpCircle, IconMinusCircle, IconSearch, IconPlus } from '@douyinfe/semi-icons';

import { isValidIPOrIpRangeOrCIDR } from '@/utils/validators';
import { Divider, Row, Col, Button, Typography, Skeleton, Popover, Tag, Space, Input } from "@douyinfe/semi-ui";
import { useLocale } from '@/locales';

import styles from './index.module.scss';
import { isIPInRange } from '@/utils/format';
import BatchEditIpList from './batch-edit-ip-list';

const { Paragraph } = Typography;

const IpList: React.FC<{
    sys?: boolean,
    isCurrent: boolean,
    data: {
        name: string,
        ipList: {
            ip: string,
            memo: string
        }[],
    },
    onchange: (ipList: {
        ip: string,
        memo: string
    }[]) => void
    onerror: () => void,
    onAdd: () => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [ipList, setIpList] = useState(props.data.ipList);

    const [showBatchEdit, setShowBatchEdit] = useState(false);

    const [isLoading, setIsLoading] = useState(false);

    const [errorNameIndexs, setErrorNameIndexs] = useState<number[]>([]);

    useEffect(() => {
        setIsLoading(true);
        setTimeout(() => {
            setIpList(props.data.ipList);
            setErrorNameIndexs([]);
            setIsLoading(false);
        }, 10);
    }, [props.data.name, props.data.ipList.length])


    const [filter, setFilter] = useState<string>('');

    const isPassFilter = (i: number) => {
        if (!filter || filter.trim() == '') {
            return true;
        }
        if (ipList.length <= i) {
            return true;
        }
        let ip = ipList[i].ip || '';
        let memo = ipList[i].memo || '';
        if (filter) {

            if (ip.indexOf(filter) == -1 && memo.indexOf(filter) == -1 && isIPInRange(filter, ip) == false) {
                return false;
            }
        }
        return true;
    }

    const handleIpChange = (val: string, item: { ip: string, memo: string }, i: number) => {
        const value = val ? val.trim() : '';
        item.ip = value;
        const newIpList = ipList.map((item, index) => {
            if (index == i) {
                return item;
            }
            return item;
        });
        setIpList(newIpList);
        let hasError = false;
        // 验证，不能为空
        if (!value || value.trim() == '') {
            hasError = true;
        }
        // 验证，是否IP格式
        let msg = isValidIPOrIpRangeOrCIDR(value)
        if (msg) {
            hasError = true;
        }
        if (hasError) {
            if (errorNameIndexs.indexOf(i) == -1) {
                errorNameIndexs.push(i);
                setErrorNameIndexs([...errorNameIndexs]);
            }
            props.onerror();
        } else {
            if (errorNameIndexs.indexOf(i) >= 0) {
                errorNameIndexs.splice(errorNameIndexs.indexOf(i), 1);
                setErrorNameIndexs([...errorNameIndexs]);
                // if (errorNameIndexs.length == 0) {
                //     props.onchange(newIpList);
                // }
            }
            props.onchange(newIpList);
        }
    }

    return <>
        <Skeleton loading={isLoading} placeholder={
            <div style={{ height: 200, width: '100%' }}>
                <Skeleton.Image style={{ height: 32, marginBottom: 20, marginTop: 10 }}></Skeleton.Image>
                <Skeleton.Image style={{ height: 100, marginBottom: 100 }}></Skeleton.Image>
            </div>
        }>
            <Divider></Divider>
            <Row style={{ marginTop: 10, marginBottom: 12 }} >
                <Col span={10}>
                    <Input showClear
                        placeholder={formatMessage({ id: 'resources.search.placeholder' })}
                        value={filter} suffix={<IconSearch />} onChange={(val) => {
                            setFilter(val);
                        }}></Input>
                </Col>
                <Col span={14} className={styles.rightColumn}>
                    <Space>
                        <Button theme='solid' onClick={() => setShowBatchEdit(true)} disabled={props.sys}>{formatMessage({ id: 'resources.button.batchEdit' })}</Button>
                        {ipList.length > 0 && <Button icon={<IconPlus />} disabled={props.sys} onClick={() => {
                            
                            let newIpList = [];
                            ipList.forEach(item => newIpList.push(item))
                            newIpList.push({
                                ip: '',
                                memo: ''
                            });
                            setIpList(newIpList);
                            

                            
                            props.onerror();

                            setTimeout(() => {
                                const input = document.querySelector('.semi-tabs-content .semi-row:last-of-type input') as HTMLInputElement;
                                if (input) {
                                    input.focus();
                                    input.setSelectionRange(0, input.value.length);
                                    input.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                                }
                            }, 200);
                        }}></Button>}
                    </Space>
                </Col>
            </Row>
            {ipList.length > 0 ? <>
                <Row className={styles.tableTitle} style={{ marginBottom: 12 }}>
                    <Col span={2}>{formatMessage({ id: 'resources.table.index' })}
                    </Col>
                    <Col span={8}>
                        IP &nbsp;
                        <Popover content={<div className='p10'>
                            <div style={{ width: 360 }}>{formatMessage({ id: 'resources.ip.helpText' })}</div>
                        </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover>
                    </Col>
                    <Col span={11}>
                        {formatMessage({ id: 'resources.table.memo' })}
                    </Col>
                    <Col span={3} className={styles.rightColumn}>
                        &nbsp;
                    </Col>
                </Row>
                <div className={styles.contentIpList}>
                    {ipList.map((item, i) =>
                        <Row className={styles.tableBody} key={i} style={{ display: isPassFilter(i) ? '' : 'none', marginBottom: 12 }}>
                            <Col span={2}>
                                <Tag style={{ height: 32, width: 32 }} color={i % 2 == 0 ? 'grey' : 'white'} >{i + 1}</Tag>
                            </Col>
                            <Col span={8}>
                                <Input disabled={props.sys} className={i == 0 ? 'firstIpGroupIp' : ''}
                                    validateStatus={errorNameIndexs.indexOf(i) >= 0 ? 'error' : 'default'}
                                    placeholder={formatMessage({ id: 'resources.ip.placeholder' })}
                                    value={item.ip}
                                    onBlur={() => { handleIpChange(item.ip, item, i) }}
                                    onPaste={(e: any) => { handleIpChange(e.target.value, item, i) }}
                                    onChange={(value) => { handleIpChange(value, item, i) }}></Input>
                                {errorNameIndexs.indexOf(i) >= 0 && <Paragraph type="danger">{item.ip == '' ? formatMessage({ id: 'resources.validation.ipRequired' }) : formatMessage({ id: 'resources.validation.ipInvalid' })}</Paragraph>}
                            </Col>
                            <Col span={11}>
                                <Input
                                    disabled={props.sys}
                                    value={item.memo}
                                    placeholder={formatMessage({ id: 'resources.memo.placeholder' })}
                                    onChange={(value: string) => {
                                        item.memo = value;
                                        setIpList([...ipList]);
                                        props.onchange(ipList);
                                    }}
                                ></Input>
                            </Col>
                            <Col span={3} className={styles.rightColumn}>
                                <Space>
                                    <Button disabled={props.sys} type='danger' icon={<IconMinusCircle />} onClick={() => {
                                        let newIpList = ipList.filter((item, index) => index != i);
                                        setIpList(newIpList);
                                        props.onchange(newIpList);
                                    }}></Button>
                                    <Button disabled={props.sys} icon={<IconCopyAdd />} onClick={() => {
                                        let newIpList = [{
                                            ip: item.ip,
                                            memo: item.memo
                                        }];
                                        ipList.forEach(item => newIpList.push(item))
                                        setIpList(newIpList);
                                    }}></Button>
                                </Space>
                            </Col>
                        </Row>
                    )}
                </div>
            </> : <>
                <div className={styles.addCenter}>
                    <Button
                        size='large'
                        icon={<IconPlus />}
                        onClick={() => {
                            setIpList([{
                                ip: '',
                                memo: ''
                            }, ...ipList]);
                        }}
                    ></Button>
                </div>

            </>}
        </Skeleton>
        {showBatchEdit && <BatchEditIpList ipList={ipList} onchange={(ipList) => {
            setIpList(ipList);
            props.onchange(ipList);
            setShowBatchEdit(false);
        }} close={() => {
            setShowBatchEdit(false);
        }
        }></BatchEditIpList>}
    </>
}

export default IpList;