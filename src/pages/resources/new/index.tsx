import React, { useState, useContext, useEffect } from 'react'
import { Typography, Form, Modal, Row, Col, Button, Divider, Notification, Popover, Card } from '@douyinfe/semi-ui';
import { IpGroup } from '@/interface/ip-group';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { getUuid } from '@/utils/common';
import { useLocale } from '@/locales';
import IpList from '../ip-list';


const { Paragraph, Text } = Typography;

interface Props {
    close: () => void;
    success: (newNode:IpGroup) => void;
    ipGroups: IpGroup[];
}

const Index: React.FC<Props> = ({ close, success, ipGroups }) => {
    const { formatMessage } = useLocale();
    // 取得资源组选项
    const getOptions = (currentNode: IpGroup, allGroups: IpGroup[]) => {
        return allGroups.map((item, i) => {
            let opt = {
                value: item.name,
                label: `${item.description}`,
                disabled: false
            }
            if (item.name == currentNode.name) {
                opt.disabled = true;
            }
            if (item.allRefIpGroups && item.allRefIpGroups.indexOf(currentNode.name) >= 0) {
                opt.disabled = true;
            }
            return opt
        })
    }


    const name = getUuid();

    const [loading, setLoading] = useState(false);

    const [formApi, setFormApi] = useState<FormApi<IpGroup>>();

    const [currentNode, setCurrentNode] = useState<IpGroup>({
        name: name,
        description: '',
        ipList: [
            {
                ip: '',
                memo: ''
            }
        ],
        refIpGroups: []
    });

    const [hasIpListError, setHasIpListError] = useState(true);

    const handleSubmit = async () => {
        if (hasIpListError) {
            Notification.error({
                title: formatMessage({ id: 'resources.validation.ipListError' })
            });
            return;
        }
        await formApi?.validate();

        let values = formApi?.getValues();
        if (!values) {
            return;
        }

        success
        && success({
            ...values,
            name: name,
            ipList: currentNode.ipList,
        });
    }

    return <>
        <Modal
            width={800}
            title={formatMessage({ id: 'resources.new.title' })}
            visible={true}
            onCancel={close}
            onOk={handleSubmit}
            // okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >
            <Form
                getFormApi={setFormApi}
                allowEmpty
                initValues={{
                    name: name,
                    description: '',
                    ipList: [{
                        ip: '',
                        memo: ''
                    }],
                    refIpGroups: []
                }}
            >
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Input
                            field='description'
                            label={formatMessage({ id: 'resources.field.name' })}
                            required
                            trigger={'blur'}
                            validate={value => {
                                if (!value) {
                                    return formatMessage({ id: 'resources.validation.nameRequired' });
                                }
                                return '';
                            }}
                        />
                    </Col>
                    <Col span={12}>
                        <Form.Select
                            field='refIpGroups'
                            label={formatMessage({ id: 'resources.field.associatedGroups' })}
                            multiple
                            style={{ width: '100%' }}
                            optionList={getOptions(currentNode, ipGroups)}
                        ></Form.Select>
                    </Col>
                </Row>
                <Row gutter={16}>
                    <Col span={24}>
                        <IpList
                            onerror={() => {
                                setHasIpListError(true);
                            }}
                            onAdd={() => {
                            }}
                            isCurrent={true}
                            data={
                                {
                                    name: currentNode.name,
                                    ipList: currentNode.ipList
                                }
                            }
                            onchange={(ipList) => {
                                setHasIpListError(false);
                                setCurrentNode({ ...currentNode, ipList: ipList });
                            }}
                        ></IpList>
                    </Col>
                </Row>

            </Form>

        </Modal>
    </>
}

export default Index;
