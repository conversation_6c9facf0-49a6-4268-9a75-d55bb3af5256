import React, { useState, useEffect, useRef } from 'react'
import { IpGroup } from '@/interface/ip-group';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconEdit, IconCopyAdd, IconArrowDown, IconArrowUp, IconMore, IconMinus, IconAlignTop, IconAlignBottom } from '@douyinfe/semi-icons';
import { getUuid } from '@/utils/common';

import { Typography, Row, Col, Popover, Space, Popconfirm, Tag, Button, Tooltip, Form, Notification } from "@douyinfe/semi-ui";
import { useLocale } from '@/locales';

import styles from './index.module.scss';
import { calAllRefIpGroups, calNodeIpList } from './util';

const { Text } = Typography;

interface Props {
    item: IpGroup;
    index: number;
    selectedNodeIndex: number;
    errorGroup: string[];
    doSelectnode: (node: IpGroup, index: number) => void;
    selectedNode?: IpGroup;
    setSelectedNode: (node: IpGroup) => void;
    setSelectedNodeIndex: (index: number) => void;
    setActiveKey: (key: string) => void;
    ipGroups: IpGroup[];
    setIpGroups: (ipGroups: IpGroup[]) => void;
    isEditName: boolean;
    setIsEditName: (isEditName: boolean) => void;
    doUpdate: (ipGroups: IpGroup[])=>void;
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const { item, index, selectedNodeIndex, doSelectnode,
        ipGroups, setIpGroups, setActiveKey,
        isEditName, setIsEditName,
        selectedNode, setSelectedNode, setSelectedNodeIndex, doUpdate } = props;


    const [errorGroup, setErrorGroup] = useState<string[]>([]);


    const nameEditRef = useRef<HTMLInputElement>(null)

    const [nameFormApi, setNameFormApi] = useState<FormApi<{ name: string, description: string }>>();

    useEffect(() => {
        setErrorGroup(props.errorGroup);
    }, [props.errorGroup]);

    const getRowClassName = (record: IpGroup, index: number) => {
        if (errorGroup.includes(record.name)) {
            return styles.nameRowError;
        }
        if (index == selectedNodeIndex) {
            if (isEditName) {
                return styles.nameRowEdit;
            }
            return styles.nameRowSelected;
        }

        return styles.nameRow;
    }


    // 计算资源组是否能被删除
    const calCanDelete = (currentNode: IpGroup, allGroups: IpGroup[]) => {
        let canDelete = true;
        allGroups.forEach((item) => {
            if (item.allRefIpGroups && item.allRefIpGroups.indexOf(currentNode.name) >= 0) {
                canDelete = false;
            }
        })
        return canDelete;
    }

    const finishEditDesciption = async () => {
        const values = nameFormApi?.getValues();

        if (!values?.description) {
            return;
        }

        if (values?.description) {
            // 验证名称是否和已有名称重复
            let exist = ipGroups.find((item, i) => {
                return i != selectedNodeIndex && item.description == values.description;
            })
            if (exist) {
                Notification.error({
                    content: formatMessage({ id: 'resources.validation.nameExists' }),
                });
                return;
            }
        }
        if (values) {
            const oldName = ipGroups[selectedNodeIndex].name;

            const newGroups = ipGroups.map((item, i) => {
                if (i == selectedNodeIndex) {
                    // item.name = values.name;
                    item.description = values.description;
                    return { ...item, ...values };
                }
                return item;
            });

            // 修改名称和描述
            setIpGroups(newGroups)
            // 修改引用
            doUpdate(newGroups);

        }
        setIsEditName(false);
        let inputs = document.querySelector('.firstIpGroupIp input');
        if (inputs && inputs instanceof HTMLInputElement) {
            inputs.focus();
        }
        setActiveKey('list')
    }
    useEffect(() => {
        if (isEditName) {
            nameEditRef.current?.focus();
        }
    }, [isEditName])

    return <div key={index} className={
        getRowClassName(item, index)
    } onClick={() => {
        if (isEditName) {
            return;
        }
        if (errorGroup.length > 0) {
            return;
        }
        doSelectnode(item, index);
    }}>
        {isEditName && index == selectedNodeIndex ?
            <Form initValues={{ name: item.name, description: item.description }} getFormApi={setNameFormApi} onValueChange={(values) => {
                setSelectedNode({ ...selectedNode, ...values });
            }}>
                <Row>
                    <Col span={24} className={styles.nameField}>
                        <Form.Input size='small' ref={nameEditRef} onBlur={finishEditDesciption} onEnterPress={finishEditDesciption} field='description' noLabel label={formatMessage({ id: 'resources.field.description' })}></Form.Input>
                    </Col>
                </Row>

            </Form> :
            <Row>
                <Col span={20}>
                    <div style={{ lineHeight: '24px', display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                        <Tag style={{ width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'}>{index + 1}</Tag>&nbsp;<Text>{item.description}</Text>
                    </div>
                </Col>
                <Col span={4} className={styles.rightColumn}>
                    {index == selectedNodeIndex && <Space>
                        <Popover position='bottomRight' content={<div className='p10'><Space>
                            <Tooltip content={formatMessage({ id: 'resources.action.moveToTop' })}>
                                <Button size='small' disabled={index == 0} icon={<IconAlignTop
                                    onClick={(e) => {
                                        if (index == 0) {
                                            return;
                                        }
                                        e.stopPropagation();
                                        // 把数据移到数据的最前面
                                        let newData = [...ipGroups];
                                        let temp = newData[index];
                                        newData.splice(index, 1);
                                        newData.unshift(temp);
                                        setIpGroups([...newData]);
                                        setSelectedNode(newData[0]);
                                        setSelectedNodeIndex(0);

                                        doUpdate(newData);
                                    }}
                                ></IconAlignTop>} />
                            </Tooltip>
                            <Tooltip content={formatMessage({ id: 'resources.action.moveUp' })}>
                                <Button size='small' disabled={index == 0} icon={<IconArrowUp
                                    onClick={(e) => {
                                        if (index == 0) {
                                            return;
                                        }
                                        e.stopPropagation();
                                        let newData = [...ipGroups];

                                        let temp = newData[index - 1];
                                        newData[index - 1] = newData[index];
                                        newData[index] = temp;
                                        setIpGroups([...newData]);
                                        setSelectedNode(newData[index - 1]);
                                        setSelectedNodeIndex(index - 1);

                                        doUpdate(newData);
                                    }}
                                ></IconArrowUp>}></Button>
                            </Tooltip>
                            <Tooltip content={formatMessage({ id: 'resources.action.moveDown' })}>
                                <Button size='small' disabled={index == ipGroups.length - 1} icon={<IconArrowDown
                                    onClick={(e) => {
                                        if (index == ipGroups.length - 1) {
                                            return;
                                        }
                                        e.stopPropagation();
                                        let newData = [...ipGroups];
                                        let temp = newData[index + 1];
                                        newData[index + 1] = newData[index];
                                        newData[index] = temp;
                                        setIpGroups([...newData]);
                                        setSelectedNode(newData[index + 1]);
                                        setSelectedNodeIndex(index + 1);

                                        doUpdate(newData);
                                    }}
                                ></IconArrowDown>}></Button>
                            </Tooltip>
                            <Tooltip content={formatMessage({ id: 'resources.action.moveToBottom' })}>
                                <Button size='small' disabled={index == ipGroups.length - 1} icon={<IconAlignBottom
                                    onClick={(e) => {
                                        if (index == ipGroups.length - 1) {
                                            return;
                                        }
                                        e.stopPropagation();
                                        let newData = [...ipGroups];
                                        let temp = newData[index];
                                        newData.splice(index, 1);
                                        newData.push(temp);
                                        setIpGroups([...newData]);
                                        setSelectedNode(newData[ipGroups.length - 1]);
                                        setSelectedNodeIndex(ipGroups.length - 1);

                                        doUpdate(newData);
                                    }}></IconAlignBottom>}></Button>
                            </Tooltip>

                            <Popconfirm
                                title={formatMessage({ id: 'resources.delete.confirmTitle' })}
                                content={formatMessage({ id: 'resources.delete.confirmContent' })}
                                onConfirm={() => {
                                    const newData = ipGroups.filter((item, i) => {
                                        return i != selectedNodeIndex;
                                    });
                                    setIpGroups(newData);
                                    errorGroup.splice(errorGroup.indexOf(item.name), 1);
                                    doSelectnode(ipGroups[0], 0);
                                    setActiveKey('list')

                                    doUpdate(newData);
                                }}
                                disabled={isEditName
                                    || !calCanDelete(item, ipGroups)
                                }

                                onCancel={() => { }}
                            >
                                <Button theme='solid'
                                    disabled={isEditName
                                        || !calCanDelete(item, ipGroups) || item.sys
                                    }
                                    type='danger'
                                    size='small'
                                    icon={<IconMinus></IconMinus>}></Button>
                            </Popconfirm>
                            <Tooltip content={formatMessage({ id: 'common.edit' })}>
                                <Button theme='solid'
                                    disabled={isEditName || item.sys}
                                    size='small'
                                    onClick={() => {
                                        setIsEditName(true);
                                    }}
                                    icon={<IconEdit></IconEdit>}></Button>
                            </Tooltip>
                            <Tooltip content={formatMessage({ id: 'resources.action.copyAdd' })}>
                                <Button size='small'
                                    theme='solid'
                                    onClick={() => {
                                        // 复制添加
                                        let newNode: IpGroup = {
                                            name: getUuid(),
                                            description: item.description + formatMessage({ id: 'resources.action.copySuffix' }),
                                            ipList: item.ipList.map(val => {
                                                return {
                                                    ip: val.ip,
                                                    memo: val.memo
                                                }
                                            }),
                                            refIpGroups: item.refIpGroups.map(val => val),
                                        }
                                        newNode.allRefIpGroups = calAllRefIpGroups(newNode, ipGroups);

                                        newNode.allIpList = calNodeIpList(newNode.ipList, newNode.allRefIpGroups || [], ipGroups);

                                        const newGroups = [newNode, ...ipGroups]

                                        setIpGroups(newGroups);
                                        setSelectedNode(newNode);
                                        
                                        doUpdate(newGroups);
                                        setTimeout(() => {

                                            setSelectedNodeIndex(0);
                                            setIsEditName(true)
                                            setActiveKey('list')
                                        }, 500);
                                    }}
                                    icon={<IconCopyAdd></IconCopyAdd>}></Button>
                            </Tooltip>
                        </Space></div>}>
                            <Button size='small' style={{ cursor: 'default' }} icon={<IconMore></IconMore>}></Button>
                        </Popover>

                    </Space>
                    }
                </Col>
            </Row>}
    </div>
}

export default Index;