import React, { useState, useRef, useContext, useEffect } from 'react'
import { Divider, Modal, Space, Tag, Input } from "@douyinfe/semi-ui";

import { Typography, Row, Col } from '@douyinfe/semi-ui';
import { ArrayField, Form, Button } from '@douyinfe/semi-ui';
import { isIPInRange, isIPV4InCIDR } from '@/utils/format';

import Expandable from '@/components/expandable/index';
import { IconPlusCircle, IconMinusCircle, IconSearch } from '@douyinfe/semi-icons';
import styles from './index.module.scss'
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';


const Index: React.FC<{
    close: () => void,
    hosts: { ip: string[], host: string }[]
}> = (props) => {
    const { formatMessage } = useLocale();
    const [filter, setFilter] = useState<string>('');


    const isPreviewFilter = (ip: string) => {
        if (!filter || filter.trim() == '') {
            return true;
        }
        if (ip.indexOf(filter) == -1 && isIPInRange(filter, ip) == false) {
            return false;
        }
        return true;
    }

    const isGroupFilter = (values: string[]) => {
        if (!filter || filter.trim() == '') {
            return true;
        }
        for (let i = 0; i < values.length; i++) {
            const name = values[i];
            if (name.indexOf(filter) != -1 || isIPInRange(filter, name) == true) {
                return true;
            }
        }
        return false;
    }

    return <><Modal
        title={formatMessage({ id: 'resources.tab.preview' })}
        visible={true}
        onCancel={props.close}
        onOk={props.close}
        hasCancel={false}
        footer={null}
        width={1080}

        maskClosable={false}
    ><div style={{ minHeight: 400 }}>
            <Row className='mb10' style={{ marginTop: 10 }}>
                <Col span={10}>
                    <Input showClear
                        placeholder={formatMessage({ id: 'resources.preview.searchPlaceholder' })}
                        value={filter} className='mb10' suffix={<IconSearch />} onChange={(val) => {
                            setFilter(val);
                        }}></Input>
                </Col>
                <Col span={14} className={styles.rightColumn}>

                </Col>
            </Row>
            {props.hosts.map((item, i) => {
                if (isGroupFilter(item.ip) == false) {
                    return ''
                }
                return <> <Row key={i} className='mb10'>
                    <Col span={6}>
                        <Tag>{i + 1}</Tag>&nbsp;{item.host}
                    </Col>
                    <Col span={18}>
                        {filter ? <Space style={{ flexWrap: 'wrap' }}>
                            {item.ip.map((ip, ipIndex) => {
                                if (isPreviewFilter(ip || '')) {
                                    return <Tag color='grey' key={ipIndex}>{ip}<Copyable content={ip}/></Tag>
                                }
                                return ''
                            })}
                        </Space>: <Expandable expand={item.ip.length > 12} collapseHeight={60} ><Space style={{ flexWrap: 'wrap' }}>
                            {item.ip.map((ip, ipIndex) => {
                                if (isPreviewFilter(ip || '')) {
                                    return <Tag color='grey' key={ipIndex}>{ip}<Copyable content={ip}/></Tag>
                                }
                                return ''
                            })}
                        </Space></Expandable>}
                    </Col>
                </Row>
                    {i < props.hosts.length - 1 && <Divider className='mb10' />}
                </>
            })}
        </div></Modal></>
}

export default Index
