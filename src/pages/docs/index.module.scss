.logo {
    width: 190px;
    margin-left: -10px;
    display: flex;
    align-items: center;
    img {
        width: 40x;
        height: 40px;
    }
    span {
        font-family: Mi<PERSON>,-apple-system,BlinkMacSystemFont,Roboto,roboto slab,droid serif,segoe ui,system-ui,<PERSON><PERSON>,sans-serif;
        margin-left: 10px;
        color: var(--semi-color-text-1);
    }
}

.layout {
    height: 100vh;
}
.content {
    min-height: calc(100vh - 60px);
}

.title {
    text-align: center;
    padding-top: 40px;
    padding-bottom: 40px;
}
.card {
    width: 600px;
    margin: 0 auto;
}
.docFrame {
    width: 100%;
    border: none;
    min-height: calc(100vh - 68px);
} 
.navLink {
    font-size: 14px;
    margin-right: 10px;
    color: var(--semi-color-text-2);
}