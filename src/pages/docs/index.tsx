import React, { useRef, useEffect, useContext } from 'react';
import { Card, Layout, Nav, Typography } from '@douyinfe/semi-ui';

import { IconDownload, IconHelpCircle } from '@douyinfe/semi-icons';
import logo from '@/assets/logo.png';
import styles from './index.module.scss'
import UserIndicator from '@/components/user-indicator';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig'

import { UserProfileContext } from '@/hooks/useUserProfile';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
const { Title, Paragraph } = Typography;
const { Header, Content } = Layout;
const Index: React.FC = () => {
    
    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);
    const navigate = useNavigate()
    const iframeRef = useRef(null);
    useEffect(() => {
        const iframe: any = iframeRef.current;
        if (!iframe) { return }
        const handleIframeLoad = () => {
            iframe.style.height = iframe.contentWindow.document.body.scrollHeight + 'px';
        };
        iframe.addEventListener('load', handleIframeLoad);
        return () => {
            iframe.removeEventListener('load', handleIframeLoad);
        };
    }, []);

    // 用户登录信息
    const userProfile = useContext(UserProfileContext)
    return <Layout className={styles.layout}>
        <Header>
            <div>
                <Nav mode="horizontal" selectedKeys={['docs']}>
                    <Nav.Header>
                        <div className={styles.logo}><img src={logo}></img><span>{globalConfig.name}</span></div>
                    </Nav.Header>
                    <Nav.Item itemKey="download" onClick={() => navigate(`${BASE_PATH}/download`)} text="下载" icon={<IconDownload />} />
                    <Nav.Item itemKey="docs" onClick={() => navigate(`/docs`)} text="文档" icon={<IconHelpCircle />} />
                  
                    <Nav.Footer>
                        <a href={`${BASE_PATH}`} className={styles.navLink}>管理控制台</a>      
                        <UserProfileContext.Provider value={userProfile}>

                            <UserIndicator></UserIndicator>
                        </UserProfileContext.Provider>
                    </Nav.Footer>
                </Nav>
            </div>
        </Header>
        <Content
            className={styles.content}
        >
            <iframe src='http://localhost:3000/'
                ref={iframeRef}
                frameBorder="0"
                scrolling="no"
                className={styles.docFrame}></iframe>
        </Content>

    </Layout>
}
export default Index;
