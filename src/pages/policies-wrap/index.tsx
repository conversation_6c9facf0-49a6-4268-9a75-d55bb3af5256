import React, { useState } from 'react'
import { Tabs, TabPane } from '@douyinfe/semi-ui';
import { useLocation, useNavigate } from 'react-router-dom';
import ACLPolicy from '../policies';
import ACLPolicyLocal from '../policies/index-local';
import AdmissionPolicy from '../admission';
import EntrancePolicy from '../entrance';
import { BASE_PATH } from '@/constants/router';
import { VITE_LOCAL_PAGER_AND_FILTER } from '@/utils/service';

const Index: React.FC = () => {
    const location = useLocation();
    let type = '';
    if (location.pathname.indexOf('/policies/acl') > -1) {
        type = 'acl';
    }
    else if (location.pathname.indexOf('/policies/admission') > -1) {
        type = 'admission';
    }
    else if (location.pathname.indexOf('/policies/entrance') > -1) {
        type = 'entrance';
    }
    const [activeKey, setActiveKey] = useState(type || 'acl');
    const navigate = useNavigate();

    const [aclParams, setAclParams] = useState('');
    const [entranceParams, setEntranceParams] = useState('');
    const [admissionParams, setAdmissionParams] = useState('');

    return <><div className='general-page'>
        <Tabs type="line" activeKey={activeKey} onChange={(key) => {
            let params = '';
            if (key === 'acl') {
                params = aclParams;
            } else if (key === 'admission') {
                params = admissionParams;
            } else if (key === 'entrance') {
                params = entranceParams;
            }

            navigate(`${BASE_PATH}/policies/${key}${params}`);
            if (activeKey === 'acl') {
                setAclParams(location.search);
            } else if (activeKey === 'admission') {
                setAdmissionParams(location.search);
            } else if (activeKey === 'entrance') {
                setEntranceParams(location.search);
            }
            setActiveKey(key);
        }}>
            <TabPane tab="访问控制策略" itemKey="acl">
                {/* <ACLPolicy /> */}
            </TabPane>
            <TabPane tab="网络准入策略" itemKey="admission">
                {/* <AdmissionPolicy /> */}
            </TabPane>
            <TabPane tab="设备准入策略" itemKey="entrance">
                {/* <EntrancePolicy /> */}
            </TabPane>
        </Tabs>
        {activeKey == 'acl' && <>{VITE_LOCAL_PAGER_AND_FILTER ? <ACLPolicyLocal /> : <ACLPolicy />}</>}
        {activeKey == 'admission' && <AdmissionPolicy />}
        {activeKey == 'entrance' && <EntrancePolicy />}
    </div></>
}

export default Index
