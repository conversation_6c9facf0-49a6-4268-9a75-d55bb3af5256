import { FC, useState, useContext, useEffect } from "react";
import { Typography, Button, Space, Popover, Notification, TreeSelect } from '@douyinfe/semi-ui';
import { IconInfoCircle } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';

import CodeEditor from '@/components/code-editor';
import CodeViewer from '@/components/code-viewer';
import { getFlynet } from "@/services/flynet";
import { AttributeTemplate } from "@/interface/attribute-template";

const { Title, Paragraph, Text } = Typography;

interface Props {

}


const AttributeEdit: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [loading, setLoading] = useState(false);

    const flynet = useContext(FlynetGeneralContext);

    const getExampleConfig = () => {
        return JSON.stringify({
            "$id": "https://flylayer.com/identity.schema.json",
            "$schema": "https://json-schema.org/draft/2020-12/schema",
            "type": "object",
            "title": "attribute",
            "description": formatMessage({ id: 'settings.schema.attribute.exampleDescription' }),
            "properties": {
                "input": {
                    "type": "object",
                    "title": formatMessage({ id: 'settings.schema.attribute.input' }),
                    "description": formatMessage({ id: 'settings.schema.attribute.input' }),
                    "properties": {
                        "User": {
                            "type": "object",
                            "title": formatMessage({ id: 'settings.schema.attribute.user' }),
                            "description": formatMessage({ id: 'settings.schema.attribute.user' }),
                            "properties": {
                                "Account": {
                                    "type": "object",
                                    "title": formatMessage({ id: 'settings.schema.attribute.account' }),
                                    "description": formatMessage({ id: 'settings.schema.attribute.account' }),
                                    "properties": {
                                        "Attrs": {
                                            "type": "object",
                                            "title": formatMessage({ id: 'settings.schema.attribute.attributes' }),
                                            "description": formatMessage({ id: 'settings.schema.attribute.attributes' }),
                                            "properties": {
                                                "nickname": {
                                                    "type": "string",
                                                    "title": formatMessage({ id: 'settings.schema.attribute.nickname' }),
                                                    "minLength": 3
                                                },
                                                "profile": {
                                                    "type": "string",
                                                    "title": formatMessage({ id: 'settings.schema.attribute.profile' })
                                                },
                                                "website": {
                                                    "type": "string",
                                                    "title": formatMessage({ id: 'settings.schema.attribute.website' })
                                                },
                                                "gender": {
                                                    "type": "string",
                                                    "title": formatMessage({ id: 'settings.schema.attribute.gender' })
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }, null, 2);
    };

    const queryFlynet = async () => {
        const res = await getFlynet(flynet.id);
        if (res && res.flynet) {
            setJsonEditorValue(res.flynet.attributeTemplate);
        }
    }

    useEffect(() => {
        queryFlynet();
    }
        , [flynet.id])

    const [jsonEditorValue, setJsonEditorValue] = useState<string>('');

    // const [aclGroupTreeData, setAclGroupTreeData] = useState<TreeNodeData[]>();
    const [machineGroupTreeData, setMachineGroupTreeData] = useState<TreeNodeData[]>();
    const [userGroupTreeData, setUserGroupTreeData] = useState<TreeNodeData[]>();
    // const [admissionTreeData, setAdmissionTreeData] = useState<TreeNodeData[]>();

    const buildTreeData = (key: string, expressionsTemplate: AttributeTemplate) => {
        let treeData: TreeNodeData = {
            label: expressionsTemplate.title,
            value: key,
            key: key,
            children: []
        }
        if (!expressionsTemplate.properties) {
            return treeData;
        }
        Object.keys(expressionsTemplate.properties).forEach((childKey) => {
            let child = buildTreeData(key + '.' + childKey, expressionsTemplate.properties[childKey]);
            treeData.children?.push(child);
        })

        return treeData;
    }
    const buildPropertyTreeData = (properties: AttributeTemplate) => {
        const treeData: TreeNodeData[] = [];
        Object.keys(properties).forEach(key => {
            let node = buildTreeData(key, (properties as any)[key]);
            treeData.push(node);
        }
        );
        return treeData;
    }

    useEffect(() => {
        if(!jsonEditorValue) {
            return;
        }
        try {
            
            const json = JSON.parse(jsonEditorValue);
            const userGroup = json.properties.input.properties.User.properties;
            const machineGroup = json.properties.input.properties.Device.properties;
            // const aclGroup = json.properties.input.properties.Policy.properties;
            // const admissionGroup = json.properties.input.properties.Request.properties;


            const userGroupTreeData = buildPropertyTreeData(userGroup);
            const machineGroupTreeData = buildPropertyTreeData(machineGroup);
            // const aclGroupTreeData = buildPropertyTreeData(aclGroup);
            // const admissionTreeData = buildPropertyTreeData(admissionGroup);
            setUserGroupTreeData(userGroupTreeData);
            setMachineGroupTreeData(machineGroupTreeData);
            // setAclGroupTreeData(aclGroupTreeData);
            // setAdmissionTreeData(admissionTreeData);
        } catch (e) {
            console.log(e);
        }
    }, [jsonEditorValue])


    const handleSave = () => {
        try {
            JSON.parse(jsonEditorValue);
        } catch (e) {
            Notification.error({
                title: formatMessage({ id: 'settings.schema.attribute.invalidJson' }),
                content: (e as any).message,
                duration: 6000,
            });
            return;
        }

        setLoading(true);
        flylayerClient.saveAttributeTemplate({
            flynetId: flynet.id,
            attributeTemplate: jsonEditorValue,
        }).then(() => {
            Notification.success({
                content: formatMessage({ id: 'components.common.saveSuccess' }),
            });
        }).catch(() => {
            Notification.error({
                content: formatMessage({ id: 'components.common.saveFailed' }),
            });
        }).finally(() => {
            setLoading(false);
        })
    }

    return <div >
        <Title heading={4} className='mb2'>{formatMessage({ id: 'settings.schema.attribute.title' })}</Title>
        <Space className='mb20'><Paragraph type='tertiary'>
            {formatMessage({ id: 'settings.schema.attribute.description' })}
        </Paragraph>
            <Popover content={<div className='p10' style={{ width: 800 }}>
                <CodeViewer language='json' height="300px" value={getExampleConfig()} />

            </div>}>
                <IconInfoCircle />
            </Popover>
        </Space>

        <CodeEditor height='320px'
            value={jsonEditorValue}
            language='json'
            onChange={(val) => {
                setJsonEditorValue(val)
            }}></CodeEditor>
        <Space style={{ marginTop: 10 }} className="mb20">
            <Button type="primary" loading={loading} onClick={handleSave} theme='solid'>{formatMessage({ id: 'settings.schema.attribute.saveConfig' })}</Button>
        </Space>
        <Paragraph type='secondary' className="mb20">{formatMessage({ id: 'settings.schema.attribute.preview' })}</Paragraph>
        <Space>
            <Space>
                <Text type='tertiary'>{formatMessage({ id: 'settings.schema.attribute.userAttributes' })}</Text>
                <TreeSelect style={{ width: 120, marginRight: 20 }} expandAll treeData={userGroupTreeData} />
            </Space>
            <Space>
                <Text type='tertiary'>{formatMessage({ id: 'settings.schema.attribute.deviceAttributes' })}</Text>
                <TreeSelect style={{ width: 120, marginRight: 20 }} expandAll treeData={machineGroupTreeData} />
            </Space>

        </Space>
    </div>
}

export default AttributeEdit