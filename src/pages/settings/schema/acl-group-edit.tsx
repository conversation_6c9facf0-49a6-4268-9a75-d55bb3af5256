import React, { useState, useRef, useContext, useEffect } from 'react'
import { Modal, Notification } from "@douyinfe/semi-ui";
import { useLocale } from '@/locales';
import styles from './index.module.scss'
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { flylayerClient } from '@/services/core';

import CodeModalEditor from '@/components/code-modal-editor';

import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";


const Index: React.FC<{
    close: () => void,
    success: (template: string) => void,
}> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [flynet, setFlynet] = useState<Flynet>(); // flynetGeneral.flynet
    const [loading, setLoading] = useState(false);

    const [templateLoaded, setTemplateLoaded] = useState(false);

    const handleOk = (template: string) => {
        try {
            JSON.parse(template);
        } catch (e) {
            Notification.error({
                title: formatMessage({ id: 'settings.schema.aclGroup.invalidJson' }),
                content: (e as any).message,
                duration: 6000,
            });
            return;
        }


        setLoading(true);
        flylayerClient.saveAclGroupTemplate({
            flynetId: flynet?.id,
            aclGroupTemplate: template,
        }).then(res => {

            props.success(template);
            Notification.success({
                content: formatMessage({ id: 'components.common.saveSuccess' }),
            });
        }).catch(_err => {
            Notification.error({
                content: formatMessage({ id: 'components.common.saveFailed' }),
            });
        }).finally(() => {
            setLoading(false);
        })
    }


    const [template, setTemplate] = useState<string>('');

    const queryFlynet = async () => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet);
            if (res && res.flynet && res.flynet.aclGroupTemplate) {
                setTemplateLoaded(true);
                if (res.flynet.aclGroupTemplate) {
                    setTemplate(res.flynet.aclGroupTemplate);
                }
            }
        }).finally(() => {
            setTemplateLoaded(true);
        });


    }

    useEffect(() => {
        queryFlynet();
    }, [])

    return <>
        <CodeModalEditor
            title={formatMessage({ id: 'settings.schema.aclGroup.editTitle' })}
            onCancel={props.close}
            onOk={handleOk}
            value={template}
            language='json'
            width={1080}
            height={620}
            loading={loading}
        ></CodeModalEditor>
    </>
}

export default Index;