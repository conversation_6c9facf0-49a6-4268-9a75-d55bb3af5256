import React, { useState, useContext, useEffect } from 'react'
import { Typography, Row, Col, Button, Divider, TreeSelect, Space } from '@douyinfe/semi-ui';

import UserGroupEdit from './user-group-edit';
import AclGroupEdit from './acl-group-edit';
import DeviceGroupEdit from './device-group-edit';
import AdmissionEdit from './admission-edit';
import { AttributeTemplate } from '@/interface/attribute-template';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';

import AttributeEdit from './attribute-edit';

import { getFlynet } from '@/services/flynet';
const { Title, Paragraph } = Typography;

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    // const flynet = useContext(FlynetGeneralContext);

    // const [expressionVisible, setExpressionVisible] = useState(false);
    // const [aclGroupVisible, setAclGroupVisible] = useState(false);
    // const [deviceGroupVisible, setDeviceGroupVisible] = useState(false);
    // const [userGroupVisible, setUserGroupVisible] = useState(false);
    // const [admissionVisible, setAdmissionVisible] = useState(false);

    // const [expressionsTemplate, setExpressionsTemplate] = useState<string>();
    // const [aclGroupTemplate, setAclGroupTemplateset] = useState<string>();
    // const [machineGroupTemplate, setMachineGroupTemplate] = useState<string>();
    // const [userGroupTemplate, setUserGroupTemplate] = useState<string>();
    // const [admissionTemplate, setAdmissionTemplate] = useState<string>();

    // const [expressionsTreeData, setExpressionsTreeData] = useState<TreeNodeData[]>();
    // const [aclGroupTreeData, setAclGroupTreeData] = useState<TreeNodeData[]>();
    // const [machineGroupTreeData, setMachineGroupTreeData] = useState<TreeNodeData[]>();
    // const [userGroupTreeData, setUserGroupTreeData] = useState<TreeNodeData[]>();
    // const [admissionTreeData, setAdmissionTreeData] = useState<TreeNodeData[]>();


    // const getDataType = (attr: string, key: string, expressionsTemplate: AttributeTemplate): string => {
    //     let dataType = '';
    //     if (key == attr) {
    //         if (expressionsTemplate.type == 'object') {
    //             if (expressionsTemplate.format == 'date-time') {
    //                 return 'datetime';
    //             }
    //             return 'string';
    //         } else {
    //             return expressionsTemplate.type;
    //         }

    //     }
    //     if (!expressionsTemplate.properties) {
    //         return dataType;
    //     }
    //     Object.keys(expressionsTemplate.properties).forEach((childKey) => {
    //         let childDataType = getDataType(attr, key + '.' + childKey, expressionsTemplate.properties[childKey]);
    //         if (childDataType) {
    //             dataType = childDataType;
    //         }
    //     })
    //     return dataType;
    // }

    // const buildTreeData = (key: string, expressionsTemplate: AttributeTemplate) => {
    //     let treeData: TreeNodeData = {
    //         label: expressionsTemplate.title,
    //         value: key,
    //         key: key,
    //         children: []
    //     }
    //     if (!expressionsTemplate.properties) {
    //         return treeData;
    //     }
    //     Object.keys(expressionsTemplate.properties).forEach((childKey) => {
    //         let child = buildTreeData(key + '.' + childKey, expressionsTemplate.properties[childKey]);
    //         treeData.children?.push(child);
    //     })

    //     return treeData;
    // }


    // useEffect(() => {
    //     if (expressionsTemplate) {
    //         const expressionsAttrs: AttributeTemplate = JSON.parse(expressionsTemplate);
    //         if (!expressionsAttrs || !expressionsAttrs.properties) {
    //             return;
    //         }
    //         const initTreeData: TreeNodeData[] = [];
    //         Object.keys(expressionsAttrs.properties).forEach((key) => {
    //             let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
    //             initTreeData.push(treeDataItem);
    //         })
    //         setExpressionsTreeData(initTreeData);
    //     }
    // }, [expressionsTemplate])

    // useEffect(() => {
    //     if (aclGroupTemplate) {
    //         const expressionsAttrs: AttributeTemplate = JSON.parse(aclGroupTemplate);
    //         if (!expressionsAttrs || !expressionsAttrs.properties) {
    //             return;
    //         }
    //         const initTreeData: TreeNodeData[] = [];
    //         Object.keys(expressionsAttrs.properties).forEach((key) => {
    //             let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
    //             initTreeData.push(treeDataItem);
    //         })
    //         setAclGroupTreeData(initTreeData);
    //     }
    // }, [aclGroupTemplate])

    // useEffect(() => {
    //     if (machineGroupTemplate) {
    //         const expressionsAttrs: AttributeTemplate = JSON.parse(machineGroupTemplate);
    //         if (!expressionsAttrs || !expressionsAttrs.properties) {
    //             return;
    //         }
    //         const initTreeData: TreeNodeData[] = [];
    //         Object.keys(expressionsAttrs.properties).forEach((key) => {
    //             let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
    //             initTreeData.push(treeDataItem);
    //         })
    //         setMachineGroupTreeData(initTreeData);
    //     }
    // }, [machineGroupTemplate])

    // useEffect(() => {
    //     if (userGroupTemplate) {
    //         const expressionsAttrs: AttributeTemplate = JSON.parse(userGroupTemplate);
    //         if (!expressionsAttrs || !expressionsAttrs.properties) {
    //             return;
    //         }
    //         const initTreeData: TreeNodeData[] = [];
    //         Object.keys(expressionsAttrs.properties).forEach((key) => {
    //             let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
    //             initTreeData.push(treeDataItem);
    //         })
    //         setUserGroupTreeData(initTreeData);
    //     }
    // }, [userGroupTemplate])

    // useEffect(() => {
    //     if (admissionTemplate) {
    //         const expressionsAttrs: AttributeTemplate = JSON.parse(admissionTemplate);
    //         if (!expressionsAttrs || !expressionsAttrs.properties) {
    //             return;
    //         }
    //         const initTreeData: TreeNodeData[] = [];
    //         Object.keys(expressionsAttrs.properties).forEach((key) => {
    //             let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
    //             initTreeData.push(treeDataItem);
    //         })
    //         setAdmissionTreeData(initTreeData);

    //     }
    // }, [admissionTemplate]);


    // const queryFlynet = async () => {
    //     let res = await getFlynet(flynet.id);
    //     if (res && res.flynet) {
    //         setAclGroupTemplateset(res.flynet.aclGroupTemplate);
    //         setMachineGroupTemplate(res.flynet.machineGroupTemplate);
    //         setUserGroupTemplate(res.flynet.userGroupTemplate);
    //         setExpressionsTemplate(res.flynet.expressionsTemplate);
    //         setAdmissionTemplate(res.flynet.admissionTemplate);
    //     }
    // }

    // useEffect(() => {
    //     queryFlynet(); 
    // }, [])

    return <><div className='settings-page'>
        <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.schema.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.schema.description' })}</Paragraph>

        <AttributeEdit></AttributeEdit>


        




    </div>
        {/* {userGroupVisible && <UserGroupEdit
            close={() => setUserGroupVisible(false)}
            success={(template) => { setUserGroupVisible(false); setUserGroupTemplate(template) }}
        ></UserGroupEdit>} */}
        {/* {admissionVisible && <AdmissionEdit
            close={() => setAdmissionVisible(false)}
            success={(template) => { setAdmissionVisible(false); setAdmissionTemplate(template) }}
        ></AdmissionEdit>} */}
    </>
}

export default Index;