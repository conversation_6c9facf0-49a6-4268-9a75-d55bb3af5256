import React, { useState, useContext, useEffect } from 'react'
import { Typography, Form, Modal, Row, Col, Tooltip, Divider, Tag, Popover, Space, Button, Notification } from '@douyinfe/semi-ui';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IconList, IconCheckList } from '@douyinfe/semi-icons';

import MachineSelector from "@/components/machine-selector";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';


import pinyin from 'tiny-pinyin';
import { sanitizeLabel } from '@/utils/common';
import { IconHelpCircle } from '@douyinfe/semi-icons';
import styles from './index.module.scss'

import UserModalSelector from '@/components/user-modal-selector';
import MachineViewer from '@/components/machine-viewer';


import {
    IconTick,
    IconPlus,
    IconArrowDown,
    IconArrowUp,
    IconArrowRight,
    IconArrowUpRight,
    IconAlignTop, IconAlignBottom,
    IconMinusCircle,
    IconMore
} from '@douyinfe/semi-icons'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { User, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'
import useServiceGroup from '@/pages/services/useServicesGroup';
import { ServiceRange } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/service_range_pb';
import useRangeData from '../useRangeData';
import { flylayerClient } from '@/services/core';


const { Title, Paragraph, Text } = Typography;
const { Input, RadioGroup, Radio, Switch, InputNumber } = Form

interface Props {
    close: () => void;
    success: () => void;
    record: ServiceRange;
}

const Index: React.FC<Props> = ({ close, success, record }) => {
    const [loading, setLoading] = useState(false);

    const flynetGeneral = useContext(FlynetGeneralContext);

    const [users, setUsers] = useState<User[]>(record.users || []);
    const [machines, setMachines] = useState<Machine[]>(record.machines || []);

    const [userModalVisible, setUserModalVisible] = useState(false);
    const [machineModalVisible, setMachineModalVisible] = useState(false);

    const {
        userGroups, loadingUserGroups,
        machineGroups, loadingMachineGroups,
    } = useRangeData();

    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
        global: boolean,
        userGroups: Array<string>,
        machineGroups: Array<string>,
        users: Array<bigint>,
        machines: Array<bigint>,
    }>>()

    const handleSubmit = async () => {
        await formApi?.validate();


        const values = formApi?.getValues();

        const userGroups = values?.userGroups.map(id => {
            return new UserGroup({ id: BigInt(id) });
        })
        const machineGroups = values?.machineGroups.map(id => {
            return new UserGroup({ id: BigInt(id) });
        })



        const range = new ServiceRange({
            id: record.id,
            alias: values?.alias,
            name: values?.name,
            description: values?.description,
            global: values?.global,
            userGroups: userGroups,
            machineGroups: machineGroups,
            users: users,
            machines: machines,
        });

        flylayerClient.updateServiceRange({
            flynetId: flynetGeneral.id,
            serviceRange: range
        }).then(() => {
            Notification.success({
                title: '修改可见范围成功',
                duration: 2000
            })

            close();
            success();
        }).catch((err) => {
            Notification.error({
                title: '修改可见范围失败',
                duration: 2000
            })
        }).finally(() => {
            setLoading(false);
        });

    }

    return <>
        <Modal
            width={600}
            title='编辑可见范围'
            visible
            onCancel={close}
            onOk={handleSubmit}
            okButtonProps={{ loading }}
            className='semi-modal'
            maskClosable={false}

        >
            <div>
                <Form
                    allowEmpty
                    getFormApi={SetFormApi}
                    initValues={{
                        alias: record.alias,
                        name: record.name,
                        description: record.description,
                        userGroups: record.userGroups.map(ug => ug.id + ''),
                        machineGroups: record.machineGroups.map(mg => mg.id + ''),
                    }}
                >

                    <Row gutter={12}>
                        <Col span={12}>
                            <Input field='alias' label='名称' trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '名称不能为空';
                                }
                                return '';
                            }} />
                        </Col>
                        <Col span={12}>
                            <Input field='name'
                                label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                                trigger={'blur'} validate={value => {
                                    if (!value) {
                                        return '编码不能为空';
                                    }

                                    // 编码不能以-开头
                                    if (value.trim().startsWith('-')) {
                                        return '编码不能以-开头'
                                    }

                                    if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                        return "编码只能包含字母、数字和'-'";
                                    }
                                    return '';
                                }}
                                readonly
                                required />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Input field='description' label='备注' />

                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Form.Select
                                style={{ width: '100%' }}
                                multiple
                                filter
                                loading={loadingUserGroups}
                                field='userGroups' label='用户组'
                                placeholder='请选择用户组'
                                dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                            >
                                {userGroups.map((item) => {
                                    return <Form.Select.Option key={item.id + ''} disabled={item.type == GroupType.GROUP_DYNAMIC} value={item.id + ''}>
                                        <Space>
                                            {item.type == GroupType.GROUP_DYNAMIC ? <Tooltip content="动态组"><IconCheckList title='动态组' /></Tooltip> : <Tooltip content="静态组"><IconList title='静态组' /></Tooltip>}
                                            {`${item.alias}(${item.name})`}
                                        </Space>
                                    </Form.Select.Option>
                                })}
                            </Form.Select>
                        </Col>
                    </Row>
                    <Row className='mb20'>
                        <Col span={24}>
                            <Paragraph type='tertiary' style={{ marginBottom: 8 }}>用户</Paragraph>
                            <Space>
                                {users.map((user) => {
                                    return <Tag key={user.id + ''} closable size='large' onClose={() => {
                                        setUsers(users.filter(u => u.id !== user.id))
                                    }}>{user.displayName}</Tag>
                                })}
                                <Button size='small' onClick={() => setUserModalVisible(true)} icon={<IconPlus />} ></Button>
                            </Space>
                        </Col>
                    </Row>
                    <Divider className='mb10'></Divider>
                    <Row>
                        <Col span={24}>
                            <Form.Select
                                style={{ width: '100%' }}
                                multiple
                                filter
                                loading={loadingMachineGroups}
                                field='machineGroups' label='设备组'
                                placeholder='请选择设备组'
                                dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                            >
                                {machineGroups.map((item) => {
                                    return <Form.Select.Option key={item.id + ''} disabled={item.type == GroupType.GROUP_DYNAMIC} value={item.id + ''}>
                                        <Space>
                                            {item.type == GroupType.GROUP_DYNAMIC ? <Tooltip content="动态组"><IconCheckList title='动态组' /></Tooltip> : <Tooltip content="静态组"><IconList title='静态组' /></Tooltip>}
                                            {`${item.alias}(${item.name})`}
                                        </Space>
                                    </Form.Select.Option>
                                })}
                            </Form.Select>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Paragraph type='tertiary' style={{ marginBottom: 8 }}>设备</Paragraph>
                            <Space>
                                {machines.map((machine) => {
                                    return <Tag key={machine.id + ''} closable size='large' onClose={() => {
                                        setMachines(machines.filter(m => m.id !== machine.id))
                                    }}>{machine.givenName ? machine.givenName : machine.name}</Tag>
                                })}
                                <Button size='small' onClick={() => setMachineModalVisible(true)} icon={<IconPlus />} ></Button>
                            </Space>
                        </Col>
                    </Row>

                </Form>
            </div>
        </Modal>
        {
            userModalVisible && <UserModalSelector
                multi
                close={() => setUserModalVisible(false)}
                onChange={(users) => {
                    setUsers(users as User[]);
                    setUserModalVisible(false);
                }}
                value={users}
            />
        }
        {
            machineModalVisible && <MachineSelector
                multi
                close={() => setMachineModalVisible(false)}
                onChange={(machines) => {
                    setMachines(machines as Machine[]);
                    setMachineModalVisible(false);
                }}
                value={machines}
            />
        }
    </>
}

export default Index