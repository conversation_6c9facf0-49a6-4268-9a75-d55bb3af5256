import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input, TabPane } from '@douyinfe/semi-ui';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';
import {Service, ServiceType, ServiceOrigin, ServiceProto, ServiceRouteMode} from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { ServiceRange } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/service_range_pb';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: ServiceRange
}

const Index: FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
  
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <>
        <Modal
            width={500}
            title={`删除可见范围 ${props.record.alias}(${props.record.name})`}
            visible={true}
            okButtonProps={{ 
                disabled: props.record.name !== confirmVal,
                loading, type: 'danger' }}
            onOk={() => {
                setLoading(true)

                flylayerClient.deleteServiceRange({
                    id: props.record.id,
                    flynetId: flynet.id
                }).then(() => {
                    Notification.success({
                        title: '删除成功',
                        duration: 2000
                    });
                    props.success && props.success();
                }).catch((e) => {
                    Notification.error({
                        title: '删除失败',
                        duration: 2000
                    });
                }).finally(() => {
                    setLoading(false)
                    props.close()
                })

            }}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'> 可见范围将被删除？
            </Paragraph><Paragraph className='mb20'> 输入 <b>{props.record.name}</b> 以确认删除
            </Paragraph>
            <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
