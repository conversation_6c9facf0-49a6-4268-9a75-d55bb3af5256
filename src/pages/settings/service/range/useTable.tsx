import { useEffect, useState, useContext } from 'react';
import { Typography, Dropdown, Space, Notification, Button, Tag, Divider, Select, Popover } from '@douyinfe/semi-ui';
import { useNavigate } from 'react-router-dom';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ServiceRange } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/service_range_pb';
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { flylayerClient } from '@/services/core';

const { Title, Paragraph } = Typography;
export type RangeFilter = {
    query: string;
}

const useTable = (initFilter: RangeFilter) => {

    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    // 数据是否正在加载中
    const [loading, setLoading] = useState(true);
    // 范围列表
    const [ranges, setRanges] = useState<ServiceRange[]>([]);
    // 全部范围列表
    const [allRanges, setAllRanges] = useState<ServiceRange[]>([]);

    const [filter, setFilter] = useState(initFilter);

    // 编辑弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 删除弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);
    // 选中的范围
    const [selectedRange, setSelectedRange] = useState<ServiceRange>();
    // 重新加载标志
    const [reloadFlag, setReloadFlag] = useState(false);

    // 当前页码
    const [page, setPage] = useState(1);
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    const pageSize = 20;

    // 表格列
    const columns = [
        {
            title: '范围名称',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (text: string, record: ServiceRange) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                        <div>
                            <Title heading={6}>
                                {record.alias} {record.description &&
                                    <Popover content={<div className='p10'>{record.description}</div>}>
                                        <IconArticle style={{
                                            fontSize: 14,
                                            marginLeft: '4px',
                                            color: '#999'
                                        }} />
                                    </Popover>}
                            </Title>
                            <Paragraph size='small'>{record.name}</Paragraph>
                        </div>
                    </div>
                </>
            }
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            render: (text: string, record: ServiceRange) => {
                return <Paragraph ellipsis={{ rows: 1, expandable: true }}>{record.description}</Paragraph>
            }
        },
        {
            title: '全局',
            dataIndex: 'global',
            key: 'global',
            render: (text: boolean, record: ServiceRange) => {
                return record.global ? '是' : '否';
            }
        },
        {
            width: 100,
            title: '',
            dataIndex: 'operate',
            render: (field: string, record: ServiceRange) => {
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => {
                                setSelectedRange(record)
                                setEditVisible(true)
                            }}>编辑范围</Dropdown.Item>
                            <Dropdown.Divider />

                            <Dropdown.Item type="danger" onClick={() => {
                                setDelVisible(true)
                                setSelectedRange(record)
                            }} >删除范围</Dropdown.Item>


                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        }
    ];

    // 过滤数据
    const doFilter = (src: Array<ServiceRange>, filter: RangeFilter) => {
        let result = src;
        const query = filter.query ? filter.query.trim() : '';
        if (query) {
            result = result.filter((item) => {
                return item.name.includes(query) || item.description.includes(query) || item.alias.includes(query);
            })
        }
        setTotal(result.length);
        return result;
    }

    // 加载数据
    const query = async () => {
        setLoading(true);

        flylayerClient.listServiceRanges({
            flynetId: flynet.id
        }).then((res) => {
            setRanges(res.serviceRanges);
            setAllRanges(res.serviceRanges);
            setTotal(res.serviceRanges.length);
        }).catch((e) => {
            Notification.error({ title: '获取范围列表失败' });
        }).finally(() => {
            setLoading(false);
        });

    }


    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setPage(1)
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    const handleFilterChange = (filter: RangeFilter) => {
        setFilter(filter);
        setRanges(doFilter(allRanges, filter));
    }

    return {
        loading,
        ranges,
        allRanges,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedRange,
        setSelectedRange,
        reloadFlag,
        setReloadFlag,
        page,
        setPage,
        pageSize,
        total,
        columns,
        doFilter,
        filter,
        handleFilterChange
    }
}

export default useTable;