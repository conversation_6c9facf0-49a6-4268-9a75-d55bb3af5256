import React, { useState, useContext } from 'react'
import { Table, Button, Row, Col, Typography, Space, Breadcrumb, Input } from '@douyinfe/semi-ui';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { getQueryParam } from '@/utils/query';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { BASE_PATH } from '@/constants/router';
import useTable, { RangeFilter } from './useTable';
import { IconSearch } from '@douyinfe/semi-icons';
import { ServiceRange } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/service_range_pb';
import TableEmpty from '@/components/table-empty';

import New from './new';
import Edit from './edit';
import Del from './del';

const { Title, Paragraph } = Typography;

// 根据URL参数设置过滤参数
const getRangeFilter = (location: Location): RangeFilter => {
    const query: string = getQueryParam('query', location) as string;

    return {
        query: query || '',
    }
}

const Index: React.FC = () => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    const [createVisible, setCreateVisible] = useState(false);

    const initFilter: RangeFilter = getRangeFilter(useLocation());

    const {
        loading,
        ranges,
        allRanges,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedRange,
        setSelectedRange,
        reloadFlag,
        setReloadFlag,
        page,
        setPage,
        pageSize,
        total,
        columns,
        filter,
        handleFilterChange,
    } = useTable(initFilter);



    return <>

        <Row className='mb10'>
            <Col span={20}>
                <Title heading={4} className="mb2">可见范围</Title>
                <Paragraph type='tertiary'>设置服务可见范围</Paragraph>
            </Col>
            <Col span={4}>
                <div className='btn-right-col mb10'>
                    <Space>
                        <Button theme='solid' onClick={() => setCreateVisible(true)}>新建范围</Button>
                    </Space>
                </div>
            </Col>
        </Row>
        <Row className='mb20'>
            <Col span={24}>
                <Input
                    placeholder='搜索范围'
                    prefix={<IconSearch />}
                    value={filter.query}
                    onChange={(val) => {
                        handleFilterChange({ query: val });
                    }}
                />
            </Col>
        </Row>
        <Table
            rowKey={(record?: ServiceRange) => record ? record.id + '' : ''}
            empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={ranges} pagination={false}
        ></Table>

        {createVisible && <New
            close={() => setCreateVisible(false)}
            success={() => {
                setCreateVisible(false)
                setReloadFlag(true);
            }}
        />}
        {editVisible && selectedRange && <Edit
            close={() => setEditVisible(false)}
            success={() => {
                setReloadFlag(true);
                setEditVisible(false)
            }}
            record={selectedRange}
        />}
        {delVisible && selectedRange && <Del
            close={() => setDelVisible(false)}
            success={() => {
                setReloadFlag(true);
                setDelVisible(false)
            }}
            record={selectedRange} />
        }
    </>
}

export default Index;
