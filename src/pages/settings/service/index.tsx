import React, { useState, useContext, useEffect } from 'react'
import { Typo<PERSON>, Row, Switch, Col, Divider } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';


import Range from './range';
const { Title, Paragraph } = Typography;


const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    return <>
        <div className='settings-page'>
            <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.service.title' })}</Title>
            <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.service.description' })}</Paragraph>
            <Range />
        </div></>
}

export default Index