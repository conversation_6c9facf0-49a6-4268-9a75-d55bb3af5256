import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { useNavigate } from 'react-router-dom';
import { useLocale } from '@/locales';

import TableEmpty from '@/components/table-empty';
import { DataSource } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/datasource_pb"
import New from './new';
import Edit from './edit';
import Del from './del';
import styles from './index.module.scss';
import useTable from './useTable';

const { Title, Paragraph, Text } = Typography;

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const navigate = useNavigate();
    const [newVisible, setNewVisible] = useState(false);

    const {
        loading,
        dataSources,
        columns,
        expandRowRender,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        setReloadFlag,
        selectedDataSource,
        setSelectedDataSource,
    } = useTable();
    return <>
        <div className='settings-page'>
            <Row className='mb10'>
                <Col span={20}>
                    <div><Title heading={3} className="mb2">{formatMessage({ id: 'settings.datasource.title' })}</Title>
                    </div></Col>
                <Col span={4}><div className='btn-right-col'>
                    <Space>
                        <Button
                            onClick={() => navigate(`${BASE_PATH}/settings/datasource/policies/import`)}
                        >{formatMessage({ id: 'settings.datasource.importPolicy' })}</Button>
                        <Button onClick={() => navigate(`${BASE_PATH}/settings/datasource/users/import`)}>{formatMessage({ id: 'settings.datasource.importUser' })}</Button>
                        <Button theme='solid' onClick={() => setNewVisible(true)}>{formatMessage({ id: 'components.common.add' })}</Button>
                    </Space>
                </div></Col>
            </Row>
            <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.datasource.description' })}</Paragraph>
            <Table
                rowKey={(record?: DataSource) => record ? record.id + '' : ''}
                loading={loading}
                columns={columns}
                dataSource={dataSources}
                expandRowByClick={true}
                expandedRowRender={expandRowRender}
                defaultExpandAllRows={true}
                rowExpandable={() => true}
                pagination={false}
                empty={<TableEmpty loading={loading} />}
            ></Table>
        </div>
        {newVisible && <New
            close={() => setNewVisible(false)}
            success={() => {
                setNewVisible(false);
                setReloadFlag(true);
            }}
        />}
        {
            editVisible && <Edit
                id={selectedDataSource?.id || BigInt(0)}
                close={() => {
                    setEditVisible(false);
                    setSelectedDataSource(undefined);
                }}
                success={() => {
                    setEditVisible(false);
                    setSelectedDataSource(undefined);
                    setReloadFlag(true);
                }}
            ></Edit>
        }
        {
            delVisible && selectedDataSource && <Del
                record={selectedDataSource}
                close={() => {
                    setDelVisible(false);
                    setSelectedDataSource(undefined);
                }}
                success={() => {
                    setDelVisible(false);
                    setSelectedDataSource(undefined);
                    setReloadFlag(true);

                }}
            ></Del>
        }
    </>
}

export default Index;