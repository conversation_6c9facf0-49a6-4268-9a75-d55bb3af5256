import React, { useState, useContext, useEffect } from "react";
import { Modal, Form, Row, Col, Skeleton, Notification, Divider, Popover } from "@douyinfe/semi-ui";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconHelpCircle } from '@douyinfe/semi-icons';
import CodeEditor from '@/components/code-editor';
import { DataSource } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/datasource_pb"

import { flylayerClient } from "@/services/core";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

const { Input } = Form

interface Props {
    close: () => void,
    success?: () => void,
    id: bigint,
}

const Index: React.FC<Props> = (props) => {
    const VALUE_FIELD = 'admission';
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
    }>>()
    // 数据是否正在加载中
    const [loading, setLoading] = useState(false);
    // 是否正在保存
    const [saveLoading, setSaveLoading] = useState(false);

    const [dataSource, setDataSource] = useState<DataSource>();

    useEffect(() => {
        setLoading(true);
        flylayerClient.getDataSource({
            dataSourceId: props.id,
        }).then(res => {
            setLoading(false);
            const dataSource = res.dataSource;
            if (dataSource) {
                setDataSource(dataSource);
                formApi?.setValues({
                    name: dataSource.name,
                    description: dataSource.description,
                    alias: dataSource.alias,
                })
                const fields = dataSource.value?.fields;
                if (fields) {
                    const text = fields[VALUE_FIELD]?.kind?.value || '';
                    setTextValue(text as string)
                }
            }
        })
    }, [])

    const handleSubmit = async () => {
        if (!formApi) {
            return
        }
        await formApi.validate();


        const values = formApi?.getValues();
        if (!values) {
            return;
        }
        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';

        setSaveLoading(true);
        flylayerClient.updateDataSource({
            dataSourceId: props.id,
            name,
            description,
            alias,
            value: {
                fields: {
                    admission: {
                        kind: {
                            value: textValue,
                            case: 'stringValue'
                        }
                    }
                }

            },
        }).then(res => {
            Notification.success({
                title: '编辑数据源成功',
                content: '编辑数据源成功'
            });
            props.close();
            props.success && props.success();
        }, err => {
            Notification.error({
                title: '编辑数据源失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        })
    }

    const [textValue, setTextValue] = useState<string>('')

    return <>
        <Modal
            title="编辑数据源"
            visible={true}
            onOk={handleSubmit}
            onCancel={props.close}
            width={600}
            okButtonProps={{ loading:saveLoading }}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Skeleton loading={loading} placeholder={
                <>
                    <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title>
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />
                </>
            }>
                {dataSource && <Form
                allowEmpty
                initValues={{
                    name: dataSource?.name,
                    description: dataSource?.description,
                    alias: dataSource?.alias,
                }}
                getFormApi={setFormApi}>
                <Row gutter={12}>
                    <Col span={12}>
                        <Input field='alias' label='名称' trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={12}>
                        <Input field='name'
                            label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}

                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '编码不能为空';
                                }
                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '编码不能以-开头'
                                }
                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "编码只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            required />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Input field='description' label='备注' />
                    </Col>
                </Row>
                <Divider></Divider>
                <CodeEditor value={textValue} height='280px' language="json" onChange={(value) => setTextValue(value||'')} ></CodeEditor>


            </Form>}
            
            </Skeleton>

        </Modal>
    </>
}

export default Index