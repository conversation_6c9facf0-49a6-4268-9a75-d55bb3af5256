import React, { useContext, useEffect, useState } from 'react'
import { AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { Typography, Space, Tag, Divider, Notification, Banner, Row, Col, Popconfirm, Steps, Table, Select, Tabs, TabPane, Popover } from '@douyinfe/semi-ui';

import { Model } from './model';
import { BASE_PATH } from '@/constants/router';
import { usePreData } from './usePreData';
const { Title, Paragraph, Text } = Typography;

const Index: React.FC<{
    data: Array<Model>,
    onPreData: (newGroups: AclGroup[], existGroups: AclGroup[]) => void
}> = (props) => {
    const {
        existGroups,
        newGroups,
        existAcls,
        newAcls
    } = usePreData(props.data, props.onPreData);

    const aclColumns = [
        {
            title: '策略名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '策略描述',
            dataIndex: 'description',
            key: 'description',
        },
        {
            title: '源',
            dataIndex: 'src',
            key: 'src',
            render: (field: string, record: AclOrigin, index: number) => {
                return <Popover content={<div className='p10' style={{ maxWidth: 400 }}>
                    <Space style={{ flexWrap: 'wrap' }}>
                        {record.src?.map((item, i) => {
                            return <Tag key={i}>{item.value}</Tag>
                        })}
                    </Space>
                </div>}>
                    <Text ellipsis={true} style={{ cursor: 'pointer' }}>查看</Text>
                </Popover>
            }
        },
        {
            title: '目标',
            dataIndex: 'dst',
            key: 'dst',
            render: (field: string, record: AclOrigin, index: number) => {
                return <Popover content={<div className='p10' style={{ maxWidth: 400 }}>
                    <Space style={{ flexWrap: 'wrap' }}>
                        {record.dst?.map((item, i) => {
                            return <Tag key={i} style={{ marginBottom: 5 }}>{item.value}</Tag>
                        })}
                    </Space>
                </div>}>
                    <Text ellipsis={true} style={{ cursor: 'pointer' }}>查看</Text>
                </Popover>
            }
        }
    ]


    return <>
        <Title heading={4} className='mb10'>
            策略组
        </Title>
        <Row className='mb40' gutter={20}>
            <Col span={12}>
                <Paragraph className='mb10'>
                    已存在的策略组
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '编码',
                            dataIndex: 'name',
                            key: 'name',
                        },
                        {
                            title: '名称',
                            dataIndex: 'description',
                            key: 'description',
                        }
                    ]}
                    dataSource={existGroups}
                    rowKey="name"
                />
            </Col>
            <Col span={12}>
                <Paragraph className='mb10'>
                    需要新建的策略组
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '编码',
                            dataIndex: 'name',
                            key: 'name',
                        },
                        {
                            title: '名称',
                            dataIndex: 'description',
                            key: 'description',
                        }
                    ]}
                    dataSource={newGroups}
                    rowKey="name"
                />
            </Col>
        </Row>
        <Divider className='mb20' />

        <Title heading={4} className='mb10'>
            策略
        </Title>
        <Row className='mb40' gutter={20}>
            <Col span={12}>
                <Paragraph className='mb10'>
                    已存在的策略
                </Paragraph>

                <Table
                    pagination={false}
                    virtualized
                    scroll={{ y: 600 }}
                    columns={aclColumns}
                    dataSource={existAcls}
                    rowKey="id"
                />
            </Col>
            <Col span={12}>

                <Paragraph className='mb10'>
                    需要新建的策略
                </Paragraph>

                <Table
                    pagination={false}
                    virtualized
                    scroll={{ y: 600 }}
                    columns={aclColumns}
                    dataSource={newAcls}
                    rowKey="id"
                />
            </Col>
        </Row>

    </>
}

export default Index;