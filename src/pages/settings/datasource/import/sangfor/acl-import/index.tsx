import React, { useContext, useState } from 'react'
import { AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { Typography, Upload, Breadcrumb, Space, Button, Input, Notification, Banner, Row, Col, Popconfirm, Steps, Table } from '@douyinfe/semi-ui';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import Papa from 'papaparse';
import { Model } from './model';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { isUTF8Encoding } from '@/utils/file';
import PreData from './pre-data';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';

const { Text } = Typography;
import styles from './index.module.scss'


const Index: React.FC = () => {
    const flynet = useContext(FlynetGeneralContext);
    // 忽略行
    const SKIP_ROW = 3;
    const columns: Array<ColumnProps> = [{
        title: '名称',
        dataIndex: 'name',
        render: (text, record, index) => {
            return <>
                <Input value={text} onChange={(val) => {
                    let newData = [...data];
                    newData[index].name = val;
                    setData(newData);
                }} />
            </>
        }
    }, {
        title: '所属资源组',
        dataIndex: 'group',
        render: (text, record, index) => {
            return <>
                <Input value={text} onChange={(val) => {
                    let newData = [...data];
                    newData[index].group = val;
                    setData(newData);
                }} />
            </>
        }
    }, {
        title: '资源描述',
        dataIndex: 'description',
        render: (text, record, index) => {
            return <>
                <Input value={text} onChange={(val) => {
                    let newData = [...data];
                    newData[index].description = val;
                    setData(newData);
                }} />
            </>
        }
    }, {
        title: '资源类型',
        dataIndex: 'type',
    }, {
        title: '资源地址',
        dataIndex: 'dst',
    }, {
        title: '可访问该资源的组、用户列表',
        dataIndex: 'src',
    }, {
        title: '是否有效',
        dataIndex: 'invalid',
        render: (text, record) => {
            return <>{record.invalid ? <Text type='danger'>{record.invalidReason}</Text> : '是'}</>
        }
    }
    ];


    // 表格内容数据
    const [data, setData] = useState<Array<Model>>([]);

    // 无效数据
    const [invalidData, setInvalidData] = useState<Array<Model>>([]);
    // 有效数据
    const [validData, setValidData] = useState<Array<Model>>([]);
    // 文件
    const [file, setFile] = useState<File>();
    // 文件
    const [files, setFiles] = useState<FileItem[]>();

    const [importTarget, setImportTarget] = useState('');


    const handleFileSelect = async (files: File[]) => {
        if (files.length > 0) {
            const isUTF8 = await isUTF8Encoding(files[0]);

            Papa.parse(files[0] as any, {
                encoding: isUTF8 ? 'UTF-8' : 'GBK',
                complete(result, file) {
                    if (result.errors && result.errors.length > 0) {
                        Notification.error({
                            title: '导入失败',
                            content: '请检查文件格式是否正确',
                            duration: 2000,
                        });
                        return;
                    }

                    if (result.data && result.data.length > 2) {
                        let pureData = result.data.slice(SKIP_ROW);

                        let data: Array<Model> = [];
                        let invalidData: Array<Model> = [];
                        let validData: Array<Model> = [];
                        for (let i = 0; i < pureData.length - 1; i++) {
                            let item: string[] = pureData[i] as string[];
                            let isInvalid = false;
                            let invalidReason = '';
                            if (item.length < 6) {
                                isInvalid = true;
                                invalidReason = '数据不完整, 缺少字段';
                            } else if (item[0] == '') {
                                isInvalid = true;
                                invalidReason = '名称不能为空';
                            } else if (item[1] == '') {
                                isInvalid = true;
                                invalidReason = '所属资源组不能为空';
                            }
                            if (item[4] == '') {
                                isInvalid = true;
                                invalidReason = '资源地址不能为空';
                            }
                            if (item[5] == '') {
                                isInvalid = true;
                                invalidReason = '可访问该资源的组、用户列表不能为空';
                            }

                            let dataItem = {
                                name: item[0],
                                group: item[1],
                                description: item[2] ? item[2] : item[0],
                                type: item[3],
                                dst: item[4],
                                src: item[5],
                                invalid: isInvalid,
                                invalidReason: invalidReason,
                            }

                            data.push(dataItem);
                            if (isInvalid) {
                                invalidData.push(dataItem)
                            } else {
                                validData.push(dataItem);
                            }
                        }
                        setInvalidData(invalidData);
                        setValidData(validData);
                        setData(data);
                        // console.log(invalidData, validData);
                    }
                    setFile(file);
                },
            });
        }

        return false
    };


    // 下载无效数据
    const handleInvalidDataDown = () => {
        let csv = Papa.unparse(invalidData);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `无效数据.csv`;
        a.click();
    }


    // 下载模板
    const handleTemplateDown = () => {
        let csv = Papa.unparse([['名称', '所属资源组', '资源描述', '资源类型', '资源地址', '可访问该资源的组、用户列表']]);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `深信服策略导入模板.csv`;
        a.click();
    }

    // 系统中已经存在服务组
    const [existGroups, setExistGroups] = useState<AclGroup[]>()
    // 需要新建服务组
    const [newGroups, setNewGroups] = useState<AclGroup[]>();
    // 是否正在导入
    const [isImporting, setIsImporting] = useState(false);

    // 是否导入成功
    const [isImportSuccess, setIsImportSuccess] = useState(false);


    const [aclCount, setAclCount] = useState(0);
    const [groupCount, setGroupCount] = useState(0);
    // 导入
    const handleImport = () => {

        let aclCount = 0;
        let groupCount = 0;
        if (newGroups) {
            groupCount = newGroups.length;
            newGroups.forEach((group) => {
                aclCount += group.acls.length;
            })
        }
        if (existGroups) {
            existGroups.forEach((group) => {
                aclCount += group.acls.length;
            })
        }

        setAclCount(aclCount);
        setGroupCount(groupCount);

        setIsImporting(true);
        flylayerClient.importAclAndGroup({
            flynetId: flynet.id,
            existGroups: existGroups,
            newGroups: newGroups,
        }).then((res) => {
            console.log('handleImport', res);
            setIsImportSuccess(true);
        }).catch((err) => {
            console.log('handleImport', err);
            setIsImportSuccess(false);
        }).finally(() => {
            setIsImporting(false);
            setStep(2);
        });

    }

    // 继续导入
    const handleContinueImport = () => {
        setFile(undefined);
        setFiles([])
        setData([]);
        setInvalidData([]);
        setStep(0);
    }

    // 步骤
    const [step, setStep] = useState(0);

    return <>
        <div className='settings-page'>
            <Breadcrumb className='mb20' routes={
                [

                    {
                        name: '设置',
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource`,
                        href: `${BASE_PATH}/settings/datasource`,
                        name: '数据源'
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource/policies/import`,
                        href: `${BASE_PATH}/settings/datasource/policies/import`,
                        name: '策略数据导入',
                    },
                    {
                        name: '深信服',
                    }
                ]
            }>
            </Breadcrumb>
            {/* <Breadcrumb className='mb20' routes={
                [
                    {
                        path: `${BASE_PATH}/settings`,
                        href: `${BASE_PATH}/settings`,
                        name: '设置'
                    },
                    {
                        path: `${BASE_PATH}/settings/import`,
                        href: `${BASE_PATH}/settings/import`,
                        name: '数据导入',
                    },
                    {
                        path: `${BASE_PATH}/settings/import/sangfor`,
                        href: `${BASE_PATH}/settings/import/sangfor`,
                        name: '深信服',
                    },
                    {
                        name: '策略',
                    }
                ]
            }>
            </Breadcrumb> */}
            <Steps type='basic' className='mb20' current={step}>
                <Steps.Step title="上传文件" />
                <Steps.Step title="确认数据" />
                <Steps.Step title="导入完成" />
            </Steps>
            <><div style={{ display: step == 0 ? '' : 'none' }} className='mt20 mb20'>
                <Row className='mb20' gutter={20}>
                    <Col span={12}>
                        <Space align='start'>
                            <Upload
                                style={{ width: 300 }}
                                action=''
                                draggable
                                dragMainText="点击上传文件或拖拽文件到这里"
                                dragSubText="支持csv文件"
                                onFileChange={handleFileSelect}
                                fileList={files}
                                onChange={({ fileList }) => setFiles(fileList)}
                                uploadTrigger='custom'
                                limit={1}
                                accept='.csv'
                            ></Upload>
                        </Space>
                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>
                                {/* <Button size='large' icon={<IconPaperclip />} onClick={handleTemplateDown}>模板下载</Button> */}
                                <Button size='large' disabled={!invalidData || invalidData.length == 0} onClick={handleInvalidDataDown}>无效数据下载</Button>
                                <Button disabled={!file} size='large' theme='solid' onClick={() => { setStep(1) }}>下一步</Button>
                            </Space>
                        </div>

                    </Col>
                </Row>

                {file && <Table
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    virtualized
                    scroll={{ y: 600 }}
                    empty={<TableEmpty loading={false}></TableEmpty>}></Table>}
            </div>
            </>
            {step === 1 && <><div className='mt20 mb20'>
                <Row className='mb20' gutter={20}>
                    <Col span={12}>

                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>
                                <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                                <Button disabled={!file} size='large' onClick={() => { setStep(0) }}>上一步</Button>

                                <Popconfirm
                                    title="确认导入？"
                                    content="确认之后，下面的数据将会被导入数据库"
                                    onConfirm={handleImport}
                                    disabled={(!newGroups || newGroups.length == 0) && (!existGroups || existGroups.length == 0)}
                                >
                                    <Button size='large' theme='solid' loading={isImporting}
                                        disabled={(!newGroups || newGroups.length == 0) && (!existGroups || existGroups.length == 0)}
                                    >开始导入</Button>
                                </Popconfirm>
                            </Space>

                        </div>

                    </Col>
                </Row>
                <div>
                    <PreData data={validData} onPreData={
                        (newGroups, existGroups) => {
                            setExistGroups(existGroups);
                            setNewGroups(newGroups);
                        }
                    }></PreData>

                </div>

            </div>
            </>}
            {step === 2 && <><div className='mt20 mb20'>
                <Row className='mb20' gutter={20}>
                    <Col span={12}>

                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>
                                <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                                <Popconfirm
                                    title="继续导入？"
                                    content="确定继续导入，之前的文件和数据将会被清空"
                                    onConfirm={handleContinueImport}

                                >
                                    <Button size='large' theme='solid' >继续导入</Button>
                                </Popconfirm>


                                {/* <Button disabled={!file} size='large' onClick={()=>{setStep(1)}}>上一步</Button> */}
                            </Space>
                        </div>
                    </Col>
                </Row>
                {isImportSuccess ? <Banner
                    className='mb20'
                    closeIcon={null}
                    fullMode={false}
                    title="导入数据成功"
                    type="success"
                    bordered
                    description={`导入了${groupCount}个策略组，${aclCount}条策略。`}
                >
                </Banner> :
                    <Banner
                        className='mb20'
                        closeIcon={null}
                        fullMode={false}
                        title="导入数据失败"
                        type="warning"
                        bordered
                        description="请重试或联系管理员。"
                    >
                    </Banner>}

            </div>
            </>}
        </div>
    </>
}

export default Index;
