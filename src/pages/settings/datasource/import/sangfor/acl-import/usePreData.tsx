import { useEffect, useState, FC, useContext } from 'react';
import { AclOrigin, AclGroup, AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';
import pinyin from 'tiny-pinyin';
import { Service, ServiceType, ServiceGroup, ServiceRouteMode, ServiceNode, ServiceNodeType, ServicePort, ServiceProto } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { Model } from './model';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { sanitizeLabel, ipRangeToCidr } from '@/utils/common';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';

const usePreData = (
    data: Array<Model>,
    onPreData: (newGroups: AclGroup[], existGroups: AclGroup[]) => void
    ) => {
    const flynet = useContext(FlynetGeneralContext); 
    // const globalConfig = useContext(GlobalConfigContext);
    
    // 系统中已经存在策略组
    const [existGroups, setExistGroups] = useState<AclGroup[]>()
    // 需要新建策略组
    const [newGroups, setNewGroups] = useState<AclGroup[]>();
    // 系统中已经存在的策略
    const [existAcls, setExistAcls] = useState<AclOrigin[]>();
    // 需要新建的策略
    const [newAcls, setNewAcls] = useState<AclOrigin[]>();

    const parseSrc = (src: string) : AclOrigin_Resource[] => {
        let result: AclOrigin_Resource[] = []
        src = src.trim();

        if(src == '*') {
            let resource = new AclOrigin_Resource();
            resource.type = AclOrigin_ResourceType.ALL;
            resource.name = '全部';
            resource.value = '*';
            resource.disabled = false;
            result.push(resource);
        } else {
            let srcList = src.split(',')
            srcList.forEach((item) => {
                item = item.trim();
                let resource = new AclOrigin_Resource();
                resource.disabled = false;
                resource.name = item;

                if(item.endsWith('/')) {
                    let groupName = '';
                    if(item.startsWith('/')) {
                        groupName = item.substring(1, item.length - 1);
                    } else {
                        groupName = item.substring(0, item.length - 1);
                    }
                    groupName = groupName.replace(/\//g, '-');
                    groupName = pinyin.convertToPinyin(groupName, '', true).toLocaleLowerCase();
                    resource.type = AclOrigin_ResourceType.USER_GROUP;                    
                    resource.value = 'group:' + groupName;
                } else {
                    resource.type = AclOrigin_ResourceType.USER;
                    let strArr = item.split('/');
                    let username = strArr[strArr.length - 1];
                    // if(username.indexOf('@') == -1) {
                    //     username  += '@' + globalConfig.code;
                    // }
                    resource.value = username;
                }


                result.push(resource);
            });
        }

        return result;
    }


    const parseDst = (dst: string, services: Service[]) : AclOrigin_Resource[] => {
        let result: AclOrigin_Resource[] = []
        dst = dst.trim();
        if(dst == '*') {
            let resource = new AclOrigin_Resource();
            resource.type = AclOrigin_ResourceType.ALL;
            resource.name = '全部';
            resource.value = '*:*';
            resource.disabled = false;
            result.push(resource);
        } else {
            let dstList = dst.split(';')
            dstList.forEach((item) => {
                let ipStr = '';
                let ip = '';
                let port = '';
                if(item.indexOf('/') == -1) {
                    port = '*';
                    ipStr = item;
                } else {
                    ipStr = item.split('/')[0];

                    let portStr = item.split('/')[1];
                    if(portStr == '1:65535') {
                        port = '*';
                    } else if(portStr.indexOf(':') == -1) {
                        port = portStr;
                    } else {
                        port = portStr.split(':')[0];
                    }
                }

                if(ipStr.indexOf('-') == -1) {
                    ip = ipStr;
                } else {
                    let ipStart = ipStr.split('-')[0];
                    let ipEnd = ipStr.split('-')[1];
                    ip = ipRangeToCidr(ipStart, ipEnd);
                }

                let service = services.find((item) => {
                    let find = false;
                    
                    item.serviceNodes?.forEach((node) => {
                        if(node.type == ServiceNodeType.SUBNET) {
                            if(node.ipv4 == ip) {
                                item?.ports?.forEach((portItem) => {
                                    if(portItem.port + '' == port) {
                                        find = true;
                                    }
                                })
                            }
                            
                        }
                    })

                    return find;
                });
                
                if(!service) {
                    let resource = new AclOrigin_Resource();
                    resource.type = AclOrigin_ResourceType.IP;
                    resource.name = ip;
                    resource.value = ip + ':' + port;
                    resource.disabled = false;
                    result.push(resource);
                } else {
                    let resource = new AclOrigin_Resource();
                    resource.type = AclOrigin_ResourceType.SERVICE;
                    resource.name = service.name;
                    resource.value = "svc:" + service.name + ":" + port;
                    resource.disabled = false;
                    result.push(resource);
                }

            });
        }
        return result;
    }
    
    // 计算策略组与策略
    const calGroups = async () => {
        const resGroup = await flylayerClient.listAclGroups({
            flynetId: flynet.id
        })
        const aclGroups = resGroup.groups;

        const resAcl = await flylayerClient.listAclOrigin({
            flynetId: flynet.id
        })

        const acls = resAcl.origins;

        const resService = await flylayerClient.listServices({
            flynetId: flynet.id
        })

        const services = resService.services;

        let existGroups: AclGroup[] = [];
        let newGroups: AclGroup[] = [];
        let existAcls: AclOrigin[] = [];
        let newAcls: AclOrigin[] = [];
        
        let existGroupNames: string[] = [];
        
        data.forEach((model: Model) => {
            let modelName = sanitizeLabel(pinyin.convertToPinyin(model.name ? model.name.trim() : '', '', true) ).toLowerCase();
            // 替换modelName中的 IP和特殊字符 . / ; : 空格等

            let existAcl = acls.find((item) => {
                return item.name == modelName;
            });
            if (existAcl) {
                existAcls.push(existAcl);
                return;
            }

            const acl = new AclOrigin();
            acl.action = 'accept'
            acl.priority = 1;
            acl.description = model.description ? model.description.trim() : '';
            acl.name = modelName;
            
            acl.src = parseSrc(model.src);
            acl.dst = parseDst(model.dst, services);
            
            newAcls.push(acl);
            
            let group: AclGroup | undefined;
            if (model.group) {
                let description = model.group.trim();
                let name = pinyin.convertToPinyin(model.group, '', true).toLowerCase();

                if (existGroupNames.includes(name)) {
                    group = existGroups.find((item) => {
                        return item.name == name
                    });
                    if (!group) {
                        group = newGroups.find((item) => {
                            return item.name == name
                        });
                    }
                } else {
                    group = aclGroups.find((item) => {
                        return item.name == name
                    })
                    if(group) {
                        group.acls = [];
                        existGroups.push(group);
                    } else {
                        group = aclGroups.find((item) => {
                            return item.name == name
                        })
                        if(group) {
                            group.acls = [];
                            existGroups.push(group);
                        } else {
                            const newGroup = new AclGroup();
                            newGroup.name = name;
                            newGroup.description = description;
                            newGroup.alias = description;
                            newGroup.acls = [];
                            newGroups.push(newGroup);
                            group = newGroup;
                        }
                        existGroupNames.push(name);
                    }
                }
            }

            group?.acls.push(acl);

        })

        setExistGroups(existGroups);
        setNewGroups(newGroups);
        setExistAcls(existAcls);
        setNewAcls(newAcls);
        onPreData(newGroups, existGroups);
    }

    useEffect(() => {
        calGroups();
    }, [])

    return {
        existGroups,
        newGroups,
        existAcls,
        newAcls
    }
}

export { usePreData }
