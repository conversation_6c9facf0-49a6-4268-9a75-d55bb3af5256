import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { useEffect, useState, FC, useContext } from 'react';
import { Model } from './model';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { isIPInRange } from "@/utils/format";

const useData = () => {

    // 无效数据
    const [invalidData, setInvalidData] = useState<Array<Model>>([]);
    // 有效数据
    const [validData, setValidData] = useState<Array<Model>>([]);

    // 其他数据
    const [otherData, setOtherData] = useState<Array<Model>>([]);


    // 当前选中数据
    const [selectedData, setSelectedData] = useState<Array<Model>>();

    // 各个子网路由数据
    const [mapData, setMapData] = useState<Map<string, Array<Model>>>(new Map());
    // 选中子网路由数据
    const [mapSelectedData, setMapSelectedData] = useState<Map<string, Array<Model>>>(new Map());

    // 待处理数据表格列
    const pendingColumns: Array<ColumnProps> = [{
        title: '资源名称',
        dataIndex: 'name',
        width: 200
    }, {
        title: '资源描述',
        dataIndex: 'description',
        width: 200,
    }, {
        title: '资源组名称', // 资源组名称
        dataIndex: 'group',
        width: 200
    }, {
        title: '地址信息', // 地址信息(支持IP段，多个地址并;隔开)
        dataIndex: 'addr',
        width: 480
    }
    ];
    // 选中数据列
    const selectedColumns: Array<ColumnProps> = [{
        title: '资源名称',
        dataIndex: 'name',
        width: 200
    }, {
        title: '资源描述',
        dataIndex: 'description',
        width: 200
    }, {
        title: '资源组名称', // 资源组名称
        dataIndex: 'group',
        width: 200
    }, {
        title: '地址信息', // 地址信息(支持IP段，多个地址并;隔开)
        dataIndex: 'addr',
        width: 480
    }
    ];

    const calRouterData = (routeMachines: Machine[]) => {
        // 是否在路由内
        const isModelInRoute = (model: Model, routeMachine: Machine): boolean => {
            if(model.addr.indexOf('-') > 0){
                return false;
            }
            let addrs = model.addr.split(';');
            if (!addrs || addrs.length != 1) {
                return false;
            }
            for (let i = 0; i < addrs.length; i++) {
                const addr = addrs[i];
                if (!addr) {
                    continue;
                }
                const ip = addr.split('/')[0];
                
                const port = addr.split('/')[1];
                if (port) {
                    if (port.indexOf(':') > 0) {
                        let ports = port.split(':');
                        if (ports.length > 1) {
                            if (ports[0] != ports[1]) {
                                continue;
                            }
                        }
                    }
                }

                if (!ip) {
                    continue;
                }

                for (let j = 0; j < routeMachine.advertisedRoutes.length; j++) {
                    let isInRange = isIPInRange(ip, routeMachine.advertisedRoutes[j])

                    if (isInRange) {
                        return true;
                    }
                }
            }
            return false;
        }
        // 已添加的数据
        const addedIds: number[] = [];

        let map: Map<string, Array<Model>> = new Map();
        routeMachines.forEach(machine => {
            if (machine.advertisedRoutes) {
                validData.forEach(model => {
                    const isInRoute = isModelInRoute(model, machine);
                    if (isInRoute) {
                        if (!addedIds.includes(model.index)) {
                            addedIds.push(model.index);
                        }

                        if (map.has(machine.id + '')) {
                            let value = map.get(machine.id + '');
                            if (value) {
                                value.push(model);
                            }
                        } else {
                            map.set(machine.id + '', [model]);
                        }
                    }
                })
            }
        })
        setMapData(map);

        // 未添加的数据
        let otherData: Model[] = [];
        validData.forEach(model => {
            if (!addedIds.includes(model.index)) {
                model.invalid = true;
                model.invalidReason = '不在路由内';
                otherData.push(model);
            }
        })
        setOtherData(otherData);

    };

    const removeSelectedData = (targetId: string) => {
        let copyedData: Map<string, Array<Model>> = new Map(mapData);
        let copyedSelectedData: Map<string, Array<Model>> = new Map(mapSelectedData);

        let originData: Array<Model> | undefined = copyedData.get(targetId);
        let removedData: Array<Model> | undefined = copyedSelectedData.get(targetId);
        if (!removedData || !originData) {
            return;
        }

        let newData: Array<Model> = [];

        originData?.forEach(data => {
            let find = false;
            if (removedData) {
                for (let i = 0; i < removedData?.length; i++) {
                    if (removedData[i].index == data.index) {
                        find = true;
                        break;
                    }
                }
            }
            if (!find) {
                newData.push(data)
            }
        })

        copyedData.set(targetId, newData);
        copyedSelectedData.set(targetId, []);

        setMapData(copyedData);
        setMapSelectedData(copyedSelectedData);
    }

    useEffect(() => {
        const map = new Map<string, Array<Model>>();
        validData.forEach((item: Model) => {
            const key = item.name;
            const value = map.get(key);
            if (value) {
                value.push(item);
            } else {
                map.set(key, [item]);
            }
        });
        setMapData(map);
    }, [validData]);

    return {
        invalidData,
        setInvalidData,
        validData,
        setValidData,
        otherData,
        setOtherData,
        selectedData,
        setSelectedData,
        mapData,
        setMapData,
        mapSelectedData,
        setMapSelectedData,
        pendingColumns,
        selectedColumns,
        removeSelectedData,
        calRouterData
    }
};

export { useData }