import { useEffect, useState, FC, useContext } from 'react';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { listMachines } from '@/services/device';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const useMachine = () => {
    const flynet = useContext(FlynetGeneralContext);
    // 设备列表
    const [routes, setRoutes] = useState<Machine[]>([]);

    
    // 当前标签页路由设备
    const [viewRoute, setViewRoute] = useState<Machine>();
    // 当前目标路由设备
    const [targetRoute, setTargetRoute] = useState<Machine>();


    const queryRoutes = async () => {
        const res = await listMachines(flynet.id);
        if (!res) {
            return;
        }
        const machines = res.machines.filter((machine: Machine) => machine.advertisedRoutes && machine.advertisedRoutes.length > 0);
        setRoutes(machines);
    }

    useEffect(() => {
        queryRoutes();
    }, []);

    return {
        routes,
        viewRoute,
        setViewRoute,
        targetRoute,
        setTargetRoute,
    }
}

export { useMachine }