import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { Service, ServiceType, ServiceGroup, ServiceRouteMode, ServiceNode, ServiceNodeType, ServicePort, ServiceProto, ServiceOrigin } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';
import pinyin from 'tiny-pinyin';
import { useEffect, useState, FC, useContext } from 'react';
import { Model } from './model';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { sanitizeLabel } from "@/utils/common";

const usePreData = (mapData: Map<string, Array<Model>>
    , routes: Array<Machine>, onPreData: (newGroups: ServiceGroup[], existGroups: ServiceGroup[]) => void) => {
    const flynet = useContext(FlynetGeneralContext);
    // 系统中已经存在服务组
    const [existGroups, setExistGroups] = useState<ServiceGroup[]>();
    // 需要新建服务组
    const [newGroups, setNewGroups] = useState<ServiceGroup[]>();
    // 系统中已经存在的服务
    const [existServices, setExistServices] = useState<Service[]>();
    // 需要新建的服务
    const [newServices, setNewServices] = useState<Service[]>();
    
    // 计算服务组与服务
    const calGroups = async () => {
        
        const resGroup = await flylayerClient.listServiceGroups({
            flynetId: flynet.id
        })
        const serviceGroups = resGroup.serviceGroups;

        const resService = await flylayerClient.listServices({
            flynetId: flynet.id
        })

        // key为节点地址，value为服务
        let mapServices: Map<string, Service> = new Map();

        const services = resService.services;

        let existGroups: ServiceGroup[] = [];
        let newGroups: ServiceGroup[] = [];
        let existServices: Service[] = [];
        let newServices: Service[] = [];
        
        let existGroupNames: string[] = [];

        mapData.forEach((value, key) => {
            const routeMachine = routes.find((item) => {
                return item.id + '' == key;
            })
            if(!routeMachine) {
                return;
            }

            const routeNode = new ServiceNode();
            routeNode.ipv4 = routeMachine.ipv4;
            routeNode.type = ServiceNodeType.GATEWAY;
            routeNode.name = routeMachine.name;
            routeNode.rank = 1;

            

            value.forEach((model: Model) => {
                if(mapServices.has(model.addr)) {
                    const service = mapServices.get(model.addr);
                    if(service) {
                        if(service.id && service.id > 0) {
                        } else{
                            service.serviceNodes?.push(routeNode);
                        }
                        
                    }
                    return;
                }

                let serviceName = sanitizeLabel(pinyin.convertToPinyin(model.name ? model.name.trim() : '', '', true) ).toLowerCase();
                let existService = services.find((item) => {
                    let find = false;
                    if(item.name == serviceName) {
                        find = true;
                    }
                    if(!find && item.ports) {
                        item.serviceNodes.forEach((node) => {
                            if(node.type == ServiceNodeType.SUBNET) {
                            item.ports.forEach((port) => {
                                if(port.proto == ServiceProto.TCP) {
                                    let nodeAddr = node.ipv4 + '/' + port.port;
                                    if(model.addr.indexOf(nodeAddr)>=0) {
                                        find = true;
                                    }
                                }
                            })   
                            }
                        })
                        
                    }

                    return find;
                });
                if (existService) {
                    existServices.push(existService);
                    mapServices.set(model.addr, existService);
                    return;
                }

                let group: ServiceGroup | undefined;
                if (model.group) {
                    
                    let description = model.group ? model.group.trim() : '';
                    let name = '';
    
                    name = pinyin.convertToPinyin(description, '', true).toLowerCase();

                    if (existGroupNames.includes(name)) {
                        group = existGroups.find((item) => {
                            return item.name == name
                        });
                        if(!group) {
                            group = newGroups.find((item) => {
                                return item.name == name
                            });
                        }
                    } else {
                        group = serviceGroups.find((item) => {
                            return item.name == name
                        });
                        if(group) {
                            group.services = [];
                            existGroups.push(group);
                        } else {
                            const newGroup = new ServiceGroup();
                            newGroup.name = name;
                            newGroup.description = description;
                            newGroup.alias = description;
                            newGroup.fullName = name;
                            newGroup.services = [];
                            newGroups.push(newGroup);
                            group = newGroup;
                        }
                        existGroupNames.push(name);
                    }
                }
                const service = new Service();
                
                service.description = model.description ? model.description.trim() : '';
                service.routeMode = ServiceRouteMode.FORWARD;
                service.origin = ServiceOrigin.SYSTEM_CONFIG;
                service.type = ServiceType.SYSTEM_DAEMON;
                service.name = serviceName;
                

                
                service.serviceNodes = [routeNode];
                service.ports = [];

                let addrs = model.addr.split(';');
                
                if(addrs.length > 0) {
                    addrs.forEach(addr => {
                        if(!addr) {
                            return;
                        }
                        const ip = addr.split('/')[0];
                        const port = addr.split('/')[1];
                        if(port) {
                            
                            if(port.indexOf(':') > 0) {
                                let ports = port.split(':');
                                
                                if(ports.length > 0) {
                                    if(ports[0]) {
                                        const servicePort = new ServicePort();
                                        servicePort.port = parseInt(ports[0]);
                                        servicePort.name = 'Default';
                                        servicePort.proto = ServiceProto.TCP;
                                        service.ports?.push(servicePort);
                                    }
                                }
                            }
                        }
                        if(!ip) {
                            return;
                        }
                        const serviceNode = new ServiceNode();
                        serviceNode.ipv4 = ip;
                        serviceNode.name = 'Node' + (service.serviceNodes.length);
                        serviceNode.rank = service.serviceNodes.length + 1;
                        serviceNode.type = ServiceNodeType.SUBNET;
                        service.serviceNodes?.push(serviceNode);
                    })
                }
                
                if (group) {
                    group.services?.push(service);
                }
                mapServices.set(model.addr, service);
                newServices.push(service);
            })
        })

        // 更新新建服务组的服务数量
        newGroups.forEach((newGroup) => {
            newGroup.serviceCount = newGroup.services?.length;
        })

        setExistGroups(existGroups);
        setNewGroups(newGroups);
        setNewServices(newServices);
        setExistServices(existServices);
        onPreData(newGroups, existGroups);
    }

    useEffect(() => {
        calGroups();
    }, [mapData])

    return {
        existGroups,
        newGroups,
        existServices,
        newServices
    }
}


export { usePreData }
