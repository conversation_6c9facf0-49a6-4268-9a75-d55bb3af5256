import React, { useState, useContext } from 'react'
import { Service, ServiceGroup, ServiceNode, ServiceNodeType, ServicePort } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { Typography, Input, Upload, Breadcrumb, Space, Button, Tag, Divider, Notification, Banner, Row, Col, Popconfirm, Steps, Table, Select, Tabs, TabPane } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import Papa from 'papaparse';
import TableEmpty from '@/components/table-empty';
import { ServiceRouteMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { Model } from './model';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { isUTF8Encoding } from '@/utils/file';
import { useMachine } from './useMachine';
import { useData } from './useData';
import PreData from './pre-data';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';


import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import styles from './index.module.scss'
import { flylayerClient } from '@/services/core';

const { Text } = Typography;

const Index: React.FC = () => {
    const flynet = useContext(FlynetGeneralContext);

    // 系统服务模式
    const [daemonMode, setDaemonMode] = useState<ServiceRouteMode>(ServiceRouteMode.FORWARD);

    const { routes, viewRoute, setViewRoute, targetRoute, setTargetRoute } = useMachine();
    const {
        invalidData,
        setInvalidData,
        validData,
        setValidData,
        otherData,
        setOtherData,
        selectedData,
        setSelectedData,
        mapData,
        setMapData,
        mapSelectedData,
        setMapSelectedData,
        pendingColumns,
        selectedColumns,
        removeSelectedData,
        calRouterData,
    } = useData();

    const columns: Array<ColumnProps> = [{
        title: '资源名称',
        dataIndex: 'name',
        width: 200,
        render: (text, record, index) => {
            return <>
                <Input value={text} onChange={(val) => {
                    let newData = [...data];
                    newData[index].name = val;
                    recalData(newData);
                }} />
            </>
        }
    }, {
        title: '资源描述',
        dataIndex: 'description',
        width: 300,
        render: (text, record, index) => {
            return <>
                <Input value={text} onChange={(val) => {
                    let newData = [...data];
                    newData[index].description = val;
                    recalData(newData);
                }} />
            </>
        }
    }, {
        title: '资源类型', // 资源类型(0为WEB应用，1为TCP应用，2为L3VPN)
        dataIndex: 'type',
        width: 100
    }, {
        title: '协议类型', // 协议类型(-1为全部协议，0为TCP，1为UDP，2为ICMP)
        dataIndex: 'protocolType',
        width: 100
    }, {
        title: '服务类型', // 服务类型(如HTTP)
        dataIndex: 'serviceType',
        width: 160
    }, {
        title: '应用程序路径', // 应用程序路径(当该资源为WEB应用的MAIL服务时，表示是域)
        dataIndex: 'appPath',
        width: 160
    }, {
        title: '资源组名称', // 资源组名称
        dataIndex: 'group',
        width: 160,
        render: (text, record, index) => {
            return <>
                <Input value={text} onChange={(val) => {
                    let newData = [...data];
                    newData[index].group = val;
                    recalData(newData);
                }} />
            </>
        }
    }, {
        title: '地址信息', // 地址信息(支持IP段，多个地址并;隔开)
        dataIndex: 'addr',
        width: 420,
        render: (text, record, index) => {
            return <>
                <Input value={text} onChange={(val) => {
                    let newData = [...data];
                    newData[index].addr = val;
                    recalData(newData);
                }} />
            </>
        }
    }, {
        title: '允许的客户端', // 允许的客户端(1<<0 PC端，1<<1 移动端，1<<2 EasyApp，1<<3 aWork)
        dataIndex: 'client',
        width: 160
    }, {
        title: '是否有效',
        width: 200,
        dataIndex: 'invalid',
        render: (text, record) => {
            return <>{record.invalid ? <Text type='danger'>{record.invalidReason}</Text> : '是'}</>
        }
    }
    ];

    // 表格内容数据
    const [data, setData] = useState<Array<Model>>([]);

    // 文件
    const [file, setFile] = useState<File>();
    // 文件
    const [files, setFiles] = useState<FileItem[]>();

    const handleFileSelect = async (files: File[]) => {
        if (files.length > 0) {
            const isUTF8 = await isUTF8Encoding(files[0]);

            Papa.parse(files[0] as any, {
                encoding: isUTF8 ? 'UTF-8' : 'GBK',
                complete(result, file) {
                    if (result.errors && result.errors.length > 0) {
                        Notification.error({
                            title: '导入失败',
                            content: '请检查文件格式是否正确',
                            duration: 2000,
                        });
                        return;
                    }
                    let modelIndex = 0;
                    if (result.data && result.data.length > 2) {
                        let pureData = result.data.slice(1);
                        let data: Array<Model> = [];
                        let invalidData: Array<Model> = [];
                        let validData: Array<Model> = [];
                        let existAddr: Array<string> = [];
                        for (let i = 0; i < pureData.length - 1; i++) {
                            let item: string[] = pureData[i] as string[];

                            let addrs = item[7].split(';');
                            if(addrs.length < 0) {
                                continue;
                            }
                            for(let j = 0; j < addrs.length; j++){
                                let addr = addrs[j];
                                if(!addr){
                                    continue;
                                }
                                let name = item[0];
                                if(j > 0) {
                                    name = name + '-' + j;
                                }
                                let dataItem: Model = {
                                    index: modelIndex++,
                                    name: name,
                                    description: item[1] ? item[1] : name,
                                    type: item[2],
                                    protocolType: item[3],
                                    serviceType: item[4],
                                    appPath: item[5],
                                    group: item[6],
                                    addr: addr,
                                    client: item[8],
                                    invalid: false,
                                    invalidReason: ''
                                }
    
                                calModelValid(dataItem);
                                if(!dataItem.invalid){
                                 
                                    if (existAddr.indexOf(dataItem.addr) >= 0) {
                                        dataItem.invalid = true;
                                        dataItem.invalidReason = '地址信息重复';
                                    } else {
                                        existAddr.push(dataItem.addr);
                                    }   
                                }
    
                                data.push(dataItem);
                                if (dataItem.invalid) {
                                    invalidData.push(dataItem);
                                } else {
                                    validData.push(dataItem);
                                }
                            }

                        }
                        setData(data)
                        setInvalidData(invalidData);
                        setValidData(validData);

                    }
                    setFile(file);
                },
            });
        }
        return false
    };

    const finishStep0 = () => {
        // 计算路由数据
        calRouterData(routes)
        setStep(1);
    }

    // 下载无效数据
    const handleInvalidDataDown = () => {
        let csv = Papa.unparse(invalidData.concat(otherData));
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `无效数据.csv`;
        a.click();
    }

    // 下载模板
    const handleTemplateDown = () => {
        let csv = Papa.unparse([['资源名称', '资源描述', '资源类型', '协议类型', '服务类型', '应用程序路径', '资源组名称', '地址信息', '允许的客户端']]);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `深信服服务导入模板.csv`;
        a.click();
    }

    // 系统中已经存在服务组
    const [existGroups, setExistGroups] = useState<ServiceGroup[]>()
    // 需要新建服务组
    const [newGroups, setNewGroups] = useState<ServiceGroup[]>();

    // 是否正在导入
    const [isImporting, setIsImporting] = useState(false);

    // 是否导入成功
    const [isImportSuccess, setIsImportSuccess] = useState(false);

    const [serviceCount, setServiceCount] = useState(0);
    const [groupCount, setGroupCount] = useState(0);

    // 导入
    const handleImport = () => {

        setIsImporting(true);

        let serviceCount = 0;
        let groupCount = 0;
        if(newGroups){
            groupCount = newGroups.length;
            newGroups.forEach((group)=>{
                serviceCount += group.services.length;
            })
        }
        if(existGroups){
            
            existGroups.forEach((group)=>{
                serviceCount += group.services.length;
            })
        }
        setServiceCount(serviceCount);
        setGroupCount(groupCount);

        flylayerClient.importServiceAndGroup({
            flynetId: flynet.id,
            existGroups: existGroups,
            newGroups: newGroups,
        }).then((res) => {
            setIsImportSuccess(true);
        }).catch((err) => {
            setIsImportSuccess(false);
        }).finally(() => {
            setIsImporting(false);
            setStep(3);
        });


    }

    // 继续导入
    const handleContinueImport = () => {
        setFile(undefined);
        setFiles([])
        setData([]);
        setInvalidData([]);
        setValidData([]);
        setOtherData([]);
        setSelectedData([]);

        setStep(0);
    }

    // 步骤
    const [step, setStep] = useState(0);

    // 计算数据是否有效
    const calModelValid = (model: Model) => {
        if (!model.name) {
            model.invalid = true;
            model.invalidReason = '资源名称不能为空';
        } else if (!model.group) {
            model.invalid = true;
            model.invalidReason = '资源组名称不能为空';
        } else if (!model.addr) {
            model.invalid = true;
            model.invalidReason = '地址信息不能为空';
        } else {
            model.invalid = false;
            model.invalidReason = '';
        }
    }

    // 重新计算有效数据
    const recalData = (newData: Array<Model>) => {

        let newInvalidData: Array<Model> = [];
        let newValidData: Array<Model> = [];

        let existAddr: Array<string> = [];

        newData.forEach((item) => {
            calModelValid(item);

            if(!item.invalid){
                             
                if (existAddr.indexOf(item.addr) >= 0) {
                    item.invalid = true;
                    item.invalidReason = '地址信息重复';
                } else {
                    existAddr.push(item.addr);
                }   
            }
            if (item.invalid) {
                newInvalidData.push(item);
            } else {
                newValidData.push(item);
            }
        });

        setData(newData);
        setInvalidData(newInvalidData);
        setValidData(newValidData);
        setOtherData(newInvalidData);
    };

    return <><div className='settings-page'>
        <Breadcrumb className='mb20' routes={
            [
                {
                    path: `${BASE_PATH}/services`,
                    href: `${BASE_PATH}/services`,
                    name: '服务'
                },
                {
                    path: `${BASE_PATH}/services/import`,
                    href: `${BASE_PATH}/services/import`,
                    name: '数据导入',
                },
                {
                    name: '深信服',
                }
            ]
        }>
        </Breadcrumb>
        {/* <Breadcrumb className='mb20' routes={
            [
                {
                    path: `${BASE_PATH}/settings`,
                    href: `${BASE_PATH}/settings`,
                    name: '设置'
                },
                {
                    path: `${BASE_PATH}/settings/import`,
                    href: `${BASE_PATH}/settings/import`,
                    name: '数据导入',
                },
                {
                    path: `${BASE_PATH}/settings/import/sangfor`,
                    href: `${BASE_PATH}/settings/import/sangfor`,
                    name: '深信服',
                },
                {
                    name: '服务',
                }
            ]
        }>
        </Breadcrumb> */}
        <Steps type='basic' className='mb20' current={step}>
            <Steps.Step title="上传文件" />
            <Steps.Step title="编辑数据" />
            <Steps.Step title="确认数据" />
            <Steps.Step title="导入完成" />
        </Steps>
        <><div style={{ display: step == 0 ? '' : 'none' }} className='mt20 mb20'>
            <Row className='mb20' gutter={20}>
                <Col span={12}>
                    <Space align='start'>
                        <Upload
                            disabled={!routes || routes.length == 0}
                            style={{ width: 300 }}
                            action=''
                            draggable
                            dragMainText="点击上传文件或拖拽文件到这里"
                            dragSubText="支持csv文件"
                            onFileChange={handleFileSelect}
                            fileList={files}
                            onChange={({ fileList }) => setFiles(fileList)}
                            uploadTrigger='custom'
                            limit={1}
                            accept='.csv'
                        ></Upload>
                    </Space>
                </Col>
                <Col span={12}>
                    <div className='btn-right-col'>
                        <Space>
                            {/* <Button size='large' icon={<IconPaperclip />} onClick={handleTemplateDown}>模板下载</Button> */}
                            <Button size='large' disabled={!invalidData || invalidData.length == 0} onClick={handleInvalidDataDown}>无效数据下载</Button>
                            <Button disabled={!file} size='large' theme='solid' onClick={() => finishStep0()}>下一步</Button>
                        </Space>
                    </div>

                </Col>
            </Row>

            <Divider className='mb20'></Divider>

            {file && <>
                <Space className='mb20'>
                    <Tag style={{ height: 32 }}>数据总数{data.length}</Tag>
                    <Tag style={{ height: 32 }}>有效条数{validData.length}</Tag>
                    <Tag style={{ height: 32 }}>无效条数{invalidData.length}</Tag>
                </Space>

                <Table
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    virtualized
                    scroll={{ y: 600 }}
                    bordered
                    rowKey={'index'}
                    empty={<TableEmpty loading={false}></TableEmpty>}></Table></>}
        </div>
        </>
        {step === 1 && <><div className='mt20 mb20'>
            <Row className='mb20' gutter={20}>
                <Col span={12}>
                    <Space>
                        <Text style={{ width: 120 }} type='tertiary'>服务模式</Text>
                        <Select placeholder="请选择服务模式" style={{ width: 160 }}
                            size='large'
                            value={daemonMode}
                            onChange={(val) => setDaemonMode(val as ServiceRouteMode)}>
                            <Select.Option value={ServiceRouteMode.FORWARD} >路由模式</Select.Option>
                            <Select.Option value={ServiceRouteMode.DIRECT} disabled>
                                直连模式
                            </Select.Option>
                        </Select>
                    </Space>
                </Col>
                <Col span={12}>
                    <div className='btn-right-col'>
                        <Space>
                            <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                            <Button disabled={!file} size='large' onClick={() => { setStep(0) }}>上一步</Button>
                            <Button size='large' theme='solid' onClick={() => setStep(2)} loading={isImporting}>下一步</Button>
                        </Space>
                    </div>
                </Col>
            </Row>
            <div>
                {daemonMode === ServiceRouteMode.FORWARD &&
                    <>
                        <Tabs
                            type='card'
                            activeKey={viewRoute ? viewRoute.id + '' : ''}
                            onChange={(key) => {
                                if (key === '') {
                                    setViewRoute(undefined);
                                }
                                let route = routes.find((route) => route.id + '' === (key));
                                setViewRoute(route)
                            }}
                        >
                            {routes.map((route) => {
                                let data = mapData.get(route.id + '');

                                if(!data || data.length == 0){
                                    return null
                                }
                                return <TabPane
                                    itemKey={route.id + ''}
                                    key={route.id + ''}
                                    tab={route.givenName ? route.givenName : route.name}
                                >
                                    <Space style={{ marginTop: 10, marginBottom: 10 }}>
                                        <Button
                                            onClick={() => {
                                                removeSelectedData(route.id + '');
                                            }}
                                            disabled={!mapSelectedData.get(route.id + '') || mapSelectedData.get(route.id + '')?.length == 0}>删除</Button>
                                        <Tag style={{ height: 32 }} size='large'>数据条数:{mapData.get(route.id + '') ? mapData.get(route.id + '')?.length : 0}</Tag>
                                    </Space>
                                    <Table
                                        virtualized
                                        pagination={false}
                                        columns={pendingColumns} rowKey={'index'}
                                        rowSelection={
                                            {
                                                onChange: (selectedRowKeys, selectedRows) => {
                                                    const mapCopy = new Map(mapSelectedData);
                                                    if (selectedRows) {
                                                        mapCopy.set(route.id + '', selectedRows);
                                                    }
                                                    setMapSelectedData(mapCopy);
                                                },
                                            }
                                        }
                                        dataSource={mapData.get(route.id + '')}
                                        scroll={{ y: 600, x: 1080 }}
                                        empty={<TableEmpty loading={false}></TableEmpty>}></Table>
                                </TabPane>
                            })}
                            <TabPane tab="无效数据" itemKey={''}>
                                {file && <>
                                    <Space style={{ marginTop: 10, marginBottom: 10 }}>
                                        <Tag style={{ height: 32 }}>数据条数:{otherData.length}</Tag>
                                    </Space>
                                    <Table columns={pendingColumns} rowKey={'index'}
                                        pagination={false}
                                        dataSource={otherData}
                                        virtualized
                                        scroll={{ y: 600, x: 1080 }}
                                        empty={<TableEmpty loading={false}></TableEmpty>}></Table></>}
                            </TabPane>
                        </Tabs>

                    </>}
            </div>

        </div>
        </>}
        {step === 2 && <><div className='mt20 mb20'>
            <Row className='mb20' gutter={20}>
                <Col span={12}>
                    <Space>
                    </Space>
                </Col>
                <Col span={12}>
                    <div className='btn-right-col'>
                        <Space>
                            <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                            <Button disabled={!file} size='large' onClick={() => { setStep(1) }}>上一步</Button>
                            <Popconfirm
                                    title="确认导入？"
                                    content="确认之后，下面的数据将会被导入数据库"
                                    onConfirm={handleImport}
                                    disabled={(!newGroups || newGroups.length == 0) && (!existGroups || existGroups.length == 0)}
                                >
                                    <Button size='large' theme='solid' loading={isImporting} disabled={(!newGroups || newGroups.length == 0) && (!existGroups || existGroups.length == 0)}>开始导入</Button>
                                </Popconfirm>
                        </Space>

                    </div>

                </Col>
            </Row>
            <PreData mapData={mapData} routes={routes}
                onPreData={(newGroups: ServiceGroup[], existGroups: ServiceGroup[]) => {
                    setExistGroups(existGroups);
                    setNewGroups(newGroups);
                }}
            ></PreData>
        </div>
        </>}
        {step === 3 && <><div className='mt20 mb20'>
            <Row className='mb20' gutter={20}>
                <Col span={12}>

                </Col>
                <Col span={12}>
                    <div className='btn-right-col'>
                        <Space>
                            <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                            <Popconfirm
                                title="继续导入？"
                                content="确定继续导入，之前的文件和数据将会被清空"
                                onConfirm={handleContinueImport}
                                
                            >
                                <Button size='large' theme='solid'
                                >继续导入</Button>
                            </Popconfirm>


                            {/* <Button disabled={!file} size='large' onClick={()=>{setStep(1)}}>上一步</Button> */}
                        </Space>

                    </div>

                </Col>
            </Row>
            {isImportSuccess ? <Banner
                className='mb20'
                closeIcon={null}
                fullMode={false}
                title="导入数据成功"
                type="success"
                bordered
                description={`成功导入${serviceCount}个服务，${groupCount}个服务组`}
            >
            </Banner> : <Banner
                className='mb20'
                closeIcon={null}
                fullMode={false}
                title="导入数据失败"
                type="warning"
                bordered
                description="请重试或联系管理员。"
            >
            </Banner>}

        </div>
        </>}
    </div>
    </>
}

export default Index;
