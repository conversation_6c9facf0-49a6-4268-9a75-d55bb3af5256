import React from 'react'
import { Typography, Divider, Row, Col, Table, Space, Tag, Popover } from '@douyinfe/semi-ui';
import { Service, ServiceGroup, ServiceProto, ServiceRouteMode, ServiceType, ServiceNode, ServiceNodeType, ServicePort } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { Model } from './model';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { usePreData } from './usePreData';

const { Title, Paragraph, Text } = Typography;

const Index: React.FC<{
    routes: Array<Machine>,
    mapData: Map<string, Array<Model>>
    onPreData: (newGroups: ServiceGroup[], existGroups: ServiceGroup[]) => void
}> = (props) => {
    const {
        existGroups, newGroups, existServices, newServices
    } = usePreData(props.mapData, props.routes, props.onPreData);

    const preServiceColumns = [
        {
            title: '服务名称',
            dataIndex: 'name',
            key: 'name',
        },
        // {
        //     title: '服务描述',
        //     dataIndex: 'description',
        //     key: 'description',
        // },
        {

            title: '协议/端口',
            dataIndex: 'ports',
            render: (field: string, record: Service, index: number) => {
                return <>
                    <Space>
                    {record.ports.map((port, i) => {
                        return <div key={i} style={{ marginBottom: 5 }}>{port.proto == ServiceProto.TCP ? <Tag size='small'>TCP/{port.port}</Tag> : <Tag size='small'>UDP/{port.port}</Tag>}</div>
                    })}
                    </Space>
                </>
            }
        },

        {
            title: '服务节点',
            dataIndex: 'servicesNodes',
            render: (field: string, record: Service, index: number) => {
                let gatewayNodes: Array<ServiceNode> = [];
                let subnetNodes: Array<ServiceNode> = [];
                if (record.type == ServiceType.SYSTEM_DAEMON) {
                    record.serviceNodes.forEach((item) => {
                        if (item.type == ServiceNodeType.GATEWAY) {
                            gatewayNodes.push(item)
                        } else if (item.type == ServiceNodeType.SUBNET) {
                            subnetNodes.push(item)
                        }
                    })
                }

                subnetNodes.sort((a, b) => {
                    return a.rank - b.rank
                })
                gatewayNodes.sort((a, b) => {
                    return a.rank - b.rank
                })

                return <>
                    <Popover content={<div className='p10'>
                        {record.type == ServiceType.REMOTE_DESKTOP &&
                            <>
                                {record.serviceNodes.map((n, i) => <Paragraph key={i}>{n.name} <Text copyable >{n.ipv4
                                }</Text></Paragraph>)}
                            </>}
                        {record.type == ServiceType.SYSTEM_DAEMON && record.routeMode == ServiceRouteMode.DIRECT && <>
                            <div>
                                {subnetNodes.map((n, i) => <Paragraph key={i}>{n.name} <Text copyable >{n.ipv4
                                }</Text></Paragraph>)}
                            </div></>}
                        {record.type == ServiceType.SYSTEM_DAEMON && record.routeMode == ServiceRouteMode.FORWARD && <>
                            <div >
                                <div className='mb10'>
                                    <Title heading={6} style={{ fontSize: 14 }} >连接器</Title>
                                    {gatewayNodes.map((n, i) => { return n.type == ServiceNodeType.GATEWAY ? <Paragraph key={i}>{n.name} <Text>{n.ipv4}</Text></Paragraph> : <Paragraph key={i}></Paragraph> })}
                                    <Title heading={6} style={{ fontSize: 14 }}>子网节点</Title>
                                    {subnetNodes.map((n, i) => { return n.type == ServiceNodeType.SUBNET ? <Paragraph key={i}>{n.name} <Text>{n.ipv4}</Text></Paragraph> : <Paragraph key={i}></Paragraph> })}

                                </div>

                            </div>
                        </>}
                    </div>}>
                        <Text ellipsis={true} style={{ cursor: 'pointer' }}>查看节点</Text>
                    </Popover>

                </>
            }
        }
    ];

    return <>
        <Title heading={4} className='mb10'>
            服务组
        </Title>
        <Row className='mb40' gutter={20}>
            <Col span={12}>
                <Paragraph className='mb10'>
                    已存在的服务组
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '编码',
                            dataIndex: 'name',
                            key: 'name',
                        },
                        {
                            title: '名称',
                            dataIndex: 'alias',
                            key: 'alias',
                        }
                    ]}
                    dataSource={existGroups}
                    rowKey="name"
                />
            </Col>
            <Col span={12}>

                <Paragraph className='mb10'>
                    需要新建的服务组
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '编码',
                            dataIndex: 'name',
                            key: 'name',
                        },
                        {
                            title: '名称',
                            dataIndex: 'alias',
                            key: 'alias',
                        }
                    ]}
                    dataSource={newGroups}
                    rowKey="name"
                />
            </Col>
        </Row>

        <Divider className='mb20' />

        <Title heading={4} className='mb10'>
            服务
        </Title>
        <Row className='mb40' gutter={20}>
            <Col span={12}>
                <Paragraph className='mb10'>
                    已存在的服务
                </Paragraph>
                <Table
                    pagination={false}
                    virtualized
                    scroll={{ y: 600 }}
                    columns={preServiceColumns}
                    dataSource={existServices}
                    rowKey="name"
                />
            </Col>
            <Col span={12}>
                <Paragraph className='mb10'>
                    需要新建的服务
                </Paragraph>
                <Table
                    pagination={false}
                    virtualized
                    scroll={{ y: 600 }}
                    columns={preServiceColumns}
                    dataSource={newServices}
                    rowKey="name"
                />

            </Col>
        </Row>
    </>
}

export default Index;
