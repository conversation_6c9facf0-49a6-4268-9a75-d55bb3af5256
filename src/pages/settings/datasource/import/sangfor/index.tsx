import React, { useContext, useEffect, useState } from 'react'
import { Typography, Breadcrumb, Space, Button, Divider, Notification, Avatar, Row, Col, Tabs, TabPane } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { Location, useLocation, useNavigate } from 'react-router-dom';

import UserImport from './user-import';
import ServiceImport from './service-import';
import AclImport from './acl-import';
const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss'
const Index: React.FC = () => {
    const navigate = useNavigate();
    return <>
        <div className='settings-page'><Breadcrumb routes={
            [
                {
                    path: `${BASE_PATH}/settings`,
                    href: `${BASE_PATH}/settings`,
                    name: '设置'
                },
                {
                    path: `${BASE_PATH}/settings/import`,
                    href: `${BASE_PATH}/settings/import`,
                    name: '数据导入',
                },
                {
                    name: '深信服',
                }
            ]
        }>
        </Breadcrumb>
            <Title heading={3} className='mb10' >深信服数据导入</Title>
            <Paragraph  type='tertiary' className='mb40'>导入深信服系统的数据</Paragraph>
            <Space>
                <Button size='large' onClick={() => navigate(`${BASE_PATH}/settings/import/sangfor/user`)}>用户数据导入</Button>
                <Button size='large' onClick={() => navigate(`${BASE_PATH}/settings/import/sangfor/service`)}>服务数据导入</Button>
                <Button size='large' onClick={() => navigate(`${BASE_PATH}/settings/import/sangfor/acl`)}>策略数据导入</Button>
            </Space>
            {/* <Tabs size='small' type='card'>
                <TabPane tab="用户" key='user' itemKey='user'>
                    <UserImport/>
                </TabPane>
                <TabPane tab="服务" key='service' itemKey='service'>
                    <ServiceImport/>
                </TabPane>
                <TabPane tab="策略" key='acl' itemKey='acl'>
                    <AclImport/>
                </TabPane>
            </Tabs> */}
        </div>
    </>
}

export default Index;
