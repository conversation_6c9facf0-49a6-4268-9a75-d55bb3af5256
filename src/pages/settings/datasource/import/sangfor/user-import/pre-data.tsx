import React from 'react'
import { User, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';


import { Typography, Row, Col, Table } from '@douyinfe/semi-ui';

const { Title, Paragraph } = Typography;

const Index: React.FC<{
    existGroups?: UserGroup[],
    newGroups?: UserGroup[],
    existUsers?: User[],
    newUsers?: User[],
}> = (props) => {
    const {
        existGroups,
        newGroups,
        existUsers,
        newUsers
    } = props;

    return <>
        <Title heading={4} className='mb10'>
            用户组
        </Title>
        <Row className='mb40' gutter={20}>
            <Col span={12}>
                <Paragraph className='mb10'>
                    已存在的用户组
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '编码',
                            dataIndex: 'name',
                            key: 'name',
                        },
                        {
                            title: '名称',
                            dataIndex: 'alias',
                            key: 'alias',
                        },
                        {
                            title: '描述',
                            dataIndex: 'description',
                            key: 'description',
                        }
                    ]}
                    dataSource={existGroups}
                    rowKey="name"
                />
            </Col>
            <Col span={12}>

                <Paragraph className='mb10'>
                    需要新建的用户组
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '编码',
                            dataIndex: 'name',
                            key: 'name',
                        },
                        {
                            title: '名称',
                            dataIndex: 'alias',
                            key: 'alias',
                        },
                        {
                            title: '描述',
                            dataIndex: 'description',
                            key: 'description',
                        }
                    ]}
                    dataSource={newGroups}
                    rowKey="name"
                />
            </Col>
        </Row>
        <Title heading={4} className='mb10'>
            用户
        </Title>
        <Row className='mb40' gutter={20}>
            <Col span={12}>
                <Paragraph className='mb10'>
                    已存在的用户
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '登录名',
                            dataIndex: 'loginName',
                            key: 'loginName',
                        },
                        {
                            title: '显示名',
                            dataIndex: 'displayName',
                            key: 'displayName',
                        }
                    ]}
                    dataSource={existUsers}
                    rowKey="loginName"
                />
            </Col>
            <Col span={12}>

                <Paragraph className='mb10'>
                    需要新建的用户
                </Paragraph>
                <Table
                    pagination={false}
                    scroll={{ y: 300 }}
                    virtualized
                    columns={[
                        {
                            title: '登录名',
                            dataIndex: 'loginName',
                            key: 'loginName',
                        },
                        {
                            title: '显示名',
                            dataIndex: 'displayName',
                            key: 'displayName',
                        }
                    ]}
                    dataSource={newUsers}
                    rowKey="loginName"
                />
            </Col>
        </Row>

    </>
}

export default Index;