import React, { useEffect, useState, useContext } from 'react'
import { Typography, Space, Button, Breadcrumb, Banner, Row, Col, Popconfirm, Steps } from '@douyinfe/semi-ui';
import { UserGroup, ImportUserAndGroupRequest_PasswordType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { generatePassword, getPasswordRank } from '@/utils/common';

import Papa from 'papaparse';
import { BASE_PATH } from '@/constants/router';
import PreData from './pre-data';

import styles from './index.module.scss'
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import FileSelect from './file-select';
import FieldMap from './field-map';
import useExpression from './useExpression';
import useFileSelect from './useFileSelect';

import usePreData from './usePreData';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';


const Index: React.FC = () => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    const {
        flynet,
        file,
        setFile,
        files,
        setFiles,
        data,
        setData,
        handleDataChange,
        invalidData,
        setInvalidData,
        validData,
        setValidData,
        handleFileSelect,
        handleInvalidDataDown,
        handleTemplateDown,
    } = useFileSelect();
    const { expressionsTreeData, expressionProperty, setExpressionProperty } = useExpression();
    const {
        existGroups,
        setExistGroups,
        newGroups,
        setNewGroups,
        existUsers,
        newUsers,
        calGroups,
        passwordType,
        setPasswordType,
        fixedPassword,
        setFixedPassword,
        userRole,
        setUserRole,
    } = usePreData();

    useEffect(() => {
        calGroups(validData);
    }, [data]);

    // 是否正在导入
    const [isImporting, setIsImporting] = useState(false);

    // 是否导入成功
    const [isImportSuccess, setIsImportSuccess] = useState(false);

    const [generatePasswords, setGeneratePasswords] = useState<{
        loginName: string,
        password: string,
    }[]>([]);

    const handleGeneratePasswordDown = () => {
        let csv = Papa.unparse(generatePasswords);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `密码.csv`;
        a.click();
    }

    const [userCount, setUserCount] = useState(0);
    const [groupCount, setGroupCount] = useState(0);

    // 导入
    const handleImport = () => {
        const submitExistGroups: UserGroup[] = [];
        const submitNewGroups: UserGroup[] = [];
        const gPasswords : {
            loginName: string,
            password: string,    
        }[] = [];
        

        existGroups?.forEach((group) => {
            group.users.forEach((user) => {
                if(passwordType == ImportUserAndGroupRequest_PasswordType.STATIC) {
                    if(user.account) {
                        user.account.attrsJson = fixedPassword;
                    }
                } else if (passwordType == ImportUserAndGroupRequest_PasswordType.RANDOM) {
                    if(user.account) {
                        user.account.attrsJson = generatePassword(16);
                        gPasswords.push({
                            loginName: user.loginName,
                            password: user.account.attrsJson
                        });
                    }
                }
            });
            submitExistGroups.push(group);
        });
        newGroups?.forEach((group) => {
            group.users.forEach((user) => {
                if(passwordType == ImportUserAndGroupRequest_PasswordType.STATIC) {
                    if(user.account) {
                        user.account.attrsJson = fixedPassword;
                    }
                } else if (passwordType == ImportUserAndGroupRequest_PasswordType.RANDOM) {
                    if(user.account) {
                        user.account.attrsJson = generatePassword(16);
                        gPasswords.push({
                            loginName: user.loginName,
                            password: user.account.attrsJson
                        });
                    }
                }
            });
            submitNewGroups.push(group);
        });

        setGroupCount(submitNewGroups.length);
        let userCount = 0;
        existGroups?.forEach((group) => {
            userCount += group.users.length;
        });
        newGroups?.forEach((group) => {
            userCount += group.users.length;
        });
        setUserCount(userCount);

        setGeneratePasswords(gPasswords);

        setIsImporting(true);
        flylayerClient.importUserAndGroup({
            flynetId: flynetGeneral.id,
            existGroups: submitExistGroups,
            newGroups: submitNewGroups,
            users: [],
            passwordType: passwordType,
        }).then((res) => {
            setIsImportSuccess(true);

            setExistGroups(submitExistGroups);
            setNewGroups(submitNewGroups);

            if(flynet && !flynet.accountManualCreate) {
                setStep(2);
            } else {
                setStep(3)
            }
        }).catch((err) => {
            setIsImportSuccess(false);
            
            if(flynet && !flynet.accountManualCreate) {
                setStep(2);
            } else {
                setStep(3)
            }
        }).finally(() => {
            setIsImporting(false);
        });
    }

    // 继续导入
    const handleContinueImport = () => {
        setFile(undefined);
        setData([]);
        setInvalidData([]);
        setFiles([]);
        setStep(0);
    }


    // 步骤
    const [step, setStep] = useState(0);

    return <>
        <div className='settings-page'>
            <Breadcrumb className='mb20' routes={
                [
                    {
                        name: '设置',
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource`,
                        href: `${BASE_PATH}/settings/datasource`,
                        name: '数据源'
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource/users/import`,
                        href: `${BASE_PATH}/settings/datasource/users/import`,
                        name: '用户数据导入',
                    },
                    {
                        name: '深信服',
                    }
                ]
            }>
            </Breadcrumb>
            {/* <Breadcrumb className='mb20' routes={
                [
                    {
                        path: `${BASE_PATH}/settings`,
                        href: `${BASE_PATH}/settings`,
                        name: '设置'
                    },
                    {
                        path: `${BASE_PATH}/settings/import`,
                        href: `${BASE_PATH}/settings/import`,
                        name: '数据导入',
                    },
                    {
                        path: `${BASE_PATH}/settings/import/sangfor`,
                        href: `${BASE_PATH}/settings/import/sangfor`,
                        name: '深信服',
                    },
                    {
                        name: '用户',
                    }
                ]
            }>
            </Breadcrumb> */}
            <Steps type='basic' className='mb20' current={step}>
                <Steps.Step title="上传文件" />
                {flynet && flynet.accountManualCreate&& <Steps.Step title="密码设置" />}
                
                <Steps.Step title="确认数据" />
                <Steps.Step title="导入完成" />
            </Steps>
            <>
                <FileSelect
                    handleFileSelect={handleFileSelect}
                    file={file}
                    files={files}
                    setFiles={setFiles}
                    data={data}
                    invalidData={invalidData}
                    onDataChange={handleDataChange}
                    handleInvalidDataDown={handleInvalidDataDown}
                    handleTemplateDown={handleTemplateDown}
                    style={{ display: step == 0 ? '' : 'none' }}
                    onNext={() => setStep(1)}
                />
            </>
            
            {flynet && flynet.accountManualCreate &&step === 1  && <FieldMap
                flynet={flynet}
                file={file}
                invalidData={invalidData}
                handleInvalidDataDown={handleInvalidDataDown}
                passwordType={passwordType}
                setPasswordType={setPasswordType}
                fixedPassword={fixedPassword}
                setFixedPassword={setFixedPassword}
                userRole={userRole}
                setUserRole={setUserRole}
                onPrev={() => setStep(0)}
                onNext={() => {
                    setStep(2);
                }}
            ></FieldMap>}
            
            {((flynet && flynet.accountManualCreate &&step == 2) || (flynet && !flynet.accountManualCreate && step === 1)) && <><div className='mt20 mb20'>
                <Row className='mb20' gutter={20}>
                    <Col span={12}>
                        <Space>
                        </Space>
                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>
                                <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                                <Button disabled={!file} size='large' onClick={() => { 
                                    if(flynet && !flynet.accountManualCreate) {
                                        setStep(0);
                                    } else {
                                        setStep(1)
                                    }
                                    
                                    
                                    }}>上一步</Button>
                                <Popconfirm
                                    title="确认导入？"
                                    content="确认之后，下面的数据将会被导入数据库"
                                    onConfirm={handleImport}
                                    disabled={(!newGroups || newGroups.length == 0) && (!existGroups || existGroups.length == 0)}
                                >
                                    <Button size='large' theme='solid' loading={isImporting} disabled={(!newGroups || newGroups.length == 0) && (!existGroups || existGroups.length == 0)}>开始导入</Button>
                                </Popconfirm>

                            </Space>

                        </div>

                    </Col>
                </Row>
                <div>
                    <PreData
                        existGroups={existGroups}
                        newGroups={newGroups}
                        existUsers={existUsers}
                        newUsers={newUsers}

                    ></PreData>
                </div>
            </div>
            </>}
            {(flynet && flynet.accountManualCreate &&step === 3 || flynet && !flynet.accountManualCreate && step === 2) && <><div className='mt20 mb20'>
                <Row className='mb20' gutter={20}>
                    <Col span={12}>

                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>
                                {
                                    passwordType == ImportUserAndGroupRequest_PasswordType.RANDOM && <Button size='large' onClick={handleGeneratePasswordDown}>密码下载</Button>
                                }
                                <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                                <Popconfirm
                                    title="继续导入？"
                                    content="确定继续导入，之前的文件和数据将会被清空"
                                    onConfirm={handleContinueImport}
                                >
                                    <Button size='large' theme='solid' >继续导入</Button>
                                </Popconfirm>
                            </Space>
                        </div>

                    </Col>
                </Row>
                {isImportSuccess ? <Banner
                    className='mb20'
                    closeIcon={null}
                    fullMode={false}
                    title="导入数据成功"
                    type="success"
                    bordered
                    description={`成功导入${userCount}个用户，${groupCount}个用户组`}
                >
                </Banner> :
                    <Banner
                        className='mb20'
                        closeIcon={null}
                        fullMode={false}
                        title="导入数据失败"
                        type="warning"
                        bordered
                        description="请重试或联系管理员。"
                    >
                    </Banner>
                }

            </div>
            </>}
        </div>
    </>
}

export default Index;
