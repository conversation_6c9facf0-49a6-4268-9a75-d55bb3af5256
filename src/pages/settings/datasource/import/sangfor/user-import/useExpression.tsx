import { useEffect, useState, useContext } from 'react';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { AttributeTemplate } from '@/interface/attribute-template';
import { getFlynet } from '@/services/flynet';
import { buildTreeData } from '@/utils/expression';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const useExpression = () => {
    const flynet = useContext(FlynetGeneralContext);

    const [expressionProperty, setExpressionProperty] = useState('');

    const [expressionsTemplate, setExpressionsTemplate] = useState<string>();
    const [expressionsTreeData, setExpressionsTreeData] = useState<TreeNodeData[]>();

    useEffect(() => {
        if (expressionsTemplate) {
            const expressionsAttrs: AttributeTemplate = JSON.parse(expressionsTemplate);
            const initTreeData: TreeNodeData[] = [];
            Object.keys(expressionsAttrs.properties).forEach((key) => {
                let treeDataItem = buildTreeData(key, expressionsAttrs.properties[key]);
                initTreeData.push(treeDataItem);
            })
            setExpressionsTreeData(initTreeData);
        }
    }, [expressionsTemplate])


    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet) {
            setExpressionsTemplate(res.flynet.expressionsTemplate);
        }
    }

    useEffect(() => {
        queryFlynet();
    }, [])

    return {
        expressionsTreeData,
        expressionProperty, setExpressionProperty
    }

}

export default useExpression;