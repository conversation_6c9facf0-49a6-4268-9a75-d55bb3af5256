import { useState, useEffect, useContext } from 'react'
import { Notification } from '@douyinfe/semi-ui';
import { UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import Papa from 'papaparse';
import { Model } from './model';
import { isUTF8Encoding } from '@/utils/file';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';


const useFileSelect = () => {
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [flynet, setFlynet] = useState<Flynet>();

    // 文件
    const [file, setFile] = useState<File>();
    // 文件
    const [files, setFiles] = useState<FileItem[]>();

    // 表格内容数据
    const [data, setData] = useState<Array<Model>>([]);

    // 无效数据
    const [invalidData, setInvalidData] = useState<Array<Model>>([]);
    // 有效数据
    const [validData, setValidData] = useState<Array<Model>>([]);

    // 文件选择
    const handleFileSelect = async (files: File[]) => {

        if (files.length > 0) {
            const isUTF8 = await isUTF8Encoding(files[0]);
            
            Papa.parse(files[0] as any, {
                encoding: isUTF8 ? 'UTF-8' : 'GBK',
                complete(result, file) {
                    if (result.errors && result.errors.length > 0) {
                        Notification.error({
                            title: '导入失败',
                            content: '请检查文件格式是否正确',
                            duration: 2000,
                        });
                        return;
                    }
                    if (result.data && result.data.length > 2) {
                        let pureData = result.data.slice(1);
                        let data: Array<Model> = [];
                        let invalidData: Array<Model> = [];
                        let validData: Array<Model> = [];

                        let names: Array<string> = [];

                        for (let i = 0; i < pureData.length - 1; i++) {
                            let item: string[] = pureData[i] as string[];
                            let isInvalid = false;
                            let invalidReason = '';
                            if (item.length < 9) {
                                isInvalid = true;
                                invalidReason = '数据格式不正确,至少有9列';
                            } else if (!item[0]) {
                                isInvalid = true;
                                invalidReason = '用户名不能为空';
                            }

                            if(names.indexOf(item[0]) > -1) {
                                isInvalid = true;
                                invalidReason = '用户名重复';
                            }

                            if(!item[1]) {
                                isInvalid = true;
                                invalidReason = '所属组路径不能为空';
                            }

                            // else if (!item[5]) {
                            //     isInvalid = true;
                            //     invalidReason = '描述不能为空';
                            // }

                            let dataItem = {
                                role: UserRole.FLYNET_USER,
                                username: item[0],
                                group: item[1],
                                password: item[2],
                                phone: item[3],
                                vip: item[4],
                                description: item[5],
                                ca: item[6],
                                cert: item[7],
                                lastLogin: item[8],
                                invalid: isInvalid,
                                invalidReason: invalidReason
                            };
                            data.push(dataItem);
                            if (isInvalid) {
                                invalidData.push(dataItem)
                            } else {
                                validData.push(dataItem);
                            }
                        }
                        setData(data)
                        setInvalidData(invalidData);
                        setValidData(validData);
                    }
                    setFile(file);
                },
            });
        }

        return false
    };

    // 下载无效数据
    const handleInvalidDataDown = () => {
        let csv = Papa.unparse(invalidData);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `无效数据.csv`;
        a.click();
    }

    // 下载模板
    const handleTemplateDown = () => {
        let csv = Papa.unparse([['用户名', '所属组路径', '密码', '手机号码', '虚拟IP地址', '描述', '所属CA', '证书绑定字段', '最近一次登录']]);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `深信服用户导入模板.csv`;
        a.click();
    }

    // 数据改变
    const handleDataChange = (data: Array<Model>) => {
        setData(data);
        let invalidData: Array<Model> = [];
        let validData: Array<Model> = [];
        for (let i = 0; i < data.length; i++) {
            let item = data[i];
            if (item.invalid) {
                invalidData.push(item);
            } else {
                validData.push(item);
            }
        }
        setInvalidData(invalidData);
        setValidData(validData);
    }

    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])
    return {
        flynet,
        file, 
        setFile,
        files, 
        setFiles,
        data, 
        setData,
        invalidData, 
        setInvalidData,
        validData, 
        setValidData,
        handleFileSelect, 
        handleInvalidDataDown,
        handleTemplateDown,
        handleDataChange
    }
}

export default useFileSelect;