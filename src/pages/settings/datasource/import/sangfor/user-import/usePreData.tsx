import { User, UserRole, UserGroup, ImportUserAndGroupRequest_PasswordType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { Account } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/accounts_pb';
import { flylayerClient } from '@/services/core';
import pinyin from 'tiny-pinyin';
import { useEffect, useState, useContext } from 'react';
import { Model } from './model';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { sanitizeLabel, ipRangeToCidr } from '@/utils/common';

const usePreData = () => {
    const flynet = useContext(FlynetGeneralContext);
    const globalConfig = useContext(GlobalConfigContext);
    // 系统中已经存在的用户组
    const [existGroups, setExistGroups] = useState<UserGroup[]>();
    // 需要新建的用户组
    const [newGroups, setNewGroups] = useState<UserGroup[]>();
    // 系统中已经存在的用户
    const [existUsers, setExistUsers] = useState<User[]>();
    // 需要新建的用户
    const [newUsers, setNewUsers] = useState<User[]>();
    // 密码类型
    const [passwordType, setPasswordType] = useState<ImportUserAndGroupRequest_PasswordType>(ImportUserAndGroupRequest_PasswordType.NONE);
    // 固定密码
    const [fixedPassword, setFixedPassword] = useState<string>('');
    // 角色
    const [userRole, setUserRole] = useState<UserRole>(UserRole.FLYNET_USER);

    // const [fieldMaps, setFieldMaps] = useState<>();

    // 生成用户显示名称
    const tinyUserDisplayName = (displayName: string) => {
        displayName = displayName ? displayName.trim() : '';
        // 查询显示名中的空格
        
        let index = displayName.indexOf(' ');
        if (index != -1) {
            displayName = displayName.substring(0, index);
        }

        return displayName;
    }

    // 计算用户组
    const calGroups = async (data: Model[]) => {
        const resGroup = await flylayerClient.listUserGroups({
            flynetId: flynet.id
        });

        const userGroups = resGroup.groups;

        const resUser = await flylayerClient.listUsers({
            flynetId: flynet.id,
            query: ""
        });

        const users = resUser.users;

        let existGroups: UserGroup[] = [];
        let newGroups: UserGroup[] = [];
        let existUsers: User[] = [];
        let newUsers: User[] = [];
        let existGroupNames: string[] = [];

        data.forEach((model: Model) => {
            let loginName = model.username ? model.username.trim() : '';

            // 判断loginName里是否有中文，如果有中文，转成拼音
            if (/[\u4e00-\u9fa5]/.test(loginName)) {
                loginName = pinyin.convertToPinyin(loginName, '', true);
            }

            // if(loginName.indexOf('@') == -1){
            //     loginName += '@' + globalConfig?.code;
            // }
            let existUser = users.find((item) => {
                return item.loginName == loginName;
            });
            if (existUser) {
                existUsers.push(existUser);
                return;
            }

            const user = new User();
            user.loginName = loginName;
            user.displayName = tinyUserDisplayName(model.description ? model.description.trim() : '');
            if(!user.displayName) {
                user.displayName = user.loginName;
            }
            user.account = new Account({
                attrsJson: ''
            });

            
            newUsers.push(user);

            let group: UserGroup | undefined;

            if (model.group) {
                let description = model.group.trim();
                let name = '';
                let alias = '';

                if (description.startsWith('/')) {
                    alias = description.substring(1);
                    alias = alias.replace(/\//g, '-');
                } else {
                    alias = description;
                    alias = alias.replace(/\//g, '-');
                }

                name = sanitizeLabel(pinyin.convertToPinyin(alias, '', true));

                // 之前的已经存在的组
                if (existGroupNames.includes(name)) {
                    group = existGroups.find((item) => {
                        return item.name == name
                    });
                    if (!group) {
                        group = newGroups.find((item) => {
                            return item.name == name
                        });
                    }
                } else {
                    group = userGroups.find((item) => {
                        return item.name == name
                    })
                    if (group) {
                        group.users = [];
                        existGroups.push(group);
                    } else {
                        const newGroup = new UserGroup();
                        newGroup.name = name;
                        newGroup.alias = alias;
                        newGroup.description = description;
                        newGroup.users = [];
                        newGroups.push(newGroup);
                        group = newGroup;
                    }
                    existGroupNames.push(name);
                }
            }
            group?.users.push(user);
        });

        setExistGroups(existGroups);
        setNewGroups(newGroups);
        setExistUsers(existUsers);
        setNewUsers(newUsers);
    }

    return {
        existGroups,
        setExistGroups,
        newGroups,
        setNewGroups,
        existUsers,
        newUsers,
        calGroups,
        passwordType,
        setPasswordType,
        fixedPassword,
        setFixedPassword,
        userRole,
        setUserRole
    }
}

export default usePreData;
