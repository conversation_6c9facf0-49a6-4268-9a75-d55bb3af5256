import React from 'react'
import { User, UserRole, ImportUserAndGroupRequest_PasswordType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { Typography, Input, Upload, Space, Button, Tag, Row, Col, Table, List, Select, Divider } from '@douyinfe/semi-ui';
import { IconArrowRight } from '@douyinfe/semi-icons';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

import { Model } from '../model';
const { Text, Title } = Typography;
import styles from './index.module.scss'
const Index: React.FC<{
    flynet: Flynet;
    file?: File;
    onNext: () => void;
    onPrev: () => void;
    invalidData: Array<Model>;
    handleInvalidDataDown: () => void;
    passwordType: ImportUserAndGroupRequest_PasswordType;
    setPasswordType: (passwordType: ImportUserAndGroupRequest_PasswordType) => void;
    fixedPassword: string;
    setFixedPassword: (fixedPassword: string) => void;
    userRole: UserRole;
    setUserRole: (userRole: UserRole) => void;
}> = (props) => {
    const { invalidData, handleInvalidDataDown } = props;

    const srcFields: Array<string> = [
        '所属组路径',
        '用户名',
        '描述',
    ];


    return <>
        <div className='mt20 mb20'>
            <Row className='mb40' gutter={20}>
                <Col span={12}>
                </Col>
                <Col span={12}>
                    <div className='btn-right-col'>
                        <Space>
                            <Button size='large' onClick={handleInvalidDataDown} disabled={!invalidData || invalidData.length == 0}>无效数据下载</Button>
                            <Button disabled={!props.file} size='large' onClick={() => { props.onPrev() }}>上一步</Button>
                            <Button disabled={!props.file || (props.passwordType === ImportUserAndGroupRequest_PasswordType.STATIC && !props.fixedPassword)} size='large' theme='solid' onClick={() => { props.onNext() }}>下一步</Button>
                        </Space>
                    </div>
                </Col>
            </Row>
            <div className={styles.fieldMapWrap}>
                {/* <div className={styles.fieldOptions}>
                    <Space style={{ alignItems: 'flex-start' }}>
                        <Text style={{ lineHeight: '32px', width: 80 }}>角色</Text>
                        <Select style={{ width: 160 }} value={props.userRole} onChange={val => props.setUserRole(val as UserRole)}>
                            <Select.Option value={UserRole.FLYNET_ADMIN}>管理员</Select.Option>
                            <Select.Option value={UserRole.FLYNET_USER}>普通用户</Select.Option>
                        </Select>
                    </Space>
                </div> */}
                <div className={styles.fieldOptions}>
                    <Space style={{ alignItems: 'flex-start' }}>
                        <Text style={{ lineHeight: '32px', width: 120, fontSize: '16px' }}>密码类型:</Text>
                        <div>
                            <Select
                                value={props.passwordType}
                                style={{ width: 260 }}
                                className='mb20'
                                size='large'
                                onChange={(val) => { props.setPasswordType(val as ImportUserAndGroupRequest_PasswordType) }}
                            >
                                <Select.Option value={ImportUserAndGroupRequest_PasswordType.NONE}>无密码</Select.Option>
                                <Select.Option value={ImportUserAndGroupRequest_PasswordType.RANDOM}>随机密码</Select.Option>
                                <Select.Option value={ImportUserAndGroupRequest_PasswordType.STATIC}>固定密码</Select.Option>
                            </Select>
                            <br />
                            {props.passwordType == ImportUserAndGroupRequest_PasswordType.STATIC && <Input
                                style={{ width: 260 }}
                                value={props.fixedPassword}
                                mode='password'
                                placeholder='请输入固定密码'
                                size='large'
                                onChange={(val) => { props.setFixedPassword(val) }}
                            />}
                        </div>
                    </Space>
                </div>

                <div style={{ display: 'none' }}>
                    <Divider className='mb40'></Divider>
                    <Title heading={5} className='mb20'>
                        字段映射
                    </Title>

                    <div className={styles.fieldMap}>
                        <div className={styles.src}>
                            <Select disabled value={'用户名'}>
                                {srcFields.map((field, index) => {
                                    return <Select.Option key={index} value={field}>{field}</Select.Option>
                                })}
                            </Select>
                        </div>
                        <div className={styles.arrow}>
                            <IconArrowRight />
                        </div>
                        <div className={styles.dst}>
                            用户-登录名
                        </div>
                    </div><div className={styles.fieldMap}>
                        <div className={styles.src}>
                            <Select disabled value={'描述'}>
                                {srcFields.map((field, index) => {
                                    return <Select.Option key={index} value={field}>{field}</Select.Option>
                                })}
                            </Select>
                        </div>
                        <div className={styles.arrow}>
                            <IconArrowRight />
                        </div>
                        <div className={styles.dst}>
                            用户-显示名
                        </div>
                    </div><div className={styles.fieldMap}>
                        <div className={styles.src}>
                            <Select disabled value={'所属组路径'}>
                                {srcFields.map((field, index) => {
                                    return <Select.Option key={index} value={field}>{field}</Select.Option>
                                })}
                            </Select>
                        </div>
                        <div className={styles.arrow}>
                            <IconArrowRight />
                        </div>
                        <div className={styles.dst}>
                            用户组-名称
                        </div>
                    </div><div className={styles.fieldMap}>
                        <div className={styles.src}>
                            <Select disabled value={'所属组路径'}>
                                {srcFields.map((field, index) => {
                                    return <Select.Option key={index} value={field}>{field}</Select.Option>
                                })}
                            </Select>
                        </div>
                        <div className={styles.arrow}>
                            <IconArrowRight />
                        </div>
                        <div className={styles.dst}>
                            用户组-别名
                        </div>
                    </div><div className={styles.fieldMap}>
                        <div className={styles.src}>
                            <Select value={'所属组路径'}>
                                {srcFields.map((field, index) => {
                                    return <Select.Option key={index} value={field}>{field}</Select.Option>
                                })}
                            </Select>
                        </div>
                        <div className={styles.arrow}>
                            <IconArrowRight />
                        </div>
                        <div className={styles.dst}>
                            用户组-描述
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </>
}

export default Index;