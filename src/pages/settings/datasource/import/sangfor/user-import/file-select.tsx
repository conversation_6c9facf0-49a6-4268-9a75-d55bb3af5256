import React from 'react'
import { Typography, Input, Upload, Space, Button, Tag, Row, Col, Table, Select } from '@douyinfe/semi-ui';
import TableEmpty from '@/components/table-empty';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { Model } from './model';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";

import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { IconPaperclip } from '@douyinfe/semi-icons';

const { Text } = Typography;

const Index: React.FC<{
    style: React.CSSProperties;
    handleFileSelect: (files: File[]) => void;
    file?: File;
    files?: FileItem[];
    setFiles: (files: FileItem[]) => void;
    onNext: () => void;
    data: Array<Model>;
    onDataChange: (data: Array<Model>) => void;
    invalidData: Array<Model>;
    handleInvalidDataDown: () => void;
    handleTemplateDown: () => void;
}> = (props) => {

    const columns: Array<ColumnProps> = [
        {
            title: '角色',
            dataIndex: 'role',
            render: (text, record, index) => {
                return <>
                    <Select style={{ width: 160 }} value={record.role}
                        onChange={val => {
                            let newData = [...props.data];
                            newData[index].role = val as UserRole;
                            props.onDataChange(newData);
                        }}>
                        <Select.Option value={UserRole.FLYNET_ADMIN}>管理员</Select.Option>
                        <Select.Option value={UserRole.FLYNET_USER}>普通用户</Select.Option>
                    </Select>
                </>
            }
        },
        {
            title: '用户名',
            dataIndex: 'username',
            render: (text, record, index) => {
                return <>
                    <Input value={text} onChange={(val) => {
                        let newData = [...props.data];

                        newData[index].username = val;

                        if (!val) {
                            newData[index].invalid = true;
                            newData[index].invalidReason = '用户名不能为空';
                        } else {
                            newData[index].invalid = false;
                            newData[index].invalidReason = '';
                        }

                        props.onDataChange(newData);
                    }} />
                </>
            }
        }, {
            title: '所属组路径',
            dataIndex: 'group',
            render: (text, record, index) => {
                return <>
                    <Input value={text} onChange={(val) => {
                        let newData = [...props.data];
                        newData[index].group = val;

                        if(!val) {
                            newData[index].invalid = true;
                            newData[index].invalidReason = '所属组路径不能为空';
                        } else {
                            newData[index].invalid = false;
                            newData[index].invalidReason = '';
                        }

                        props.onDataChange(newData);
                    }} />
                </>
            }
        }, {
            title: '密码',
            dataIndex: 'password',
        }, {
            title: '手机号码',
            dataIndex: 'phone',
        }, {
            title: '虚拟IP地址',
            dataIndex: 'vip',
        }, {
            title: '描述',
            dataIndex: 'description',
            render: (text, record, index) => {
                return <>
                    <Input value={text} onChange={(val) => {
                        let newData = [...props.data];
                        newData[index].description = val;

                        props.onDataChange(newData);
                    }} />
                </>
            }
        }, {
            title: '所属CA',
            dataIndex: 'ca',
        }, {
            title: '证书绑定字段',
            dataIndex: 'cert',
        }, {
            title: '最近一次登录',
            dataIndex: 'lastLogin',
        }, {

            title: '是否有效',
            dataIndex: 'invalid',
            render: (text, record) => {
                return <>{record.invalid ? <Text type='danger'>{record.invalidReason}</Text> : '是'}</>
            }
        }
    ];

    return <>
        <div style={props.style} className='mt20 mb20'>
            <Row className='mb20' gutter={20}>
                <Col span={12}>
                    <Space align='start'>
                        <Upload
                            style={{ width: 300 }}
                            action=''
                            fileList={props.files}
                            draggable
                            dragMainText="点击上传文件或拖拽文件到这里"
                            dragSubText="支持csv文件"
                            onFileChange={props.handleFileSelect}
                            onChange={({ fileList }) => props.setFiles(fileList)}
                            uploadTrigger='custom'
                            limit={1}
                            accept='.csv'
                        ></Upload>
                    </Space>
                </Col>
                <Col span={12}>
                    <div className='btn-right-col'>
                        <Space>
                            {/* <Button size='large' icon={<IconPaperclip />} onClick={props.handleTemplateDown}>模板下载</Button> */}
                            <Button size='large' disabled={!props.invalidData || props.invalidData.length == 0} onClick={props.handleInvalidDataDown}>无效数据下载</Button>
                            <Button disabled={!props.file} size='large' theme='solid' onClick={() => { props.onNext() }}>下一步</Button>
                        </Space>
                    </div>
                </Col>
            </Row>
            {props.file && <>

                <Space className='mb20'>
                    <Tag style={{ height: 32 }}>数据总数{props.data.length}</Tag>
                    <Tag style={{ height: 32 }}>有效条数{props.data.length - props.invalidData.length}</Tag>
                    <Tag style={{ height: 32 }}>无效条数{props.invalidData.length}</Tag>
                </Space>
                <Table
                    columns={columns}
                    dataSource={props.data}
                    pagination={false}
                    virtualized
                    scroll={{ y: 600 }}
                    bordered
                    rowKey={'index'}
                    empty={<TableEmpty loading={false}></TableEmpty>}></Table></>}
        </div>
    </>;
}

export default Index;
