import { FC, useState, useContext } from "react";
import { BASE_PATH } from '@/constants/router';
import { Model } from "./model";
import { isUTF8Encoding } from '@/utils/file';
import <PERSON> from 'papaparse';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { Typography, Table, Tag, Upload, Popconfirm, Space, Button, Notification, Breadcrumb, Row, Col, Banner, TabPane } from '@douyinfe/semi-ui';
import TableEmpty from '@/components/table-empty';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { OidcItem } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { IconPaperclip } from '@douyinfe/semi-icons';

import styles from './index.module.scss'
import { flylayerClient } from "@/services/core";

const { Title, Paragraph, Text } = Typography;

const Index: FC = () => {
    const flynetGeneral = useContext(FlynetGeneralContext);

    const columns = [
        {
            title: '系统内部用户标识',
            dataIndex: 'loginName',
            key: 'loginName',
        },
        {
            title: 'IDP标识',
            dataIndex: 'idp',
            key: 'idp',
        },
        {
            title: '系统外部标识',
            dataIndex: 'subject',
            key: 'subject',
        },
    ];

    // 文件
    const [file, setFile] = useState<File>();
    // 文件
    const [files, setFiles] = useState<FileItem[]>();


    // 表格内容数据
    const [data, setData] = useState<Array<Model>>([]);

    // 无效数据
    const [invalidData, setInvalidData] = useState<Array<Model>>([]);
    // 有效数据
    const [validData, setValidData] = useState<Array<Model>>([]);

    // 显示导入结果
    const [showImportResult, setShowImportResult] = useState(false);

    // 文件选择
    const handleFileSelect = async (files: File[]) => {

        if (files.length > 0) {
            const isUTF8 = await isUTF8Encoding(files[0]);

            Papa.parse(files[0] as any, {
                encoding: isUTF8 ? 'UTF-8' : 'GBK',
                complete(result, file) {
                    if (result.errors && result.errors.length > 0) {
                        Notification.error({
                            title: '导入失败',
                            content: '请检查文件格式是否正确',
                            duration: 2000,
                        });
                        return;
                    }
                    if (result.data && result.data.length > 2) {
                        let pureData = result.data.slice(1);
                        let data: Array<Model> = [];
                        let invalidData: Array<Model> = [];
                        let validData: Array<Model> = [];

                        let names: Array<string> = [];

                        for (let i = 0; i < pureData.length - 1; i++) {
                            let item: string[] = pureData[i] as string[];
                            let isInvalid = false;
                            let invalidReason = '';
                            if (item.length < 3) {
                                isInvalid = true;
                                invalidReason = '数据格式不正确,至少有3列';
                            } else if (!item[0]) {
                                isInvalid = true;
                                invalidReason = '系统内部用户标识不能为空';
                            } else if (!item[1]) {
                                isInvalid = true;
                                invalidReason = 'IDP标识不能为空';
                            } else if (!item[2]) {
                                isInvalid = true;
                                invalidReason = '系统外部标识不能为空';
                            }


                            let dataItem = {
                                loginName: item[0],
                                idp: item[1],
                                subject: item[2],

                            };
                            data.push(dataItem);
                            if (isInvalid) {
                                invalidData.push(dataItem)
                            } else {
                                validData.push(dataItem);
                            }
                        }
                        setData(data)
                        setInvalidData(invalidData);
                        setValidData(validData);
                    }
                    setFile(file);
                },
            });
        }

        return false
    };

    // 模板下载
    const handleTemplateDown = () => {
        let csv = Papa.unparse({
            fields: ['系统内部用户标识', 'IDP标识', '系统外部标识'],
            data: [
                ['internalXXX', 'idpXXX', 'externalXXX'],
            ],
        });
        let blob = new Blob([csv], { type: 'text/csv;charset=utf-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `三方数据绑定模板.csv`;
        a.click();
    }

    // 导入结果
    const [allCount, setAllCount] = useState(0);
    // 错误结果
    const [errorItems, setErrorItems] = useState<Array<OidcItem>>([]);
    // 未找到结果
    const [notFoundItems, setNotFoundItems] = useState<Array<OidcItem>>([]);

    // 导入
    const [isImporting, setIsImporting] = useState(false);
    const handleImport = () => {

        setIsImporting(true);
        setAllCount(validData.length);
        flylayerClient.importOidcUser({
            flynetId: flynetGeneral.id,
            items: validData.map(item => {
                return {
                    loginName: item.loginName,
                    idp: item.idp,
                    subject: item.subject,
                }
            })
        }).then(res => {
            Notification.success({
                title: '导入成功',
                content: '导入成功',
                duration: 2000,
            });

            setErrorItems(res.errorItems);
            setNotFoundItems(res.notFoundItems)

            setShowImportResult(true);



        }).catch(err => {
            Notification.error({
                title: '导入失败',
                content: '导入失败,请稍后再试。',
                duration: 2000,
            });
        }).finally(() => {
            setIsImporting(false);
        })

    }

    // 继续导入
    const handleContinueImport = () => {
        setFile(undefined);
        setData([]);
        setInvalidData([]);
        setValidData([]);
        setFiles([]);
    }
    return <>
        <div className='settings-page'>
            <Breadcrumb className='mb20' routes={
                [
                    {
                        name: '设置',
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource`,
                        href: `${BASE_PATH}/settings/datasource`,
                        name: '数据源'
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource/users/import`,
                        href: `${BASE_PATH}/settings/datasource/users/import`,
                        name: '用户数据导入',
                    },
                    {
                        name: '三方数据绑定',
                    }
                ]
            }>
            </Breadcrumb>

            <div className='mt20 mb20'>
                <Row className="mb20" gutter={20}>
                    <Col span={12}>
                        <Space align='start'>
                            <Upload
                                style={{ width: 300 }}
                                action=''
                                fileList={files}
                                draggable
                                dragMainText="点击上传文件或拖拽文件到这里"
                                dragSubText="支持csv文件"
                                onFileChange={handleFileSelect}
                                onChange={({ fileList }) => setFiles(fileList)}
                                uploadTrigger='custom'
                                limit={1}
                                accept='.csv'
                            ></Upload>
                        </Space>
                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>
                                <Button size='large' icon={<IconPaperclip />} onClick={handleTemplateDown}>模板下载</Button>
                                {showImportResult ? <Button onClick={() => {
                                    handleContinueImport()
                                    setShowImportResult(false)
                                }} size='large' theme='solid'>
                                    继续导入
                                </Button> : <Popconfirm
                                    title="确认导入？"
                                    content="确认之后，下面的数据将会被导入数据库"
                                    onConfirm={handleImport}
                                    disabled={(!validData || validData.length == 0)}
                                >
                                    <Button size='large' theme='solid' loading={isImporting}
                                        disabled={(!validData || validData.length == 0)}

                                    >开始导入</Button>
                                </Popconfirm>}

                            </Space>
                        </div>
                    </Col>
                </Row>
                {!showImportResult && file && <>

                    <Space className='mb20'>
                        <Tag style={{ height: 32 }}>数据总数{data.length}</Tag>
                        <Tag style={{ height: 32 }}>有效条数{data.length - invalidData.length}</Tag>
                        <Tag style={{ height: 32 }}>无效条数{invalidData.length}</Tag>
                    </Space>
                    <Table
                        columns={columns}
                        dataSource={data}
                        pagination={false}
                        virtualized
                        scroll={{ y: 600 }}
                        bordered
                        rowKey={'index'}
                        empty={<TableEmpty loading={false}></TableEmpty>}></Table></>}
            </div>
            {showImportResult && <><Banner
                className='mb20'
                closeIcon={null}
                fullMode={false}
                title="导入数据成功"
                type="success"
                bordered
                description={`成功导入${allCount - errorItems.length - notFoundItems.length}条数据${errorItems.length > 0 ? '，其中' + errorItems.length + '条数据导入失败' : ''}${notFoundItems.length > 0 ? '，' + notFoundItems.length + '条数据系统中未找到对应的用户。' : ''}`}
            >
            </Banner>
                {notFoundItems.length > 0 && <>
                    <Title heading={5} className="mb10">以下数据系统中未找到用户</Title>
                    <Table columns={[{
                        title: '系统内部用户标识',
                        dataIndex: 'loginName',
                        key: 'loginName',
                    },
                    {
                        title: 'IDP标识',
                        dataIndex: 'idp',
                        key: 'idp',
                    },
                    {
                        title: '系统外部标识',
                        dataIndex: 'subject',
                        key: 'subject',
                    },]} dataSource={notFoundItems}></Table>
                </>}
                {
                    errorItems.length > 0 && <>
                        <Title heading={5} className="mb10">以下数据导入失败</Title>
                        <Table columns={[{
                            title: '系统内部用户标识',
                            dataIndex: 'loginName',
                            key: 'loginName',
                        },
                        {
                            title: 'IDP标识',
                            dataIndex: 'idp',
                            key: 'idp',
                        },
                        {
                            title: '系统外部标识',
                            dataIndex: 'subject',
                            key: 'subject',
                        },]} dataSource={errorItems}></Table>
                    </>
                }

            </>
            }
        </div></>
}

export default Index;

