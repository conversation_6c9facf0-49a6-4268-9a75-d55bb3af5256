import React, { useContext, useEffect, useState } from 'react'
import { Typography, Avatar, Upload, Input, Space, Button, Notification, Breadcrumb, Row, Col, Tabs, TabPane, Divider } from '@douyinfe/semi-ui';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import SangforLogo from '@/assets/thirdparty/sangfor-logo.jpeg';
import OidcLogo from '@/assets/thirdparty/oidc-logo.jpeg';
const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss'
const Index: React.FC = () => {
    const navigate = useNavigate();

    return <>
        <div className='settings-page'>
            <Breadcrumb routes={
                [
                    {
                        name: '设置',
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource`,
                        href: `${BASE_PATH}/settings/datasource`,
                        name: '数据源'
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource/users/import`,
                        href: `${BASE_PATH}/settings/datasource/users/import`,
                        name: '用户数据导入',
                    }
                ]
            }>
            </Breadcrumb>
            <Title heading={3} className='mb10' >数据导入</Title>
            <Paragraph type='tertiary' className='mb40'>将已有的数据导入到本系统中</Paragraph>

            <Title heading={5} style={{textAlign: 'center'}} className='mb10'>基础数据导入</Title>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Space style={{ padding: 20 }}>
                    <Button block size='large'
                        style={{ height: 80, width: 200 }}
                        onClick={() => navigate(`${BASE_PATH}/settings/datasource/users/import/sangfor`)}><Avatar src={SangforLogo} style={{ marginRight: 20 }}></Avatar> <Text >深信服VPN</Text></Button>
                        
                </Space>
            </div>

            <Divider className='mb40'></Divider>

            <Title heading={5} style={{textAlign: 'center'}} className='mb10'>其他数据</Title>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Space style={{ padding: 20 }}>
                    <Button block size='large'
                        style={{ height: 80, width: 200 }}
                        onClick={() => navigate(`${BASE_PATH}/settings/datasource/users/import/oidc`)}><Avatar src={OidcLogo} style={{ marginRight: 20 }}></Avatar> <Text >三方数据绑定</Text></Button>
                </Space>
            </div>


            <Row>
                <Col span={12}>

                </Col>

                <Col span={12}>
                
                </Col>
            </Row>

          
        </div>
    </>
}

export default Index;
