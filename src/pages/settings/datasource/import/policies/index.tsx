import React, { useContext, useEffect, useState } from 'react'
import { Typography, Banner, Upload, Input, Space, Button, Notification, Breadcrumb, Avatar, Col, Tabs, TabPane } from '@douyinfe/semi-ui';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

import SangforLogo from '@/assets/thirdparty/sangfor-logo.jpeg';
const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss'
const Index: React.FC = () => {
    const navigate = useNavigate();

    return <>
        <div className='settings-page'>
            <Breadcrumb routes={
                [
                    {
                        name: '设置',
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource`,
                        href: `${BASE_PATH}/settings/datasource`,
                        name: '数据源'
                    },
                    {
                        path: `${BASE_PATH}/settings/datasource/policies/import`,
                        href: `${BASE_PATH}/settings/datasource/policies/import`,
                        name: '策略数据导入',
                    }
                ]
            }>
            </Breadcrumb>
            <Title heading={3} className='mb10' >数据导入</Title>
            <Paragraph type='tertiary' className='mb40'>将已有的数据导入到本系统中</Paragraph>

            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Space style={{ padding: 20 }}>
                    <Button block size='large'
                        style={{ height: 80, width: 200 }}
                        onClick={() => navigate(`${BASE_PATH}/settings/datasource/policies/import/sangfor`)}><Avatar src={SangforLogo} style={{ marginRight: 20 }}></Avatar> <Text >深信服VPN</Text></Button>
                </Space>
            </div>


        </div>
    </>
}

export default Index;
