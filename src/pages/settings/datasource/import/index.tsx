import React, { useContext, useEffect, useState } from 'react'
import { Typography, Banner, Upload, Input, Space, Button, Notification, Breadcrumb, Row, Col, Tabs, TabPane } from '@douyinfe/semi-ui';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss'
const Index: React.FC = () => {
    const navigate = useNavigate();

    return <>
        <div className='settings-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/settings`,
                        href: `${BASE_PATH}/settings`,
                        name: '设置'
                    },
                    {
                        name: '数据导入',
                    }
                ]
            }>
            </Breadcrumb>
            <Title heading={3} className='mb10' >数据导入</Title>
            <Paragraph type='tertiary' className='mb40'>导入其他系统的数据</Paragraph>

            <Tabs type='card'>
                <TabPane tab="深信服">
                    <Space style={{padding: 20}}>
                        <Button size='large' onClick={() => navigate(`${BASE_PATH}/settings/import/sangfor/user`)}>用户</Button>
                        <Button size='large' onClick={() => navigate(`${BASE_PATH}/settings/import/sangfor/service`)}>服务</Button>
                        <Button size='large' onClick={() => navigate(`${BASE_PATH}/settings/import/sangfor/acl`)}>策略</Button>
                    </Space>
                </TabPane>
            </Tabs>

        </div>
    </>
}

export default Index;
