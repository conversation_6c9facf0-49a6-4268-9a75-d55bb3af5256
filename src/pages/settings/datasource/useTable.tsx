import { useState, useEffect, useContext } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Notification, Dropdown, Button, Divider } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';
import CodeViewer from '@/components/code-viewer'
import { flylayerClient } from '@/services/core';
import { DataSource } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/datasource_pb"

const useTable = () => {
    const VALUE_FIELD = 'admission';
    const flynet = useContext(FlynetGeneralContext);

    const [dataSources, setDataSources] = useState<Array<DataSource>>();

    const [loading, setLoading] = useState(true);

    const [editVisible, setEditVisible] = useState(false);

    const [delVisible, setDelVisible] = useState(false);

    const [selectedDataSource, setSelectedDataSource] = useState<DataSource>();

    // 加载数据
    const query = () => {
        setLoading(true);

        flylayerClient.listDataSources({
            flynetId: flynet.id
        }).then(res => {
            setDataSources(res.dataSources);
        }, err => {
            Notification.error({
                title: '获取数据源列表失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        })

    }

    useEffect(() => {
        query()
    }, []);

    const columns = [{
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        render: (name: string, record: DataSource) => {
            return <>{record.alias} ({name})</>;
        }
    }, {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        width: 300,
    }, {
        title: '',
        dataIn1dex: 'id',
        key: 'id',
        width: 100,
        render: (id: string, record: DataSource) => {
            return <><div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>

                        <Dropdown.Item
                            onClick={() => {
                                setEditVisible(true);
                                setSelectedDataSource(record);
                            }}
                        >编辑数据源</Dropdown.Item>
                        <Divider />
                        <Dropdown.Item
                            onClick={() => {
                                setDelVisible(true);
                                setSelectedDataSource(record);
                            }}
                            type='danger'>删除数据源</Dropdown.Item>

                    </Dropdown.Menu>}><Button><IconMore className='align-v-center' /></Button>
            </Dropdown>
            </div>
            </>;
        }
    }];

    const expandRowRender = (record: DataSource | undefined) => {
        const fields = record?.value?.fields;
        let value = '';
        if (fields) {
            value = fields[VALUE_FIELD]?.kind?.value as string || '';
        }
        return <div style={{ display: 'flex', position: 'relative', height: 200 }}>
            <div style={{ width: '100%', position: 'absolute', left: 0, right: 0 }}>
                <CodeViewer value={value} height='200px' />
            </div>
        </div>
    }

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    return {
        loading,
        dataSources,
        columns,
        expandRowRender,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        setReloadFlag,
        selectedDataSource,
        setSelectedDataSource,
    }
}

export default useTable;