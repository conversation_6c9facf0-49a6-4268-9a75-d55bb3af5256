import React, { useState, useContext } from "react";
import { Modal, Form, Row, Col, Notification, Divider, Popover } from "@douyinfe/semi-ui";
import { Struct, Value } from "@bufbuild/protobuf";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import CodeEditor from '@/components/code-editor';
import { IconHelpCircle } from '@douyinfe/semi-icons';

import { flylayerClient } from "@/services/core";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

const { Input } = Form

interface Props {
    close: () => void,
    success?: () => void,
}

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
    }>>()
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 是否正在保存
    const [saveLoading, setSaveLoading] = useState(false);

    const handleOk = async () => {
        if (!formApi) {
            return
        }
        await formApi.validate();


        const values = formApi?.getValues();
        if (!values) {
            return;
        }
        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';

        setSaveLoading(true);
        flylayerClient.createDataSource({
            flynetId: flynet.id,
            name,
            description,
            alias,
            value: new Struct({
                fields: {
                    admission: new Value({
                        kind: {
                            value: textValue,
                            case: 'stringValue'
                        }
                    })
                }
            
            }),
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'settings.datasource.createSuccess' }),
                content: formatMessage({ id: 'settings.datasource.createSuccess' })
            });
            props.close();
            props.success && props.success();
        }, err => {
            Notification.error({
                title: formatMessage({ id: 'settings.datasource.createFailed' }),
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        })
    }

    const [textValue, setTextValue] = useState<string>('')

    return <>
        <Modal
            title={formatMessage({ id: 'settings.datasource.addTitle' })}
            visible={true}
            onOk={handleOk}
            onCancel={props.close}
            width={600}
            okButtonProps={{ loading:saveLoading }}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Form
                allowEmpty
                getFormApi={setFormApi}>

                <Row gutter={12}>
                    <Col span={12}>
                        <Input field='alias' label={formatMessage({ id: 'settings.datasource.field.name' })} trigger={'blur'} validate={value => {
                            if (!value) {
                                return formatMessage({ id: 'settings.datasource.validation.nameRequired' });
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={12}>
                        <Input field='name'
                            label={<>{formatMessage({ id: 'settings.datasource.field.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'settings.datasource.field.codeHint' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}

                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return formatMessage({ id: 'settings.datasource.validation.codeRequired' });
                                }
                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '编码不能以-开头'
                                }
                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "编码只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            required />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Input field='description' label='备注' />
                    </Col>
                </Row>
                <Divider></Divider>
                <CodeEditor value={textValue} height='280px' language="json" onChange={(value)=>setTextValue(value||'')} ></CodeEditor>


            </Form>

        </Modal>
    </>
}

export default Index