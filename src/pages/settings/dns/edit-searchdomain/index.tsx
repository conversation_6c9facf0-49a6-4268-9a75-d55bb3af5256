import { FC, useState } from 'react'
import { Typography, Modal, Notification, Tabs, Popover, Form, Row, Col, ArrayField, Button } from '@douyinfe/semi-ui';
import { DNSConfig, Routes, Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';
import { IconPlusCircle, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons';
import SearchdomainNameInfo from '@/components/domain-manager/searchdomain-name-info';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
import { DOMAIN_STATIC_IP } from '@/constants';
import { validSearchDomainName } from '@/utils/validators';
const { Paragraph, Title } = Typography;

interface Props {
    flynetId: bigint,
    close: () => void,
    success?: () => void,
    record: DNSConfig,
    data: {
        domain: string,
        extraRecords: {
            name: string,
            type: string,
            value: string,
            sys: boolean
        }[]
    }
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleOk = () => {
        if (!formApi) {
            return
        }
        formApi.validate().then((res) => {

            const values: {
                domain: string,
                extraRecords: {
                    name: string,
                    type: string,
                    value: string,
                    sys?: boolean
                }[]
            } = formApi?.getValues();

            // 域名转换成小写和去除前后空格
            if (values.domain) {
                values.domain = values.domain.toLowerCase();
                values.domain = values.domain.trim();
            }
            setLoading(true)

            let config = { ...props.record }
            
            if (values.domain && values.extraRecords) {
                if (config.extraRecords) {
                    //  域名修改的情况,删除原来的数据

                    let newRecords = config.extraRecords.filter(val => val.domain && val.domain != values.domain && val.domain != props.data.domain);
                    values.extraRecords.forEach((record) => {
                        const name = record.name.trim().toLocaleLowerCase();
                        newRecords?.push(new Record({
                            domain: values.domain,
                            name: name,
                            type: record.type,
                            value: record.value,
                            sys: record.sys
                        }))
                    })
                    config.extraRecords = newRecords;
                } else {
                    config.extraRecords = [
                    ]

                    values.extraRecords.forEach((record) => {
                        const name = record.name.trim().toLocaleLowerCase();
                        config.extraRecords?.push(new Record({
                            domain: values.domain,
                            name: name,
                            type: record.type,
                            value: record.value
                        }))
                    })
                }
            }

            flylayerClient.setDNSConfig({
                flynetId: props.flynetId,
                config: config
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'settings.dns.editSearchdomain.success' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                Notification.error({ 
                    title: formatMessage({ id: 'settings.dns.editSearchdomain.failed' }), 
                    content: (err as any).message,
                    position: "bottomRight" })
            }).finally(() => setLoading(false))
        }).catch((err) => {
            console.error(err);
        });



    }


    // 验证IP
    const validateIP = (value: string, values: any) => {
        
        if (!value) {
            return formatMessage({ id: 'settings.dns.validation.ipRequired' })
        }
        if (value == DOMAIN_STATIC_IP) {
            return formatMessage({ id: 'settings.dns.validation.cannotUseDefaultIp' })
        }
        const reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'settings.dns.validation.invalidIp' })
        }
        return "";
    }


    // 验证域名
    const validateDomain = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'settings.dns.validation.domainRequired' })
        }
        const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'settings.dns.validation.invalidDomain' })
        }

        if (props.data.domain != value) {
            let findDomain = false;
            props.record.extraRecords.forEach(val => {
                if (val.domain === value) {
                    findDomain = true
                }
            })
            if (findDomain) {
                return formatMessage({ id: 'settings.dns.validation.domainExists' })
            }
        }
        return "";
    }

    return <>
        <Modal
            width={760}
            title={formatMessage({ id: 'settings.dns.editSearchdomain.title' })}
            visible={true}
            okButtonProps={{ loading: loading }}
            onOk={handleOk}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Form
                initValues={props.data}
                getFormApi={SetFormApi}
            >
                <> <Title heading={6}>{formatMessage({ id: 'settings.dns.editSearchdomain.domainTitle' })}</Title>
                    {/* <Paragraph type="tertiary">使用 IPv4 或 IPv6 地址解析域名。</Paragraph> */}
                    <Form.Input field='domain' validate={validateDomain} placeholder={formatMessage({ id: 'settings.dns.editSearchdomain.domainPlaceholder' })} noLabel className='mb0' ></Form.Input>
                    <ArrayField field='extraRecords'>
                        {
                            ({ add, arrayFields, addWithInitValue }) => {
                                return <>
                                    <Row className='tableTitle' gutter={16}>
                                        <Col span={6}>{formatMessage({ id: 'settings.dns.editSearchdomain.nameColumn' })}  <Popover content={<div className='p10' style={{ width: 400 }}>
                                            <SearchdomainNameInfo></SearchdomainNameInfo>
                                        </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></Col>
                                        <Col span={4}>{formatMessage({ id: 'settings.dns.editSearchdomain.typeColumn' })}</Col>
                                        <Col span={11}>{formatMessage({ id: 'settings.dns.editSearchdomain.valueColumn' })}</Col>
                                        <Col span={3} className='btn-right-col'><Button icon={<IconPlusCircle />} onClick={() => { addWithInitValue({ name: '', type: 'A', value: '' }); }} style={{}}>{formatMessage({ id: 'components.common.add' })}</Button></Col>
                                    </Row>

                                    {arrayFields.map(({ field, key, remove }, i) => {
                                        return <Row key={key} gutter={16} className={styles.tableRow}>
                                            <Col span={6}> <Form.Input
                                                field={`${field}[name]`}
                                                noLabel
                                                placeholder={formatMessage({ id: 'settings.dns.editSearchdomain.namePlaceholder' })}
                                                validate={validSearchDomainName}
                                                style={{ width: '100%' }}
                                                disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                            >
                                            </Form.Input></Col>
                                            <Col span={4}><Form.Select
                                                field={`${field}[type]`}
                                                noLabel
                                                style={{ width: '100%' }}
                                                optionList={[
                                                    { label: formatMessage({ id: 'settings.dns.recordType.a' }), value: 'A' },
                                                    { label: 'CNAME', value: 'CNAME' },
                                                ]}
                                                disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                            >
                                            </Form.Select></Col>
                                            <Col span={11}>{formApi?.getValue('extraRecords')?.[i]?.type == "A" && <Form.Input
                                                field={`${field}[value]`}
                                                noLabel
                                                validate={validateIP}
                                                placeholder={formatMessage({ id: 'settings.dns.editSearchdomain.ipv4Placeholder' })}

                                                style={{ width: '100%' }}
                                                disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                            />}
                                                {formApi?.getValue('extraRecords')?.[i]?.type == "CNAME" && <Form.Input
                                                    field={`${field}[value]`}
                                                    noLabel
                                                    validate={(value: string, values: any) => {

                                                        let vals: {
                                                            domain: string,
                                                            extraRecords: {
                                                                name: string,
                                                                type: string,
                                                                value: string
                                                            }[]
                                                        } = values;
                                                        if (!value) {
                                                            return formatMessage({ id: 'settings.dns.validation.cnameRequired' })
                                                        }

                                                        const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
                                                        if (!reg.test(value)) {
                                                            return formatMessage({ id: 'settings.dns.validation.invalidCname' })
                                                        }

                                                        let findSame = false;
                                                        const thisItem = vals.extraRecords[i];

                                                        vals.extraRecords.forEach((val, index) => {
                                                            if (index == i) {
                                                                return;
                                                            }
                                                            if (val.type == thisItem.type && val.value == value && val.name == thisItem.name) {
                                                                findSame = true;
                                                            }
                                                        })

                                                        if (findSame) {
                                                            return formatMessage({ id: 'settings.dns.validation.recordExists' })
                                                        }

                                                        // 查找循环引用
                                                        let refList: { name: string, type: string, value: string }[] = [];
                                                        var getAllRefrence = (
                                                            domain: string,
                                                            thisItem: { name: string, type: string, value: string },
                                                            thisIndex: number,
                                                            extraRecords: { name: string, type: string, value: string }[], list: { name: string, type: string, value: string }[]) => {

                                                            extraRecords.forEach((val, index) => {
                                                                if (index == thisIndex) {
                                                                    return;
                                                                }
                                                                if (val.type == "CNAME" && thisItem.value == val.name + "." + domain) {
                                                                    list.push(val);
                                                                    if (list.length >= extraRecords.length) {
                                                                        return;
                                                                    }
                                                                    getAllRefrence(domain, val, index, extraRecords, list);
                                                                }
                                                            })
                                                        }
                                                        getAllRefrence(vals.domain, thisItem, i, vals.extraRecords, refList);

                                                        if (refList.length >= vals.extraRecords.length) {
                                                            return formatMessage({ id: 'settings.dns.validation.circularReference' })
                                                        }
                                                        return "";
                                                    }}
                                                    placeholder={formatMessage({ id: 'settings.dns.editSearchdomain.cnamePlaceholder' })}

                                                    style={{ width: '100%' }}
                                                    disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                                />}</Col>
                                            <Col span={3} className='btn-right-col'>
                                                <Button
                                                    type='danger'
                                                    theme='borderless'
                                                    disabled={arrayFields.length <= 1 || formApi?.getValue('extraRecords')?.[i]?.sys}
                                                    icon={<IconMinusCircle />}
                                                    onClick={remove}
                                                    style={{ margin: 12 }}
                                                />
                                            </Col>

                                        </Row>
                                    }

                                    )}
                                </>
                            }
                        }
                    </ArrayField>



                </>
            </Form>
        </Modal>
    </>
}

export default Index;