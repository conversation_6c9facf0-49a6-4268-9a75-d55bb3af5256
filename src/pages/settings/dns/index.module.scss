.tailnetName {
    border: 1px solid var(--semi-color-border);
    padding: 8px 12px;
    max-width: 600px;
    display: flex;
    justify-content: space-between;
    border-radius: var(--semi-border-radius-medium);
    align-items: center;
    margin-bottom: 20px;
}
.nameForm {
    max-width: 600px;
}
// 长得像输入框的div
.displayInput {
    border: 1px solid var(--semi-color-border);
    border-radius: var(--semi-border-radius-medium);
    padding: 8px 12px;
    max-width: 600px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px!important;
}

.splitDNSTitle {
    display: flex;
    align-items: center;
    margin-bottom: 10px!important;
    .splitDNSIcon {
        margin-right: 8px;
        color: var(--semi-color-text-3);
    }
    .iconSplitDNS {
        width: 10px;
        height: 10px;
        margin-right: 2px;
    }
}

.iconMagicDns {
    width: 10px;
    height: 10px;
}

.ipList {
    max-width: 626px;
    border: 1px solid var(--semi-color-border);
    border-radius: var(--semi-border-radius-medium);
    margin-bottom: 20px;
    .ipListItem {

    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    color: var(--semi-color-text-1);
    }
} 

.overrideLocalDns {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 12px;
    color: var(--semi-color-text-2);
}

.banner {
    margin-bottom: 20px;
    max-width: 600px;

} 
.searchdomainDetail {
    margin-left: 18px;
    margin-top: 12px;
    th, td {
        // padding-bottom: 10px!important;
    }
    
} 

.layout {
    min-height: calc(100vh - 60px);
}
.sider {
    min-height: calc(100vh - 60px);
    width: 241px;
    border-right: 1px solid var(--semi-color-border);    
    >div {
        width: 241px;
    }
}
.nav {
    border: none;
    position: fixed;
}
.hash {
    display: block;
    height: 80px;
    margin-top: -40px;
     a {
        display: block;
     }
    
}
.titleHash {
    display: block;
    height: 76px;
    margin-top: -76px;
     a {
        display: block;
     }
}