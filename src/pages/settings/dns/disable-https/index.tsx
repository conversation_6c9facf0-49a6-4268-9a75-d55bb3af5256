import { FC, useState } from 'react'
import { Typography, Modal, Notification, Tabs, TabPane } from '@douyinfe/semi-ui';
import { DNSConfig } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
const { Paragraph, Title } = Typography;

interface Props {
    flynetId: bigint,
    close: () => void,
    success?: () => void,
    record: DNSConfig
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    return <>
        <Modal
            width={400}
            title={formatMessage({ id: 'settings.dns.disableHttps.title' })}
            visible={true}
            okButtonProps={{ loading, type:'danger' }}
            onOk={() => {
                setLoading(true)
                flylayerClient.setDNSConfig({
                    flynetId: props.flynetId,
                    config: {...props.record, httpsCerts: false}
                }).then(() => {
                    Notification.success({content: formatMessage({ id: 'settings.dns.disableHttps.success' }),position: "bottomRight"})
                    if (props.success) {
                        props.success();
                    }
                }).catch((err) => {
                    Notification.error({
                        title: formatMessage({ id: 'settings.dns.disableHttps.failed' }),
                        content: (err as any).message,
                        position: "bottomRight"})
                }).finally(() => setLoading(false))
            }}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph>{formatMessage({ id: 'settings.dns.disableHttps.description' })}</Paragraph>
            
        </Modal></>
}
export default Index;
