import { FC, useState } from 'react'
import { Typography, Modal, Notification, Tag, TabPane, Form, Row, Col } from '@douyinfe/semi-ui';
import { DNSConfig, Routes } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import IconSplitDns from "@/assets/icon/split-dns.svg";
import styles from './index.module.scss';
import { DOMAIN_STATIC_IP } from '@/constants';
const { Paragraph, Title } = Typography;

interface Props {
    flynetId: bigint,
    close: () => void,
    success?: () => void,
    record: DNSConfig,
    data: {
        ip: string,
        domain: string,
        useSplitDNS: boolean
    }
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleOk = async () => {
        if (!formApi) {
            return
        }
        const values = formApi?.getValues();
        // 去除前后空格
        values.ip = values.ip.trim();
        // 域名转换成小写和去除前后空格
        if (values.domain) {
            values.domain = values.domain.toLowerCase();
            values.domain = values.domain.trim();
        }
        formApi.validate().then((res) => {

            setLoading(true)
            let config = { ...props.record }
            if (values.useSplitDNS && values.domain) {

                // 之前为split DNS, 需要替换旧数据
                if (props.data.useSplitDNS) {
                    if (config.routes) {
                        if (config.routes[props.data.domain]) {
                            config.routes[props.data.domain].routes = config.routes[props.data.domain].routes.filter((val) => val != props.data.ip)
                            if (config.routes[props.data.domain].routes.length == 0) {
                                delete config.routes[props.data.domain]
                            }
                            
                        }
                    }
                }
                // 之前为普通DNS, 需要删除旧数据
                else {
                    if (config.nameservers) {
                        config.nameservers = config.nameservers.filter((val) => val != props.data.ip)
                    }
                }


                if (config.routes) {
                    if (config.routes[values.domain]) {
                        config.routes[values.domain].routes.push(values.ip)
                    } else {
                        config.routes[values.domain] = new Routes({ routes: [values.ip] })
                    }

                } else {
                    config.routes = {
                        [values.domain]: new Routes({ routes: [values.ip] })
                    }
                }
            } else if (values.ip) {
                // 之前为split DNS, 需要删除旧数据
                if (props.data.useSplitDNS) {
                    if (config.routes) {
                        if (config.routes[props.data.domain]) {
                            config.routes[props.data.domain].routes = config.routes[props.data.domain].routes.filter((val) => val != props.data.ip)
                            if(config.routes[props.data.domain].routes.length == 0  ) {
                                delete config.routes[props.data.domain]
                            }
                        }
                    }

                    if (config.nameservers) {
                        config.nameservers.push(values.ip)
                    } else {
                        config.nameservers = [values.ip]
                    }
                }
                // 更新数据即可
                else {
                    if (config.nameservers) {
                        config.nameservers = config.nameservers.map((val) => {
                            if (val == props.data.ip) {
                                return values.ip
                            }
                            return val;
                        })
                    }
                }

            }

            flylayerClient.setDNSConfig({
                flynetId: props.flynetId,
                config: config
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'settings.dns.editNameserver.success' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                Notification.error({ title: formatMessage({ id: 'settings.dns.editNameserver.failed' }), 
                content: (err as any).message,
                position: "bottomRight" })
            }).finally(() => setLoading(false))
        }).catch((err) => {
            console.error(err)
        })


    }

    // 验证IP
    const validateIP = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'settings.dns.validation.ipRequired' })
        }
        if (value == DOMAIN_STATIC_IP) {
            return formatMessage({ id: 'settings.dns.validation.cannotUseDefaultIp' })
        }
        const reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'settings.dns.validation.invalidIp' })
        }
        // 当前值
        const values = formApi?.getValues();

        // 之前是split DNS
        if (props.data.useSplitDNS) {
            // 现在也是split DNS
            if (values.useSplitDNS) {
                // 域名没改的情况, IP改了
                if (values.domain == props.data.domain && values.ip != props.data.ip) {
                    if (props.record.routes) {
                        if (props.record.routes[values.domain]) {
                            if (props.record.routes[values.domain].routes.includes(value)) {
                                return formatMessage({ id: 'settings.dns.validation.ipExists' })
                            }
                        }
                    }
                }
                // 域名改了的情况
                else if (values.domain != props.data.domain) {
                    if (props.record.routes) {
                        if (props.record.routes[values.domain]) {
                            if (props.record.routes[values.domain].routes.includes(value)) {
                                return formatMessage({ id: 'settings.dns.validation.ipExists' })
                            }
                        }
                    }
                }

            }
            // 现在是普通DNS
            else {
                if (props.record.nameservers) {
                    if (props.record.nameservers.includes(value)) {
                        return formatMessage({ id: 'settings.dns.validation.ipExists' })
                    }
                }
            }
        }
        // 之前是普通DNS
        else {

            // 现在是split DNS
            if (values.useSplitDNS) {
                if (props.record.routes) {
                    if (props.record.routes[values.domain]) {
                        if (props.record.routes[values.domain].routes.includes(value)) {
                            return formatMessage({ id: 'settings.dns.validation.ipExists' })
                        }
                    }
                }
            }
            // 现在也是普通DNS
            else {
                // ip变了的情况
                if (values.ip != props.data.ip) {
                    if (props.record.nameservers) {
                        if (props.record.nameservers.includes(value)) {
                            return formatMessage({ id: 'settings.dns.validation.ipExists' })
                        }
                    }
                }
            }

        }

        return "";
    }

    // 验证域名
    const validateDomain = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'settings.dns.validation.domainRequired' })
        }
        const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'settings.dns.validation.invalidDomain' })
        }
        return "";
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'settings.dns.editNameserver.title' })}
            visible={true}
            okButtonProps={{ loading: loading }}
            onOk={handleOk}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Form
                getFormApi={SetFormApi}
                initValues={props.data}
                render={(formState) => {
                    return <> <Title heading={6}>{formatMessage({ id: 'settings.dns.editNameserver.dnsServerTitle' })}</Title>
                        <Paragraph type="tertiary">{formatMessage({ id: 'settings.dns.editNameserver.dnsServerDescription' })}</Paragraph>
                        <Form.Input noLabel className='mb20' field='ip' validate={validateIP}></Form.Input>
                        <Row>
                            <Col span={12}>
                                <Title heading={6}>{formatMessage({ id: 'settings.dns.editNameserver.applyToSpecificDomain' })}<Tag style={{ marginLeft: 8 }}> <img className={styles.iconSplitDNS} src={IconSplitDns}></img>&nbsp;{formatMessage({ id: 'settings.dns.splitDomain' })}</Tag></Title>
                            </Col>
                            <Col span={12}>
                                <div className={styles.colFormItem}>
                                    <Form.Switch noLabel field='useSplitDNS' onChange={() => setTimeout(() => formApi?.validate(), 100)} />
                                </div>
                            </Col>
                        </Row>
                        <Paragraph type="tertiary" className='mb20'>{formatMessage({ id: 'settings.dns.editNameserver.splitDnsDescription' })}</Paragraph>
                        {formState.values.useSplitDNS && <>

                            <Title heading={6}>{formatMessage({ id: 'settings.dns.editNameserver.domainTitle' })}</Title>
                            <Form.Input placeholder={'example.com'} onChange={() => setTimeout(() => formApi?.validate(), 100)} validate={validateDomain} className='mb0' noLabel field='domain'></Form.Input>
                            <Paragraph type='tertiary'>
                                {formatMessage({ id: 'settings.dns.editNameserver.domainUsageDescription' })}
                            </Paragraph>
                        </>}
                    </>
                }}
            >

            </Form>
        </Modal>
    </>
}

export default Index;