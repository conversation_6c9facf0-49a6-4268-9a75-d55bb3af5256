import { FC, useState } from 'react'
import { Typography, Modal, Notification, Collapse, Tag, Form, Row, Col } from '@douyinfe/semi-ui';
import { DNSConfig, Routes } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import IconSplitDns from "@/assets/icon/split-dns.svg";
import styles from './index.module.scss';
import { DOMAIN_STATIC_IP } from '@/constants';
const { Paragraph, Title } = Typography;

interface Props {
    flynetId: bigint,
    close: () => void,
    success?: () => void,
    record: DNSConfig
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleOk = async () => {
        if (!formApi) {
            return
        }
        const values = formApi?.getValues();
        // 去除前后空格
        values.ip = values.ip.trim();
        // 域名转换成小写和去除前后空格
        if (values.domain) {
            values.domain = values.domain.toLowerCase();
            values.domain = values.domain.trim();
        }
        formApi.validate().then((res) => {

            setLoading(true)
            let config = { ...props.record }
            if (values.useSplitDNS && values.domain) {
                if (config.routes) {
                    if (config.routes[values.domain]) {
                        config.routes[values.domain].routes.push(values.ip)
                    } else {
                        config.routes[values.domain] = new Routes({ routes: [values.ip] })
                    }

                } else {
                    config.routes = {
                        [values.domain]: new Routes({ routes: [values.ip] })
                    }
                }
            } else if (values.ip) {
                if (config.nameservers) {
                    config.nameservers.push(values.ip)
                } else {
                    config.nameservers = [values.ip]
                }
            }

            flylayerClient.setDNSConfig({
                flynetId: props.flynetId,
                config: config
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'settings.dns.addNameserver.success' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                Notification.error({ title: formatMessage({ id: 'settings.dns.addNameserver.failed' }), 
                content: (err as any).message,
                position: "bottomRight" })
            }).finally(() => setLoading(false))
        }).catch((err) => {
            console.error(err)
        })


    }

    // 验证IP
    const validateIP = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'settings.dns.validation.ipRequired' })
        }
        if (value == DOMAIN_STATIC_IP) {
            return formatMessage({ id: 'settings.dns.validation.cannotUseDefaultIp' })
        }
        const reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'settings.dns.validation.invalidIp' })
        }

        const values = formApi?.getValues();
        
        // 查重 ip重复
        if(values?.useSplitDNS && values?.domain){
            if (props.record.routes) {
                if (props.record.routes[values.domain]) {
                    if (props.record.routes[values.domain].routes.includes(value)) {
                        return formatMessage({ id: 'settings.dns.validation.ipExists' })
                    }
                }
            }
        } else {
            if (props.record.nameservers) {
                if (props.record.nameservers.includes(value)) {
                    return formatMessage({ id: 'settings.dns.validation.ipExists' })
                }
            }
        }
        return "";
    }

    // 验证域名
    const validateDomain = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'settings.dns.validation.domainRequired' })
        }
        const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'settings.dns.validation.invalidDomain' })
        }
        return "";
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'settings.dns.addNameserver.title' })}
            visible={true}
            okButtonProps={{ loading: loading }}
            onOk={handleOk}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Form
                getFormApi={SetFormApi}
                render={(formState) => {
                    return <> <Title heading={6}>{formatMessage({ id: 'settings.dns.addNameserver.dnsServerTitle' })}</Title>
                        <Paragraph type="tertiary">{formatMessage({ id: 'settings.dns.addNameserver.dnsServerDescription' })}</Paragraph>
                        <Form.Input noLabel className='mb20' field='ip' validate={validateIP} placeholder={formatMessage({ id: 'settings.dns.addNameserver.ipPlaceholder' })}></Form.Input>
                        <Row>
                            <Col span={12}>
                                <Title heading={6}>{formatMessage({ id: 'settings.dns.addNameserver.applyToSpecificDomain' })}<Tag style={{ marginLeft: 8 }}> <img className={styles.iconSplitDNS} src={IconSplitDns}></img>&nbsp;{formatMessage({ id: 'settings.dns.splitDomain' })}</Tag></Title>
                            </Col>
                            <Col span={12}>
                                <div className={styles.colFormItem}>
                                    <Form.Switch noLabel field='useSplitDNS' onChange={()=>setTimeout(()=>formApi?.validate(), 100) }/>
                                </div>
                            </Col>
                        </Row>
                        <Paragraph  type="tertiary" className='mb20'>{formatMessage({ id: 'settings.dns.addNameserver.splitDnsDescription' })}</Paragraph>
                        {formState.values.useSplitDNS && <>

                            <Title heading={6}>{formatMessage({ id: 'settings.dns.addNameserver.domainTitle' })}</Title>
                            <Form.Input placeholder={'example.com'} validate={validateDomain} onChange={()=>setTimeout(()=>formApi?.validate(), 100) } className='mb0' noLabel field='domain'></Form.Input>
                            <Paragraph type='tertiary'>
                                {formatMessage({ id: 'settings.dns.addNameserver.domainUsageDescription' })}
                            </Paragraph>
                        </>}
                    </>
                }}
            >

            </Form>
        </Modal>
    </>
}

export default Index;