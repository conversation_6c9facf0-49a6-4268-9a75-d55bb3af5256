import React, { useEffect, useState } from 'react'
import { Typography, Banner, Upload, Input, Space, Button, Divider, Notification, Avatar, Row, Col } from '@douyinfe/semi-ui';
import { IconCamera } from '@douyinfe/semi-icons';

const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'
const Index: React.FC = () => {

    return <><div className='settings-page'>
        <Title heading={3} className='mb10' >通用</Title>
        <Paragraph className='mb40' type='tertiary'>查看和管理您的企业通用设置</Paragraph>

        <Title heading={4} className="mb2">基本信息</Title>
        <Paragraph className='mb20' type='tertiary'>设置企业图标和名称</Paragraph>

        <div className={styles.workspaceForm}>
            <Row>
                <Col span={6}><Text>图标</Text></Col>
                <Col span={16}><Upload onError={() => Notification.error({content: "上传失败",position: "bottomRight"})} showUploadList={false} accept='image/*' action='' >
                    <Avatar size='large' shape="square" src='https://internal.s3.cn-northwest-1.amazonaws.com.cn/workspace-logo/92/4D/1633768710509068288.jpg' hoverMask={(
                        <div className={styles.uploadMask}>
                            <IconCamera size='extra-large'/>
                        </div>
                    )}></Avatar>
                </Upload></Col>
            </Row>
            <Row>
                <Col span={6}><Text>名称</Text></Col>
                <Col span={18}> <Input ></Input></Col>
            </Row>
            <Space>
                <Button htmlType="reset">重置</Button>
                <Button type="primary" theme='solid' htmlType="submit">提交</Button>
            </Space>

        </div>

        <Divider className='mb40' />
        <Title heading={4} className="mb2">删除企业</Title>
        <Paragraph className='mb20' type='tertiary'>永久删除该企业。 该操作无法撤消 </Paragraph>
        <Banner
            bordered
            fullMode={false}
            type="info"
            closeIcon={null}
            description="请联系管理员进行该操作"
        />
    </div></>
}

export default Index