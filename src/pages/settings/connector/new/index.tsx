import React, { useState, useContext, useEffect } from 'react'
import { Typography, Form, Modal, Row, Col, Button, Divider, Notification, Popover, Space } from '@douyinfe/semi-ui';
import { ConnectorGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import pinyin from 'tiny-pinyin';
import { sanitizeLabel } from '@/utils/common';
import { IconHelpCircle } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
import styles from './index.module.scss'
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';

import MachineViewer from '@/components/machine-viewer';
import {
    IconTick,
    IconPlus,
    IconArrowDown,
    IconArrowUp,
    IconArrowRight,
    IconArrowUpRight,
    IconAlignTop, IconAlignBottom,
    IconMinusCircle,
    IconMore
} from '@douyinfe/semi-icons'
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import MachineSelector from '@/components/machine-selector'
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'


const { Title, Paragraph, Text } = Typography;
const { Input, RadioGroup, Radio, Switch, InputNumber } = Form

interface Props {
    close: () => void;
    groups: ConnectorGroup[];
    success: (group: ConnectorGroup) => void;
}

const Index: React.FC<Props> = ({ close, groups, success }) => {
    const { formatMessage } = useLocale();
    const [loading, setLoading] = useState(false);

    const flynetGeneral = useContext(FlynetGeneralContext);

    // 网关节点选择
    const [gatewaySelectorVisible, setGatewaySelectorVisible] = useState(false);

    const [gatewayIp, setGatewayIp] = useState('');

    // 网关节点
    const [machines, setMachines] = useState<Machine[]>([]);


    const [gatewayNodeViewerVisible, setGatewayNodeViewerVisible] = useState(false);

    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
    }>>()

    const handleSubmit = async () => {

        await formApi?.validate()

        setLoading(true);

        const newGroups = groups || [];
        const values = formApi?.getValues();
        const group = new ConnectorGroup({
            alias: values?.alias,
            name: values?.name,
            description: values?.description,
            machines: machines,
            primaryMachine: machines[0]
        });

        newGroups.push(group);

        flylayerClient.setConnectorGroup({
            flynetId: flynetGeneral.id,
            connectorGroups: newGroups
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'settings.connector.createSuccess' }),
                duration: 2000
            });
            close();
            const group = new ConnectorGroup();
            success(group);
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'settings.connector.createFailed' }),
                duration: 2000
            });
        }).finally(() => {
            setLoading(false);
        });
    }

    return <><Modal
        width={600}
        title={formatMessage({ id: 'settings.connector.createTitle' })}
        visible
        onCancel={close}
        onOk={handleSubmit}
        okButtonProps={{ loading, disabled: machines.length === 0 }}
        className='semi-modal'
        maskClosable={false}
    >
        <div>
            <Form
                allowEmpty
                getFormApi={SetFormApi}
                onValueChange={(values, changedValue) => {
                    if (changedValue.hasOwnProperty('alias')) {
                        formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                    }
                }}
            >
                <Row gutter={12}>
                    <Col span={12}>
                        <Input field='alias' label={formatMessage({ id: 'settings.connector.field.name' })} trigger={'blur'} validate={value => {
                            if (!value) {
                                return formatMessage({ id: 'settings.connector.validation.nameRequired' });
                            }
                            let isRepeat = false;
                            groups.forEach(group => {
                                if (group.alias === value) {
                                    isRepeat = true;
                                    return;
                                }
                            });
                            if (isRepeat) {
                                return formatMessage({ id: 'settings.connector.validation.nameDuplicate' });
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={12}>
                        <Input field='name'
                            label={<>{formatMessage({ id: 'settings.connector.field.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'settings.connector.field.codeHint' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return formatMessage({ id: 'settings.connector.validation.codeRequired' });
                                }

                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '编码不能以-开头'
                                }

                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "编码只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            required />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Input field='description' label='备注' />
                    </Col>
                </Row>
                <Divider className='mb10'></Divider>
                <Row className="mb20">
                    <Col span={20}>
                        <Title heading={6} className="mb10">连接器</Title>
                    </Col>
                    <Col span={4} className={styles.rightColumn}>
                        {machines.length > 0 &&
                            <Button
                                onClick={() => setGatewaySelectorVisible(true)}
                                icon={<IconPlus />}
                            ></Button>
                        }
                    </Col>
                </Row>
                {machines.length > 0 ? <>
                    <Row className={styles.tableTitle} >
                        <Col span={10}>名称</Col>
                        <Col span={9}>地址</Col>
                        <Col span={3}>&nbsp;</Col>
                        <Col span={2}>&nbsp;</Col>
                    </Row>
                    {machines.map((machine, i) => (
                        <Row className={styles.tableBody} key={i} >
                            <Col xs={24} sm={10}>
                                {machine.name}
                            </Col>
                            <Col xs={24} sm={9}>
                                {machine.ipv4}
                            </Col>
                            <Col xs={24} sm={3}>

                                <Button icon={<IconArrowUpRight />} style={{ width: 32 }} onClick={() => {
                                    setGatewayIp(machine.ipv4)
                                    setGatewayNodeViewerVisible(true)
                                }}></Button>
                            </Col>
                            <Col xs={24} sm={2} className={styles.rightColumn}>
                                <Popover
                                    position='left'
                                    style={{
                                        padding: 5,
                                    }}
                                    content={<>
                                        <Space >
                                            <Button icon={<IconAlignTop />}
                                                disabled={i == 0}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []

                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i - 1];
                                                    newList[i - 1] = temp;
                                                    setMachines(newList);
                                                }}
                                            ></Button>
                                            <Button icon={<IconArrowUp />}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []

                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i - 1];
                                                    newList[i - 1] = temp;

                                                    setMachines(newList)
                                                }}

                                                disabled={i == 0}
                                            ></Button>
                                            <Button
                                                icon={<IconArrowDown />}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []

                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i + 1];
                                                    newList[i + 1] = temp;
                                                    setMachines(newList)
                                                }}
                                                disabled={i == machines.length - 1}
                                            ></Button>
                                            <Button icon={<IconAlignBottom />} disabled={i == machines.length - 1}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []
                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i + 1];
                                                    newList[i + 1] = temp;

                                                    setMachines(newList)
                                                }}
                                            ></Button>
                                            <Button
                                                type='danger'
                                                theme='borderless'
                                                icon={<IconMinusCircle />}
                                                onClick={() => {
                                                    const newList: Array<Machine> = [];
                                                    machines.forEach((m, index) => {
                                                        if (index !== i) {
                                                            newList.push(m);
                                                        }
                                                    });
                                                    setMachines(newList);
                                                }}

                                            />
                                        </Space>
                                    </>}><Button icon={<IconMore />}></Button></Popover>


                            </Col>

                        </Row>
                    ))}</> : <div className={styles.addCenter}>
                    <Button
                        size='large'
                        onClick={() => setGatewaySelectorVisible(true)}
                        icon={<IconPlus />}
                    ></Button>
                </div>}
            </Form>
        </div>
    </Modal>
        {gatewaySelectorVisible && <MachineSelector
            multi={true}
            value={machines}
            close={() => setGatewaySelectorVisible(false)}
            gateway={true}
            onChange={(value) => {
                if (value instanceof Machine) {
                    let exist = machines.find(m => m.ipv4 === value.ipv4);
                    if (!exist) {
                        setMachines([...machines, value]);
                    }
                } else if (value instanceof Array) {
                    const newList: Array<Machine> = [];
                    machines.forEach(m => {
                        newList.push(m);
                    });
                    value.forEach(v => {
                        let exist = machines.find(m => m.ipv4 === v.ipv4);
                        if (!exist) {
                            newList.push(v);
                        }
                    });
                    setMachines(newList);

                }
                setGatewaySelectorVisible(false);
            }}   ></MachineSelector>
        }

        {
            gatewayNodeViewerVisible && gatewayIp && <MachineViewer
                ip={gatewayIp}
                close={() => setGatewayNodeViewerVisible(false)}
            ></MachineViewer>
        }
    </>
}

export default Index;