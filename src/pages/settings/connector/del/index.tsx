import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input, TabPane } from '@douyinfe/semi-ui';
import { ConnectorGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";

import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success: (record: ConnectorGroup) => void,
    record: ConnectorGroup,
    groups: ConnectorGroup[]
}
const Index: React.FC<Props> = (props) => {

    const thisIndex = props.groups.findIndex(g => g.name === props.record.name);

    const [groupIndex, setGroupIndex] = useState(thisIndex);
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={`删除设备组${props.record.name}`}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

            const newGroups = props.groups.filter((g, i) => i !== groupIndex);

            flylayerClient.setConnectorGroup({
                flynetId: flynet.id,
                connectorGroups: newGroups
            }).then(() => {
                Notification.success({
                    title: '删除成功'
                });
                props.success && props.success(props.record);
                props.close();
            }).catch((e) => {
                Notification.error({
                    title: '删除失败',
                });
            }).finally(() => {
                setLoading(false);
            })

        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'> 设备组将被删除，删除后将无法给该设备组分配设备。
        </Paragraph>
        <Paragraph className='mb20'> 输入 <b>{props.record.name}</b> 以确认删除
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;