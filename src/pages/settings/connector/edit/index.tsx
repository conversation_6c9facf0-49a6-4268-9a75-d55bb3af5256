import React, { useState, useContext, useEffect } from 'react'
import { Typography, Form, Modal, Row, Col, Button, Divider, Notification, Popover, Space } from '@douyinfe/semi-ui';
import { getFlynet } from '@/services/flynet';
import { ConnectorGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import pinyin from 'tiny-pinyin';
import { sanitizeLabel } from '@/utils/common';
import { IconHelpCircle } from '@douyinfe/semi-icons';
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'
import MachineViewer from '@/components/machine-viewer';
import MachineSelector from '@/components/machine-selector'
import styles from './index.module.scss'
import { flylayerClient } from '@/services/core';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import {
    IconTick,
    IconPlus,
    IconArrowDown,
    IconArrowUp,
    IconArrowRight,
    IconArrowUpRight,
    IconAlignTop, IconAlignBottom,
    IconMinusCircle,
    IconMore
} from '@douyinfe/semi-icons'
const { Paragraph, Text, Title } = Typography;
const { Input, RadioGroup, Radio, Switch, InputNumber } = Form

interface Props {
    close: () => void;
    group: ConnectorGroup;
    groups: ConnectorGroup[];
    success: (group: ConnectorGroup) => void;
}

const Index: React.FC<Props> = ({ close, group, groups, success }) => {

    const thisIndex = groups.findIndex(g => g.name === group.name);

    const [groupIndex, setGroupIndex] = useState(thisIndex);


    const [loading, setLoading] = useState(false);

    const flynetGeneral = useContext(FlynetGeneralContext);

    // 网关节点选择
    const [gatewaySelectorVisible, setGatewaySelectorVisible] = useState(false);

    const [gatewayIp, setGatewayIp] = useState('');

    // 网关节点
    const [machines, setMachines] = useState<Machine[]>(group.machines);


    const [gatewayNodeViewerVisible, setGatewayNodeViewerVisible] = useState(false);

    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
    }>>()

    const handleSubmit = async () => {

        await formApi?.validate();

        setLoading(true);

        const values = formApi?.getValues();

        const newGroup = new ConnectorGroup({
            alias: values?.alias,
            name: values?.name,
            description: values?.description,
            machines: machines,
            primaryMachine: machines[0]
        });

        const newGroups:Array<ConnectorGroup> = groups.map((g, i) => {
            if (i === groupIndex) {
                return newGroup;
            }
            return g;
        });

        flylayerClient.setConnectorGroup({
            flynetId: flynetGeneral.id,
            connectorGroups: newGroups
        }).then(res => {
            Notification.success({
                title: '编辑连接器组成功',
                duration: 2000
            });
            close();
            const group = new ConnectorGroup();
            success(group);
        }).catch(err => {
            Notification.error({
                title: '编辑连接器组失败',
                duration: 2000
            });
        }).finally(() => {
            setLoading(false);
        });
    }

    return <><Modal
        width={600}
        title='编辑连接器组'
        visible
        onCancel={close}
        onOk={handleSubmit}
        okButtonProps={{ loading }}
        className='semi-modal'
        maskClosable={false}
    >
        <div>
            <Form
                allowEmpty
                getFormApi={SetFormApi}
                initValues={{
                    alias: group.alias,
                    name: group.name,
                    description: group.description
                }}
            >
                <Row gutter={12}>
                    <Col span={12}>
                        <Input field='alias' label='名称' trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            let isRepeat = false;
                            groups.forEach((g, i) => {
                                if (i !== groupIndex && g.alias === value) {
                                    isRepeat = true;
                                }
                            });
                            if (isRepeat) {
                                return '名称重复';
                            }

                            return '';
                        }} />
                    </Col>
                    <Col span={12}>
                        <Input field='name'
                            label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '编码不能为空';
                                }

                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '编码不能以-开头'
                                }

                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "编码只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            readonly
                            required />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Input field='description' label='备注' />
                    </Col>
                </Row>
                <Divider className='mb10'></Divider>
                <Row className="mb20">
                    <Col span={20}>
                        <Title heading={6} className="mb10">连接器</Title>
                    </Col>
                    <Col span={4} className={styles.rightColumn}>
                        {machines.length > 0 &&
                            <Button
                                onClick={() => setGatewaySelectorVisible(true)}
                                icon={<IconPlus />}
                            ></Button>
                        }
                    </Col>
                </Row>
                {machines.length > 0 ? <>
                    <Row className={styles.tableTitle} >
                        <Col span={10}>名称</Col>
                        <Col span={9}>地址</Col>
                        <Col span={3}>&nbsp;</Col>
                        <Col span={2}>&nbsp;</Col>
                    </Row>
                    {machines.map((machine, i) => (
                        <Row className={styles.tableBody} key={i} >
                            <Col xs={24} sm={10}>
                                {machine.name}
                            </Col>
                            <Col xs={24} sm={9}>
                                {machine.ipv4}
                            </Col>
                            <Col xs={24} sm={3}>

                                <Button icon={<IconArrowUpRight />} style={{ width: 32 }} onClick={() => {
                                    setGatewayIp(machine.ipv4)
                                    setGatewayNodeViewerVisible(true)
                                }}></Button>
                            </Col>
                            <Col xs={24} sm={2} className={styles.rightColumn}>
                                <Popover
                                    position='left'
                                    style={{
                                        padding: 5,
                                    }}
                                    content={<>
                                        <Space >
                                            <Button icon={<IconAlignTop />}
                                                disabled={i == 0}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []

                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i - 1];
                                                    newList[i - 1] = temp;
                                                    setMachines(newList);
                                                }}
                                            ></Button>
                                            <Button icon={<IconArrowUp />}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []

                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i - 1];
                                                    newList[i - 1] = temp;

                                                    setMachines(newList)
                                                }}

                                                disabled={i == 0}
                                            ></Button>
                                            <Button
                                                icon={<IconArrowDown />}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []

                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i + 1];
                                                    newList[i + 1] = temp;
                                                    setMachines(newList)
                                                }}
                                                disabled={i == machines.length - 1}
                                            ></Button>
                                            <Button icon={<IconAlignBottom />} disabled={i == machines.length - 1}
                                                onClick={() => {
                                                    let newList: Array<Machine> = []
                                                    machines?.forEach((item: Machine) => {
                                                        newList.push(item)
                                                    })
                                                    let temp = newList[i];
                                                    newList[i] = newList[i + 1];
                                                    newList[i + 1] = temp;

                                                    setMachines(newList)
                                                }}
                                            ></Button>
                                            <Button
                                                type='danger'
                                                theme='borderless'
                                                icon={<IconMinusCircle />}
                                                onClick={() => {
                                                    const newList: Array<Machine> = [];
                                                    machines.forEach((m, index) => {
                                                        if (index !== i) {
                                                            newList.push(m);
                                                        }
                                                    });
                                                    setMachines(newList);
                                                }}

                                            />
                                        </Space>
                                    </>}><Button icon={<IconMore />}></Button></Popover>


                            </Col>

                        </Row>
                    ))}</> : <div className={styles.addCenter}>
                    <Button
                        size='large'
                        onClick={() => setGatewaySelectorVisible(true)}
                        icon={<IconPlus />}
                    ></Button>
                </div>}

            </Form>
        </div>
    </Modal>
        {gatewaySelectorVisible && <MachineSelector
            multi={true}
            value={machines}
            close={() => setGatewaySelectorVisible(false)}
            gateway={true}
            onChange={(value) => {
                if (value instanceof Machine) {
                    let exist = machines.find(m => m.ipv4 === value.ipv4);
                    if (!exist) {
                        setMachines([...machines, value]);
                    }
                } else if (value instanceof Array) {
                    const newList: Array<Machine> = [];
                    machines.forEach(m => {
                        newList.push(m);
                    });
                    value.forEach(v => {
                        let exist = machines.find(m => m.ipv4 === v.ipv4);
                        if (!exist) {
                            newList.push(v);
                        }
                    });
                    setMachines(newList);

                }
                setGatewaySelectorVisible(false);
            }}   ></MachineSelector>
        }

        {
            gatewayNodeViewerVisible && gatewayIp && <MachineViewer
                ip={gatewayIp}
                close={() => setGatewayNodeViewerVisible(false)}
            ></MachineViewer>
        }

    </>
}

export default Index;