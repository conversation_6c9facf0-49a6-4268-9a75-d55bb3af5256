import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Divider } from '@douyinfe/semi-ui';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import New from './new';
import Edit from './edit';
import Del from './del';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
// import { ConnectorGroup } from '@/interface/connector-group';
import useTable from './useTable';
import { ConnectorGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";
import { useLocale } from '@/locales';


const { Title, Paragraph } = Typography;

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const [createVisible, setCreateVisible] = useState(false);

    const {
        loading,
        groups,
        columns,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedGroup,
        setSelectedGroup,
        reloadFlag,
        setReloadFlag
    } = useTable({});

    return <><div className='settings-page'>
        <Title heading={3} className='mb10'>{formatMessage({ id: 'settings.connector.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.connector.description' })}</Paragraph>

        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>{formatMessage({ id: 'settings.connector.groups.title' })}</Title>
                <Paragraph type='tertiary'>
                    {formatMessage({ id: 'settings.connector.groups.description' })}
                </Paragraph>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>{formatMessage({ id: 'settings.connector.createGroup' })}</Button>
                </Space>
            </div></Col>
        </Row>
        <Divider className='mb20'></Divider>

        <Table
            rowKey={(record?: ConnectorGroup) => record?.name || ''}
            expandRowByClick
            // expandAllRows={services.length < 10}
            empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={groups} pagination={false} />
        {createVisible && <New close={() => setCreateVisible(false)} groups={groups} success={(group) => {
            setReloadFlag(!reloadFlag);
        }
        } />}
        {editVisible && selectedGroup && <Edit close={() => setEditVisible(false)} group={selectedGroup} groups={groups} success={(group) => {
            setReloadFlag(!reloadFlag);
        }
        } />}
        {delVisible && selectedGroup && <Del
            close={() => setDelVisible(false)}
            record={selectedGroup}
            groups={groups}
            success={(group) => {
                setReloadFlag(!reloadFlag);
            }
            } />}
    </div></>
}

export default Index;
