import { useEffect, useState, FC, useContext } from 'react';
import { getFlynet } from '@/services/flynet';

import { Typography, Dropdown, Button, Popover } from '@douyinfe/semi-ui';
import { IconMore, IconArticle, IconHelpCircle } from '@douyinfe/semi-icons';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

import { flylayerClient } from '@/services/core';
import { ConnectorGroup} from "@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb";
import DateFormat from '@/components/date-format';
import { BASE_PATH } from '@/constants/router';
import { useNavigate } from 'react-router-dom';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';


const { Title, Paragraph, Text } = Typography;
export type GroupFilter = {
    query?: string;
}

const useTable = (filterParam: GroupFilter) => {
 
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();

    const [flynet, setFlynet] = useState<Flynet>();

    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);   

    const [groups, setGroups] = useState<ConnectorGroup[]>([]);


    // 编辑弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 删除弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);

    // 当前菜单选中服务
    const [selectedGroup, setSelectedGroup] = useState<ConnectorGroup>();

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    // 表格列
    const columns = [{
        title: '设备组名称',
        dataIndex: 'name',
        render: (field: string, record: ConnectorGroup, index: number) => {
            return <>
            <div style={{ display: 'inline-flex' }}>
                <div>
                    <Title heading={6}>
                        {record.alias}{record.description &&
                            <Popover content={<div className='p10'>{record.description}</div>}>
                                <IconArticle style={{
                                    fontSize: 14,
                                }} />
                            </Popover>}
                    </Title>
                    <Paragraph>
                        <Text>{record.name}</Text>
                    </Paragraph>
                </div>
            </div>
            </>
        }
    }, {
        title: '',
        dataIndex: 'operation',
        render: (field: string, record: ConnectorGroup, index: number) => {
            
            return <div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>
                      
                        <Dropdown.Item
                            onClick={() => {
                                setSelectedGroup(record);
                                setEditVisible(true);
                            }}
                        >编辑连接器组</Dropdown.Item>
                        
                    
                        <Dropdown.Divider />

                        <Dropdown.Item type="danger"
                            onClick={() => {
                                setSelectedGroup(record)
                                setDelVisible(true)
                            }}
                        >删除连接器组</Dropdown.Item>
                    </Dropdown.Menu>
                }
            >
                <Button><IconMore className='align-v-center' /></Button>
            </Dropdown></div>;

           
        }
    }];

    const query = () => {
        setLoading(true);

        flylayerClient.listConnectorGroups({
            flynetId: flynetGeneral.id
        }).then(res => {
            if (!res.connectorGroups || res.connectorGroups.length < 0) {
                const msg = '查询连接器组异常，请稍后再试';
                console.error(msg);
                
            } else {
                setGroups(res.connectorGroups)
            }
        }, err => {
            console.error(err);
            
        }).finally(() => {
            setLoading(false);
        })
        
    }

    useEffect(() => {
        query();
    }, []);

    useEffect(() => {
        if (reloadFlag) {
            query();
            setReloadFlag(false);
        }
    }, [reloadFlag]);

    return {
        loading,
        groups,
        columns,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedGroup,
        setSelectedGroup,
        reloadFlag,
        setReloadFlag
    }

    
}

export default useTable;