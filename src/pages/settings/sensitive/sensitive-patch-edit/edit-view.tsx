import { FC, useState } from 'react'
import { Value, ListValue, Struct } from '@bufbuild/protobuf';

import { SensitivePatch } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import CodeEditor from '@/components/code-editor';

import { JSONPatch } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

const Index: FC<{
    sensitivePatch: SensitivePatch
    onChange: (sensitivePatch?: SensitivePatch) => void
}> = (props) => {

    const sensitivePatchToJsonStr = (patch?: SensitivePatch) => {
        
        let jsonStr = '[\n';
        if (patch && patch.patches) {
            patch.patches.forEach((item: JSONPatch, index) => {
                let value = item.value;
                let valueStr = "";
                if (index > 0) {
                    jsonStr += ',\n'
                }
                if (value) {
                    switch (value.kind.case) {
                        case 'stringValue':
                            valueStr = `, "value": "${value.kind.value}"`;
                            break;
                        case 'numberValue':
                            valueStr = `, "value": ${value.kind.value}`;
                            break;
                        case 'structValue':

                            valueStr = `, "value": ${JSON.stringify(value.kind.value)}`;
                            break;
                        case 'listValue':
                            valueStr = `, "value": ${JSON.stringify(value.kind.value)}`;
                            break;
                        default:
                            // valueStr = `, "value": ${JSON.stringify(value.kind.value)}`;
                            break;
                    }
                }

                jsonStr += `    { "op": "${item.op}", "path": "${item.path}"${valueStr} }`
            })
        }
        jsonStr += '\n]'

        return jsonStr;
    }

    const [editValue, setEditValue] = useState<string>(sensitivePatchToJsonStr(props.sensitivePatch));

    const parseObject = (value: any): Value => {

        if (typeof value === 'object') {
            if (Array.isArray(value)) {
                let values: Value[] = [];
                for (let i = 0; i < value.length; i++) {
                    values.push(parseObject(value[i]));
                }
                return new Value({
                    kind: {
                        value: new ListValue({
                            values: values
                        }), case: 'listValue'
                    }
                })
            } else {
                let valueStruct = new Struct({
                    fields: {}
                });

                for (let key in value) {
                    valueStruct.fields[key] = parseObject(value[key]);
                }

                return new Value({
                    kind: { value: valueStruct, case: 'structValue' }
                });
            }

        } else if (typeof value === 'string') {
            return new Value({
                kind: { value: value, case: 'stringValue' }
            })
        } else if (typeof value === 'number') {
            return new Value({
                kind: { value: value, case: 'numberValue' }
            })
        } else {
            return new Value({
                kind: { value: value, case: 'structValue' }
            })
        }
    }

    const jsonStrToSensitivePatch = (jsonStr: string) => {
        let patch = new SensitivePatch();
        let jsonPatches = JSON.parse(jsonStr);

        patch.patches = jsonPatches.map((item: any) => {
            let patch = new JSONPatch();
            patch.op = item.op;
            patch.path = item.path;
            patch.value = item.value ? parseObject(item.value) : undefined;

            return patch;
        });
        return patch;
    }


    return <>
        <div>
            <CodeEditor height={"369px"} language='json' value={editValue}
                onChange={(value) => {
                    setEditValue(value || '');
                    let sensitivePatch = jsonStrToSensitivePatch(value || '[]');

                    props.onChange(sensitivePatch);
                }}
            />
        </div>

    </>

    return <></>
}

export default Index;