import React, { useState, useContext, useEffect } from 'react'
import { Typography } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { getFlynet } from '@/services/flynet';
import { useLocale } from '@/locales';

import SensitivePatchEdit from './sensitive-patch-edit';
const { Title, Paragraph } = Typography;
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();


    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {
            setFlynet(res.flynet)

           
        }
    }

 

    useEffect(() => {
        queryFlynet()
    }, [])

    



    return <><div className='settings-page'>
        <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.sensitive.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.sensitive.description' })}</Paragraph>

        {flynet && <SensitivePatchEdit flynet={flynet} success={()=> queryFlynet()}/>}
        <div style={{height:40}}></div>
    </div></>
}

export default Index