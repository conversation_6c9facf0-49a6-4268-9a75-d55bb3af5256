import React from 'react'
import { Typography } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;
import RelayMapEdit from './relay-map-edit';
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    return <><div className='settings-page'>
        <Title heading={3} className='mb10'>{formatMessage({ id: 'settings.relay.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.relay.description' })}</Paragraph>
        <RelayMapEdit />
        <div style={{ height: 40 }}></div>
    </div>
    </>
}

export default Index;