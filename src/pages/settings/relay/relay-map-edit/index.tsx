import { FC, useState, useContext, useEffect } from 'react'
import { Notification } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import RelayMapManager from '@/components/relay-map-manager';
import { useLocale } from '@/locales';



const Index: FC<{}> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [loading, setLoading] = useState(false);

    const [dataLoaded, setDataLoaded] = useState(false);

    const [value, setValue] = useState<Uint8Array>();

    const handleSave = (value:Uint8Array) => {
        setLoading(true)

        flylayerClient.setRelayMap({
            flynetId: flynetGeneral.id,
            value: value
        }).then(() => {
            Notification.success({
                title: formatMessage({ id: 'settings.relay.saveSuccess' })
            })
        }).catch((e) => {
            Notification.error({
                title: formatMessage({ id: 'settings.relay.saveFailed' }),
                content: e.message
            })
        }).finally(() => {
            setLoading(false)
        });
    }



    useEffect(() => {
        flylayerClient.getRelayMap({
            flynetId: flynetGeneral.id
        }).then(res => {
            setValue(res.value);
            setDataLoaded(true);
        });
    }, [])


    return <>
        {dataLoaded && value && <RelayMapManager flynetId={flynetGeneral.id} value={value} onSave={handleSave}></RelayMapManager>}
    </>
}

export default Index