import React, { useContext, useState, useEffect } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { getFlynet } from '@/services/flynet';
import { useLocale } from '@/locales';

import { IconEdit, IconListView } from '@douyinfe/semi-icons'
import { Typography, Divider, Button, Skeleton, Tabs, TabPane, Space, Spin } from '@douyinfe/semi-ui';

import ApiAccessEdit from '@/pages/settings/api/api-access-edit';
const { Title, Paragraph } = Typography;
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();


    const [flynetLoading, setFlynetLoading] = useState(false)



    const queryFlynet = async () => {
        setFlynetLoading(true);

        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        }).catch((e) => {
            console.error(e)
        }).finally(() => {
            setFlynetLoading(false)
        })
    }

    useEffect(() => {
        queryFlynet()
    }, [])


    return <><div className='settings-page'>
        <Title heading={3} className='mb10'>{formatMessage({ id: 'settings.api.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.api.description' })}</Paragraph>

        <Skeleton loading={flynetLoading} placeholder={<>
            <Title heading={4} className='mb2'>{formatMessage({ id: 'settings.api.whitelist.title' })}</Title>
            <Paragraph className='mb20' type='tertiary'>
                {formatMessage({ id: 'settings.api.whitelist.description' })}
            </Paragraph>
            <Tabs type='card'>
                <TabPane tab={
                    <span>
                        <IconListView />
                        {formatMessage({ id: 'settings.api.editMode.list' })}
                    </span>
                } itemKey='list'>
                    <div style={{ paddingTop: 10, width: 420, height: 251, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Spin size='large' />
                    </div>
                </TabPane>
                <TabPane tab={
                    <span>
                        <IconEdit />
                        {formatMessage({ id: 'settings.api.editMode.text' })}
                    </span>
                } itemKey='edit'>

                </TabPane>
            </Tabs>
            <Space>
                <Button type="primary" theme='solid'>{formatMessage({ id: 'components.common.submit' })}</Button>
                <Button>{formatMessage({ id: 'components.common.reset' })}</Button>
            </Space>
        </>}>
            {flynet && <ApiAccessEdit
                success={() => queryFlynet()}
                whiteList={flynet.apiAccessControl ? flynet.apiAccessControl.whiteList : []} />}
        </Skeleton>

        <Divider className='mb40' style={{ marginTop: 20 }} />

        <div style={{ height: 40 }}></div>
    </div>
      
    </>
}

export default Index;