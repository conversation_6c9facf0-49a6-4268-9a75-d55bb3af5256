import { FC, useEffect, useState } from 'react'
import CodeEditor from '@/components/code-editor';


const Index: FC<{
    jsonEditorValue: string
    whiteList: string[],
    onValueChange: (whiteList: string[]) => void
    success?: () => void
}> = (props) => {

    const [jsonEditorValue, setJsonEditorValue] = useState<string>(props.jsonEditorValue);


    useEffect(() => {
        if (props.whiteList) {
            setJsonEditorValue(props.whiteList.join(',\n'))
        }
    }, [props.whiteList])

    return <>
        <CodeEditor height='259px'
            value={jsonEditorValue}
            language='text'

            onChange={(val) => {
                
                if (!val) {
                    return;
                }
                
                // val去除空格，制表符，换行符等
                let newVal = val.replace(/\s/g, '').replace(/\t/g, '').replace(/\n/g, '');
                const whiteList = newVal.split(',').map(item => item.trim())
                props.onValueChange(whiteList)

            }}></CodeEditor>
    </>
}

export default Index