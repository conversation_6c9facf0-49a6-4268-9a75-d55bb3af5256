import { FC, useEffect, useState } from 'react'
import { Typography, Row, Col, Button, Form, ArrayField, Popover, Space, Notification } from '@douyinfe/semi-ui';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { IconPlusCircle, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons'
import { isValidIPOrIpRangeOrCIDR } from '@/utils/validators';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;

const Index: FC<{
    whiteList: string[],
    jsonEditorValue: string
    success?: () => void,
    onValueChange: (whiteList: string[]) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, setFormApi] = useState<FormApi<{
        whiteList: Array<{ ip: string }>;
    }>>();

    useEffect(() => {
        
        if (formApi && props.jsonEditorValue) {
            formApi.setValue('whiteList', props.jsonEditorValue.split(',').map(item => ({ ip: item })))
        }
    },[props.jsonEditorValue])


    return <>

        <div style={{ width: 420 }}>
            <Form getFormApi={setFormApi} initValues={{
                whiteList: props.whiteList ? props.whiteList.map(item => ({ ip: item })) : []
            }} onChange={(formState) => {
                const values = formState.values
                if(!values.whiteList){
                    return;
                }
                const whiteList = values.whiteList.map((item: { ip: string }) => item.ip)
                props.onValueChange(whiteList)
            }}>
                {({  }) => (<>
                    <ArrayField field='whiteList'>
                        {({ add, arrayFields }) => (
                            <>
                                <Row className='tableTitle'>
                                    <Col span={20}><Space>IP&nbsp;
                                        <Popover content={<div className='p10'>
                                            <Paragraph>{formatMessage({ id: 'settings.api.ipFormat.title' })}</Paragraph>
                                            <Paragraph>{formatMessage({ id: 'settings.api.ipFormat.single' })}</Paragraph>
                                            <Paragraph>{formatMessage({ id: 'settings.api.ipFormat.cidr' })}</Paragraph>
                                            <Paragraph>{formatMessage({ id: 'settings.api.ipFormat.range' })}</Paragraph>

                                        </div>}><IconHelpCircle /></Popover>
                                    </Space>
                                    </Col>
                                    <Col span={4} className="btn-right-col">
                                        <Button type='primary' icon={<IconPlusCircle />} onClick={() => {
                                            add()
                                        }}></Button>
                                    </Col>
                                </Row>
                                <div style={{
                                    height: 200,
                                    overflowY: 'scroll',
                                }}>
                                    {arrayFields.map(({ field, remove }, i) => {
                                        return <Row key={i}>
                                            <Col span={20}>
                                                <Form.Input field={`${field}.ip`} noLabel validate={(val) => {
                                                    if (!val) {
                                                        return formatMessage({ id: 'settings.api.validation.ipRequired' })
                                                    }
                                                    return isValidIPOrIpRangeOrCIDR(val);

                                                }} placeholder={formatMessage({ id: 'settings.api.validation.ipPlaceholder' })} />
                                            </Col>
                                            <Col span={4} className="btn-right-col" style={{ paddingTop: 12, paddingRight: 4 }}>
                                                <Button type="danger" onClick={remove} icon={<IconMinusCircle />}></Button>
                                            </Col>
                                        </Row>
                                    })}
                                </div>
                            </>
                        )}
                    </ArrayField>
                </>)}
            </Form>
        </div>
    </>
}

export default Index