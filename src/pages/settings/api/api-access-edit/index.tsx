import { FC, useState, useContext } from 'react'
import { Typography, Button, Space, Tabs, TabPane, Notification } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { IconEdit, IconListView } from '@douyinfe/semi-icons'
import ListView from './list-view';
import EditView from './edit-view';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;

const Index: FC<{
    whiteList?: string[],
    success?: () => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [loading, setLoading] = useState(false);

    const [whiteList, setWhiteList] = useState<string[]>(props.whiteList || []);
    const [jsonEditorValue, setJsonEditorValue] = useState<string>(props.whiteList ? props.whiteList.join(',\n') : '');

    const handleSave = () => {
        setLoading(true)
        flylayerClient.setAPIAccessControl({
            flynetId: flynetGeneral.id,
            apiAccessControl: {
                whiteList
            }
        }).then(() => {
            Notification.success({
                title: formatMessage({ id: 'settings.api.notification.success' })
            })
            props.success?.()
        }).catch(() => {
            Notification.error({
                title: formatMessage({ id: 'settings.api.notification.failed' })
            })
        }).finally(() => {
            setLoading(false)
        });
    }

    return <>
        <Title heading={4} className='mb2'>{formatMessage({ id: 'settings.api.whitelist.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>
            {formatMessage({ id: 'settings.api.whitelist.description' })}
        </Paragraph>
        <Tabs type='card'>
            <TabPane tab={
                <span>
                    <IconListView />
                    {formatMessage({ id: 'settings.api.editMode.list' })}
                </span>
            } itemKey='list'>
                <div style={{ paddingTop: 10 }}>
                    <ListView whiteList={whiteList} jsonEditorValue={jsonEditorValue} onValueChange={(val) => {
                        
                        setWhiteList(val)
                    }} success={props.success} />
                </div>
            </TabPane>
            <TabPane tab={
                <span>
                    <IconEdit />
                    {formatMessage({ id: 'settings.api.editMode.text' })}
                </span>
            } itemKey='edit'>
                <EditView jsonEditorValue={jsonEditorValue} whiteList={whiteList} onValueChange={(val) => {
                    setJsonEditorValue(val.join(','))
                }} success={props.success} />
            </TabPane>
        </Tabs>
        <Space>
            <Button type="primary" loading={loading} onClick={handleSave} disabled={!whiteList || whiteList.length == 0} theme='solid'>{formatMessage({ id: 'components.common.submit' })}</Button>
            <Button onClick={() => {
                setWhiteList(props.whiteList || [])
                setJsonEditorValue(props.whiteList ? props.whiteList.join(',') : '')
            }}>{formatMessage({ id: 'components.common.reset' })}</Button>
        </Space>
    </>
}

export default Index
