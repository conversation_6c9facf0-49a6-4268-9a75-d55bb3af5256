import React, { useContext, useState, useEffect } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';

import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { setApplicationPanelEnabled, getFlynet } from '@/services/flynet';
import { VITE_LOCAL_PAGER_AND_FILTER } from '@/utils/service';

import { IconEdit, IconTick, IconUndo, IconHelpCircle } from '@douyinfe/semi-icons'
import { Typography, Row, Col, Button, Space, Input, Tooltip, Notification } from '@douyinfe/semi-ui';
import Renew from '@/components/renew';
import { flylayerClient } from '@/services/core';

const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const globalConfig = useContext(GlobalConfigContext);
    const [flynet, setFlynet] = useState<Flynet>();

    // 邀请码对话框是否显示
    const [inviteCodeModalVisible, setInviteCodeModalVisible] = useState(false)

    const [applicationLoading, setApplicationLoading] = useState(false);
    const [applicationEnabled, setApplicationEnabled] = useState(false);

    const [flynetLoading, setFlynetLoading] = useState(false)

    // 显示续费按钮
    const [showRenewButton, setShowRenewButton] = useState(false)

    const [isEditFlynetAlias, setIsEditFlynetAlias] = useState(false)
    const [flynetAlias, setFlynetAlias] = useState(flynetGeneral.alias)
    const [editAlias, setEditAlias] = useState(flynetGeneral.alias)
    const [editAliasLoading, setEditAliasLoading] = useState(false)
    const handleSaveAlias = () => {

        setIsEditFlynetAlias(false)
        setEditAliasLoading(true)
        flylayerClient.updateFlynetAlias({
            flynetId: flynetGeneral.id,
            alias: editAlias
        }).then(() => {
            setFlynetAlias(editAlias)
            Notification.success({
                title: formatMessage({ id: 'common.updateSuccess' })
            });
        }).catch((e) => {
            console.error(e)
            Notification.error({
                title: formatMessage({ id: 'common.updateFailed' }),
                content: e.message
            });
        }).finally(() => {
            setEditAliasLoading(false)
        })
    }
    const handleUndoSaveAlias = () => {
        setIsEditFlynetAlias(false)
        setEditAlias(flynetAlias)
    }

    const queryFlynet = async () => {
        setFlynetLoading(true);

        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
            setApplicationEnabled(res.flynet.applicationEnabled)
        }).catch((e) => {
            console.error(e)
        }).finally(() => {
            setFlynetLoading(false)
        })
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    useEffect(() => {
        if (!flynetGeneral.disabledExpiry && flynetGeneral.expiresAt) {
            if (!flynetGeneral.expired) {
                const expiry = flynetGeneral.expiresAt.toDate()
                const now = new Date()
                const expireTimes = expiry.getTime() - now.getTime()
                if (expireTimes < 1000 * 60 * 60 * 24 * 30) {
                    setShowRenewButton(true)
                }
            }
        }
    }, [flynetGeneral.disabledExpiry])

    const handleApplicationEnabledChange = (checked: boolean) => {

        if (!flynet) {
            return;
        }

        setApplicationLoading(true)
        setApplicationPanelEnabled(flynet?.id, checked).then(() => {
            setFlynet(Object.assign({}, flynet, { applicationEnabled: checked }))
            setApplicationLoading(false)
            setApplicationEnabled(checked)
        })
    }


    return <><div className='settings-page'>
        <Title heading={3} className='mb10'>{formatMessage({ id: 'settings.flynet.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.flynet.description' })}</Paragraph>

        <Title heading={4} className="mb2">{formatMessage({ id: 'settings.flynet.basicInfo.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.flynet.basicInfo.description' })}</Paragraph>

        <div className={styles.workspaceForm}>
            
            <Row>
                <Col span={8}><Text>{formatMessage({ id: 'settings.flynet.networkName' })}</Text></Col>
                <Col span={16}>
                    <Text type='tertiary'>
                        {flynetGeneral.name}
                    </Text>
                </Col>
            </Row>
            <Row>
                <Col span={8}><Text>{formatMessage({ id: 'settings.flynet.networkId' })}</Text></Col>
                <Col span={16}>
                    <Text type='tertiary' copyable>
                        {flynetGeneral.id + ''}
                    </Text>
                </Col>
            </Row>
            <Row>
                <Col span={8}><Text>{formatMessage({ id: 'settings.flynet.page.networkName' })}</Text></Col>
                <Col span={16}>
                    <Space>
                        {isEditFlynetAlias ? <>
                            <Input value={editAlias} onChange={(e) => {
                                setEditAlias(e)
                            }}></Input>
                            <Tooltip content={formatMessage({ id: 'settings.flynet.page.cancelEdit' })}>
                                <Button icon={<IconUndo></IconUndo>}
                                    onClick={handleUndoSaveAlias}
                                ></Button>
                            </Tooltip>
                            <Tooltip content={formatMessage({ id: 'settings.flynet.page.saveEdit' })}>
                                <Button theme='solid'
                                    loading={editAliasLoading}
                                    onClick={handleSaveAlias}
                                    icon={<IconTick></IconTick>}></Button>
                            </Tooltip>
                        </> : <>
                            <Text type='tertiary'>
                                {flynetAlias}
                            </Text>
                            <Tooltip content={formatMessage({ id: 'components.common.edit' })}>
                                <Button icon={<IconEdit />} onClick={() => {
                                    setIsEditFlynetAlias(true)
                                }}></Button>
                            </Tooltip>
                        </>}
                    </Space>
                </Col>
            </Row>
            <Row>
                <Col span={8}><Text>{formatMessage({ id: 'settings.flynet.page.zeroTrustSegment' })}</Text></Col>
                <Col span={16}>
                    <Space>
                        {globalConfig.natRanges && globalConfig.natRanges.length > 0 ? globalConfig.natRanges.map((item, index) => {
                            return <Text key={index} type='tertiary'>{item}</Text>
                        }) :
                        <Text type='tertiary'> **********/10</Text>}
                        <Tooltip content={formatMessage({ id: 'settings.flynet.page.contactServiceProvider' })}>
                            <IconHelpCircle></IconHelpCircle>
                        </Tooltip></Space>
                </Col>
            </Row>

        </div>

        <div style={{ height: 40 }}></div>
    </div>
        {/**邀请码弹出框 */}
        {inviteCodeModalVisible && <Renew flynetGeneral={flynetGeneral} close={() => { setInviteCodeModalVisible(false) }} success={() => {
            setInviteCodeModalVisible(false)
            window.location.reload();
        }} />}
    </>
}

export default Index;