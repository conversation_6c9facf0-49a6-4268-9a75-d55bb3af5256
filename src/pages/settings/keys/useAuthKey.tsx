import { useEffect, useState, useContext } from "react";
import { Button,Notification } from '@douyinfe/semi-ui';
import { Timestamp } from "@bufbuild/protobuf";
import { useLocale } from '@/locales';

import { AuthKey } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/auth_keys_pb";
import { flylayerClient } from '@/services/core';
import DateFormat from "@/components/date-format";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'

const usePreAuthKey = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    // 正常的授权密钥列表
    const columns = [
        {
            title: 'ID',
            dataIndex: 'key',
            width: 200,
            render: (field: bigint) => {
                return <>{field + ''}</>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.createdAt' }),
            dataIndex: 'createdAt',
            width: 250,
            render: (field: Timestamp) => {
                return <><DateFormat date={field}></DateFormat></>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.expiration' }),
            dataIndex: 'expiresAt',
            width: 250,
            render: (field: Timestamp) => {
                return <><DateFormat date={field}></DateFormat></>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.config' }),
            width: 300,           
            render: (field: string, record: AuthKey) => {
                let configs: Array<string> = []
                if (record.reusable) {
                    configs.push(formatMessage({ id: 'settings.keys.config.reusable' }))
                } else {

                    configs.push(formatMessage({ id: 'settings.keys.config.oneTime' }))
                }
                if (record.ephemeral) {
                    configs.push(formatMessage({ id: 'settings.keys.config.ephemeral' }))
                } else {
                    configs.push(formatMessage({ id: 'settings.keys.config.persistent' }))
                }
                if (record.preAuthorized) {
                    configs.push(formatMessage({ id: 'settings.keys.config.preAuthorized' }))
                } else {
                    configs.push(formatMessage({ id: 'settings.keys.config.notPreAuthorized' }))
                }
                return <>{configs.join(',')}</>
            }
        },
        {
            title: '',
            render: (field: string, record: AuthKey) => {
                return <div className='table-last-col'><Button type="danger" onClick={() => {
                    setCurAuthKey(record)
                    setDelVisiable(true)
                }}>{formatMessage({ id: 'settings.keys.action.revoke' })}</Button>
                </div>
            }
        }
    ];
    // 无效的授权密钥列表
    const columnsInvalid = [
        {
            title: 'ID',
            dataIndex: 'key',
            width: 200,
            render: (field: bigint) => {
                
                
                return <>{field + ''}</>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.createdAt' }),
            dataIndex: 'createdAt',
            width: 250,
            render: (field: Timestamp) => {
                return <><DateFormat date={field}></DateFormat></>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.expiration' }),
            dataIndex: 'expiresAt',
            width: 250,
            render: (field: Timestamp, record: AuthKey) => {
                return <><DateFormat date={field}></DateFormat>{record.revoked ? `(${formatMessage({ id: 'settings.keys.status.revoked' })})`: ''}</>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.config' }),
            width: 300,
            render: (field: string, record: AuthKey) => {
                let configs: Array<string> = []
                if (record.reusable) {
                    configs.push(formatMessage({ id: 'settings.keys.config.reusable' }))
                } else {
                    configs.push(formatMessage({ id: 'settings.keys.config.oneTime' }))
                }
                if (record.ephemeral) {
                    configs.push(formatMessage({ id: 'settings.keys.config.ephemeral' }))
                } else {
                    configs.push(formatMessage({ id: 'settings.keys.config.persistent' }))
                }
                if (record.preAuthorized) {
                    configs.push(formatMessage({ id: 'settings.keys.config.preAuthorized' }))
                } else {
                    configs.push(formatMessage({ id: 'settings.keys.config.notPreAuthorized' }))
                }
                return <>{configs.join(',')}</>
            }
        },
        {
            title: '',
            render: (field: string, record: AuthKey) => {
                return <></>
            }
        }
    ];
    
    // 列表数据是否正在加载中
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<Array<AuthKey>>();
    const [dataInvalid, setDataInvalid] = useState<Array<AuthKey>>()
    // 当前菜单选中设备
    const [curAuthKey, setCurAuthKey] = useState<AuthKey>();
    // 删除用户弹出框是否可见
    const [delVisiable, setDelVisiable] = useState(false);
        
    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    const expandRowRender = (record: AuthKey | undefined) => {

        return <>{formatMessage({ id: 'settings.keys.field.description' })}：{record?.description}</>;
    };
    const query = () => {
        
        flylayerClient.listAuthKeys({
            flynetId: flynetGeneral.id
        }).then(res => {
            const list:Array<AuthKey> = [];
            const listInvalid:Array<AuthKey> = [];



            res.authKeys.forEach((item: AuthKey) => {
                if(item.revoked){
                    listInvalid.push(item)
                }else{
                    list.push(item)
                }
            })

            // list根据createdAt 降序排序
            list.sort((a, b)=>{
                if(a.createdAt && b.createdAt){
                    let diff = b.createdAt.seconds - a.createdAt.seconds;
                    return diff > 0 ? 1 : -1;
                }
                return 0;
            })


            // list根据createdAt 降序排序
            listInvalid.sort((a, b)=>{
                if(a.createdAt && b.createdAt){
                    let diff = b.createdAt.seconds - a.createdAt.seconds;
                    return diff > 0 ? 1 : -1;
                }
                return 0;
            })


            setData(list)
            setDataInvalid(listInvalid)
        }, err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'settings.keys.error.fetchFailed' }) })
        }).finally(() => setLoading(false))
    }

    useEffect(() => {
        query()
    }, [])

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])
    return { columns, columnsInvalid, loading, data, dataInvalid, reloadFlag, setReloadFlag, curAuthKey, setCurAuthKey, delVisiable, setDelVisiable, expandRowRender}
}

export default usePreAuthKey;