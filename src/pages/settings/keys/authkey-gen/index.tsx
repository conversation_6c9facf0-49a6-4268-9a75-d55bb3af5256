import React, { useState, useEffect, useContext, useCallback } from 'react'
import { Typography, Modal, Form, Row, Col, Notification, Divider } from '@douyinfe/semi-ui';
import { Timestamp, Duration } from "@bufbuild/protobuf";
import { useLocale } from '@/locales';

import useUserProfile, { UserProfileContext } from '@/hooks/useUserProfile';
const { Title, Paragraph, Text } = Typography;
const { Switch, InputNumber, Select, Section, Input } = Form
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AuthKey, CreateAuthKeyRequest } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/auth_keys_pb";
import { flylayerClient } from '@/services/core';
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'

interface Props {
    close: () => void,
    success?: (authKey: AuthKey, value: string) => void
}


const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const userProfile = useContext(UserProfileContext)
    const [defaultKeyMinExpireDays, setDefaultKeyMinExpireDays] = useState(1);
    const [defaultKeyMaxExpireDays, setDefaultKeyMaxExpireDays] = useState(90);

    const [formApi, SetFormApi] = useState<FormApi>()

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const [tags, setTags] = useState<Array<string>>([]);


    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {

            if (res.constraint) {
                setDefaultKeyMinExpireDays(res.constraint.defaultKeyMinExpireDays);
                setDefaultKeyMaxExpireDays(res.constraint.defaultKeyMaxExpireDays);
                formApi?.setValue("expiry", res.constraint.defaultKeyMaxExpireDays)
            }
        }
    }

    // 获取访问控制策略
    const getACLPolicy = () => {
        
        flylayerClient.getACLPolicy({
            flynetId: flynetGeneral.id
        }).then(res => {
            if (res.policy) {
                const tags: string[] = [];
                const userMail = userProfile?.identity?.traits?.email;
                const aclPolicy = res.policy;
                if(res.policy && res.policy.tagowners ){
                    Object.keys(aclPolicy.tagowners).forEach(key => {
                        aclPolicy.tagowners[key].values.map((item) => {
                            const itemVal = item.kind.value + ''
                            if (itemVal == userMail) {
                                tags.push(key)
                            }
                        })
                    })
                }
                setTags(tags);

            }

        }).catch(err => {
            console.error(err);

            Notification.error({ content: formatMessage({ id: 'settings.keys.error.fetchAclFailed' }), position: "bottomRight" })
        })
    }

    useEffect(() => {
        getACLPolicy();
    }, [])
    const handleOk = () => {
        const values = formApi?.getValues();
        if(values.useTags && (!values.tags || values.tags.length == 0) ){
            Notification.error({ content: formatMessage({ id: 'settings.keys.error.selectTags' }), position: "bottomRight" })
            return;
        }
        setLoading(true)
        // if(true) {
        //     console.log(values);
        //     setLoading(false)
        //     return;
        // }
        
        let request = new CreateAuthKeyRequest({
            flynetId: flynetGeneral.id,
            preAuthorized: values.preAuthorized,
            ephemeral: values.ephemeral,
            reusable: values.reusable,
            tags: values.tags,
            description: values.description,
        });
        if (values.useExpiration) {

            request.expiry = new Duration({ seconds: BigInt(values.expiry * 24 * 60 * 60), nanos: 0 })
        }

        if (values) {
            flylayerClient.createAuthKey(request).then((res) => {
                if (props.success && res.authKey) {
                    Notification.success({ content: formatMessage({ id: 'settings.keys.createTokenSuccess' }), position: "bottomRight" })
                    props.success(res.authKey, res.value);
                }
            }, (err) => {
                console.error(err);
                
                Notification.error({ content: formatMessage({ id: 'settings.keys.createTokenFailed' }), position: "bottomRight" })

            }).finally(() => setLoading(false))
        }
    };
    const handleCancel = () => {
        props.close();
    };

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'settings.keys.generateAuthKey' })}
            visible={true}
            okButtonProps={{ loading }}
            onOk={handleOk}
            // afterClose={handleAfterClose} //>=1.16.0
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
        >
            <Form getFormApi={SetFormApi} initValues={{useExpiration: true, expiry:defaultKeyMaxExpireDays}} render={({ formState }) => (<>
                <Row>
                    <Col span={24}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.description' })}</Title></Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Input field='description' noLabel></Input>
                    </Col>
                </Row>
                
                <Row>
                    <Col span={6}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.reusable' })}</Title></Col>
                    <Col span={18}><div className={styles.colFormItem}><Switch
                        field='reusable'
                        noLabel />
                    </div></Col>
                </Row>
                <Row style={{ marginBottom: 20 }}>
                    <Col><Text type="tertiary">{formatMessage({ id: 'settings.keys.field.reusableDesc' })}</Text></Col>
                </Row>
                <Row>
                    <Col span={6}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.validity' })}</Title></Col>
                    <Col span={18}><div className={styles.colFormItem}>
                        {/* <Switch
                            field='useExpiration'

                            noLabel /> */}
                    </div></Col>
                </Row>
                <Row style={{ marginBottom: 10 }}>
                    <Col>
                        <Text type="tertiary">{formatMessage({ id: 'settings.keys.field.expiryDays' })}</Text></Col>
                </Row>
                {formState.values.useExpiration ? (
                    <Row style={{ marginBottom: 20 }}>
                        <Col><div className={styles.singleFormItem}>
                            <InputNumber
                                initValue={defaultKeyMaxExpireDays}
                                min={defaultKeyMinExpireDays}
                                max={defaultKeyMaxExpireDays}
                                style={{ width: 90, marginRight: 10 }}
                                field='expiry' noLabel></InputNumber><Text type="tertiary">{formatMessage({ id: 'settings.keys.field.expiryRange' }).replace('{min}', String(defaultKeyMinExpireDays)).replace('{max}', String(defaultKeyMaxExpireDays))}</Text></div></Col>

                    </Row>
                ) : null}

                <Section text={formatMessage({ id: 'settings.keys.section.deviceSettings' })}>
                    <Row style={{ marginBottom: 20 }}>
                        <Col>
                            <Text type="tertiary">{formatMessage({ id: 'settings.keys.section.deviceSettingsDesc' })}</Text></Col>
                    </Row>

                    <Row>
                        <Col span={6}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.ephemeral' })}</Title></Col>
                        <Col span={18}><div className={styles.colFormItem}><Switch
                            field='ephemeral'
                            noLabel />
                        </div></Col>
                    </Row>
                    <Row style={{ marginBottom: 20 }}>
                        <Col><Text type="tertiary">{formatMessage({ id: 'settings.keys.field.ephemeralDesc' })}</Text></Col>
                    </Row>
                    <Row>
                        <Col span={6}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.preAuthorized' })}</Title></Col>
                        <Col span={18}><div className={styles.colFormItem}><Switch
                            field='preAuthorized'
                            noLabel />
                        </div></Col>
                    </Row>
                    <Row style={{ marginBottom: 20 }}>
                        <Col><Text type="tertiary">{formatMessage({ id: 'settings.keys.field.preAuthorizedDesc' })}</Text></Col>
                    </Row>

                    <Row>
                        <Col span={6}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.tags' })}</Title></Col>
                        <Col span={18}><div className={styles.colFormItem}><Switch
                            field='useTags'
                            noLabel />
                        </div></Col>
                    </Row>
                    <Row style={{ marginBottom: 10 }}>
                        <Col><Text type="tertiary">{formatMessage({ id: 'settings.keys.field.tagsDesc' })}</Text></Col>
                    </Row>

                    {formState.values.useTags ? (
                        <Row>
                            <Col><div className={styles.singleFormItem}><Select
                                field="tags"
                                multiple
                                placeholder={formatMessage({ id: 'settings.keys.field.tagsPlaceholder' })}
                                noLabel
                            >
                            {tags.map((item, index) => {
                                return <Select.Option key={index} value={item}>{item}</Select.Option>
                            })}
                            </Select></div></Col>
                        </Row>
                    ) : null}

                </Section></>)}>
            </Form>
        </Modal>
    </>
}
export default Index