import React from 'react'
import { Divider, Typography } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;
import KeyExpire from './keyExpire';
import AuthKey from './authKey';
import ApiKey from './apiKey';
import { VITE_USE_KEY } from '@/utils/service';
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    return <><div className='settings-page'>
        <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.keys.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.keys.description' })}</Paragraph>
        <KeyExpire></KeyExpire>
        <div className='mb40'></div>
        {VITE_USE_KEY && <>
            <Divider className='mb40'></Divider>
            <AuthKey></AuthKey>
            <div className='mb40'></div>
            <Divider className='mb40'></Divider>
            <ApiKey></ApiKey>
        </>}

    </div>

    </>
}
export default Index