import { FC, useState } from 'react'
import { <PERSON>po<PERSON>, Modal, Banner, Row, Col, Button, Divider } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

import { ApiKey } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/api_keys_pb";
import styles from './index.module.scss';
import { formatDisplayTimestamp } from '@/utils/format';

const { Title, Paragraph, Text } = Typography;


interface Props {
    keyValue: string
    ApiKey: ApiKey
    close: () => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const expire = formatDisplayTimestamp(props.ApiKey.expiresAt);
    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'settings.keys.result.title' })}
            visible={true}
            okButtonProps={{ loading }}
            onOk={() => props.close()}
            onCancel={() => props.close()}
            closeOnEsc={true}
            hasCancel={false}
            maskClosable={false}
        >
            <Paragraph type='danger' className='mb20'>{formatMessage({ id: 'settings.keys.result.warning' })}</Paragraph>
            <Paragraph className='mb20 copyable-code' copyable>{props.keyValue}</Paragraph>
            {props.ApiKey.expiresAt ? <Banner
            type="info"
            closeIcon={null}
            description={formatMessage({ id: 'settings.keys.result.expireInfo' }).replace('{expire}', expire)}
        /> : ''}
        </Modal>
    </>
}

export default Index;