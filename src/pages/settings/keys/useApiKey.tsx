import { useEffect, useState, useContext } from "react";
import { Button,Notification } from '@douyinfe/semi-ui';
import { Timestamp } from "@bufbuild/protobuf";
import { useLocale } from '@/locales';

import { ApiKey } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/api_keys_pb";
import { flylayerClient } from '@/services/core';
import DateFormat from "@/components/date-format";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'

const usePreAuthKey = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    // 正常的授权密钥列表
    const columns = [
        {
            title: 'ID',
            dataIndex: 'key',
            width: 200,
            render: (field: bigint) => {
                return <>{field + ''}</>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.createdAt' }),
            dataIndex: 'createdAt',
            width: 250,
            render: (field: Timestamp) => {
                return <><DateFormat date={field}></DateFormat></>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.expiration' }),
            dataIndex: 'expiresAt',
            width: 250,
            render: (field: Timestamp) => {
                return <><DateFormat date={field}></DateFormat></>
            }
        },
        
        {
            title: '',
            render: (field: string, record: ApiKey) => {
                return <div className='table-last-col'><Button type="danger" onClick={() => {
                    setCurAuthKey(record)
                    setDelVisiable(true)
                }}>{formatMessage({ id: 'settings.keys.action.revoke' })}</Button>
                </div>
            }
        }
    ];
    // 无效的授权密钥列表
    const columnsInvalid = [
        {
            title: 'ID',
            dataIndex: 'key',
            width: 200,
            render: (field: bigint) => {
                
                
                return <>{field + ''}</>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.createdAt' }),
            dataIndex: 'createdAt',
            width: 250,
            render: (field: Timestamp) => {
                return <><DateFormat date={field}></DateFormat></>
            }
        },
        {
            title: formatMessage({ id: 'settings.keys.column.expiration' }),
            dataIndex: 'expiresAt',
            width: 250,
            render: (field: Timestamp, record: ApiKey) => {
                return <><DateFormat date={field}></DateFormat>{record.revoked ? `(${formatMessage({ id: 'settings.keys.status.revoked' })})`: ''}</>
            }
        },
        
        {
            title: '',
            render: (field: string, record: ApiKey) => {
                return <></>
            }
        }
    ];
    
    // 列表数据是否正在加载中
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<Array<ApiKey>>();
    const [dataInvalid, setDataInvalid] = useState<Array<ApiKey>>()
    // 当前菜单选中设备
    const [curAuthKey, setCurAuthKey] = useState<ApiKey>();
    // 删除用户弹出框是否可见
    const [delVisiable, setDelVisiable] = useState(false);
        
    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    const expandRowRender = (record: ApiKey | undefined) => {

        return <>{formatMessage({ id: 'settings.keys.field.description' })}：{record?.description}</>;
    };
    const query = () => {
        
        flylayerClient.listApiKeys({
            flynetId: flynetGeneral.id
        }).then(res => {
            const list:Array<ApiKey> = [];
            const listInvalid:Array<ApiKey> = [];
            res.apiKeys.forEach((item: ApiKey) => {
                if(item.revoked){
                    listInvalid.push(item)
                }else{
                    list.push(item)
                }
            })

            // list根据createdAt 降序排序
            list.sort((a, b)=>{
                if(a.createdAt && b.createdAt){
                    let diff = b.createdAt.seconds - a.createdAt.seconds;
                    return diff > 0 ? 1 : -1;
                }
                return 0;
            })


            // list根据createdAt 降序排序
            listInvalid.sort((a, b)=>{
                if(a.createdAt && b.createdAt){
                    let diff = b.createdAt.seconds - a.createdAt.seconds;
                    return diff > 0 ? 1 : -1;
                }
                return 0;
            })
            
            setData(list)
            setDataInvalid(listInvalid)
        }, err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'settings.keys.error.fetchFailed' }) })
        }).finally(() => setLoading(false))
    }

    useEffect(() => {
        query()
    }, [])

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])
    return { columns, columnsInvalid, loading, data, dataInvalid, reloadFlag, setReloadFlag, curAuthKey, setCurAuthKey, delVisiable, setDelVisiable, expandRowRender}
}

export default usePreAuthKey;