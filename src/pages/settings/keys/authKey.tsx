import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Collapsible } from '@douyinfe/semi-ui';
import { IconChevronRight, IconChevronDown } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';

import AuthkeyGen from '@/pages/settings/keys/authkey-gen';
import AuthResult from '@/pages/settings/keys/authkey-result';

import AuthkeyRevoke from '@/pages/settings/keys/authkey-revoke';
import styles from './index.module.scss';
import usePreAuthKey from './useAuthKey';
const { Title, Paragraph, Text } = Typography;

import { AuthKey } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/auth_keys_pb";
import { flylayerClient } from '@/services/core';

import TableEmpty from '@/components/table-empty';

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const { columns, columnsInvalid, loading, data, dataInvalid, reloadFlag, setReloadFlag, curAuthKey, setCurAuthKey, delVisiable, setDelVisiable, expandRowRender } = usePreAuthKey();

    const [invalidKeyShow, setInvalidKeyShow] = useState(false);
    // 生成密钥对话框是否展示
    const [authKeyGenVisiable, setAuthKeyGenVisiable] = useState(false);
    // 生成密钥结果对话框是否展示
    const [genResultVisiable, setGenResultVisiable] = useState(false);
    // 生成结果key
    const [genResultKeyValue, setGenResultKeyValue] = useState('');

    const [authKey, setAuthKey] = useState<AuthKey>();

    // 认证密码创建成功后回调
    const handleKeyGenSuccess = (authKey: AuthKey, keyValue: string) => {
        setAuthKeyGenVisiable(false);
        setGenResultVisiable(true)
        setAuthKey(authKey);
        setGenResultKeyValue(keyValue)

    }


    return <>

        <Row className='mb10'>
            <Col span={20}>
                <div><Title heading={4} className="mb2">{formatMessage({ id: 'settings.keys.authKey.title' })}</Title>
                </div></Col>
            <Col span={4}><div className='btn-right-col'>
                <Button theme='solid' onClick={() => setAuthKeyGenVisiable(true)}>{formatMessage({ id: 'settings.keys.authKey.generateButton' })}</Button>
            </div></Col>
        </Row>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.keys.authKey.description' })}</Paragraph>
        

        <Table
            rowKey={(record?: AuthKey) => record ? record.id + '' : ''}
            className='mb20'
            expandRowByClick={true}
            expandedRowRender={expandRowRender}
            rowExpandable={record => {
                if (record && record.description && record.description.trim() != '') {
                    return true;
                }
                return false;
            }}
            empty={<TableEmpty loading={loading} />}
            loading={loading}
            columns={columns}
            virtualized
            scroll={{ y: 300 }}
            dataSource={data} pagination={false} />
        {dataInvalid && dataInvalid?.length > 0 ? <>
            <p className={styles.toggleCollapsible} onClick={() => setInvalidKeyShow(!invalidKeyShow)}>{invalidKeyShow ? <IconChevronDown /> : <IconChevronRight />}<span>&nbsp;{formatMessage({ id: 'settings.keys.authKey.invalidKeysCount' }).replace('{count}', String(dataInvalid?.length))}</span></p>

            <Collapsible isOpen={invalidKeyShow}>
                <Table
                    columns={columnsInvalid}
                    dataSource={dataInvalid}
                    virtualized
                    scroll={{ y: 300 }}
                    expandRowByClick={true}
                    expandedRowRender={expandRowRender}
                    rowExpandable={record => {
                        if (record && record.description && record.description.trim() != '') {
                            return true;
                        }
                        return false;
                    }}
                    pagination={false} />
            </Collapsible>
        </> : undefined}


        {authKeyGenVisiable ? <AuthkeyGen success={handleKeyGenSuccess} close={() => setAuthKeyGenVisiable(false)}></AuthkeyGen> : ''}
        {genResultVisiable && authKey ? <AuthResult close={() => {
            setGenResultVisiable(false)
            setAuthKey(undefined)
            setReloadFlag(true)
        }}

            keyValue={genResultKeyValue} authKey={authKey}></AuthResult> : undefined}
        {delVisiable && curAuthKey ? <AuthkeyRevoke
            record={curAuthKey}
            close={() => {
                setDelVisiable(false)
                setCurAuthKey(undefined)
            }}
            success={() => {

                setDelVisiable(false)
                setCurAuthKey(undefined)
                setReloadFlag(true)
            }}
        ></AuthkeyRevoke> : ''}
    </>
}
export default Index