import { FC, useState } from 'react'
import { Typography, Modal, Notification, Tabs, TabPane } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';

import { AuthKey } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/auth_keys_pb";
import styles from './index.module.scss';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: AuthKey
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    return <>
        <Modal
            width={400}
            title={formatMessage({ id: 'settings.keys.revoke.title' })}
            visible={true}
            okButtonProps={{ loading, type: 'danger' }}
            onOk={() => {
                setLoading(true)
                flylayerClient.deleteAuthKey({
                    authKeyId: props.record.id
                }).then(() => {
                    Notification.success({content: formatMessage({ id: 'settings.keys.revoke.success' }),position: "bottomRight"})
                    if (props.success) {
                        props.success();
                    }
                }).catch((_err) => {
                    Notification.error({content: formatMessage({ id: 'settings.keys.revoke.failed' }),position: "bottomRight"})
                }).finally(() => setLoading(false))
            }}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph>{formatMessage({ id: 'settings.keys.revoke.authDescription' })}</Paragraph>
        </Modal></>
}
export default Index;
