import React, { useState, useEffect, useContext, useCallback } from 'react'
import { Typography, Modal, Form, Row, Col, Notification, Divider } from '@douyinfe/semi-ui';
import { Timestamp, Duration } from "@bufbuild/protobuf";

import useUserProfile, { UserProfileContext } from '@/hooks/useUserProfile';
const { Title, Paragraph, Text } = Typography;
const { Switch, InputNumber, Input } = Form
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { ApiKey, CreateApiKeyRequest } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/api_keys_pb";
import { flylayerClient } from '@/services/core';
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

interface Props {
    close: () => void,
    success?: (authKey: ApiKey, value: string) => void
}


const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [defaultKeyMinExpireDays, setDefaultKeyMinExpireDays] = useState(1);
    const [defaultKeyMaxExpireDays, setDefaultKeyMaxExpireDays] = useState(90);

    const [formApi, SetFormApi] = useState<FormApi>()

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);




    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {

            if (res.constraint) {
                setDefaultKeyMinExpireDays(res.constraint.defaultKeyMinExpireDays);
                setDefaultKeyMaxExpireDays(res.constraint.defaultKeyMaxExpireDays);
                formApi?.setValue("expiry", res.constraint.defaultKeyMaxExpireDays)
            }
        }
    }


    const handleOk = () => {
        setLoading(true)
        const values = formApi?.getValues();
        // if(true) {
        //     console.log(values);
        //     setLoading(false)
        //     return;
        // }
        let request = new CreateApiKeyRequest({
            flynetId: flynetGeneral.id,
            description: values.description,
            expiryDisabled: !values.useExpiration
        });
        if (values.useExpiration) {
            
            request.expiry = new Duration({ seconds: BigInt(values.expiry * 24 * 60 * 60), nanos: 0 })
        }

        if (values) {
            flylayerClient.createApiKey(request).then((res) => {
                if (props.success && res.apiKey) {
                    Notification.success({ content: formatMessage({ id: 'settings.keys.createTokenSuccess' }), position: "bottomRight" })
                    props.success(res.apiKey, res.value);
                }
            }, (err) => {
                console.error(err);
                
                Notification.error({ content: formatMessage({ id: 'settings.keys.createTokenFailed' }), position: "bottomRight" })

            }).finally(() => setLoading(false))
        }
    };
    const handleCancel = () => {
        props.close();
    };

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'settings.keys.generateApiToken' })}
            visible={true}
            okButtonProps={{ loading }}
            onOk={handleOk}
            // afterClose={handleAfterClose} //>=1.16.0
            onCancel={handleCancel}
            maskClosable={false}
            closeOnEsc={true}
        >
            <Form getFormApi={SetFormApi} initValues={{useExpiration: true, expiry:defaultKeyMaxExpireDays}} render={({ formState }) => (<>
                <Row>
                    <Col span={24}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.description' })}</Title></Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Input field='description' noLabel></Input>
                    </Col>
                </Row>            
                <Row>
                    <Col span={6}><Title heading={6}>{formatMessage({ id: 'settings.keys.field.validity' })}</Title></Col>
                    <Col span={18}><div className={styles.colFormItem}>
                        <Switch
                            field='useExpiration'
                            noLabel />
                    </div></Col>
                </Row>
                <Row style={{ marginBottom: 10 }}>
                    <Col>
                    {formState.values.useExpiration ?   
                        <Text type="tertiary">{formatMessage({ id: 'settings.keys.field.expiryDays' })}</Text> : <Text type="tertiary">{formatMessage({ id: 'settings.keys.field.permanent' })}</Text>}</Col>
                </Row>
                {formState.values.useExpiration ? (
                    <Row style={{ marginBottom: 20 }}>
                        <Col><div className={styles.singleFormItem}>
                            <InputNumber
                                initValue={defaultKeyMaxExpireDays}
                                min={defaultKeyMinExpireDays}
                                max={defaultKeyMaxExpireDays}
                                style={{ width: 90, marginRight: 10 }}
                                field='expiry' noLabel></InputNumber><Text type="tertiary">{formatMessage({ id: 'settings.keys.field.expiryRange' }).replace('{min}', defaultKeyMinExpireDays.toString()).replace('{max}', defaultKeyMaxExpireDays.toString())}</Text></div></Col>

                    </Row>
                ) : null}
</>)}>
            </Form>
        </Modal>
    </>
}
export default Index