import React, { useContext, useState, useEffect } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { Typography, Row, Switch, Col, Form, Divider, Button, Space, Popover } from '@douyinfe/semi-ui';
import { disableMachineAuthorization, enableMachineAuthorization, getFlynet, setDefaultKeyExpireDays, setMeshEnabled, setRdpSettings } from '@/services/flynet';
import { useLocale } from '@/locales';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

const { Title, Paragraph } = Typography;


const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();
    // 默认密钥过期天数loading Flag
    const [keyExpireDaysLoading, setKeyExpireDaysLoading] = useState(false);

    const [defaultKeyMinExpireDays, setDefaultKeyMinExpireDays] = useState(1);
    const [defaultKeyMaxExpireDays, setDefaultKeyMaxExpireDays] = useState(180);

    const [keyExpireFormApi, SetKeyExpireFormApi] = useState<FormApi<{ defaultKeyExpireDays: number }>>()


    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {
            setFlynet(res.flynet)
            keyExpireFormApi?.setValue('defaultKeyExpireDays', res.flynet.defaultKeyExpireDays)

            if (res.constraint) {
                setDefaultKeyMinExpireDays(res.constraint.defaultKeyMinExpireDays);
                setDefaultKeyMaxExpireDays(res.constraint.defaultKeyMaxExpireDays);
            }
        }
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    const handleKeyExpireSubmit = (values: { defaultKeyExpireDays: number }) => {
        if (!flynet) {
            return;
        }
        setKeyExpireDaysLoading(true)
        setDefaultKeyExpireDays(flynet?.id, values.defaultKeyExpireDays)
            .then(() => { setFlynet(Object.assign({}, flynet, { setDefaultKeyExpireDays: values.defaultKeyExpireDays })) })
            .finally(() => setKeyExpireDaysLoading(false))
    }


    return <>
        <Title heading={4} className="mb2">{formatMessage({ id: 'settings.keys.keyExpire.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.keys.keyExpire.description' })}</Paragraph>

        <div className='mb20'>
            {flynet ? <Form onSubmit={handleKeyExpireSubmit} getFormApi={SetKeyExpireFormApi} initValues={{ defaultKeyExpireDays: flynet?.defaultKeyExpireDays }}>

                <Form.InputNumber labelPosition='left'
                    min={defaultKeyMinExpireDays}
                    max={defaultKeyMaxExpireDays}
                    field='defaultKeyExpireDays' label={formatMessage({ id: 'settings.keys.keyExpire.daysLabel' })} extraText={formatMessage({ id: 'settings.keys.keyExpire.daysRange' }).replace('{min}', String(defaultKeyMinExpireDays)).replace('{max}', String(defaultKeyMaxExpireDays))}></Form.InputNumber>
                <Space>
                    <Button htmlType="reset">{formatMessage({ id: 'components.common.reset' })}</Button>
                    <Button type="primary" theme='solid' loading={keyExpireDaysLoading} htmlType="submit">{formatMessage({ id: 'components.common.submit' })}</Button>
                </Space>
            </Form> : ''}

        </div>
    </>
}

export default Index;