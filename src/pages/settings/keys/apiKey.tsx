import React, { useState, useContext } from 'react'
import { Typography, Table, Row, Col, Button, Collapsible } from '@douyinfe/semi-ui';
import { IconChevronRight, IconChevronDown } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';

import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import ApikeyGen from '@/pages/settings/keys/apikey-gen';
import ApikeyResult from '@/pages/settings/keys/apikey-result';

import ApikeyRevoke from '@/pages/settings/keys/apikey-revoke';
import styles from './index.module.scss';
import useApiKey from './useApiKey';
const { Title, Paragraph, Text } = Typography;

import { ApiKey } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/api_keys_pb";
import { flylayerClient } from '@/services/core';

import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import TableEmpty from '@/components/table-empty';

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const globalConfig = useContext(GlobalConfigContext);
    const { columns, columnsInvalid, loading, data, dataInvalid, reloadFlag, setReloadFlag, curAuthKey, setCurAuthKey, delVisiable, setDelVisiable, expandRowRender } = useApiKey();

    const [invalidKeyShow, setInvalidKeyShow] = useState(false);
    // 生成密钥对话框是否展示
    const [authKeyGenVisiable, setAuthKeyGenVisiable] = useState(false);
    // 生成密钥结果对话框是否展示
    const [genResultVisiable, setGenResultVisiable] = useState(false);
    // 生成结果key
    const [genResultKeyValue, setGenResultKeyValue] = useState('');

    const [apikey, setAuthKey] = useState<ApiKey>();

    // API密码创建成功后回调
    const handleKeyGenSuccess = (apikey: ApiKey, keyValue: string) => {
        setAuthKeyGenVisiable(false);
        setGenResultVisiable(true)
        setAuthKey(apikey);

        setGenResultKeyValue(keyValue)


    }


    return <>
        <Row className='mb10'>
            <Col span={20}>
                <div><Title heading={4} className="mb2">{formatMessage({ id: 'settings.keys.apiKey.title' })}</Title>
                </div></Col>
            <Col span={4}><div className='btn-right-col'>
                <Button theme='solid' onClick={() => setAuthKeyGenVisiable(true)}>{formatMessage({ id: 'settings.keys.apiKey.generateButton' })}</Button>
            </div></Col>
        </Row>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.keys.apiKey.description' }).replace('{url}', globalConfig.ctrlUrl)}<Copyable content={globalConfig.ctrlUrl} />，{formatMessage({ id: 'settings.keys.apiKey.sdkInfo' })}<a href='https://buf.build/flylayer/api' target='_blank'>buf.build</a>{formatMessage({ id: 'settings.keys.apiKey.generateSdk' })}</Paragraph>
        <Table
            virtualized
            scroll={{ y: 300 }}
            rowKey={(record?: ApiKey) => record ? record.id + '' : ''}
            className='mb20'
            empty={<TableEmpty loading={loading} />}
            loading={loading}
            columns={columns}

            expandRowByClick={true}
            expandedRowRender={expandRowRender}
            rowExpandable={record => {
                if (record && record.description && record.description.trim() != '') {
                    return true;
                }
                return false;
            }}
            dataSource={data}
            pagination={false} />
        {dataInvalid && dataInvalid?.length > 0 ? <>
            <p className={styles.toggleCollapsible} onClick={() => setInvalidKeyShow(!invalidKeyShow)}>{invalidKeyShow ? <IconChevronDown /> : <IconChevronRight />}<span>&nbsp;{formatMessage({ id: 'settings.keys.apiKey.invalidKeysCount' }).replace('{count}', String(dataInvalid?.length))}</span></p>

            <Collapsible isOpen={invalidKeyShow}>
                <Table

                    virtualized
                    scroll={{ y: 300 }}
                    columns={columnsInvalid}
                    dataSource={dataInvalid}
                    expandRowByClick={true}
                    expandedRowRender={expandRowRender}
                    rowExpandable={record => {
                        if (record && record.description && record.description.trim() != '') {
                            return true;
                        }
                        return false;
                    }}
                    pagination={false} />
            </Collapsible>
        </> : undefined}


        {authKeyGenVisiable ? <ApikeyGen success={handleKeyGenSuccess} close={() => setAuthKeyGenVisiable(false)}></ApikeyGen> : ''}
        {genResultVisiable && apikey ? <ApikeyResult close={() => {
            setGenResultVisiable(false)
            setAuthKey(undefined)
            setReloadFlag(true)
        }}

            keyValue={genResultKeyValue} ApiKey={apikey}></ApikeyResult> : undefined}
        {delVisiable && curAuthKey ? <ApikeyRevoke
            record={curAuthKey}
            close={() => {
                setDelVisiable(false)
                setCurAuthKey(undefined)
            }}
            success={() => {

                setDelVisiable(false)
                setCurAuthKey(undefined)
                setReloadFlag(true)
            }}
        ></ApikeyRevoke> : ''}
    </>
}
export default Index