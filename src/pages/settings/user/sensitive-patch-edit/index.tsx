import { FC, useEffect, useState } from 'react'
import { Ty<PERSON>graphy, Button, Space, Tabs, TabPane, Notification } from '@douyinfe/semi-ui';
import { IconListView, IconEdit } from '@douyinfe/semi-icons'
import { Flynet, SensitivePatch } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import ListView from './list-view';
import EditView from './edit-view';


const { Title, Paragraph } = Typography;

const Index: FC<{
    flynet: Flynet
    success?: () => void
}> = (props) => {
    const { formatMessage } = useLocale();

    const [isEditMode, setIsEditMode] = useState(false);

    const [loading, setLoading] = useState(false);

    const [sensitivePatch, setSensitivePatch] = useState(props.flynet.sensitivePatch);

    useEffect(() => {
        if(props.flynet.sensitivePatch) {
            setSensitivePatch(props.flynet.sensitivePatch);
        } else {
            setSensitivePatch(new SensitivePatch());
        }
        
    }, [props.flynet])

    const handleSubmit = () => {
        setLoading(true)

        let errMsg = "";
        sensitivePatch?.patches?.forEach((item, index) => {
            if (!item.path) {
                errMsg += formatMessage({ id: 'settings.user.sensitivePatch.validation.pathRequired' }).replace('{index}', String(index + 1));
            } else if (item.path.indexOf('/') !== 0) {
                errMsg += formatMessage({ id: 'settings.user.sensitivePatch.validation.pathFormat' }).replace('{index}', String(index + 1));
            }
             else if (!item.op) {
                errMsg += formatMessage({ id: 'settings.user.sensitivePatch.validation.opRequired' }).replace('{index}', String(index + 1));
            }
            if(item.op != 'mask' && !item.value) {
                errMsg += formatMessage({ id: 'settings.user.sensitivePatch.validation.valueRequired' }).replace('{index}', String(index + 1));
            }
        })

        if (errMsg) {
            Notification.error({
                title: formatMessage({ id: 'settings.user.sensitivePatch.validation.incomplete' }),
                content: errMsg
            })
            setLoading(false)
            return;
        }

        flylayerClient.setSensitivePatch({
            flynetId: props.flynet.id,
            sensitivePatch: sensitivePatch
        }).then(() => {
            Notification.success({
                title: formatMessage({ id: 'settings.user.sensitivePatch.success' })
            })
            props.success?.()
        }).catch(e => {
            console.error(e);
            Notification.error({
                title: formatMessage({ id: 'settings.user.sensitivePatch.failed' })
            })
        }).finally(() => {
            setLoading(false)
        });
    }

    return <>
        <Title heading={4} className='mb2'>{formatMessage({ id: 'settings.user.sensitivePatch.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>
            {formatMessage({ id: 'settings.user.sensitivePatch.description' })}
        </Paragraph>
        <Tabs type="card" onChange={(activeKey) => {
            if (activeKey === 'list') {
                setIsEditMode(false);
            } else {
                setIsEditMode(true);
            }
        }}>
            <TabPane
                tab={
                    <span>
                        <IconListView />
                        {formatMessage({ id: 'settings.user.sensitivePatch.listEdit' })}
                    </span>
                }
                itemKey="list">
                <div>
                    {!isEditMode && sensitivePatch && <ListView sensitivePatch={sensitivePatch} onChange={(sensitivePatch) => {
                        setSensitivePatch(sensitivePatch);
                    }} />}
                </div>
            </TabPane>
            <TabPane
                tab={
                    <span>
                        <IconEdit />
                        {formatMessage({ id: 'settings.user.sensitivePatch.textEdit' })}
                    </span>
                }
                itemKey="edit">
                {isEditMode && sensitivePatch && <EditView sensitivePatch={sensitivePatch} onChange={(sensitivePatch) => {
                    setSensitivePatch(sensitivePatch);
                }} />}
            </TabPane>

        </Tabs>
        <Space>
            <Button type="primary" loading={loading} theme='solid' onClick={handleSubmit}>{formatMessage({ id: 'components.common.submit' })}</Button>
            <Button onClick={() => {
                setSensitivePatch(props.flynet.sensitivePatch);
            }}>{formatMessage({ id: 'components.common.reset' })}</Button>
        </Space>
    </>

    return <></>
}

export default Index;