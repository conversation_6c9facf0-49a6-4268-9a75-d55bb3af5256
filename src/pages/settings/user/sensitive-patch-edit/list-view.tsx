import { FC, useState } from 'react'
import { Form, ArrayField, Row, Col, Button, Space } from '@douyinfe/semi-ui';
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons'
import { Value, ListValue, Struct } from '@bufbuild/protobuf';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { SensitivePatch } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { JSONPatch } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { useLocale } from '@/locales';

const Index: FC<{
    sensitivePatch: SensitivePatch
    onChange: (sensitivePatch?: SensitivePatch) => void
}> = (props) => {
    const { formatMessage } = useLocale();

    const [formApi, setFormApi] = useState<FormApi<{
        patches: Array<{
            op: string,
            path: string,
            valueType: 'nullValue' | 'numberValue' | 'stringValue' | 'boolValue' | 'structValue' | 'listValue' | string,
            value: string
        }>;
    }>>();



    const parseObject = (value: any): Value => {

        if (typeof value === 'object') {
            if (Array.isArray(value)) {
                let values: Value[] = [];
                for (let i = 0; i < value.length; i++) {
                    values.push(parseObject(value[i]));
                }
                return new Value({
                    kind: {
                        value: new ListValue({
                            values: values
                        }), case: 'listValue'
                    }
                })
            } else {

                return new Value({
                    kind: {
                        value: new Struct({
                            fields: Object.keys(value).reduce((prev, key) => {
                                prev[key] = parseObject(value[key]);
                                return prev;
                            }, {} as { [key: string]: Value })
                        }), case: 'structValue'
                    }
                });
            }

        } else if (typeof value === 'string') {
            return new Value({
                kind: { value: value, case: 'stringValue' }
            })
        } else if (typeof value === 'number') {
            return new Value({
                kind: { value: value, case: 'numberValue' }
            })
        } else {
            return new Value({
                kind: { value: value, case: 'structValue' }
            })
        }
    }


    return <>
        <div style={{width:800}}>
            <Form getFormApi={setFormApi}
                onValueChange={async (values) => {
                    await formApi?.validate();
                    if (!values) {
                        props.onChange(undefined)
                        return;
                    }
                    try {

                        let sensitivePatch = new SensitivePatch({
                            patches: values.patches.map((item:
                                {
                                    op: string,
                                    path: string,
                                    valueType: 'nullValue' | 'numberValue' | 'stringValue' | 'boolValue' | 'structValue' | 'listValue' | string,
                                    value: string
                                }) => {
                                let value: Value | undefined = undefined;
                                try {
                                    switch (item.valueType) {
                                        case 'stringValue':
                                            value = new Value({
                                                kind: { value: item.value || '', case: 'stringValue' }
                                            });
                                            break;
                                        case 'numberValue':
                                            value = new Value({
                                                kind: { value: Number(item.value), case: 'numberValue' }
                                            });
                                            break;
                                        case 'boolValue':
                                            value = new Value({
                                                kind: { value: item.value == 'true' ? true : false, case: 'boolValue' }
                                            });
                                            break;
                                        case 'listValue':
                                            let arr = JSON.parse(decodeURIComponent(item.value))
                                            
                                            value = parseObject(arr)
                                            
                                            break;
                                        case 'structValue':
                                            value = parseObject(JSON.parse(item.value))
                                            break;
                                        default: break;
                                    }
                                } catch (e) {
                                    console.error(e, item.value);
                                }

                                return new JSONPatch({
                                    op: item.op,
                                    path: item.path,
                                    value: value
                                })
                            })
                        });
                        props.onChange(sensitivePatch);

                    } catch (e) {
                        console.error(e)
                    }
                }}
                initValues={{
                    patches: props.sensitivePatch?.patches.map(item => {
                        let value = '';
                        switch (item.value?.kind.case) {
                            case 'stringValue':
                                value = item.value.kind.value;
                                break;
                            case 'numberValue':
                                value = item.value.kind.value + '';
                                break;
                            case 'boolValue':
                                value = item.value.kind.value ? 'true' : 'false';
                                break;
                            case 'structValue':
                                value = JSON.stringify(item.value.kind.value);
                                break;
                            case 'listValue':
                                value = JSON.stringify(item.value.kind.value);
                                break;
                        }
                        return {
                            op: item.op,
                            path: item.path,
                            valueType: item.value?.kind.case,
                            value: value
                        }
                    }) || []
                }} >
                {({ values }) => (<>
                    <ArrayField field='patches'>
                        {({ addWithInitValue, arrayFields }) => (
                            <>
                                <Row className='tableTitle'>
                                    <Col span={3}>{formatMessage({ id: 'settings.user.sensitivePatch.list.operation' })}</Col>
                                    <Col span={5}>{formatMessage({ id: 'settings.user.sensitivePatch.list.path' })}</Col>
                                    <Col span={12}>{formatMessage({ id: 'settings.user.sensitivePatch.list.value' })}</Col>
                                    <Col span={4} className="btn-right-col">
                                        <Button type='primary' icon={<IconPlusCircle />} onClick={() => {
                                            addWithInitValue({
                                                op: 'add',
                                                path: '',
                                                valueType: 'stringValue',
                                                value: ''
                                            })
                                        }}></Button>
                                    </Col>
                                </Row>
                                <div style={{
                                    overflowY: 'scroll',
                                    height: 300
                                }}>
                                    {arrayFields.map(({ field, remove }, i) => {
                                        return <Row key={i}>
                                            <Col span={3} style={{paddingRight:8}}>
                                                <Form.Select style={{ width: '100%' }} field={`${field}.op`} noLabel optionList={[
                                                    { label: formatMessage({ id: 'settings.user.sensitivePatch.operation.add' }), value: 'add' },
                                                    { label: formatMessage({ id: 'settings.user.sensitivePatch.operation.replace' }), value: 'replace' },
                                                    // { label: formatMessage({ id: 'settings.user.sensitivePatch.operation.remove' }), value: 'remove' },
                                                    { label: formatMessage({ id: 'settings.user.sensitivePatch.operation.mask' }), value: 'mask' },
                                                ]} />
                                            </Col>
                                            <Col span={5} style={{paddingRight:8}}>
                                                <Form.Input field={`${field}.path`} noLabel
                                                    validate={(value) => {
                                                        if (!value) {
                                                            return formatMessage({ id: 'settings.user.sensitivePatch.validation.pathEmpty' })
                                                        }
                                                        if (value.indexOf('/') !== 0) {
                                                            return formatMessage({ id: 'settings.user.sensitivePatch.validation.pathStartSlash' })
                                                        }

                                                        return '';
                                                    }}
                                                />
                                            </Col>
                                            <Col span={12}>
                                                <Space>
                                                    {values.patches[i] && values.patches[i].op != 'mask' && values.patches[i].op != 'remove' && <>
                                                        <Form.Select style={{ width: 120 }} field={`${field}.valueType`} noLabel optionList={[
                                                            { label: formatMessage({ id: 'settings.user.sensitivePatch.valueType.string' }), value: 'stringValue' },
                                                            { label: formatMessage({ id: 'settings.user.sensitivePatch.valueType.number' }), value: 'numberValue' },
                                                            { label: formatMessage({ id: 'settings.user.sensitivePatch.valueType.boolean' }), value: 'boolValue' },
                                                            { label: formatMessage({ id: 'settings.user.sensitivePatch.valueType.object' }), value: 'structValue' },
                                                            { label: formatMessage({ id: 'settings.user.sensitivePatch.valueType.array' }), value: 'listValue' },
                                                            { label: formatMessage({ id: 'settings.user.sensitivePatch.valueType.null' }), value: 'nullValue' },
                                                        ]} />
                                                        <Form.Input field={`${field}.value`} noLabel />
                                                    </>}

                                                </Space>
                                            </Col>
                                            <Col span={4} className="btn-right-col" style={{ paddingTop: 12, paddingRight: 4 }}>
                                                <Button type='danger' icon={<IconMinusCircle />} onClick={() => {
                                                    remove()
                                                }}></Button>
                                            </Col>
                                        </Row>
                                    })}
                                </div>
                            </>
                        )}
                    </ArrayField>
                </>)}
            </Form>
        </div>
    </>

}

export default Index;