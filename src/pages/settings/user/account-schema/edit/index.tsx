import { FC, useState, useCallback } from 'react'
import { Typography, Modal, Checkbox, Notification, Space } from '@douyinfe/semi-ui';
import styles from './index.module.scss';
import { AccountSchema } from '@/interface/account-schema';
import { flylayerClient } from '@/services/core';
import { Field, FieldRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import Dnd from '@/components/dnd';
import { useLocale } from '@/locales';
const { Text } = Typography;
interface Props {
    flynetId: bigint;
    fields: AccountSchema[];
    accountAvailableFields: Field[];
    close: () => void;
    success: (accountAvailableFields: Field[]) => void;
}




const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const { flynetId, close, success, accountAvailableFields } = props

    // 根据accountAvailableFields，获取fields的排序，accountAvailableFields中的字段在fields中的顺序
    const getSortedFields = (accountAvailableFields: Field[], fields: AccountSchema[]) => {
        let sortedFields: AccountSchema[] = [];
        accountAvailableFields.forEach((field) => {
            let schema = fields.find((schema) => schema.name === field.key);
            if (schema) {
                sortedFields.push(schema);
            }
        });

        // 如果有字段不在accountAvailableFields中，添加到sortedFields中
        fields.forEach((field) => {
            if (!sortedFields.find((schema) => schema.name === field.name)) {
                sortedFields.push(field);
            }
        });

        return sortedFields;
    }

    const [selectedNames, setSelectedNames] = useState<string[]>(accountAvailableFields.map(field => field.key));
    const [fields, setFields] = useState<AccountSchema[]>(getSortedFields(accountAvailableFields, props.fields));
    const [loading, setLoading] = useState(false);


    const moveItem = useCallback((dragIndex: number, hoverIndex: number) => {
        const updatedItems = [...fields];
        const [removedItem] = updatedItems.splice(dragIndex, 1);
        updatedItems.splice(hoverIndex, 0, removedItem);
        setFields(updatedItems);
    }, [fields]);

    const handleSubmit = () => {
        setLoading(true);
        let newFields: Field[] = [];

        fields.forEach((schema) => {
            if (selectedNames.indexOf(schema.name) >= 0) {
                let type = schema.type;
                let required = schema.required;

                const rules: FieldRule[] = [];

                if (required) {
                    rules.push(new FieldRule({
                        required: true,
                        whitespace: false,
                    }));
                }
                if (schema.format) {
                    rules.push(new FieldRule({
                        required: false,
                        whitespace: false,
                        format: schema.format,
                        pattern: schema.pattern,
                    }));
                }
                if (schema.minLength && schema.minLength > 0) {
                    rules.push(new FieldRule({
                        required: false,
                        whitespace: false,
                        minLength: schema.minLength
                    }));
                }
                if (schema.maxLength && schema.maxLength > 0) {
                    rules.push(new FieldRule({
                        required: false,
                        whitespace: false,
                        maxLength: schema.maxLength
                    }));
                }

                let field = new Field({
                    key: schema.name,
                    type: type,
                    extra: schema.description,
                    label: schema.title,
                    rules: rules,
                    default: '',
                    placeholder: schema.description,
                });
                newFields.push(field);
            }
        })

        flylayerClient.setAccountAvailableField({
            flynetId: flynetId,
            accountAvailableFields: newFields,
        }).then(() => {
            Notification.success({
                title: formatMessage({ id: 'settings.user.schema.edit.success' }),
                content: formatMessage({ id: 'settings.user.schema.edit.success' }),
            });
            success(newFields);
        }).catch(() => {
            Notification.error({
                title: formatMessage({ id: 'settings.user.schema.edit.failed' }),
                content: formatMessage({ id: 'settings.user.schema.edit.failed' }),
            });
        }).finally(() => {
            setLoading(false);
        });
    }

    return <><Modal
        width={700}
        title={formatMessage({ id: 'settings.user.schema.edit.title' })}
        visible={true}
        onCancel={close}
        onOk={handleSubmit}
        okButtonProps={{
            loading: loading,
        }}
        className='semi-modal'
        maskClosable={false}
    >

        <Dnd
            containerClassName={styles.dragContainer}
            itemClassName={styles.dragItem}
            items={fields.map((item, index) => (
                <Space key={index}>
                    <Checkbox
                        checked={selectedNames.indexOf(item.name) >= 0}
                        onChange={(e) => {
                            if (e.target.checked) {
                                setSelectedNames([...selectedNames, item.name]);
                            } else {
                                setSelectedNames(selectedNames.filter((name) => name !== item.name));
                            }
                        }}
                    ></Checkbox>
                    <Text>{item.title}</Text>
                </Space>
            ))}
            moveItem={moveItem}
        ></Dnd>
    </Modal>

    </>
}

export default Index;