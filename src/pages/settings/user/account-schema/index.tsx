import React, { useState, useContext, useEffect } from 'react'
import { Typography, Row, Table, Col, Button, Space, Tag } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'
import { UserProfileContext } from '@/hooks/useUserProfile';
import { AccountSchema } from '@/interface/account-schema';
import { getAccountSchema } from '@/services/user';
import { useLocale } from '@/locales';

import Edit from './edit';
import View from './view';

const Index: React.FC<{ flynet: Flynet }> = ({ flynet }) => {
    const { formatMessage } = useLocale();
    const userProfile = useContext(UserProfileContext)

    const [accountSchemaLoading, setAccountSchemaLoading] = useState(false);
    const [accountSchema, setAccountSchema] = useState<AccountSchema>();
    const [fields, setFields] = useState<AccountSchema[]>([]);

    const [editVisible, setEditVisible] = useState(false);
    const [viewVisible, setViewVisible] = useState(false);

    const [accountAvailableFields, setAccountAvailableFields] = useState<Field[]>(flynet.accountAvailableFields);


    const columns = [{
        title: formatMessage({ id: 'settings.user.schema.column.name' }),
        dataIndex: 'name',
        key: 'name',
        render: (text: string) => <Text>{text}</Text>
    }, {
        title: formatMessage({ id: 'settings.user.schema.column.title' }),
        dataIndex: 'title',
        key: 'title',
        render: (text: string) => <Text>{text}</Text>
    }, {
        title: formatMessage({ id: 'settings.user.schema.column.type' }),
        dataIndex: 'type',
        key: 'type',
        render: (text: string) => <Text>{text}</Text>
    }, {
        title: formatMessage({ id: 'settings.user.schema.column.enabled' }),
        dataIndex: 'enabled',
        key: 'enabled',
        render: (text: boolean, accountSchema: AccountSchema) => {
            let field = accountAvailableFields.find(f => f.key === accountSchema.name);
            if (field) {
                return <Tag color='green'>{formatMessage({ id: 'settings.user.schema.status.enabled' })}</Tag>
            } else {
                return <Tag color='red'>{formatMessage({ id: 'settings.user.schema.status.disabled' })}</Tag>
            }
        }
    }];


    const query = () => {
        setAccountSchemaLoading(true);
        getAccountSchema(userProfile.identity?.schema_url || '').then((res) => {
            // console.log(result);
            // const mockDataStr = `
            // {
            //     "type": "object",
            //     "properties": {
            //       "email": {
            //         "type": "string",
            //         "format": "email",
            //         "title": "E-Mail",
            //         "ory.sh/kratos": {
            //           "credentials": {
            //             "password": {
            //               "identifier": true
            //             },
            //             "webauthn": {
            //               "identifier": true
            //             },
            //             "token": {
            //               "identifier": true
            //             }
            //           },
            //           "verification": {
            //             "via": "email"
            //           },
            //           "recovery": {
            //             "via": "email"
            //           }
            //         }
            //       },
            //       "username": {
            //         "type": "string",
            //         "title": "Username",
            //         "ory.sh/kratos": {
            //           "credentials": {
            //             "password": {
            //               "identifier": true
            //             }
            //           }
            //         }
            //       },
            //       "name": {
            //         "type": "string",
            //         "title": "Name"
            //       },
            //       "family_name": {
            //          "type": "string",
            //          "title": "Family Name"
            //       },
            //       "middle_name": {
            //          "type": "string",
            //          "title": "Middle Name"
            //       },
            //       "family_name": {
            //         "type": "string",
            //         "title": "Family Name"
            //       },
            //       "nickname": {
            //         "type": "string",
            //         "title": "Nickname",
            //         "minLength":3
            //       },
            //       "preferred_username": {
            //         "type": "string",
            //         "title": "Preferred Username"
            //       },
            //       "profile": {
            //         "type": "string",
            //         "title": "Profile"
            //       },
            //       "picture": {
            //         "type": "string",
            //         "title": "Picture",
            //         "required": true
            //       },
            //       "website": {
            //         "type": "string",
            //         "title": "Website",
            //         "format": "date-time"
            //       },
            //       "gender": {
            //         "type": "boolean",
            //         "title": "Gender"
            //       },
            //       "birthdate": {
            //         "type": "string",
            //         "title": "Birthday",
            //         "format": "date"
            //       },
            //       "zoneinfo": {
            //         "type": "string",
            //         "title": "Time zone"
            //       },
            //       "locale": {
            //         "type": "number",
            //         "title": "Locale"
            //       },
            //       "phone_number": {
            //         "type": "string",
            //         "title": "Phone Number",
            //         "pattern": "^[0-9]{5}(?:-[0-9]{4})?$",
            //         "required": true
            //       },
            //       "address": {
            //         "type": "string",
            //         "title": "Address"
            //       },
            //       "team": {
            //         "type": "string",
            //         "title": "Team",
            //         "format": "email"
            //       },
            //       "extra": {
            //         "type": "array",
            //         "title": "Extra",
            //         "description": "Extra fields",
            //         "required": true
            //       }
            //     },
            //     "additionalProperties": false
            //   }
            // `
            // let res = JSON.parse(mockDataStr);

            setAccountSchema(res);
            if (res.properties && res.properties) {

                setFields(Object.keys(res.properties).filter(key=>{
                    let sys = res.properties ? res.properties[key].sys : false;
                    return !sys;
                }).map(key => {
                    let type = res.properties ? res.properties[key].type : 'string';
                    let title = res.properties ? res.properties[key].title : '';
                    let description = res.properties ? res.properties[key].description : '';
                    let format = res.properties ? res.properties[key].format : '';
                    let required = res.properties ? res.properties[key].required : false;
                    let minLength = res.properties ? res.properties[key].minLength : -1;
                    let maxLength = res.properties ? res.properties[key].maxLength : -1;
                    let pattern = res.properties ? res.properties[key].pattern : '';
                    let items = res.properties ? res.properties[key].items : [];

                    let field: AccountSchema = {
                        name: key,
                        type: type,
                        title: title,
                        description: description,
                        format: format as any,
                        required: required,
                        minLength: minLength,
                        maxLength: maxLength,
                        pattern: pattern,
                        items: items
                    }
                    return field;
                }))
            }

        }).finally(() => setAccountSchemaLoading(false))
    }

    useEffect(() => {
        query()
    }, []);

    return <>

        <Row className='mb10'>
            <Col span={20}>
                <Title heading={4} className="mb2">{formatMessage({ id: 'settings.user.schema.title' })}</Title>
                <Paragraph type='tertiary'>{formatMessage({ id: 'settings.user.schema.description' })}</Paragraph>
            </Col>
            <Col span={4}>
                <div className={styles.rightColumn}>
                    <Space>
                        <Button onClick={() => setViewVisible(true)}>{formatMessage({ id: 'policies.common.preview' })}</Button>
                        <Button theme='solid' onClick={() => setEditVisible(true)}>{formatMessage({ id: 'components.common.edit' })}</Button>
                    </Space>
                </div></Col>
        </Row>
        <Table columns={columns} dataSource={fields}></Table>
        {editVisible && <Edit
            flynetId={flynet.id}
            fields={fields}
            accountAvailableFields={accountAvailableFields}
            close={() => setEditVisible(false)}
            success={(fields) => {
                setEditVisible(false);
                setAccountAvailableFields(fields);
                query()
            }}
        ></Edit>}
        {viewVisible && <View
            fields={fields}
            accountAvailableFields={accountAvailableFields}
            close={() => setViewVisible(false)}
        ></View>}

    </>
}

export default Index
