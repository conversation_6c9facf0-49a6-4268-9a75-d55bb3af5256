import { FC } from 'react'
import { Modal, Form, Row } from '@douyinfe/semi-ui';
import styles from './index.module.scss';
import { AccountSchema } from '@/interface/account-schema';
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { renderFormElement } from '@/utils/user';
import { useLocale } from '@/locales';

interface Props {
    fields: AccountSchema[];
    accountAvailableFields: Field[];
    close: () => void;
}

const Index: FC<Props> = ({ close, fields, accountAvailableFields }) => {
    const { formatMessage } = useLocale();
    const handleSubmit = () => {}
    const nodes = renderFormElement(accountAvailableFields);
    return <><Modal
        width={700}
        title={formatMessage({ id: 'settings.user.schema.preview.title' })}
        visible={true}
        onCancel={close}
        onOk={handleSubmit}
        className='semi-modal'
        maskClosable={false}
    >
        <Form>
            <Row gutter={16}>
                {nodes}
            </Row>
        </Form>
    </Modal>
        
    </>
}

export default Index;