import React, { useState, useContext, useEffect } from 'react'
import { Typo<PERSON>, Row, Switch, Col, Divider } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { saveAccountManualCreate, getFlynet, setAccountMfaEnabled } from '@/services/flynet';
import { useLocale } from '@/locales';
import AccountSchema from './account-schema';
import SensitivePatchEdit from './sensitive-patch-edit';
const { Title, Paragraph } = Typography;
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();


    const [accountManualCreate, setAccountManualCreate] = useState(false);
    const [accountManualCreateLoading, setAccountManualCreateLoading] = useState(false);

    const [mfaEnabled, setMfaEnabled] = useState(false);
    const [mfaEnabledLoading, setMfaEnabledLoading] = useState(false);

    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {
            setFlynet(res.flynet)

            if (res.flynet) {
                setAccountManualCreate(res.flynet.accountManualCreate);
                setMfaEnabled(res.flynet.mfaEnabled);
            }
        }
    }

    const handleAccountManualCreateChange = (checked: boolean) => {
        if (!flynet) {
            return;
        }
        setAccountManualCreateLoading(true)
        saveAccountManualCreate(flynet.id, checked).then(() => {
            setAccountManualCreate(checked)
            setFlynet(Object.assign({}, flynet, { accountManualCreate: checked }))
        }).finally(() => setAccountManualCreateLoading(false))
    }

    const handleMfaEnabledChange = (checked: boolean) => {

        if (!flynet) {
            return;
        }
        setMfaEnabledLoading(true)
        setAccountMfaEnabled(flynet.id, checked).then(() => {
            setMfaEnabled(checked)
            setFlynet(Object.assign({}, flynet, { mfaEnabled: checked }))
        }).finally(() => setMfaEnabledLoading(false))
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    



    return <><div className='settings-page'>
        <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.user.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.user.description' })}</Paragraph>

        <Title heading={4} className="mb2">{formatMessage({ id: 'settings.user.manualCreate.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.user.manualCreate.description' })}</Paragraph>
        <Row className='mb40'>
            <Col span={4}><div className={styles.colFormItem}>
                <Switch
                    checked={accountManualCreate}
                    loading={accountManualCreateLoading}
                    onChange={handleAccountManualCreateChange}
                /></div></Col>
            <Col span={20}><Paragraph>{formatMessage({ id: 'settings.user.manualCreate.enable' })}</Paragraph></Col>
        </Row>

        <Divider className='mb40' />

        {flynet && <AccountSchema flynet={flynet}/>}
        <div style={{height:40}}></div>
    </div></>
}

export default Index