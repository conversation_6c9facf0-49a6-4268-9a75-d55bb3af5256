import React, { useState } from 'react'
import { Typography, Form, Notification, Modal } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'
import { useLocale } from '@/locales';
import {
    Platform,
    CreatePackageRequest,
    Install, Distro
} from '@buf/flylayer_api.bufbuild_es/flylayer/v1/install_pb';

interface FormFormat {
    installId: string;
    installDistroId: string;
    downloadLink: string;
    packageType: number;
}

const { Switch, Input, Select } = Form
const Index: React.FC<{
    install: Install,
    distros: Array<Distro>,
    platform: Platform,
    close: () => void,
    success: () => void,
}> = (props) => {
    const { formatMessage } = useLocale();
    const { platform } = props
    const handleSubmit = () => {
        if (!formApi) {
            return;
        }
        const values: FormFormat = formApi.getValues();

        if (platform?.name != 'MacOS' && platform?.name != 'Linux' && props.distros.length > 0) {
            values.installDistroId = props.distros[0].distroId + '';
        }

        formApi.validate().then((res) => {

            if (res.hasError) {
                return;
            }

            let installDistroId = BigInt(values.installDistroId)
            if (platform.name == 'Windows') {
                props.distros.forEach((val) => {
                    if (val.arch == '386') {
                        installDistroId = BigInt(val.distroId)
                    }
                })
            }

            let request = new CreatePackageRequest({
                package: {
                installId: props.install.installId,
                installDistroId: installDistroId,
                downloadLink: values.downloadLink,
                packageType: values.packageType,
            }})

            flylayerClient.createPackage(request).then(() => {
                Notification.success({
                    content: formatMessage({ id: 'settings.client.addPackage.createSuccess' }),
                    position: 'bottomRight'
                })
                props.success()
            }, () => {
                Notification.error({ content: formatMessage({ id: 'settings.client.addPackage.createFailed' }), position: "bottomRight" })
            })
        })
    }

    const [loading, setLoading] = useState(false);

    const [formApi, setFormApi] = useState<FormApi>()

    const validateForm = (values: FormFormat) => {
        let errors: any = {}
        
        if (!values.downloadLink) {
            errors['downloadLink'] = formatMessage({ id: 'settings.client.addPackage.validation.downloadLinkRequired' })
        }
        if (platform.name == 'MacOS' || platform.name == 'Linux') {
            if (values.installDistroId == '') {
                errors['installDistroId'] = formatMessage({ id: 'settings.client.addPackage.validation.distroRequired' })
            }
        }
        if (values.packageType !== 0 && values.packageType !== 1 && values.packageType !== 2) {
            errors['packageType'] = formatMessage({ id: 'settings.client.addPackage.validation.packageTypeRequired' })
        }
        
        if (Object.keys(errors).length > 0) {
            return errors;
        }
        else {
            return ''
        }
    }
    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'settings.client.addPackage.title' })}
            visible={true}
            maskClosable={false}
            closeOnEsc={true}
            okText={formatMessage({ id: 'components.common.confirm' })}
            onCancel={() => props.close()}
            okButtonProps={{ loading }}
            onOk={handleSubmit}
        >
            <div className={styles.addRoute}>
                <Form getFormApi={setFormApi} validateFields={validateForm} render={({ formState }) => (<>

                    <Select label={formatMessage({ id: 'settings.client.addPackage.form.packageType' })} style={{ width: 200 }} field='packageType'>
                        <Select.Option value={0}>{formatMessage({ id: 'settings.client.addPackage.packageType.installer' })}</Select.Option>
                        {/* <Select.Option value={1}>{formatMessage({ id: 'settings.client.addPackage.packageType.incremental' })}</Select.Option> */}
                        <Select.Option  value={2}>{formatMessage({ id: 'settings.client.addPackage.packageType.upgrade' })}</Select.Option>
                    </Select>
                    {platform.name == 'Linux' && <Select label={formatMessage({ id: 'settings.client.addPackage.form.distro' })} style={{ width: 200 }} field='installDistroId'>
                        {props.distros.map((val, index) => {
                            return <Select.Option key={platform.platformId + ''}  value={val.distroId + ''}>{val.name}</Select.Option>

                        })}
                    </Select>}
                    {platform.name == 'MacOS' && <Select label={formatMessage({ id: 'settings.client.addPackage.form.chipType' })} style={{ width: 200 }} field='installDistroId'>
                        {props.distros.map((val, index) => {
                            return <Select.Option key={platform.platformId + ''}  value={val.distroId + ''}>{val.arch}</Select.Option>

                        })}
                    </Select>}
                    <Input field='downloadLink' label={formatMessage({ id: 'settings.client.addPackage.form.downloadLink' })}></Input>

                </>)} />
            </div>
        </Modal>

    </>
}

export default Index