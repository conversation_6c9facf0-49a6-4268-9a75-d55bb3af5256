import React, { useEffect } from 'react'
import { <PERSON>po<PERSON>, Tabs, Row, Switch, Col, Button, Collapsible, Divider } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

const { Title, Paragraph, Text } = Typography;
import AddPackage from './add-package';
import Version from './version';
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();

    return <>
        <div className='settings-page'>
            <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.client.title' })}</Title>
            <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.client.description' })}</Paragraph>
            <Version></Version>
            
        </div></>
}

export default Index