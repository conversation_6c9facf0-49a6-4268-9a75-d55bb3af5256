import React, { useEffect, useState } from "react";
import {
  Typography,
  Row,
  Space,
  Col,
  Button,
  TabPane,
  Tabs,
  List,
  Divider,
  Tooltip,
  Table,
  Popconfirm,
} from "@douyinfe/semi-ui";
import { flylayerClient } from "@/services/core";
import {
  ListVersionResponse,
  Platform,
  Platform_Type,
  Install,
  VersionType,
  Distro,
} from "@buf/flylayer_api.bufbuild_es/flylayer/v1/install_pb";
import { FormApi } from "@douyinfe/semi-ui/lib/es/form";
// import AddPackage from "../add-package";
import AddInstall from "../add-install";
import { IconDownload, IconPlusCircle, IconMinusCircle, IconCopyAdd } from "@douyinfe/semi-icons";

const { Title, Paragraph, Text } = Typography;
import styles from "./index.module.scss";

import android from "@/assets/icon/android.svg";
import iOS from "@/assets/icon/iOS.svg";
import linux from "@/assets/icon/linux.svg";
import macOS from "@/assets/icon/macOS.svg";
import windows from "@/assets/icon/windows.svg";

import DateFormat from "@/components/date-format";
import { useLocale } from '@/locales';


const Index: React.FC = () => {
  const { formatMessage } = useLocale();
  // const [versions, setVersions] = useState<Array<string>>([]);
  const [mapPlatforms, setMapPlatforms] = useState<Map<string, Array<Install>>>(new Map());

  // 平台列表
  const [platforms, setPlatforms] = useState<Array<Platform>>([]);

  // 添加包对话框标识
  // const [addPackageVisible, setAddPackageVisible] = useState(false);

  // 添加版本对话框标识
  const [addInstallVisible, setAddInstallVisible] = useState(false);

  const [curInstall, setCurInstall] = useState<Install>();
  const [curPlatform, setCurPlatform] = useState<Platform>({} as Platform);
  const [isEdit, setIsEdit] = useState<boolean>(false);




  useEffect(() => {
    query();
  }, []);

  useEffect(() => {
    if (platforms.length !== 0 && platforms[0]) {
      setCurPlatform(platforms[0]);
    }
  }, [platforms]);

  const query = () => {
    flylayerClient.listVersion({}).then((res: ListVersionResponse) => {
      if (res.installs) {
        let mapPlatforms = new Map<string, Array<Install>>();
        res.installs.forEach((install) => {
          if (install.platformId) {
            if (!mapPlatforms.has(install.platformId.toString())) {
              mapPlatforms.set(install.platformId.toString(), [install]);

            } else {
              let installs = mapPlatforms.get(install.platformId.toString()) as Array<Install>;
              installs.push(install);
              mapPlatforms.set(install.platformId.toString(), installs);

            }
          }
        });
        setMapPlatforms(mapPlatforms);
      }
      setPlatforms(res.platforms.filter(p => p.name !== "Linux") as Array<Platform>);
    }).catch((err) => {
      console.error(err);
    });
  };

  const handleChange = (activeKey: string) => {
    const platform = platforms.find(p => p.platformId?.toString() === activeKey) as Platform;
    setCurPlatform(platform);
  }

  const handleDeleteInstall = (installId: string) => {
    flylayerClient.deleteInstall({
      installId:  BigInt(installId)
    }).then((res) => {
      query();
    }).catch((err) => {
      console.error(err);
    });
  }

  return (
    <>
      <Row className="mb10">
        <Col span={20}>
          <div>
            <Title heading={4} className="mb2">
              {formatMessage({ id: 'settings.client.version.list.title' })}
            </Title>
          </div>
        </Col>
        <Col span={4}>
          <div className="btn-right-col">
            <Button theme="solid" onClick={() => setAddInstallVisible(true)}>
              {formatMessage({ id: 'settings.client.version.list.createVersion' })}
            </Button>
          </div>
        </Col>
      </Row>

      <Tabs type="button" size="small"
        onChange={handleChange}>
        {platforms.map((platform) => {
          const platformType = platform.type;


          return (
            <TabPane
              key={platform.platformId.toString()}
              tab={
                <div className={styles.tabItem} title={platform.name}>
                  <img
                    src={
                      platformType === Platform_Type.LINUX
                        ? linux
                        : platformType === Platform_Type.WINDOWS
                          ? windows
                          : platformType === Platform_Type.MACOS
                            ? macOS
                            : platformType === Platform_Type.IOS
                              ? iOS
                              : platformType === Platform_Type.ANDROID
                                ? android
                                : ""
                    }
                  />
                </div>
              }
              itemKey={platform.platformId.toString()}
            >
              <div className="mb40" style={{ border: "1px solid var(--semi-color-border)", padding: 16 }}>
                <Table
                  dataSource={
                    Array.from(
                      mapPlatforms.get(platform.platformId.toString()) || []
                    ).map((install, index) => ({
                      key: install.installId?.toString(),
                      versionName: install.versionName || formatMessage({ id: 'settings.client.version.unknownVersion' }),
                      versionType: install.versionType,
                      releaseDate: install.releaseDate || new Date(),
                      releaseNotes: install.releaseNotes,
                      installId: install.installId?.toString(),
                      platformId: install.platformId?.toString(),
                      forceUpgrade: install.forceUpgrade ? formatMessage({ id: 'components.common.yes' }) : formatMessage({ id: 'components.common.no' }),
                      packages: install.packages
                    }))
                  }

                  columns={[

                    {
                      title: formatMessage({ id: 'settings.client.version.table.index' }),
                      dataIndex: 'key',
                      render: (text: any, record: any, index: any) => (
                        <Text strong>{index + 1}</Text>
                      )
                    },

                    {
                      title: formatMessage({ id: 'settings.client.version.table.versionNumber' }),
                      dataIndex: 'versionName',
                      render: (text: any) => <Text strong>{text}</Text>
                    },

                    {
                      title: formatMessage({ id: 'settings.client.version.table.releaseNotes' }),
                      dataIndex: 'releaseNotes',
                      render: (notes: any) => (
                        <Tooltip
                          content={
                            <ol style={{ margin: 0 }}>
                              {notes?.map((note: any, index: any) => (
                                <li key={index}>{note}</li>
                              ))}
                            </ol>
                          }
                          position="top"
                        >
                          <div className={styles.notesPreview}>
                            <ol>
                              {notes?.slice(0, 2).map((note: any, index: any) => (
                                <li key={index}>{note}</li>
                              ))}
                              {notes && notes.length > 2 && <li>...</li>}
                            </ol>
                          </div>
                        </Tooltip>
                      )
                    },
                    {
                      hidden: true,
                      title: formatMessage({ id: 'settings.client.version.table.forceUpgrade' }),
                      dataIndex: 'forceUpgrade',
                      render: (text: any) => <Text strong>{text}</Text>
                    },
                    {
                      title: formatMessage({ id: 'settings.client.version.table.releaseDate' }),
                      dataIndex: 'releaseDate',
                      render: (date: any) => <DateFormat date={date} />
                    },
                    {
                      title: formatMessage({ id: 'settings.client.version.table.actions' }),
                      render: (_: any, record: any) => (
                        <Space>

                          <Popconfirm
                            title={formatMessage({ id: 'settings.client.version.delete.confirmTitle' })}
                            content={formatMessage({ id: 'settings.client.version.delete.confirmContent' })}
                            okType="danger"
                            onConfirm={() => handleDeleteInstall(record.installId)}
                          >
                            <Button type='danger'>{formatMessage({ id: 'components.common.delete' })}</Button>
                          </Popconfirm>
                          <Button type='primary'
                            onClick={() => {

                              const installs = mapPlatforms.get(record.platformId)?.filter(
                                install => install.platformId === platform.platformId
                              ) || [];

                              const install = installs.find(
                                i => i.versionName === record.versionName
                              );
                              if (install) {
                                setIsEdit(true);
                                setCurInstall(install);
                                setAddInstallVisible(true);
                              }
                            }}
                          >{formatMessage({ id: 'components.common.edit' })}</Button>

                        </Space>
                      )
                    }
                  ]}
                  pagination={false}
                  rowKey="key"
                />
              </div>
            </TabPane>
          );
        })}
      </Tabs>

      {addInstallVisible && (
        <AddInstall
          success={() => {
            setAddInstallVisible(false);
            query();
          }}
          curPlatform={curPlatform}
          isEdit={isEdit}
          platforms={platforms}
          install={curInstall as Install}
          mapInstalls={mapPlatforms}
          close={() => {
            setAddInstallVisible(false);
            setCurInstall(undefined);
            setIsEdit(false);
          }}
        ></AddInstall>
      )}
    </>
  );
};

export default Index;
