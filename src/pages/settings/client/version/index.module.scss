.tabItem {
    color: var(--semi-color-text-3);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    img {
        width: 20px;
        height: 20px;
    }
}
.packageDownload {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    .packageLink {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 5px;
        :first-child {
            margin-right: 2px;
        }
        :last-child {
            font-size: 12px;
        }
    }
}

.releaseNotes {
    color: var(--semi-color-text-2);
    font-size: 12px;
    margin-bottom: 20px;
}
.notesPreview {
  max-height: 3em;
  overflow: hidden;
  ol {
    display: -webkit-box;
    -webkit-line-clamp: 2; // 限制两行
    line-clamp: 2; // 添加标准属性实现更好的兼容性
    -webkit-box-orient: vertical;
    margin: 0;
    padding-left: 20px;
  }
}