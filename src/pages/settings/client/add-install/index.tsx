import React, { useEffect, useState } from 'react'
import { Typography, Form, Row, Col, Button, Modal, ArrayField, Notification, Upload, Space, Tooltip, Avatar, Spin } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import {
    CreateInstallRequest,
    Platform,
    VersionType,
    Package,
    Distro, Install
} from '@buf/flylayer_api.bufbuild_es/flylayer/v1/install_pb';

import { Timestamp } from "@bufbuild/protobuf";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { uploadFileWithProgress } from '@/services/file';
const { Text } = Typography;
import styles from './index.module.scss'
import { IconUpload, IconCloud } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';


interface FormFormat {
    versionName: string;
    platformId: string;
    forceUpgrade: boolean;
    releaseDate: Date;
    releaseNotes: string[];
    packages: Array<{
        packageType: number;
        installDistroId: string;
        downloadLink: string;
    }>
}
const Index: React.FC<{
    platforms: Array<Platform>,
    mapInstalls: Map<string, Array<Install>>,
    curPlatform: Platform,
    install: Install,
    // 是否编辑还是新建
    isEdit: boolean,
    close: () => void,
    success: () => void,

}> = (props) => {
    const { formatMessage } = useLocale();
    const [distros, setDistros] = useState<Array<Distro>>([]);
    const [platform, setPlatform] = useState<Platform | null>(null);
    const [formApi, setFormApi] = useState<FormApi>()
    const [loading, setLoading] = useState(false);
    const [isEdit, setIsEdit] = useState(props.isEdit);
    const [platforms, setIsPlatforms] = useState<Array<Platform>>([]);
    const [install, setInstall] = useState<Install>(props.install);
    const [fileUploading, setFileUploading] = useState<{ [key: string]: boolean }>({});
    const [fileLists, setFileLists] = useState<{ [key: string]: any[] }>({});
    const [isAppstoreDeploy, setIsAppstoreDeploy] = useState(false);
    const rootPath = 'client';




    useEffect(() => {
        setPlatform(props.curPlatform);
        setDistros(props.curPlatform.distros);
        setInstall(props.install);
        setIsEdit(props.isEdit);
        setIsPlatforms(props.platforms);
    }, [props]);


    useEffect(() => {
        if (isEdit && install) {
            let packages = install.packages?.map(pkg => ({
                packageType: pkg.packageType,
                installDistroId: pkg.installDistroId?.toString() || '', // BigInt转字符串
                downloadLink: pkg.downloadLink?.toString() || ''
            })) || []
            formApi?.setValue('packages', packages);
            formApi?.setValue('versionName', install.versionName);
            formApi?.setValue('platformId', install.platformId?.toString());
            formApi?.setValue('forceUpgrade', install.forceUpgrade);
            formApi?.setValue('releaseDate', install.releaseDate?.toDate() || new Date());
            formApi?.setValue('releaseNotes', install.releaseNotes || []);
            handlePlatFormChange(install.platformId.toString());
        } else if (platform) {
            let packages = getPackages(platform, platform.distros);
            formApi?.setValue('packages', packages);
            formApi?.setValue('platformId', platform.platformId?.toString());
            formApi?.setValue('releaseDate', new Date());
            handlePlatFormChange(platform.platformId.toString());
        }

    }, [platform, install]);

    const handleSubmit = () => {
        if (!formApi) {
            return;
        }
        const values: FormFormat = formApi.getValues();

        formApi.validate().then((res) => {
            if (res.hasError) {
                return;
            }

            let packages: Array<Package> = [];
            let installDistroId = '';
            if (platform?.name !== 'MacOS' && platform?.name !== 'Windows') {
                installDistroId = distros[0].distroId + '';
            }

            values.packages.forEach((val, index) => {
                var distroId = installDistroId ? installDistroId : val.installDistroId;

                packages.push(new Package({
                    packageType: val.packageType,
                    installDistroId: BigInt(distroId),
                    downloadLink: val.downloadLink,
                }))
            })

            let releaseDate = values.releaseDate;
            if (releaseDate instanceof Date) {
                // 保持日期，补充当前时分秒
                const now = new Date();
                releaseDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());
            }
            let request = new CreateInstallRequest({
                install: {
                    installId: BigInt(install?.installId || 0),
                    platformId: BigInt(values.platformId),
                    versionName: values.versionName,
                    versionCode: 1,
                    versionType: VersionType.STABLE,
                    releaseDate: Timestamp.fromDate(values.releaseDate),
                    forceUpgrade: values.forceUpgrade,
                    releaseNotes: values.releaseNotes,
                    packages: packages,
                }
            });

            setLoading(true);
            if (isEdit) {
                flylayerClient.updateInstall(request).then(() => {
                    Notification.success({ content: formatMessage({ id: 'settings.client.addInstall.updateSuccess' }), position: "bottomRight" })
                    props.success();
                }, () => {
                    Notification.error({ content: formatMessage({ id: 'settings.client.addInstall.updateFailed' }), position: "bottomRight" })

                }).finally(() => {
                    setLoading(false);
                })
            } else {
                flylayerClient.createInstall(request).then(() => {
                    Notification.success({ content: formatMessage({ id: 'settings.client.addInstall.createSuccess' }), position: "bottomRight" })
                    props.success();
                }, () => {
                    Notification.error({ content: formatMessage({ id: 'settings.client.addInstall.createFailed' }), position: "bottomRight" })

                }).finally(() => {
                    setLoading(false);
                })
            }
        });
    }


    const getPackages = (curPlatform: Platform, curDistros: Distro[]) => {
        let packages: Array<{
            packageType: number;
            installDistroId: string;
            downloadLink: string;
        }> = [];
        if (curPlatform.name == 'MacOS') {
            if (curDistros && curDistros.length > 0) {
                for (let i = 0; i < curDistros.length; i++) {
                    packages.push({
                        packageType: 0,
                        installDistroId: curDistros[i].distroId + '',
                        downloadLink: '',
                    });
                    packages.push({
                        packageType: 2,
                        installDistroId: curDistros[i].distroId + '',
                        downloadLink: '',
                    });
                }
            }

        } else if (curPlatform.name == 'Windows') {
            let installDistroId = '';
            if (curDistros && curDistros.length > 0) {
                curDistros.forEach((val) => {
                    if (val.arch == '386') {
                        installDistroId = val.distroId + '';
                    }
                })
            }
            packages = [{
                packageType: 0,
                installDistroId: installDistroId,
                downloadLink: '',
            }, {
                packageType: 2,
                installDistroId: installDistroId,
                downloadLink: '',
            }]
        } else if (curPlatform.name == 'Android') {
            packages = [{
                packageType: 0,
                installDistroId: '',
                downloadLink: '',
            }]
        } else if (curPlatform.name == 'iOS') {
            packages = [{
                packageType: 0,
                installDistroId: '',
                downloadLink: '',
            }]
        }

         console.log('packages----', packages);
        return packages;

    }


    const handlePlatFormChange = (value: any) => {
        let curPlatform: Platform | null = null;
        let curDistros: Array<Distro> = [];

        for (let i = 0; i < platforms.length; i++) {
            if (platforms[i].platformId == value) {
                curPlatform = platforms[i];
                curDistros = platforms[i].distros;
                setPlatform(platforms[i]);
                setDistros(curPlatform.distros);
                break;
            }
        }
        if (curPlatform && !isEdit) {
            let packages = getPackages(curPlatform, curDistros);
            formApi?.setValue('packages', packages);
        }
    }

    const handleAppstoreDeployChange = (value: any) => {
        console.log('handleAppstoreDeployChange', value);
        setIsAppstoreDeploy(value);
    }


    const validateForm = (values: FormFormat) => {

        let errors: any = {
            packages: [],
        }
        if (!values.versionName) {
            errors.versionName = formatMessage({ id: 'settings.client.addInstall.validation.versionNameRequired' })
        } else if (!/^\d+\.\d+\.\d+$/.test(values.versionName)) {
            errors.versionName = formatMessage({ id: 'settings.client.addInstall.validation.versionNameFormat' })
        } else {
            // 校验版本号是否已存在
            if (platform && props.mapInstalls) {
                const key = platform.platformId?.toString();
                const installs = props.mapInstalls.get(key) || [];
                const exist = installs.some(item => item.versionName === values.versionName);
                if (exist && !isEdit) {
                    errors.versionName = formatMessage({ id: 'settings.client.addInstall.validation.versionExists' });
                }
            }
        }
        if (!values.platformId) {
            errors.platformId = formatMessage({ id: 'settings.client.addInstall.validation.platformRequired' })
        }
        if (!values.releaseDate) {
            errors.releaseDate = formatMessage({ id: 'settings.client.addInstall.validation.releaseDateRequired' })
        }

        if (!values.releaseNotes || values.releaseNotes.length == 0) {
            errors.releaseNotes = formatMessage({ id: 'settings.client.addInstall.validation.releaseNotesRequired' })
        }
        if (values.packages.length > 0) {

            values.packages.forEach((val, index) => {
                if (platform) {
                    if (platform.name == 'MacOS') {
                        if (!val.installDistroId) {
                            if (errors.packages[index] == undefined) {
                                errors.packages[index] = { installDistroId: formatMessage({ id: 'settings.client.addInstall.validation.distroRequired' }) }
                            } else {
                                errors.packages[index].installDistroId = formatMessage({ id: 'settings.client.addInstall.validation.distroRequired' })
                            }
                        }
                    }
                }
                if (!val.downloadLink) {
                    if (errors.packages[index] == undefined) {
                        errors.packages[index] = { downloadLink: formatMessage({ id: 'settings.client.addInstall.validation.downloadLinkRequired' }) }
                    } else {
                        errors.packages[index].downloadLink = formatMessage({ id: 'settings.client.addInstall.validation.downloadLinkRequired' })
                    }
                }

            })
        }
        if (errors.packages.length == 0) {
            delete errors.packages;
        }
        if (Object.keys(errors).length > 0) {
            return errors;
        }
        else {
            return ''
        }

    }


    const handleFileSelect = async (key: string, files: File[]) => {
        // 清空全部文件传输列表
        setFileLists({});
    };
    const handleChange = (key: string, field: string, info: any, isIntall: boolean) => {
        const filteredFileList = info.fileList.filter((file: any) => 
            !file.name?.startsWith('.')
        );
        setFileLists(prev => ({
            ...prev,
            [key]: filteredFileList // 使用过滤后的文件列表
        }));

        if (isIntall) {
            // 上传安装包逻辑
            if (info.currentFile && info.currentFile.status === 'success') {
                // 这里需要单独处理iOS的 downloadLink 逻辑 
                let accessUrl = '';
                if (platform?.name == 'iOS') {
                    accessUrl = `itms-services://?action=download-manifest&url=${info.currentFile.response.accessUrl}`;
                } else {
                    accessUrl = info.currentFile.response.accessUrl;
                }
                Notification.success({ content: formatMessage({ id: 'settings.client.addInstall.upload.installerSuccess' }), position: "bottomRight" });
                formApi?.setValue(`${field}[downloadLink]`, accessUrl);
                setFileLists({});
                setFileUploading(prev => ({ ...prev, [key]: false }));
            }
        } else {
            // 上传升级包逻辑
            const allDone = info.fileList.length > 0 && info.fileList.every((file: any) =>  file.name?.startsWith('.') || file.status === 'success');
            if (allDone) {
                // 这里需要单独处理iOS的 downloadLink 逻辑 
                Notification.success({ content: formatMessage({ id: 'settings.client.addInstall.upload.upgradeSuccess' }), position: "bottomRight" });
                const accessUrl = info.currentFile.response.accessUrl;
                // 从accessUrl 截取文件夹路径
                const folderPath = accessUrl.substring(0, accessUrl.lastIndexOf('/'));
                // 路径最后加上 / 
                formApi?.setValue(`${field}[downloadLink]`, folderPath + '/');
                setFileLists({});
                setFileUploading(prev => ({ ...prev, [key]: false }));
            }
        }
    }

    const checkFileType = (file: any, isIntall: boolean): boolean => {
        if (file.name.startsWith('.')) {
            return true;
        }
        switch (platform?.name) {
            case "MacOS":
                if (isIntall) {
                    return file.name.endsWith('.pkg');
                } else {
                    return file.name.endsWith('.zip') || file.name.endsWith('.Blockmap') || file.name.endsWith('.yml');
                }

            case 'Windows':
                if (isIntall) {
                    return file.name.endsWith('.exe');
                } else {
                    return file.name.endsWith('.exe') || file.name.endsWith('.Blockmap') || file.name.endsWith('.yml');
                }
            case 'Android':
                return file.name.endsWith('.apk');
            case 'iOS':
                return file.name.endsWith('.plist');
        }
        return false;
    }

    const getTooltipContent = (isIntall: boolean, distroId: string): string => {
        let curDistro = distros.find(d => d.distroId.toString() == distroId);
        if (isIntall) {
            switch (platform?.name) {
                case "MacOS":
                    return formatMessage({ id: 'settings.client.addInstall.upload.macosInstallerFolder' }).replace('{arch}', curDistro?.arch || '');
                case 'Windows':
                    return formatMessage({ id: 'settings.client.addInstall.upload.windowsInstallerFolder' });
                case 'Android':
                    return formatMessage({ id: 'settings.client.addInstall.upload.androidInstallerFolder' });
                case 'iOS':
                    return formatMessage({ id: 'settings.client.addInstall.upload.iosInstallerFolder' });
            }
        } else {
            switch (platform?.name) {
                case "MacOS":
                    return formatMessage({ id: 'settings.client.addInstall.upload.macosUpgradeFolder' }).replace('{arch}', curDistro?.arch || '');
                case 'Windows':
                    return formatMessage({ id: 'settings.client.addInstall.upload.windowsUpgradeFolder' });
                case 'Android':
                    return formatMessage({ id: 'settings.client.addInstall.upload.androidUpgradeFolder' });
                case 'iOS':
                    return formatMessage({ id: 'settings.client.addInstall.upload.iosUpgradeFolder' });
            };
        }
        return formatMessage({ id: 'settings.client.addInstall.upload.defaultTip' });
    }

    const renderUpload = (field: any, key: any, index: number) => {
        const packageTypeValue = formApi?.getValue(`${field}[packageType]`);
        const installDistroIdValue = formApi?.getValue(`${field}[installDistroId]`);
        const isIntall = packageTypeValue === 0;
        const versionNameValue = formApi?.getValue('versionName');
        const isError = formApi?.getError(`${field}[downloadLink]`) !== undefined;
        const marginBottomNew = isError ? '24px' : '0px';

        return <>
            <Upload
                beforeUpload={(prop) => {
                    const file = prop.file;
                    if (file.name.startsWith('.')) {
                        return false;
                    }
                    if (!file.fileInstance) {
                        setFileLists({});
                        Notification.error({ content: formatMessage({ id: 'settings.client.addInstall.upload.invalidFormat' }), position: "bottomRight" });
                        return false;
                    }
                    if (!checkFileType(file, isIntall)) {
                        Notification.error({ content: formatMessage({ id: 'settings.client.addInstall.upload.invalidFormat' }), position: "bottomRight" });
                        setFileLists({});
                        return false;
                    }
                    return true;
                }}
                onFileChange={files => handleFileSelect(key, files)}
                customRequest={(options) => {
                    let ext = options.file.name.split('.').pop();
                    if (!ext) return;
                    setFileUploading(prev => ({ ...prev, [key]: true }));
                    let uploadPath = rootPath;
                    let uploadName = options.file.name;
                    if (!isIntall) {
                        if (versionNameValue) {
                            uploadPath = `${rootPath}/${versionNameValue}/${installDistroIdValue}`;
                        } else {
                            uploadPath = `${rootPath}/${installDistroIdValue}`;
                        }
                    }
                    flylayerClient.getUploadUrlWithPathName({ path: uploadPath, name: uploadName }).then((res) => {
                        uploadFileWithProgress(
                            res.uploadUrl,
                            options.fileInstance,
                            (total, loaded) => {
                                options.onProgress && options.onProgress({ total, loaded });
                            }
                        ).then(() => {
                            options.onSuccess(res);
                        }).catch((e) => {
                            options.onError(e);
                        }).finally(() => {

                        });
                    }).catch((e) => {
                        options.onError(e);
                    });
                }}
                onChange={info => handleChange(key, field, info, isIntall)}
                action={""}
                directory
                showUploadList={false}
                disabled={fileUploading[key]}
                onError={() => {
                    Notification.error({ content: formatMessage({ id: 'settings.client.addInstall.upload.failed' }), position: "bottomRight" });
                    setFileLists({});
                    setFileUploading(prev => ({ ...prev, [key]: false }));
                }}

            >

                <Tooltip motion content={getTooltipContent(isIntall, installDistroIdValue)}>
                    <Button
                        icon={<IconUpload />}
                        theme="light"
                        block
                        style={{ width: '100%', marginBottom: marginBottomNew }}
                    >
                        {formatMessage({ id: 'settings.client.addInstall.upload.clickToUpload' })}
                    </Button>
                </Tooltip>
                <Avatar size='small' shape="square" style={{ width:40, marginBottom: marginBottomNew, marginLeft: "8px" }}>
                    <div className={styles.uploadMask}>
                        {fileUploading[key] ? <Spin /> : <IconCloud size='large' />}
                    </div>
                </Avatar>
            </Upload>
        </>

    }

    return <>
        <Modal
            width={900}
            title={isEdit ? formatMessage({ id: 'settings.client.addInstall.editTitle' }) : formatMessage({ id: 'settings.client.addInstall.createTitle' })}
            visible={true}
            maskClosable={false}
            closeOnEsc={true}
            okText={formatMessage({ id: 'components.common.confirm' })}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            okButtonProps={{ loading }}

        >
            <div className={styles.addRoute}>
                <Form getFormApi={setFormApi} validateFields={validateForm}
                >
                    <Row>
                        <Col span={12}>
                            <Form.Input field='versionName' style={{ width: 200 }} maxLength={64} label={formatMessage({ id: 'settings.client.addInstall.form.versionName' })} required disabled={isEdit}></Form.Input>
                        </Col>
                        <Col span={12}><Form.Select
                            initValue={platform ? platform.platformId?.toString() : ''}
                            disabled={isEdit}
                            aria-required
                            field='platformId'
                            trigger='blur'
                            style={{ width: 200 }}
                            label={formatMessage({ id: 'settings.client.addInstall.form.platform' })}
                            placeholder={formatMessage({ id: 'settings.client.addInstall.form.platformPlaceholder' })}
                            onChange={handlePlatFormChange}
                        >
                            {platforms.map((platform) => {
                                return <Form.Select.Option key={platform.platformId + ''} value={platform.platformId + ''}>{platform.name}</Form.Select.Option>
                            })}
                        </Form.Select>
                        </Col>
                    </Row>
                    <Row>

                        <Col span={12}>
                            <Form.DatePicker style={{ width: 200 }} field='releaseDate' defaultPickerValue={[new Date('2022-08-08 00:00'), new Date('2022-08-09 12:00')]} trigger='blur' className='mb20' label={formatMessage({ id: 'settings.client.addInstall.form.releaseDate' })}></Form.DatePicker>
                            {platform?.name === 'iOS' && (
                                <Form.Switch
                                    field='appstore'
                                    label={formatMessage({ id: 'settings.client.addInstall.form.appstoreRelease' })}
                                    onChange={handleAppstoreDeployChange}
                                />
                        )}
                        </Col>
                        <Col span={12}><Form.Switch field='forceUpgrade' label={formatMessage({ id: 'settings.client.addInstall.form.forceUpgrade' })}></Form.Switch>
                       
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Form.TagInput addOnBlur field='releaseNotes' label={formatMessage({ id: 'settings.client.addInstall.form.releaseNotes' })}></Form.TagInput>
                        </Col>
                    </Row>


                    {platform &&
                        <ArrayField field='packages'>
                            {({ arrayFields }) => (
                                <>
                                    <Row>
                                        <Col span={4}><Text type='tertiary'>{formatMessage({ id: 'settings.client.addInstall.form.packageType' })}</Text></Col>
                                        {platform?.name == 'MacOS' && <Col span={6}><Text type='tertiary'>{formatMessage({ id: 'settings.client.addInstall.form.chipType' })}</Text></Col>}
                                        <Col span={12}><Text type='tertiary'>{formatMessage({ id: 'settings.client.addInstall.form.address' })}</Text></Col>
                                        <Col></Col>
                                    </Row>
                                    {arrayFields.map(({ field, key, remove }, i) => (
                                        <Row className={styles.tableBody} key={key}>
                                            <Col xs={24} sm={4}>

                                                <Form.Select noLabel style={{ width: '100%' }} field={`${field}[packageType]`} showArrow={false} disabled>
                                                    <Form.Select.Option value={0}>{formatMessage({ id: 'settings.client.addInstall.packageType.installer' })}</Form.Select.Option>
                                                    {/* <Form.Select.Option value={1}>{formatMessage({ id: 'settings.client.addInstall.packageType.incremental' })}</Form.Select.Option> */}
                                                    <Form.Select.Option value={2}>{formatMessage({ id: 'settings.client.addInstall.packageType.upgrade' })}</Form.Select.Option>
                                                </Form.Select></Col>

                                            {platform?.name == 'MacOS' &&
                                                <Col xs={24} sm={4}>
                                                    <Form.Select style={{ width: '100%' }} field={`${field}[installDistroId]`} noLabel showArrow={false} disabled >
                                                        {distros.map((val, index) => {
                                                            return <Form.Select.Option key={val.distroId?.toString()} value={val.distroId + ''}>{val.arch}</Form.Select.Option>
                                                        })}
                                                    </Form.Select>
                                                </Col>
                                            }

                                            <Col xs={24} sm={16}>
                                                <Space wrap>
                                                    {
                                                        formApi?.getValue(`${field}[downloadLink]`)
                                                            ? (
                                                                <Tooltip motion content={formApi?.getValue(`${field}[downloadLink]`)}>
                                                                    <Form.Input
                                                                        field={`${field}[downloadLink]`}
                                                                        noLabel
                                                                        disabled={!isAppstoreDeploy}
                                                                        style={{ width: '100%' }}
                                                                    />
                                                                </Tooltip>
                                                            )
                                                            : (
                                                                <Form.Input
                                                                    field={`${field}[downloadLink]`}
                                                                    noLabel
                                                                    disabled={!isAppstoreDeploy}
                                                                    style={{ width: '100%' }}
                                                                />
                                                            )
                                                    }
                                                    {!isAppstoreDeploy && renderUpload(field, key, i)}
                                                </Space>
                                            </Col>

                                        </Row>

                                    ))
                                    }
                                </>
                            )}
                        </ArrayField>}

                </Form>
            </div>
        </Modal>

    </>
}

export default Index