

.layout {
    min-height: calc(100vh - 60px);
}
.sider {
    padding-top: 12px;
    min-height: calc(100vh - 60px);
    width: 241px;
    border-right: 1px solid var(--semi-color-border);
    >div {
        width: 241px;
    }
}
.content {
}
.nav {
    border: none;
}

.collapse {
    >div {
        border-bottom: none !important;
        margin-bottom: 8px!important;
    }
}

.collapsePanel {
    div[class="semi-collapse-content"] {
        padding: 4px 8px 8px 8px!important;
    }
    div[role="button"] {
        margin-bottom: 8px!important;
    }
}

.list {
    
    ul {
        li {
            border-bottom: none!important;
            padding: 8px 12px !important;
            margin-bottom: 8px!important;
            cursor: pointer;
            &:hover {
                background-color: var(--semi-color-fill-0) !important;
            }
            &:active {
                background-color: var(--semi-color-fill-1) !important;
            }
            text-indent: 28px!important;
        }
    }
}

.active {
    background-color: var(--semi-color-fill-1) !important;
}