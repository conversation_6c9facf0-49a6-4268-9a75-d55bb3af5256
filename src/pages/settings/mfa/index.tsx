import React, { useState, useContext, useEffect } from 'react'
import { Typo<PERSON>, Row, Switch, Col, Divider } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { saveAccountManualCreate, getFlynet, setAccountMfaEnabled } from '@/services/flynet';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;
import styles from './index.module.scss'

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();


    const [mfaEnabled, setMfaEnabled] = useState(false);
    const [mfaEnabledLoading, setMfaEnabledLoading] = useState(false);

    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {
            setFlynet(res.flynet)

            if (res.flynet) {
                setMfaEnabled(res.flynet.mfaEnabled);
            }
        }
    }

    const handleMfaEnabledChange = (checked: boolean) => {

        if (!flynet) {
            return;
        }
        setMfaEnabledLoading(true)
        setAccountMfaEnabled(flynet.id, checked).then(() => {
            setMfaEnabled(checked)
            setFlynet(Object.assign({}, flynet, { mfaEnabled: checked }))
        }).finally(() => setMfaEnabledLoading(false))
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    



    return <><div className='settings-page'>
        <Title heading={3} className='mb10' >{formatMessage({ id: 'settings.mfa.title' })}</Title>
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'settings.mfa.description' })}</Paragraph>


        <Title heading={4} className="mb2">{formatMessage({ id: 'settings.mfa.sectionTitle' })}</Title>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'settings.mfa.sectionDescription' })}</Paragraph>
        <Row className='mb40'>
            <Col span={4}><div className={styles.colFormItem}>
                <Switch
                    checked={mfaEnabled}
                    loading={mfaEnabledLoading}
                    onChange={handleMfaEnabledChange}
                /></div></Col>
            <Col span={20}><Paragraph>{formatMessage({ id: 'settings.mfa.enableMfa' })}</Paragraph></Col>
        </Row>

           <div style={{height:40}}></div>
    </div></>
}

export default Index