import React, { useEffect, useRef, useContext } from 'react'
import ReactECharts from 'echarts-for-react';
import styles from './index.module.scss'
import { Typography, Row, Col, Select } from '@douyinfe/semi-ui';
import { getEchartsTheme } from '@/utils/common';
import moment from 'moment';
import EChartsReact from 'echarts-for-react';
import { Matrix, MetricValue } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { formatByteSize, formatByteSizeYAxis } from '@/utils/format';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { LocaleFormatter, useLocale } from '@/locales';



const { Title } = Typography;

// 清理合并单个数据(时间相同的数据)
const cleanMatrix = (mat: Matrix) => {

  const device = mat.metric.device
  let map: Map<string, string> = new Map();
  let values: MetricValue[] = [];

  mat.values.forEach(val => {
    const time = val.time;
    const key = device + '-' + time;
    if (!map.has(key)) {
      values.push(val)
      map.set(key, key)
    }
  })

  mat.values = values;
  return mat;
}


// 合并数据 （单个节点时间相同数据）
const combineMatrixSingle = (matrix: Matrix) => {
  let mapTime: Map<number, number> = new Map();
  let timeList: Array<number> = [];
  let newMatrix = cleanMatrix(matrix);
  newMatrix.values.forEach(val => {
    if (!mapTime.has(val.time)) {
      timeList.push(val.time);
    }
    let existVal = mapTime.get(val.time) || 0;

    mapTime.set(val.time, existVal + Number(val.value));
  })

  timeList.sort();

  let values: MetricValue[] = []
  timeList.forEach(time => {
    values.push(new MetricValue({
      time: time,
      value: mapTime.get(time) + '' || '0'
    }))
  })
  newMatrix.values = values;
  return newMatrix
}

export const getDisplayTraffic = (recvTop: number, transTop: number, recvTotal: number, transTotal: number, formatMessage?: (descriptor: { id: string }) => string) => {
  if (formatMessage) {
    return `${formatMessage({ id: 'overview.traffic.upPeak' })}: ${formatByteSize(recvTop)}b/s、
        ${formatMessage({ id: 'overview.traffic.downPeak' })}: ${formatByteSize(transTop)}b/s、
        ${formatMessage({ id: 'overview.traffic.upTotal' })}: ${formatByteSize(recvTotal)}B、${formatMessage({ id: 'overview.traffic.downTotal' })}: ${formatByteSize(transTotal)}B
        `
  }
  return `上行流量峰值： ${formatByteSize(recvTop)}b/s、
      下行流量峰值： ${formatByteSize(transTop)}b/s、
      上行流量总计：${formatByteSize(recvTotal)}B、下行流量总计：${formatByteSize(transTotal)}B
      `
}

export const getTrafficOpt = (srcTransMatrix: Matrix[], srcRecvMatrix: Matrix[], formatMessage?: (descriptor: { id: string }) => string) => {
  let transTop = 0, recvTop = 0;
  const formatTransTimeList: string[] = [];
  const formatRecvTimeList: string[] = [];
  const seriesList: any = [];
  const legendData: any = [];

  let transTimeList: number[] = [];
  let recvTimeList: number[] = [];

  srcRecvMatrix.forEach((matrix, _index) => {
    let mat = combineMatrixSingle(matrix);
    const map: Map<string, {
      time: number,
      trans: number,
      recv: number,
      device: string,
      instance: string,
    }> = new Map();

    const data: number[] = [];
    const device = matrix.metric.device ? matrix.metric.device : matrix.metric.instance;
    const instance = mat.metric.instance
    mat.values.forEach(val => {
      const time = val.time;
      if (!recvTimeList.includes(time)) {
        recvTimeList.push(time)
      }
      const value = Number(val.value) * 8;
      if (value > recvTop) {
        recvTop = value
      }
      const key = device + '-' + time
      if (!map.has(key)) {
        map.set(key, {
          time: time,
          trans: 0,
          recv: Number(value),
          device: device,
          instance
        })

        data.push(value / 1048576)

      } else {
        let item = map.get(key);
        if (item) {
          item.recv = Number(value);
          map.set(key, item)
        }
      }
    })

    const receiveName = formatMessage ? `${formatMessage({ id: 'overview.traffic.receive' })} ${device}` : `接收 ${device}`;
    legendData.push(receiveName);
    seriesList.push({
      name: receiveName,
      type: 'line',
      symbol: 'none',
      smooth: true,
      xAxisIndex: 0,
      yAxisIndex: 0,
      data: data,
      lineStyle: {
        width: 1//设置线条粗细
      },
      areaStyle: {},

    })
  })

  srcTransMatrix.forEach((matrix, _index) => {
    let mat = combineMatrixSingle(matrix);
    const map: Map<string, {
      time: number,
      trans: number,
      recv: number,
      device: string,
      instance: string,
    }> = new Map();

    const data: number[] = [];
    const device = matrix.metric.device ? matrix.metric.device : matrix.metric.instance;
    const instance = mat.metric.instance

    mat.values.forEach(val => {

      const time = val.time;
      if (!transTimeList.includes(time)) {
        transTimeList.push(time)
      }
      const value = Number(val.value) * 8;
      if (value > transTop) {
        transTop = value
      }
      const key = device + '-' + time;
      if (!map.has(key)) {
        map.set(key, {
          time: time,
          trans: Number(value),
          recv: 0,
          device: device,
          instance
        })
        // 转换单位
        data.push(value / 1048576)

      }

    })

    const sendName = formatMessage ? `${formatMessage({ id: 'overview.traffic.send' })} ${device}` : `发送 ${device}`;
    legendData.push(sendName);
    seriesList.push({
      name: sendName,
      type: 'line',
      symbol: 'none',
      smooth: true,
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: data,
      lineStyle: {
        width: 1//设置线条粗细
      },
      areaStyle: {},
    })

  });


  transTimeList.forEach(time => {
    formatTransTimeList.push(moment(time * 1000).format('MM-DD HH:mm'));
  })


  recvTimeList.forEach(time => {
    formatRecvTimeList.push(moment(time * 1000).format('MM-DD HH:mm'));
  })
  const option = {

    // color: ['rgb(29,33,41)', 'rgb(78,89,105)', 'rgb(134,144,156)', 'rgb(39,46,59)', 'rgb(107,119,133)', 'rgb(169,174,184)'],
    legend: {
      data: legendData,
      
      bottom: 0
    },

    axisPointer: {
      link: [
        {
          xAxisIndex: 'all'
        }
      ]
    },
    dataZoom: [

      {
        type: 'inside',
        realtime: true,
        xAxisIndex: [0, 1]
      }
    ],
    grid: [
      {
        left: 100,
        right: 30,
        top: 80,
        height: '38%'
      },
      {
        left: 100,
        right: 30,
        top: 314,
        height: '38%'
      }
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        animation: false
      },
      formatter: (params: Array<{
        componentType: 'series',
        // 系列类型
        seriesType: string,
        // 系列在传入的 option.series 中的 index
        seriesIndex: number,
        // 系列名称
        seriesName: string,
        // 数据名，类目名
        name: string,
        // 数据在传入的 data 数组中的 index
        dataIndex: number,
        // 传入的原始数据项
        data: number,
        // 传入的数据值。在多数系列下它和 data 相同。在一些系列下是 data 中的分量（如 map、radar 中）
        value: number,
        // 坐标轴 encode 映射信息，
        // key 为坐标轴（如 'x' 'y' 'radius' 'angle' 等）
        // value 必然为数组，不会为 null/undefied，表示 dimension index 。
        // 其内容如：
        // {
        //     x: [2] // dimension index 为 2 的数据映射到 x 轴
        //     y: [0] // dimension index 为 0 的数据映射到 y 轴
        // }
        encode: object,
        // 维度名列表
        dimensionNames: Array<string>,

        // 数据图形的颜色
        color: string,
        axisIndex: number,

      }>, _ticket: string) => {
        if (params.length === 0) {
          return ''
        }
        let date = params[0].name;
        let axisIndex = params[0].axisIndex;
        let div = document.createElement('div');
        div.appendChild(document.createTextNode(date));

        const addItem = (div: HTMLDivElement, name: string, value: number, color: string) => {

          let p = document.createElement('p');
          p.style.margin = '0';
          let span = document.createElement('span');
          span.style.display = 'inline-block';
          span.style.width = '10px';
          span.style.height = '10px';
          span.style.borderRadius = '50%';
          span.style.marginRight = '5px';

          span.style.backgroundColor = color;
          p.appendChild(span);
          p.innerHTML += name + ' ' + formatByteSize(value * 1048576) + 'b/s';
          div.appendChild(p);
        }
        if (axisIndex == 0) {

          params.forEach((item, _index) => {
            addItem(div, item.seriesName, item.value, item.color);
          })
        } else {
          for (let i = 0; i < params.length; i++) {
            let item = params[params.length - i - 1];
            addItem(div, item.seriesName, item.value, item.color);

          }
        }
        return div.outerHTML
      },
      position: function (pos: any, _params: any, _dom: any, _rect: any, size: any) {
        // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
        var obj: any = { top: 60 };
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 5;
        return obj;
        // 固定在顶部
        // return [pos[0], '10%'];
      }
    },

    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisLine: { onZero: true },
        data: formatTransTimeList,
        show: false
      },
      {
        gridIndex: 1,
        type: 'category',
        boundaryGap: false,
        axisLine: { onZero: true },
        data: formatRecvTimeList,

        // position: 'top',
      }
    ],
    yAxis: [
      {
        name: '',
        // name: 'b/s',
        type: 'value',
        max: (recvTop > transTop ? recvTop : transTop) / 1048576 * 1.1,
        axisLabel: {
          formatter: (value: number,) => {
            if (value == 0) {
              return '0'
            }
            return formatByteSizeYAxis(value) + 'b/s';
          }
        },
      },
      {
        gridIndex: 1,
        name: '',
        type: 'value',
        inverse: true,
        max: (recvTop > transTop ? recvTop : transTop) / 1048576 * 1.1,
        axisLabel: {
          formatter: (value: number,) => {
            if (value == 0) {
              return '0'
            }
            return formatByteSizeYAxis(value) + 'b/s';
          }
        }
      }
    ],
    series: seriesList
  };

  return {
    option,
    transTop,
    recvTop
  };
}

const getTotal = (matrixList: Matrix[]) => {
  let total = 0;
  matrixList.forEach(mat => {

    if (mat.values.length > 1) {
      let minValue = 0;
      let maxValue = 0;
      for (let i = 0; i < mat.values.length; i++) {
        if (i == 0) {
          minValue = Number(mat.values[i].value)
          maxValue = Number(mat.values[i].value)
          continue;
        }

        const val = Number(mat.values[i].value)
        // 重新开始一段
        if (val < maxValue) {
          total += maxValue - minValue;
          minValue = val;
          maxValue = val;
          // if (i == mat.values.length - 1) {
          //   total += val;
          // }
        } else {
          maxValue = val;
          if (i == mat.values.length - 1) {

            total += maxValue - minValue;
          }
        }


      }
    }
  })
  return total;
}

export const getTrafficTotal = (srcTransMatrix: Matrix[], srcRecvMatrix: Matrix[]) => {
  let transTotal = 0;
  let recvTotal = 0;

  transTotal = getTotal(srcTransMatrix);

  recvTotal = getTotal(srcRecvMatrix);
  return { transTotal, recvTotal }
}

const Index: React.FC<{
  trafficTransMatrix: Matrix[];
  trafficRecvMatrix: Matrix[];
  transTotal: number;
  recvTotal: number;
  refEcharts: React.MutableRefObject<EChartsReact | null>
  refTime: React.MutableRefObject<number>
  refTop: React.MutableRefObject<any>
  handleRangeChange: (val: number) => void
}> = (props) => {
  const globalTheme = useContext(GlobalThemeContext);
  const { formatMessage } = useLocale();
  const rootRef = useRef<EChartsReact>(null)
  const topRef = useRef<HTMLParagraphElement>(null);
  useEffect(() => {
    if (rootRef === null || rootRef.current === null) {
      return
    }
    props.refEcharts.current = rootRef.current;
  }, [props.refEcharts])

  useEffect(() => {
    if (topRef === null || topRef.current === null) {
      return
    }
    props.refTop.current = topRef.current;
  }, [props.refTop])
  const { option,
    transTop,
    recvTop } = getTrafficOpt(props.trafficTransMatrix, props.trafficRecvMatrix, formatMessage)


  return <><div className={styles.chartPanel}>
    <Row className={styles.title}>
      <Col xs={24} sm={18} style={{ display: 'flex', alignItems: 'center' }} className='traffic-title'>
        <Title heading={5}><LocaleFormatter id="overview.traffic.title" /></Title>
        <p ref={topRef} style={{ paddingLeft: 10, paddingTop: 3, color: 'var(--semi-color-text-2)', fontSize: '12px' }}>
          {getDisplayTraffic(recvTop, transTop, props.recvTotal, props.transTotal, formatMessage)}
        </p>
      </Col>
      <Col xs={24} sm={6} style={{ display: 'flex', justifyContent: 'right' }}>
        <Select defaultValue={6} onChange={(val) => props.handleRangeChange(val as any)}>
          <Select.Option value={6}><LocaleFormatter id="overview.traffic.recent6h" /></Select.Option>
          <Select.Option value={12}><LocaleFormatter id="overview.traffic.recent12h" /></Select.Option>
          <Select.Option value={24}><LocaleFormatter id="overview.traffic.recent24h" /></Select.Option>
          <Select.Option value={3 * 24}><LocaleFormatter id="overview.traffic.recent3d" /></Select.Option>
          <Select.Option value={7 * 24}><LocaleFormatter id="overview.traffic.recent7d" /></Select.Option>
        </Select>
      </Col>
    </Row>
    <ReactECharts
      style={{ height: '618px' }}
      option={option}
      ref={props.refEcharts}
      theme={getEchartsTheme(globalTheme)}
      onEvents={{
        'click': () => {
        }
      }}

    /></div>


  </>

}

export default Index