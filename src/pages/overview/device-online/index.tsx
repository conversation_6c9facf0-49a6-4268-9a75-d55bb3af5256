import React, { useEffect, useRef, useContext } from 'react'
import styles from './index.module.scss'
import ReactECharts from 'echarts-for-react';
import { Typography, Row, Col, Select } from '@douyinfe/semi-ui';
import { Matrix } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { getEchartsTheme } from '@/utils/common';
import { LocaleFormatter, useLocale } from '@/locales';
import EChartsReact from 'echarts-for-react';

import moment from 'moment';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
const { Title } = Typography;

export const getDataFromMatris = (machineOnlineMatrix: Matrix[], formatMessage?: (descriptor: { id: string }) => string) => {
    let xAxis: Array<string> = [];
    let series: Array<string> = [];
    if(machineOnlineMatrix.length > 0) {
        for(let i = 0; i < machineOnlineMatrix[0].values.length; i ++) {
            let item = machineOnlineMatrix[0].values[i];
            const {time} = item;
            xAxis.push(moment(time*1000).format('MM-DD HH:mm'));
            series.push(item.value);
        }
    }
    let opt = {
        // color: ['#86909c', '#a9aeb8'],
        title: {
            text: ''
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                
                label: {
                    // backgroundColor: '#6a7985'
                }
            }
        },
        dataZoom: [
          {
            type: 'inside',
            realtime: true,
            xAxisIndex: [0, 1]
          }
        ],
        // legend: {
        //     data: ['设备连接数'],
        //     bottom: 0
        // },
        grid: {
            left: '20',
            right: '20',
            bottom: '4%',
            top: '14%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: xAxis
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: [
            {
                name: formatMessage ? formatMessage({ id: 'overview.deviceOnline.deviceCount' }) : '设备连接数',
                type: 'line',
                areaStyle: {},
                emphasis: {
                    focus: 'series'
                },
                data: series
            },
            
        ]
    }

    return opt;
};

const Index: React.FC<{
    machineOnlineMatrix: Matrix[];
    refEcharts: React.MutableRefObject<EChartsReact | null>
    refTime: React.MutableRefObject<number>
    handleRangeChange: (val:number)=>void
}> = (props) => {
    const rootRef = useRef<EChartsReact>(null)
    const { formatMessage } = useLocale();

    const globalTheme = useContext(GlobalThemeContext);

    // const timeRef = useRef<number>(6);

    useEffect(() => {
        if (rootRef === null || rootRef.current === null) {
            return
        }
        props.refEcharts.current = rootRef.current;
    }, [props.refEcharts])

    const opt = getDataFromMatris(props.machineOnlineMatrix, formatMessage);
    return <div className={styles.chartPanel}><Row className={styles.title}>
        <Col span={12} style={{ display: 'flex', alignItems: 'center', height: 32, paddingLeft: 16 }}>
            <Title heading={5}><LocaleFormatter id="overview.deviceOnline.title" /></Title>
        </Col>
        <Col span={12} style={{ display: 'flex', justifyContent: 'right' }}>
        <Select defaultValue={6} onChange={(val)=>props.handleRangeChange(val as any)}>
            <Select.Option value={6}><LocaleFormatter id="overview.deviceOnline.recent6h" /></Select.Option>
            <Select.Option value={12}><LocaleFormatter id="overview.deviceOnline.recent12h" /></Select.Option>
            <Select.Option value={24}><LocaleFormatter id="overview.deviceOnline.recent24h" /></Select.Option>
            <Select.Option value={3*24}><LocaleFormatter id="overview.deviceOnline.recent3d" /></Select.Option>
            <Select.Option value={7*24}><LocaleFormatter id="overview.deviceOnline.recent7d" /></Select.Option>
        </Select>
        </Col>
    </Row>
        <ReactECharts
            theme={getEchartsTheme(globalTheme)}
            ref={rootRef}
            style={{ height: '600px' }}
            option={opt}></ReactECharts></div>
}

export default Index;