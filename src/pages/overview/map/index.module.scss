.map {
    position: relative;
    
    .loading {
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--semi-color-fill-2);
        z-index: 1;
        display: flex;
        justify-content: center;
        border-radius: 3px;
    }
    .generalInfo {
        position: absolute;
        top: 58px;
        left: 20px;
        border-radius: 3px;
        background-color: rgba(var(--semi-gray-6), 0.2);
        opacity: 0.9;
        padding: 10px;
        td, th {
            padding-bottom: 2px;
        }
        td:last-of-type {
            padding-right: 10px;
            text-align: right;
        }
    }
    .title {
        top: 20px;
        position: absolute;
        z-index: 1;
        margin: auto;
        width: 100%;
        text-align: center;
        height: 32px;
        line-height: 32px;
    }
    .topInfo {
        position: absolute;
        font-size: 10px;
        bottom: 18px;
        right: 20px;
        border-radius: 3px;
        background-color: rgba(var(--semi-gray-4), 0.2);
        opacity: 0.9;
        padding: 0 0 15px 0;
        transition-duration: 1s;
        transition-property: all;
        
        td, th {
            padding-bottom: 0;
        }
        .topInfoTitle {

            width: 180px;
            padding: 2px 10px 2px 10px;
            margin: 10px 10px 0 10px;
            background-color: var(--semi-color-fill-1);
            
        }
        .topInfoContent {
            width: 180px;
            padding: 2px 10px;
            margin: 0 10px 0 10px;
        }
        .topInfoVal {

            text-align: right;
        }
    }
}