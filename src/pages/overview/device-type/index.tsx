import React, { useEffect, useRef, useContext } from 'react'
import ReactECharts from 'echarts-for-react';
import styles from './index.module.scss'
import EChartsReact from 'echarts-for-react';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { getEchartsTheme } from '@/utils/common';
import { useLocale } from '@/locales';
export function getDeviceTypeOpt(machineStatsByClientType: { [key: string]: number }, formatMessage?: (descriptor: { id: string }) => string) {
  const data: Array<{ name: string, value: number }> = [];
  machineStatsByClientType && Object.keys(machineStatsByClientType).sort().forEach((key) => {
    if(key) {
      data.push({
        name: key,
        value: machineStatsByClientType[key]
      })
    }
  })
  return {
    // color: ['rgb(78,89,105)', 'rgb(107,119,133)', 'rgb(134,144,156)', 'rgb(169,174,184)', 'rgb(201,205,212)', 'rgb(229,230,235)'],
    title: {
      text: formatMessage ? formatMessage({ id: 'overview.deviceType.title' }) : '设备类型分布',
      // subtext: '接入设备的类型',
      left: 'center',
      top: '3%',
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: '3%',
      top: '3%',
    },
    series: [
      {
        name: formatMessage ? formatMessage({ id: 'overview.deviceType.name' }) : '设备类型',
        type: 'pie',
        radius: '50%',
        label: {
          show: true,
          formatter(param: any) {
            // correct the percentage
            return param.name + ' (' + param.percent + '%)';
          }
        },
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
}
const Index: React.FC<{
  machineStatsByClientType: { [key: string]: number };
  refEcharts: React.MutableRefObject<EChartsReact | null>
}> = (props) => {

  const globalTheme = useContext(GlobalThemeContext);
  const { formatMessage } = useLocale();
  const rootRef = useRef<EChartsReact>(null)

  useEffect(() => {

    if (rootRef === null || rootRef.current === null) {
      return
    }

    props.refEcharts.current = rootRef.current;
  }, [props.refEcharts])

  const option = getDeviceTypeOpt(props.machineStatsByClientType, formatMessage)

  return <>
    <div className={styles.map}>
      <ReactECharts
        style={{ height: '600px' }}
        option={option}
        ref={rootRef}
        theme={getEchartsTheme(globalTheme)}
        onEvents={{
          'click': () => { }
        }}

      />
    </div></>

}

export default Index