import React, { useEffect, useState, useRef, useContext } from 'react'
import ReactECharts from 'echarts-for-react';
import EChartsReact from 'echarts-for-react';
import { Matrix, MetricValue } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import styles from './index.module.scss'
import { GlobalThemeContext, useGlobalTheme } from '@/hooks/useGlobalTheme';
import { getEchartsTheme } from '@/utils/common';
/** 计算N个点均匀排列成圆的坐标
 * @param nodesCount 节点数
 * @param center 中心点
 * @param radius 半径
 */
function calcCircularLayout(nodesCount: number, center: { x: number, y: number }, radius: number) {

  let layouts: Array<{ x: number, y: number }> = [];
  for (let i = 0; i < nodesCount; i++) {
    const x = center.x + radius * Math.sin(2 * Math.PI * i / nodesCount);
    const y = center.y + radius * Math.cos(2 * Math.PI * i / nodesCount);
    layouts.push({ x, y })
  }
  return layouts;
}

export function getRelayOpt(srcTransMatrix: Matrix[], srcRecvMatrix: Matrix[]) {
  let graph: any = {
    nodes: [],
    links: [],
    categories: [
      {
        "name": "Device"
      },
      {
        "name": "Relay"
      },
    ]
  }

  const mapNode: Map<string, {
    id: string,
    name: string,
    symbolSize: number,
    x: number,
    y: number,
    value: number,
    category: string,
    label: {
      show: boolean
    }
  }> = new Map();

  let idIndex = 1;
  let instanceIndex = 0;

  srcTransMatrix.forEach(matrix => {
    let instance = matrix.metric.instance;
    if (instance) {
      instance = instance.split(':')[0];
    }

    let instanceInMap = mapNode.get(instance);

    const unitSize = 20;

    if (!instanceInMap) {
      instanceInMap = {
        id: idIndex + '',
        name: instance,
        symbolSize: unitSize,
        x: 10 + instanceIndex * 50,
        y: 200,
        value: 1,
        category: 'Relay',
        label: {
          show: true
        }
      }
      mapNode.set(instance, instanceInMap);
      idIndex++;
      instanceIndex++;


      graph.nodes.push(instanceInMap)
    } else {
      instanceInMap.value += 1;
    }




  });


  srcRecvMatrix.forEach(matrix => {

    let instance = matrix.metric.instance;
    if (instance) {
      instance = instance.split(':')[0];
    }
    let instanceInMap = mapNode.get(instance);

    const unitSize = 5;

    if (!instanceInMap) {
      instanceInMap = {
        id: idIndex + '',
        name: instance,
        symbolSize: unitSize,
        x: 10 + instanceIndex * 50,
        y: 200,
        value: 1,
        category: 'Relay',
        label: {
          show: true
        }
      }
      mapNode.set(instance, instanceInMap);
      idIndex++;
      instanceIndex++;


      graph.nodes.push(instanceInMap)
    } else {
      instanceInMap.value += 1;
    }

  });


  let layouts = calcCircularLayout(graph.nodes.length, { x: 100, y: 100 }, 200)
  graph.nodes.forEach((node: any, index: number) => {
    node.x = layouts[index].x;
    node.y = layouts[index].y;
  })

  for (let i = 0; i < graph.nodes.length; i++) {
    let nodei = graph.nodes[i]
    for (let j = 0; j < graph.nodes.length; j++) {
      let nodej = graph.nodes[j]
      if(i != j) {

        graph.links.push({
          source: nodei.id,
          target: nodej.id
        })
        graph.links.push({
          source: nodej.id,
          target: nodei.id
        })
      }
    }
  }

  const option = {
    // color: ['rgb(78,89,105)', 'rgb(201,205,212)', 'rgb(107,119,133)', 'rgb(134,144,156)', 'rgb(169,174,184)', 'rgb(229,230,235)'],

    title: {
      text: '中继网格分布',
      // subtext: 'Default layout',
      top: 'top',
      left: 'left'
    },
    tooltip: {},
    legend: [
      {
        // selectedMode: 'single',
        data: graph.categories.map(function (a: any) {
          return a.name;
        }),
        top: 'bottom'
      }
    ],
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: [
      {
        name: 'Les Miserables',
        type: 'graph',
        layout: 'none',
        data: graph.nodes,
        links: graph.links,
        categories: graph.categories,
        roam: true,
        label: {
          position: 'right',
          formatter: '{b}'
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 10
          }
        }
      }
    ]
  };
  return option;
}


export function getRelayOptBak(srcTransMatrix: Matrix[], srcRecvMatrix: Matrix[]) {
  let graph: any = {
    nodes: [],
    links: [],
    categories: [
      {
        "name": "Device"
      },
      {
        "name": "Relay"
      },
    ]
  }

  const mapNode: Map<string, {
    id: string,
    name: string,
    symbolSize: number,
    x: number,
    y: number,
    value: number,
    category: string,
    label: {
      show: boolean
    }
  }> = new Map();

  let idIndex = 1;
  let deviceIndex = 0;
  let instanceIndex = 0;

  srcTransMatrix.forEach(matrix => {
    const device = matrix.metric.device;
    let instance = matrix.metric.instance;
    if (instance) {
      instance = instance.split(':')[0];
    }

    let deviceInMap = mapNode.get(device);
    let instanceInMap = mapNode.get(instance);

    const unitSize = 20;


    if (!deviceInMap) {
      deviceInMap = {
        id: idIndex + '',
        name: device,
        symbolSize: unitSize,
        x: 10 + deviceIndex * 100,
        y: 10,
        value: 1,
        category: 'Device',
        label: {
          show: true
        }
      }
      mapNode.set(device, deviceInMap);
      idIndex++;
      deviceIndex++;

      graph.nodes.push(deviceInMap)
    } else {
      deviceInMap.value += 1;
    }
    if (!instanceInMap) {
      instanceInMap = {
        id: idIndex + '',
        name: instance,
        symbolSize: unitSize,
        x: 10 + instanceIndex * 50,
        y: 200,
        value: 1,
        category: 'Relay',
        label: {
          show: true
        }
      }
      mapNode.set(instance, instanceInMap);
      idIndex++;
      instanceIndex++;


      graph.nodes.push(instanceInMap)
    } else {
      instanceInMap.value += 1;
    }

    let layouts = calcCircularLayout(graph.nodes.length, { x: 100, y: 100 }, 200)
    graph.nodes.forEach((node: any, index: number) => {
      node.x = layouts[index].x;
      node.y = layouts[index].y;
    })
    graph.links.push({
      source: deviceInMap.id,
      target: instanceInMap.id
    })


  });


  srcRecvMatrix.forEach(matrix => {
    const device = matrix.metric.device;
    let instance = matrix.metric.instance;
    if (instance) {
      instance = instance.split(':')[0];
    }

    let deviceInMap = mapNode.get(device);
    let instanceInMap = mapNode.get(instance);

    const unitSize = 5;


    if (!deviceInMap) {
      deviceInMap = {
        id: idIndex + '',
        name: device,
        symbolSize: unitSize,
        x: 10 + deviceIndex * 100,
        y: 10,
        value: 1,
        category: 'Device',
        label: {
          show: true
        }
      }
      mapNode.set(device, deviceInMap);
      idIndex++;
      deviceIndex++;

      graph.nodes.push(deviceInMap)
    } else {
      deviceInMap.value += 1;
    }
    if (!instanceInMap) {
      instanceInMap = {
        id: idIndex + '',
        name: instance,
        symbolSize: unitSize,
        x: 10 + instanceIndex * 50,
        y: 200,
        value: 1,
        category: 'Relay',
        label: {
          show: true
        }
      }
      mapNode.set(instance, instanceInMap);
      idIndex++;
      instanceIndex++;


      graph.nodes.push(instanceInMap)
    } else {
      instanceInMap.value += 1;
    }

    graph.links.push({
      source: instanceInMap.id,
      target: deviceInMap.id
    })


  });




  const option = {
    color: ['rgb(78,89,105)', 'rgb(201,205,212)', 'rgb(107,119,133)', 'rgb(134,144,156)', 'rgb(169,174,184)', 'rgb(229,230,235)'],

    title: {
      text: '中继网格分布',
      // subtext: 'Default layout',
      top: 'top',
      left: 'left'
    },
    tooltip: {},
    legend: [
      {
        // selectedMode: 'single',
        data: graph.categories.map(function (a: any) {
          return a.name;
        }),
        top: 'bottom'
      }
    ],
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: [
      {
        name: 'Les Miserables',
        type: 'graph',
        layout: 'none',
        data: graph.nodes,
        links: graph.links,
        categories: graph.categories,
        roam: true,
        label: {
          position: 'right',
          formatter: '{b}'
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 10
          }
        }
      }
    ]
  };
  return option;
}

const Index: React.FC<{
  refEcharts: React.MutableRefObject<EChartsReact | null>
  option: any;
}> = (props) => {
  const globalTheme = useContext(GlobalThemeContext);
  const rootRef = useRef<EChartsReact>(null)
  useEffect(() => {
    if (rootRef === null || rootRef.current === null) {
      return
    }
    props.refEcharts.current = rootRef.current;
  }, [props.refEcharts])

  return <ReactECharts
    style={{ height: '600px' }}
    option={props.option}
    ref={rootRef}
    theme={getEchartsTheme(globalTheme)}
    onEvents={{
      'click': (param: any) => {
      }
    }}
  />
}
export default Index;