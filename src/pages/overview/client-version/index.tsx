import React, { useEffect, useRef, useContext } from 'react'
import ReactECharts from 'echarts-for-react';
import styles from './index.module.scss'
import { ClientStat } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb';
import { getEchartsTheme } from '@/utils/common';
import { LocaleFormatter, useLocale } from '@/locales';

import EChartsReact from 'echarts-for-react';
import { Typography, Row, Col, Select } from '@douyinfe/semi-ui';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const { Title } = Typography;

const getDataFromOs = (machineStatsByClientVersion: { [key: string]: ClientStat }, os: string) => {
  const data: any = [];
  const clientStat = machineStatsByClientVersion[os];

  if (!clientStat) {
    return data;
  }
  let total = 0;
  clientStat.stats && Object.keys(clientStat.stats).sort().forEach((key) => {
    if(key) {
      data.push({
        name: key,
        value: clientStat.stats[key]
      })
      total += clientStat.stats[key];
    }
    
  })
  data.push({
    // make an record to fill the bottom 50%
    value: total,
    itemStyle: {
      // stop the chart from rendering this piece
      color: 'none',
      decal: {
        symbol: 'none'
      }
    },
    label: {
      show: false
    }
  })


  return data;
}

export function getClientVersionOpt(machineStatsByClientVersion: { [key: string]: ClientStat }, defaultOs: string, formatMessage?: (descriptor: { id: string }) => string) {

  const option =
  {
    // color: ['rgb(78,89,105)', 'rgb(107,119,133)', 'rgb(134,144,156)', 'rgb(169,174,184)', 'rgb(201,205,212)', 'rgb(229,230,235)'],
    title: {
      // text: '客户端版本分布',
      // subtext: '接入客户端的版本',
      // left: 'center',
      // top: '3%',
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      bottom: 100,
      // left: 'center',
      // doesn't perfectly work with our tricks, disable it
      selectedMode: false
    },
    grid: {
        left: 20,
        right: 20,
        top: 100,
        containLabel: false
    },
    series: [
      {
        name: formatMessage ? formatMessage({ id: 'overview.clientVersion.name' }) : '客户端版本',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        // adjust the start angle
        startAngle: 180,
        label: {
          show: true,
          formatter(param: any) {
            // correct the percentage
            return param.name + ' (' + param.percent * 2 + '%)';
          }
        },
        data: getDataFromOs(machineStatsByClientVersion, defaultOs),
      }
    ]
  };

  return option;
}

const Index: React.FC<{
  machineStatsByClientVersion: { [key: string]: ClientStat },
  onOsChange: (os: string) => void,
  refEcharts: React.MutableRefObject<EChartsReact | null>
  defaultOs: string;
  
}> = (props) => {
  const globalTheme = useContext(GlobalThemeContext);
  const { formatMessage } = useLocale();
  const rootRef = useRef<EChartsReact>(null)
  useEffect(() => {

    if (rootRef === null || rootRef.current === null) {
      return
    }

    props.refEcharts.current = rootRef.current;
  }, [props.refEcharts])

  const osList = Object.keys(props.machineStatsByClientVersion).filter((val)=>val).sort();

  const option = getClientVersionOpt(props.machineStatsByClientVersion, props.defaultOs, formatMessage);

  const handleOsChange = (value: any) => {
    props.onOsChange(value);
  }

  return <>
    <div className={styles.chartPanel}>

      <Row className={styles.title}>
        <Col span={12} style={{ display: 'flex', alignItems: 'center', height: 32, paddingLeft: 16 }}>
          <Title heading={5}><LocaleFormatter id="overview.clientVersion.title" /></Title>
        </Col>
        <Col span={12} style={{ display: 'flex', justifyContent: 'right' }}>
          <Select defaultValue={props.defaultOs} insetLabel={formatMessage({ id: 'overview.clientVersion.deviceType' })} onChange={handleOsChange} style={{ width: 180 }}>
            {osList.map((os) => {
              return <Select.Option key={os} value={os}>{os}</Select.Option>
            })}
          </Select>
        </Col>
      </Row>

      <ReactECharts
        style={{ height: '600px' }}
        option={option}
        theme={getEchartsTheme(globalTheme)}
        notMerge={false}
        lazyUpdate={true}
        ref={rootRef}
        onEvents={{
          'click': () => {

          }
        }}

      />
    </div></>

}

export default Index