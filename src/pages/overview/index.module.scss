.overview {
    // min-width: 1200px;
    position: relative;
}


.up, .down {
    display: flex;
    align-items: center;
    margin-left: 10px;
    font-size: 12px;
    height: 16px;
    >span {
        height: 16px;
        width: 16px;
        margin-right: 10px;
        border-radius: 16px;
        font-size: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
}
.up {
    color: rgba(var(--semi-green-9),1);
    >span {
        background-color: rgba(var(--semi-green-1),1);
    }
    
}
.down {
    color: rgba(var(--semi-red-9),1);
    >span {
        background-color: rgba(var(--semi-red-1),1);
    }
    
}

.cards {
    display: flex;
    .card {
        border-right: 1px solid var(--semi-color-border);
    }
    .card:last-of-type {
        border-right: none;
    }
}
.card {
    padding: 20px;
    flex-grow: 1;
    height: 110px;
    .cardColumn {
        display: flex;
        .title {
            font-size: 14px;
            margin-bottom: 20px;
        }
        .value {
            font-size: 36px;
            margin-bottom: 30px;

        }
    }

    .cardSum {
        // display: flex;
        display: none;
        font-size: 14px;
        >div:first-of-type {
            flex-grow: 1;
            display: flex;
            align-items: center;
        }
    }
}

.primary {
    // background-color: rgba(var(--semi-arcoblue-5),1);
    background-color: var(--semi-color-fill-3);
    div  {
        color: #fff;
    }
    .up, .down {
        color: #fff;
        // >span {
        //     background-color: rgba(var(--semi-arcoblue-2),1);
        // }
        
    }
}

.cardIcon {
    flex-grow: 1;
    display: flex;
    justify-content: flex-end;
    img {
        width: 50px;
        height: 50px;
    }
}

.navIcon {
    cursor: pointer;
} 

.skeletonSpin {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--semi-color-fill-1);
}

.refreshDropdown {
    ul div {
        margin: 0;
    }
    li {
        justify-content: flex-end;
        font-size: 10px;
        padding: 4px 16px;
    }
}

.chartCol {
    position: relative;
    .loading {
        position: absolute;
        display: none;
        right: 14px;
        top: 8px;
        z-index: 1;
    }
}