import React, { useState, useEffect, useContext, useRef } from 'react'
import { Typography, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { LocaleFormatter, useLocale } from '@/locales';
import moment from 'moment';
import { IconTreeTriangleDown, IconSync } from '@douyinfe/semi-icons';
import Map, { convertMapData, getMapOpt } from './map';
import DeviceType, { getDeviceTypeOpt } from './device-type';
import NetworkTraffic, { getDisplayTraffic, getTrafficOpt, getTrafficTotal } from './network-traffic';
import DeviceOnline, { getDataFromMatris } from './device-online';
import ClientVersion, { getClientVersionOpt } from './client-version';
import Relay, { getRelayOpt } from './relay';
import { ClientStat, LocationStat } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb';
import { Matrix } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { flylayerClient } from '@/services/core';
import EChartsReact from 'echarts-for-react';

import styles from './index.module.scss'
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const { Text } = Typography;
const Index: React.FC = () => {
    const globalTheme = useContext(GlobalThemeContext);
    const { formatMessage } = useLocale();

    const [firstLoading, setFirstLoading] = useState<boolean>(true);

    const flynet = useContext(FlynetGeneralContext);

    const [machineTotal, setMachineTotal] = useState<number>(0);
    const [machineOnline, setMachineOnline] = useState<number>(0);
    const [userTotal, setUserTotal] = useState<number>(0);
    const [userOnline, setUserOnline] = useState<number>(0);

    const [machineStatsByClientType, setMachineStatsByClientType] = useState<{ [key: string]: number }>()
    const [machineStatsByClientVersion, setMachineStatsByClientVersion] = useState<{ [key: string]: ClientStat }>()
    const [machineStatsByLocation, setMachineStatsByLocation] = useState<{ [key: string]: LocationStat }>();

    // 设备数
    const [machineOnlineMatrix, setMachineOnlineMatrix] = useState<Matrix[]>();

    const [refreshInterval, setRefreshInterval] = useState<number>(-1);
    const [refreshLabel, setRefreshLabel] = useState<string>('');
    const [refreshTime, setRefreshTime] = useState<string>('');

    const refUpdateTime = useRef<HTMLSpanElement>(null);

    const topRef = useRef<HTMLDivElement | null>(null);
    const totalRef = useRef<HTMLDivElement | null>(null);
    const mapRef = useRef<EChartsReact | null>(null);
    const deviceTypeRef = useRef<EChartsReact | null>(null);

    const clientVersionRef = useRef<EChartsReact | null>(null);
    const clientVersionOsRef = useRef<string>('');

    // 在线设备数
    const deviceOnlineTimeRef = useRef<number>(6);
    const deviceOnlineRef = useRef<EChartsReact | null>(null);


    // 流量
    const trafficTimeRef = useRef<number>(6);
    const trafficRef = useRef<EChartsReact | null>(null);
    const [trafficTransMatrix, setTrafficTransMatrix] = useState<Matrix[]>();
    const [trafficRecvMatrix, setTrafficRecvMatrix] = useState<Matrix[]>();
    const trafficTopRef = useRef<HTMLParagraphElement>();
    const [transTotal, setTransTotal] = useState(0)
    const [recvTotal, setRecvTotal] = useState(0)
    const relayRef = useRef<EChartsReact | null>(null);

    // 更新引用
    const loadingRefMap = useRef<HTMLDivElement>(null);
    const loadingRefTraffic = useRef<HTMLDivElement>(null);
    const loadingRefDeviceOnline = useRef<HTMLDivElement>(null);
    const loadingRefClientVersion = useRef<HTMLDivElement>(null);
    const loadingRefDeviceType = useRef<HTMLDivElement>(null);

    const setRefresh = (time: number, label: string) => {
        setRefreshInterval(time);
        setRefreshLabel(label);
    }


    const [intervalID, setIntervalID] = useState<NodeJS.Timer>();

    useEffect(() => {
        if (intervalID) {
            clearInterval(intervalID);
        }
        if (refreshInterval > 0) {
            const id = setInterval(() => {
                update();
            }, refreshInterval * 1000);
            setIntervalID(id);
        }
    }, [refreshInterval])


    // 更新总览信息
    const update = () => {
        const mapEcharts = mapRef.current?.getEchartsInstance();
        const deviceTypeEcharts = deviceTypeRef.current?.getEchartsInstance();
        const clientVersionEcharts = clientVersionRef.current?.getEchartsInstance();


        if (loadingRefDeviceType.current) {
            loadingRefDeviceType.current.style.display = 'block';
        }
        if (loadingRefClientVersion.current) {
            loadingRefClientVersion.current.style.display = 'block';
        }
        if (loadingRefMap.current) {
            loadingRefMap.current.style.display = 'block';
        }

        flylayerClient.getFlynetStats({
            flynetId: flynet.id
        }).then((res) => {
            if (mapEcharts) {
                mapEcharts.setOption(getMapOpt(globalTheme.colorMode, res.machineStatsByLocation, false, formatMessage))
            }
            if (deviceTypeEcharts) {
                deviceTypeEcharts.setOption(getDeviceTypeOpt(res.machineStatsByClientType, formatMessage))
            }
            if (clientVersionEcharts) {
                clientVersionEcharts.setOption(getClientVersionOpt(res.machineStatsByClientVersion, clientVersionOsRef.current, formatMessage));
            }
            if (refUpdateTime.current) {
                refUpdateTime.current.innerHTML = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            }

            if (totalRef && totalRef.current) {
                const tdList = totalRef.current.querySelectorAll('td span');
                if (tdList.length >= 4) {
                    tdList[0].innerHTML = res.userTotal + '';
                    tdList[1].innerHTML = res.userOnline + '';
                    tdList[2].innerHTML = res.machineTotal + '';
                }
            }

            if (topRef && topRef.current) {
                const $rows = topRef.current?.children;
                const calData = convertMapData(res.machineStatsByLocation);
                const calTopData = calData
                    .sort(function (a: any, b: any) {
                        return b.value[2] - a.value[2];
                    })
                    .slice(0, 6);

                if ($rows && $rows?.length === calTopData.length + 1) {
                    for (let i = 0; i < calTopData.length; i++) {
                        const $row = $rows[i + 1];
                        const $col = $row.children;
                        const data = calTopData[i];
                        $col[0].innerHTML = data.name;
                        $col[1].innerHTML = data.value[2];
                        $col[2].innerHTML = res.machineStatsByLocation[data.name].online + ''
                    }
                }
            }
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'overview.getOverviewFailed' }) })
        }).finally(() => {

            if (loadingRefDeviceType.current) {
                loadingRefDeviceType.current.style.display = 'none';
            }
            if (loadingRefClientVersion.current) {
                loadingRefClientVersion.current.style.display = 'none';
            }
            if (loadingRefMap.current) {
                loadingRefMap.current.style.display = 'none';
            }
        });

        updateDeviceOnline(deviceOnlineTimeRef.current)
        updateTraffic(trafficTimeRef.current);
    }

    // 查询总览信息
    const query = () => {

        flylayerClient.getFlynetStats({
            flynetId: flynet.id
        }).then((res) => {
            if (firstLoading) {
                setFirstLoading(false)
            }
            if (res.machineStatsByClientVersion) {

                const osList = Object.keys(res.machineStatsByClientVersion).sort();

                let defaultOs = '';
                if (res.machineStatsByClientVersion['windows'] && res.machineStatsByClientVersion['windows'].stats
                    && Object.keys(res.machineStatsByClientVersion['windows'].stats).length > 0) {
                    defaultOs = 'windows'
                } else {
                    for (let i = 0; i < osList.length; i++) {
                        if (osList[i]) {
                            defaultOs = osList[i];
                            break;
                        }
                    }
                }
                clientVersionOsRef.current = defaultOs;
            }
            setMachineStatsByClientType(res.machineStatsByClientType)
            setMachineStatsByClientVersion(res.machineStatsByClientVersion)

            setMachineStatsByLocation(res.machineStatsByLocation)

            if (machineTotal != res.machineTotal) {
                setMachineTotal(res.machineTotal)
            }

            if (userTotal != res.userTotal) {
                setUserTotal(res.userTotal)
            }

            if (userOnline != res.userOnline) {
                setUserOnline(res.userOnline)
            }

            setRefreshTime(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'overview.getOverviewFailed' }) })
        });


        queryDeviceOnline();
        queryTraffic();
    };

    useEffect(() => {
        query();
    }, [])

    const updateDeviceOnline = (time: number) => {

        if (loadingRefDeviceOnline.current) {
            loadingRefDeviceOnline.current.style.display = 'block';
        }
        const echartsRef = deviceOnlineRef.current?.getEchartsInstance();
        // 取得当前日期和7天前日期，以便于设置默认的时间范围
        const endTime = new Date();
        const startTime = new Date(endTime.getTime() - time * 60 * 60 * 1000);

        let newStartTime = startTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;
        let newEndTime = endTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;

        const endTimeStr = moment(newEndTime).format('YYYY-MM-DD HH:mm:ss');
        const startTimeStr = moment(newStartTime).format('YYYY-MM-DD HH:mm:ss');


        flylayerClient.getFlynetMachineOnlineTrend({
            flynetId: flynet.id,
            query: `start=${startTimeStr}&end=${endTimeStr}`
        }).then((res) => {
            const opt = getDataFromMatris(res.matrix, formatMessage);
            if (echartsRef) {
                echartsRef.setOption(opt);
            }
            // 取全网设备分布里的在线数
            if (res.matrix.length > 0) {
                let last = res.matrix[res.matrix.length - 1];
                if (last.values.length > 0) {

                    setMachineOnline(parseInt(last.values[last.values.length - 1].value))


                    if (totalRef && totalRef.current) {
                        const tdList = totalRef.current.querySelectorAll('td span');
                        if (tdList.length >= 4) {
                            tdList[3].innerHTML = parseInt(last.values[last.values.length - 1].value) + '';
                        }
                    }
                }

            }
        }).finally(() => {

            if (loadingRefDeviceOnline.current) {
                loadingRefDeviceOnline.current.style.display = 'none';
            }
        })
    }

    const queryDeviceOnline = () => {

        // 取得当前日期和7天前日期，以便于设置默认的时间范围
        const endTime = new Date();
        const startTime = new Date(endTime.getTime() - deviceOnlineTimeRef.current * 60 * 60 * 1000);

        let newStartTime = startTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;
        let newEndTime = endTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;

        const endTimeStr = moment(newEndTime).format('YYYY-MM-DD HH:mm:ss');
        const startTimeStr = moment(newStartTime).format('YYYY-MM-DD HH:mm:ss');


        flylayerClient.getFlynetMachineOnlineTrend({
            flynetId: flynet.id,
            query: `start=${startTimeStr}&end=${endTimeStr}`
        }).then((res) => {
            setMachineOnlineMatrix(res.matrix)
            // 取全网设备分布里的在线数
            if (res.matrix.length > 0) {
                let last = res.matrix[res.matrix.length - 1];
                if (last.values.length > 0) {
                    setMachineOnline(parseInt(last.values[last.values.length - 1].value))
                }

            }
        })
    }

    const updateTraffic = (time: number) => {
        const endTime = new Date();
        const startTime = new Date(endTime.getTime() - time * 60 * 60 * 1000);

        let newStartTime = startTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;
        let newEndTime = endTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;

        const endTimeStr = moment(newEndTime).format('YYYY-MM-DD HH:mm:ss');
        const startTimeStr = moment(newStartTime).format('YYYY-MM-DD HH:mm:ss');

        const queryStr = `start=${startTimeStr}&end=${endTimeStr}&direction=transmit`
        const queryStrRecive = `start=${startTimeStr}&end=${endTimeStr}&direction=receive`

        const totalQueryStr = `start=${startTimeStr}&end=${endTimeStr}&direction=transmit&type=total`
        const totalQueryStrRecive = `start=${startTimeStr}&end=${endTimeStr}&direction=receive&type=total`

        const query1 = flylayerClient.getRelayTraffic({
            query: queryStr
        })
        const query2 = flylayerClient.getRelayTraffic({
            query: queryStrRecive
        })
        const query3 = flylayerClient.getRelayTraffic({
            query: totalQueryStr
        })
        const query4 = flylayerClient.getRelayTraffic({
            query: totalQueryStrRecive
        })
        const echartsRef = trafficRef.current?.getEchartsInstance();
        const relayEchartsRef = relayRef.current?.getEchartsInstance();

        console.log(loadingRefTraffic.current)
        if (loadingRefTraffic.current) {

            loadingRefTraffic.current.style.display = 'block';
        }

        Promise.all([query1, query2, query3, query4]).then((values) => {
            const { option,
                transTop,
                recvTop } = getTrafficOpt(values[0].matrix, values[1].matrix, formatMessage)

            if (echartsRef) {
                echartsRef.setOption(option);
            }



            if (trafficTopRef.current) {
                const { transTotal, recvTotal } = getTrafficTotal(values[2].matrix, values[3].matrix)
                trafficTopRef.current.innerHTML = getDisplayTraffic(recvTop, transTop, recvTotal, transTotal);
            }

            const relayOpt: any = getRelayOpt(values[0].matrix, values[1].matrix)
            if (relayEchartsRef) {
                relayEchartsRef.setOption(relayOpt)
            }
        }).finally(() => {

            if (loadingRefTraffic.current) {
                loadingRefTraffic.current.style.display = 'none';
            }
        });


    }

    const queryTraffic = () => {
        // 取得当前日期和7天前日期，以便于设置默认的时间范围
        const endTime = new Date();
        const startTime = new Date(endTime.getTime() - deviceOnlineTimeRef.current * 60 * 60 * 1000);

        let newStartTime = startTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;
        let newEndTime = endTime.getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000;

        const endTimeStr = moment(newEndTime).format('YYYY-MM-DD HH:mm:ss');
        const startTimeStr = moment(newStartTime).format('YYYY-MM-DD HH:mm:ss');

        const queryStr = `start=${startTimeStr}&end=${endTimeStr}&direction=transmit`
        const queryStrRecive = `start=${startTimeStr}&end=${endTimeStr}&direction=receive`


        const totalQueryStr = `start=${startTimeStr}&end=${endTimeStr}&direction=transmit&type=total`
        const totalQueryStrRecive = `start=${startTimeStr}&end=${endTimeStr}&direction=receive&type=total`

        const query1 = flylayerClient.getRelayTraffic({
            query: queryStr
        })
        const query2 = flylayerClient.getRelayTraffic({
            query: queryStrRecive
        })

        const query3 = flylayerClient.getRelayTraffic({
            query: totalQueryStr
        })
        const query4 = flylayerClient.getRelayTraffic({
            query: totalQueryStrRecive
        })

        Promise.all([query1, query2, query3, query4]).then((values) => {

            const total = getTrafficTotal(values[2].matrix, values[3].matrix)

            setTransTotal(total.transTotal)
            setRecvTotal(total.recvTotal);
            setTrafficTransMatrix(values[0].matrix)
            setTrafficRecvMatrix(values[1].matrix)
        });
        // start=<yyyy-MM-dd HH:mm:ss>&end=<yyyy-MM-dd HH:mm:ss>&direction=<receive|transmit></yyyy-MM-dd>
    }

    return <>
        <div className='general-page'>

            <div className={styles.overview}>
                <Row className='mb20'>
                    <Col span={24} style={{ display: 'flex', justifyContent: 'right' }}><div>
                        <Text type='tertiary' style={{ fontSize: 12 }}><LocaleFormatter id="overview.lastQueryTime" />：<span ref={refUpdateTime}>{refreshTime}</span></Text>
                        <SplitButtonGroup style={{ marginLeft: 10 }} aria-label="项目操作按钮组">
                            <Button size='small' onClick={() => { update() }} icon={<IconSync />}></Button>
                            <Dropdown className={styles.refreshDropdown} menu={[
                                { node: 'item', name: formatMessage({ id: 'overview.refresh.none' }), onClick: () => setRefresh(-1, '') },
                                { node: 'divider' },
                                { node: 'item', name: formatMessage({ id: 'overview.refresh.5s' }), onClick: () => setRefresh(5, formatMessage({ id: 'overview.refresh.5s' })) },
                                { node: 'item', name: formatMessage({ id: 'overview.refresh.10s' }), onClick: () => setRefresh(10, formatMessage({ id: 'overview.refresh.10s' })) },
                                { node: 'item', name: formatMessage({ id: 'overview.refresh.30s' }), onClick: () => setRefresh(30, formatMessage({ id: 'overview.refresh.30s' })) },
                                { node: 'divider' },
                                { node: 'item', name: formatMessage({ id: 'overview.refresh.1m' }), onClick: () => setRefresh(60, formatMessage({ id: 'overview.refresh.1m' })) },
                                { node: 'item', name: formatMessage({ id: 'overview.refresh.5m' }), onClick: () => setRefresh(300, formatMessage({ id: 'overview.refresh.5m' })) },
                                { node: 'item', name: formatMessage({ id: 'overview.refresh.15m' }), onClick: () => setRefresh(900, formatMessage({ id: 'overview.refresh.15m' })) },
                            ]} trigger="hover" position="bottomRight">
                                <Button style={{ paddingLeft: 4, paddingRight: 4 }} size='small' >{refreshLabel}<IconTreeTriangleDown /></Button>
                            </Dropdown>
                        </SplitButtonGroup></div>
                    </Col>
                </Row>
                <Row gutter={16}>
                    <Col xs={24} sm={12} style={{ marginBottom: 16 }} className={styles.chartCol}>

                        <div className={styles.loading} ref={loadingRefMap}><Spin></Spin></div>
                        <Card bodyStyle={{ padding: 0 }}>
                            <Skeleton
                                placeholder={<div style={{ height: 600 }} className={styles.skeletonSpin}><Spin size='large'></Spin></div>}
                                loading={firstLoading}
                            >
                                {machineStatsByLocation && <Map
                                    refEcharts={mapRef}
                                    refTopTable={topRef}
                                    refTotalTable={totalRef}
                                    machineTotal={machineTotal}
                                    machineOnline={machineOnline}
                                    userTotal={userTotal}
                                    userOnline={userOnline}
                                    machineStatsByLocation={machineStatsByLocation}></Map>}

                            </Skeleton>

                        </Card>
                    </Col>
                    <Col xs={24} sm={12} style={{ marginBottom: 16 }} className={styles.chartCol}>

                        <div className={styles.loading} ref={loadingRefDeviceOnline}><Spin></Spin></div>
                        <Card bodyStyle={{ padding: 0 }}>
                            <Skeleton
                                placeholder={<div style={{ height: 600 }} className={styles.skeletonSpin}><Spin size='large'></Spin></div>}
                                loading={firstLoading}
                            >
                                {machineOnlineMatrix && <DeviceOnline
                                    machineOnlineMatrix={machineOnlineMatrix}
                                    refEcharts={deviceOnlineRef}
                                    refTime={deviceOnlineTimeRef}
                                    handleRangeChange={(val) => {
                                        deviceOnlineTimeRef.current = val;

                                        updateDeviceOnline(val)
                                    }}
                                ></DeviceOnline>}
                            </Skeleton>
                        </Card>
                    </Col>
                </Row>
                <Row style={{ marginBottom: 16 }}>
                    <Col span={24} className={styles.chartCol}>
                        <div className={styles.loading} ref={loadingRefTraffic} style={{ right: 6 }}><Spin></Spin></div>
                        <Card bodyStyle={{ padding: 0 }}>
                            {trafficRecvMatrix && trafficTransMatrix && <NetworkTraffic
                                trafficRecvMatrix={trafficRecvMatrix}
                                trafficTransMatrix={trafficTransMatrix}
                                transTotal={transTotal}
                                recvTotal={recvTotal}
                                refTop={trafficTopRef}
                                refEcharts={trafficRef}
                                refTime={trafficTimeRef}
                                handleRangeChange={(val) => {
                                    trafficTimeRef.current = val;

                                    updateTraffic(val)
                                }}
                            ></NetworkTraffic>}

                        </Card>
                    </Col>
                </Row>
                <Row style={{ marginBottom: 16, display: 'none' }}>
                    <Col span={24}>
                        <Card>
                            {trafficRecvMatrix && trafficTransMatrix && <Relay
                                refEcharts={relayRef}
                                option={getRelayOpt(trafficTransMatrix, trafficRecvMatrix)}
                            ></Relay>}

                        </Card>
                    </Col>
                </Row>
                <Row gutter={16} style={{ marginBottom: 16 }}>
                    {/* 设备类型分布 */}
                    <Col xs={24} sm={12} style={{ marginBottom: 16 }} className={styles.chartCol}>

                        <div className={styles.loading} ref={loadingRefDeviceType}><Spin></Spin></div>
                        <Card bodyStyle={{ padding: 0 }}>

                            <Skeleton
                                placeholder={<div style={{ height: 600 }} className={styles.skeletonSpin}><Spin size='large'></Spin></div>}
                                loading={firstLoading}
                            >
                                {machineStatsByClientType && <DeviceType refEcharts={deviceTypeRef} machineStatsByClientType={machineStatsByClientType}></DeviceType>}
                            </Skeleton>

                        </Card>
                    </Col>
                    {/* 版本分布； */}
                    <Col xs={24} sm={12} style={{ marginBottom: 16 }} className={styles.chartCol}>

                        <div className={styles.loading} ref={loadingRefClientVersion}><Spin></Spin></div>
                        <Skeleton
                            placeholder={<div style={{ height: 600 }} className={styles.skeletonSpin}><Spin size='large'></Spin></div>}
                            loading={firstLoading}
                        >
                            <Card bodyStyle={{ padding: 0 }}>
                                {machineStatsByClientVersion &&
                                    <ClientVersion defaultOs={clientVersionOsRef.current} onOsChange={(os: string) => {
                                        clientVersionOsRef.current = os;
                                        const clientVersionEcharts = clientVersionRef.current?.getEchartsInstance();

                                        if (clientVersionEcharts) {
                                            clientVersionEcharts.setOption(getClientVersionOpt(machineStatsByClientVersion, os, formatMessage));
                                        }
                                    }}
                                        refEcharts={clientVersionRef}
                                        machineStatsByClientVersion={machineStatsByClientVersion}></ClientVersion>}
                            </Card>
                        </Skeleton>


                    </Col>
                </Row>
            </div>
        </div>

    </>
}

export default Index;
