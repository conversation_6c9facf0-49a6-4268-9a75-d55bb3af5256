import React, { useState, useEffect, useContext } from 'react'
import { Typography, Notification, Dropdown, Button, Divider, Space, Tag, Popover, Collapsible } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Service, ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { getFlynet } from '@/services/flynet';

import { IpGroup } from '@/interface/ip-group';
import { ACLPolicy, AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Title, Paragraph } = Typography;
import { IconMore, IconHelpCircle } from '@douyinfe/semi-icons';
import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { Machine, MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';

import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import Expandable from '@/components/expandable/index';
import { useLocale } from '@/locales';
import styles from './index.module.scss';
import { getSimpleServiceGroupName, getSimpleServiceName } from '@/utils/service';
import { caseInsensitiveIncludes } from '@/utils/common';

export type ACLFilter = {
    query: string;
    users: string[];
    services: string;
    servicesGroup: string;
    status: string;
    group: string
}

let mapServicesData: Map<string, Service> = new Map();
let mapServicesGroupData: Map<string, ServiceGroup> = new Map();
let mapUser: Map<string, User> = new Map();


let mapMachine: Map<string, Machine> = new Map();
let mapMachineGroup: Map<string, MachineGroup> = new Map();

const useTable = (filterParam: ACLFilter) => {
    const { formatMessage } = useLocale();
    // 过滤参数
    const [filter, setFilter] = useState<ACLFilter>(filterParam);
    const flynet = useContext(FlynetGeneralContext);
    // 访问控制策略是否在加载中
    const [aclPolicyLoading, setACLPolicyLoading] = useState(true);
    // 访问控制策略
    const [aclPolicy, setACLPolicy] = useState<ACLPolicy>();
    // 访问策略
    const [acls, setACLs] = useState<AclOrigin[]>([]);
    // 所有的访问策略
    const [allACLs, setAllACLs] = useState<AclOrigin[]>([]);
    // 存储的访问策略
    const [storedACLs, setStoredACLs] = useState<AclOrigin[]>([]);


    // 服务组列表
    const [groups, setGroups] = useState<AclGroup[]>([]);
    // 当前选中服务组名称
    const [curGroup, setCurGroup] = useState<AclGroup>();


    // 选中的访问策略
    const [selectedACL, setSelectedACL] = useState<AclOrigin>();
    //  选中的访问策略索引
    const [selectedACLIndex, setSelectedACLIndex] = useState<number>(-1);

    // 规则编辑弹窗是否显示
    const [ruleEditVisible, setRuleEditVisible] = useState(false);
    // 规则删除弹窗是否显示
    const [ruleDeleteVisible, setRuleDeleteVisible] = useState(false);
    // 规则启用弹窗是否显示
    const [ruleEnableVisible, setRuleEnableVisible] = useState(false);
    // 规则禁用弹窗是否显示
    const [ruleDisableVisible, setRuleDisableVisible] = useState(false);
    // 用户组
    const [userGroups, setUserGroups] = useState<UserGroup[]>([]);
    // 设备组
    const [machineGroups, setMachineGroups] = useState<MachineGroup[]>([]);
    // 从策略组中删除弹出框是否可见
    const [removeFromGroupVisible, setRemoveFromGroupVisible] = useState(false);
    // 编辑策略组弹出框是否可见
    const [editGroupVisible, setEditGroupVisible] = useState(false);
    useEffect(() => {
        flylayerClient.listUserGroups({
            flynetId: flynet.id
        }).then((res) => {
            setUserGroups(res.groups)
        })
    }, []);


    const [total, setTotal] = useState(0);


    const getSrcDstText = (txt: string) => {
        return <span className={styles.srcDstText} title={txt}>{txt}</span>
    }

    const getSrcDisplay = (ar: AclOrigin_Resource, showLabel: boolean) => {
        let src = ar.value;
        let display = src;
        if (ar.type == AclOrigin_ResourceType.ALL) {
            display = formatMessage({ id: 'policies.selector.all' });
        }
        else if (ar.type == AclOrigin_ResourceType.IP) {
            display = "IP:" + src;
        }
        else if (ar.type == AclOrigin_ResourceType.IP_GROUP) {
            display = (showLabel ? formatMessage({ id: 'policies.label.resourceGroup' }) + ":" : "") + src;
        }
        else if (ar.type == AclOrigin_ResourceType.USER) {
            const user = mapUser.get(src);
            if (user) {
                display = (showLabel ? formatMessage({ id: 'policies.label.user' }) + ":" : "") + user.displayName + '(' + user.loginName + ')';
            } else {
                display = (showLabel ? formatMessage({ id: 'policies.label.user' }) + ":" : "") + src;
            }
        }
        else if (ar.type == AclOrigin_ResourceType.USER_GROUP) {
            let groupName = src.replace('group:', '');

            let displayName = '';
            userGroups.forEach(userGroup => {
                if (userGroup.name == groupName) {
                    displayName = userGroup.alias ? `${userGroup.alias}(${userGroup.name})` : userGroup.name

                }
            })
            if (!displayName) {
                displayName = groupName;
            }
            display = showLabel ? formatMessage({ id: 'policies.label.userGroup' }) + ":" + displayName : displayName;
        }
        else if (ar.type == AclOrigin_ResourceType.USER_AUTO_GROUP) {
            if (src == 'autogroup:self') {
                display = showLabel ? formatMessage({ id: 'policies.label.builtinUserGroup' }) + ":" + formatMessage({ id: 'policies.builtinGroup.self' }) : formatMessage({ id: 'policies.builtinGroup.self' });
            } else if (src == 'autogroup:members') {
                display = showLabel ? formatMessage({ id: 'policies.label.builtinUserGroup' }) + ":" + formatMessage({ id: 'policies.builtinGroup.members' }) : formatMessage({ id: 'policies.builtinGroup.members' });
            } else if (src == 'autogroup:internet') {
                display = showLabel ? formatMessage({ id: 'policies.label.builtinUserGroup' }) + ":" + formatMessage({ id: 'policies.builtinGroup.internet' }) : formatMessage({ id: 'policies.builtinGroup.internet' });
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE) {
            const machine = mapMachine.get(src)
            if (machine) {
                display = (showLabel ? formatMessage({ id: 'policies.label.device' }) + ":" : "") + machine.name;
            } else {
                display = ''
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE_GROUP) {
            const machineGroup = mapMachineGroup.get(src);
            if (machineGroup) {
                display = (showLabel ? formatMessage({ id: 'policies.label.deviceGroup' }) + ":" : "") + `${machineGroup.alias}(${machineGroup.name})`
            } else {
                display = ''
            }
        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE) {
            const service = mapServicesData.get(src.replace('svc:', ''));
            if (service) {
                display = (showLabel ? formatMessage({ id: 'policies.label.service' }) + ":" : "") + getSimpleServiceName(service);
            }
        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
            const serviceGroup = mapServicesGroupData.get(src.replace('svg:', ''));
            if (serviceGroup) {
                display = (showLabel ? formatMessage({ id: 'policies.label.serviceGroup' }) + ":" : "") + getSimpleServiceGroupName(serviceGroup);
            }
        }
        else if (ar.type == AclOrigin_ResourceType.EXPRESSION) {
            if (src.startsWith('exp:')) {
                display = src.replace('exp:', showLabel ? formatMessage({ id: 'policies.label.expression' }) + ":" : "");
            }
            else if (src.startsWith('l7:exp:')) {
                display = src.replace('l7:exp:', showLabel ? formatMessage({ id: 'policies.label.l7Expression' }) + ":" : "");
            }
        }
        else if (ar.type == AclOrigin_ResourceType.TAG) {
            display = src.replace('tag:', showLabel ? formatMessage({ id: 'policies.label.tag' }) + ":" : "");
        }



        return display;
    }

    const getDstDisplay = (ar: AclOrigin_Resource, showLabel: boolean) => {
        let dst = ar.value
        let display = '';

        if (ar.type == AclOrigin_ResourceType.ALL) {
            display = formatMessage({ id: 'policies.selector.all' }) + ":" + dst.replace('*:', '');
        }
        else if (ar.type == AclOrigin_ResourceType.IP) {
            if (dst.match(/\d+\.\d+\.\d+\.\d+/)) {
                display = (showLabel ? "IP:" : "") + dst;
            }
            //  是否是IP：后面加文本 
            else if (dst.match(/\d+\.\d+\.\d+\.\d+:/)) {
                display = "IP:" + dst;
            }
        }
        else if (ar.type == AclOrigin_ResourceType.IP_GROUP) {
            let displayLable = '';
            let port = '';

            if (ipGroups) {
                if (dst.indexOf(':') > -1) {
                    let arr = dst.split(':');
                    if (arr.length == 2) {
                        let ig = ipGroups.find(ig => ig.name == arr[0])
                        if (ig) {
                            displayLable = ig.description
                        }
                        port = arr[1];
                    }
                } else {
                    let ig = ipGroups.find(ig => ig.name == dst)
                    if (ig) {
                        displayLable = ig.description
                    }
                }

            }
            if (displayLable) {
                display = (showLabel ? formatMessage({ id: 'policies.label.resourceGroup' }) + ":" : "") + displayLable + (port ? ':' + port : '');
            } else {
                display = '';
            }
        }
        else if (ar.type == AclOrigin_ResourceType.USER) {
            let arr = dst.split(':');
            if (arr.length >= 1) {
                const user = mapUser.get(arr[0]);
                if (user) {
                    display = (showLabel ? formatMessage({ id: 'policies.label.user' }) + ":" : "") + user.displayName + '(' + user.loginName + ')';
                } else {
                    display = (showLabel ? formatMessage({ id: 'policies.label.user' }) + ":" : "") + arr[0];
                }
            }
            if (arr.length == 2 && display != '') {
                display += ':' + arr[1];
            }
        }
        else if (ar.type == AclOrigin_ResourceType.USER_GROUP) {
            let groupName = dst.split(':')[1];
            let displayName = '';
            userGroups.forEach(userGroup => {
                if (userGroup.name == groupName) {
                    displayName = userGroup.alias ? `${userGroup.alias}(${userGroup.name})` : userGroup.name
                }
            })
            if (!displayName) {
                displayName = groupName;
            }
            let groupArr = dst.split(':');
            if (groupArr.length == 3) {
                display = (showLabel ? formatMessage({ id: 'policies.label.userGroup' }) + ":" : "") + displayName + ':' + groupArr[2];
            } else {
                display = (showLabel ? formatMessage({ id: 'policies.label.userGroup' }) + ":" : "") + displayName;
            }
        }
        else if (ar.type == AclOrigin_ResourceType.USER_AUTO_GROUP) {
            if (dst.startsWith('autogroup:self')) {
                display = dst.replace('autogroup:self:', showLabel ? formatMessage({ id: 'policies.label.builtinUserGroup' }) + ":" + formatMessage({ id: 'policies.builtinGroup.self' }) + ":" : "");
            } else if (dst.startsWith('autogroup:members')) {
                display = dst.replace('autogroup:members:', showLabel ? formatMessage({ id: 'policies.label.builtinUserGroup' }) + ":" + formatMessage({ id: 'policies.builtinGroup.members' }) + ":" : "");
            } else if (dst.startsWith('autogroup:internet')) {
                display = dst.replace('autogroup:internet:', showLabel ? formatMessage({ id: 'policies.label.builtinUserGroup' }) + ":" + formatMessage({ id: 'policies.builtinGroup.internet' }) + ":" : "");
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE) {

            let arr = dst.split(':');
            if (arr.length >= 1) {
                const machine = mapMachine.get(arr[0])
                if (machine) {
                    display = (showLabel ? formatMessage({ id: 'policies.label.device' }) + ":" : "") + machine.name;
                    if (arr.length == 2) {
                        display += ':' + arr[1];
                    }
                }
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE_GROUP) {
            let arr = dst.split(':');
            if (arr.length >= 1) {

                let arr = dst.split(':');
                if (arr.length >= 1) {
                    const machineGroup = mapMachineGroup.get(arr[0]);
                    if (machineGroup) {
                        display = (showLabel ? formatMessage({ id: 'policies.label.deviceGroup' }) + ":" : "") + `${machineGroup.alias}(${machineGroup.name})`;
                        if (arr.length == 2) {
                            display += ':' + arr[1];
                        }
                    }
                }
            }
            if (arr.length == 2) {
                display += ':' + arr[1];
            }
        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE) {
            let arr = dst.split(':');
            if (arr.length >= 2) {
                const service = mapServicesData.get(arr[1]);
                if (service) {
                    display = (showLabel ? formatMessage({ id: 'policies.label.service' }) + ":" : "") + getSimpleServiceName(service);
                }
            }
            if (arr.length == 3) {
                display += ':' + arr[2];
            }
        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
            let arr = dst.split(':');
            if (arr.length >= 2) {
                const serviceGroup = mapServicesGroupData.get(arr[1]);
                if (serviceGroup) {
                    display = (showLabel ? formatMessage({ id: 'policies.label.serviceGroup' }) + ":" : "") + getSimpleServiceGroupName(serviceGroup);
                }
            }
            if (arr.length == 3) {
                display += ':' + arr[2];
            }
        }
        else if (ar.type == AclOrigin_ResourceType.EXPRESSION) {
            if (dst.startsWith('exp:')) {
                display = dst.replace('exp:', showLabel ? formatMessage({ id: 'policies.label.expression' }) + ":" : "");
            } else if (dst.startsWith('l7:exp:')) {
                display = dst.replace('l7:exp:', showLabel ? formatMessage({ id: 'policies.label.l7Expression' }) + ":" : "");
            }
        }
        else if (ar.type == AclOrigin_ResourceType.TAG) {
            display = dst.replace('tag:', showLabel ? formatMessage({ id: 'policies.label.tag' }) + ":" : "");
        }


        return display;
    }

    const columns = [
        {
            title: formatMessage({ id: 'policies.table.policyName' }),
            dataIndex: 'name',
            key: 'name',
            width: 160,
            render: (field: string, acl: AclOrigin, index: number) => {
                return <>
                    <Popover position='bottomLeft' content={<div className='p10 mw300'><Paragraph>{acl.description}</Paragraph></div>}>
                        <Title heading={6}>
                            {acl.name}
                        </Title>
                    </Popover>
                </>;
            },
        },
        {
            title: <>{formatMessage({ id: 'policies.table.priority' })} <Popover content={<div className='p10'>{formatMessage({ id: 'policies.table.priorityTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover> </>,
            dataIndex: '',
            key: '',
            width: 100,
            render: (field: string, acl: AclOrigin, index: number) => {
                return <>
                    {acl.priority <= 0 ? '1' : acl.priority}
                </>;
            }
        },
        {
            title: formatMessage({ id: 'policies.table.policyGroup' }),
            dataIndex: 'aclGroups',
            key: 'aclGroups',
            width: 110,
            render: (field: string, acl: AclOrigin, index: number) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{acl.aclGroups.map((g, i) => <Tag key={i} size='large' style={{ maxWidth: 100 }}>{g.alias}</Tag>)}</Space></div>
            }

        },
        {
            title: formatMessage({ id: 'policies.table.source' }),
            dataIndex: 'src',
            key: 'src',
            width: 300,
            render: (field: string, acl: AclOrigin, index: number) => {

                return <Expandable expand={acl.src.length > 5} collapseHeight={60}
                    collapseChildren={<Space style={{ flexWrap: 'wrap', maxWidth: 260 }}>
                        {acl.src.map((val, index) => {
                            if (index > 4) {
                                return null;
                            }
                            return <Tag key={index} size='small' className={styles.tagSrc}>{getSrcDstText(getSrcDisplay(val, true))}<Copyable content={val.value} /></Tag>
                        })}
                    </Space>}
                ><Space style={{ flexWrap: 'wrap', maxWidth: 260 }}>
                        {acl.src.map((val, index) => <Tag key={index} size='small' className={styles.tagSrc}>{getSrcDstText(getSrcDisplay(val, true))}<Copyable content={val.value} /></Tag>)}
                    </Space></Expandable>
            }
        },
        {
            title: <>{formatMessage({ id: 'policies.table.action' })} <Popover content={<div className='p10'>{formatMessage({ id: 'policies.table.actionTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover> </>,
            dataIndex: 'action',
            key: 'action',
            width: 100,
            render: (field: string, acl: AclOrigin) => {
                return <>{acl.action == 'accept' ? <Tag color='green'>{formatMessage({ id: 'policies.action.allow' })}</Tag> : ''}
                    {acl.action == 'reject' ? <Tag color='red'>{formatMessage({ id: 'policies.action.deny' })}</Tag> : ''}
                </>
            }
        },
        {
            title: formatMessage({ id: 'policies.table.target' }),
            dataIndex: 'dst',
            key: 'dst',
            width: 300,
            render: (field: string, acl: AclOrigin, index: number) => {

                return <Expandable expand={acl.dst.length > 5}
                    collapseChildren={<Space style={{ flexWrap: 'wrap', maxWidth: 260 }}>
                        {acl.dst.map((val, index) => {
                            if (index > 4) {
                                return null;
                            }
                            return <React.Fragment key={index}>{getDstDisplay(val, true) &&
                                <Tag size='small' className={styles.tagDst}>{getSrcDstText(getDstDisplay(val, true))}<Copyable content={val.value} /></Tag>}
                            </React.Fragment>
                        })}
                    </Space>}
                    collapseHeight={60}><Space style={{ flexWrap: 'wrap', maxWidth: 260 }}>
                        {acl.dst.map((val, index) =>
                            <React.Fragment key={index}>{getDstDisplay(val, true) &&
                                <Tag size='small' className={styles.tagDst}>{getSrcDstText(getDstDisplay(val, true))}<Copyable content={val.value} /></Tag>}
                            </React.Fragment>)}
                    </Space></Expandable>
            }
        },
        {
            title: formatMessage({ id: 'common.status' }),
            dataIndex: 'disabled',
            key: 'disabled',
            width: 80,
            render: (fieldd: string, acl: AclOrigin, index: number) => {
                return <>
                    {acl.disabled ? <Tag color='red'>{formatMessage({ id: 'common.disabled' })}</Tag> : <Tag color='green'>{formatMessage({ id: 'common.enabled' })}</Tag>}
                </>;
            }
        },

        {
            title: '',
            dataIndex: 'option',
            key: 'option',
            render: (fieldd: string, acl: AclOrigin, index: number) => {
                return <><div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            {acl.disabled ? <Dropdown.Item
                                disabled={acl.sys}
                                onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleEnableVisible(true);
                                    setSelectedACLIndex(index);
                                }}
                            >{formatMessage({ id: 'policies.action.enable' })}</Dropdown.Item> :
                                <Dropdown.Item disabled={acl.sys} onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleDisableVisible(true);
                                    setSelectedACLIndex(index);
                                }}>{formatMessage({ id: 'policies.action.disable' })}</Dropdown.Item>
                            }

                            {/* <Dropdown.Item disabled>观察策略</Dropdown.Item> */}
                            <Divider />
                            <Dropdown.Item
                                disabled={acl.sys}
                                onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleEditVisible(true);
                                    setSelectedACLIndex(index);
                                }}
                            >{formatMessage({ id: 'policies.action.editPolicy' })}</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                disabled={acl.sys}
                                onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleDeleteVisible(true);
                                    setSelectedACLIndex(index);
                                }}
                                type='danger'>{formatMessage({ id: 'policies.action.deletePolicy' })}</Dropdown.Item>

                            {/* <Dropdown.Item
                                onClick={() => {
                                    setEditGroupVisible(true);
                                    setSelectedACL(acl);
                                }}
                            >
                                编辑策略组
                            </Dropdown.Item> */}
                            {curGroup && curGroup.type == GroupType.GROUP_STATIC && <Dropdown.Item
                                onClick={() => {
                                    setSelectedACL(acl);
                                    setRemoveFromGroupVisible(true);

                                }}
                            >{formatMessage({ id: 'policies.action.removeFromGroup' })}</Dropdown.Item>}
                        </Dropdown.Menu>}><Button><IconMore className='align-v-center' /></Button>
                </Dropdown>
                </div>
                </>;
            }
        },
    ];


    // 获取访问控制策略
    const getACLPolicy = async () => {

        const res = await flylayerClient.listAclGroups({
            flynetId: flynet.id
        })

        let curGroup: AclGroup | undefined;
        setGroups(res.groups)
        if (res.groups) {
            res.groups.forEach(g => {
                if (g.name == filterParam.group) {
                    curGroup = g;
                }
            })
        }
        setCurGroup(curGroup)

        setACLPolicyLoading(true);

        const queryServices = flylayerClient.listServices({
            flynetId: flynet.id
        });
        const queryServicesGroups = flylayerClient.listServiceGroups({
            flynetId: flynet.id
        });
        const queryUsers = flylayerClient.listUsers({
            flynetId: flynet.id
        });
        const queryMachines = flylayerClient.listMachines({
            flynetId: flynet.id
        });


        flylayerClient.listMachineGroups({
            flynetId: flynet.id
        }).then((res) => {
            setMachineGroups(res.groups)
            if (res.groups && res.groups.length > 0) {
                res.groups.forEach(g => {
                    mapMachineGroup.set(g.name, g);
                })
            }
        })


        Promise.all([queryServices, queryServicesGroups, queryUsers, queryMachines]).then(res => {
            if (res) {
                mapServicesData = new Map();
                res[0].services.forEach(service => {
                    mapServicesData.set(service.name, service)
                })

                mapServicesGroupData = new Map();
                res[1].serviceGroups.forEach(serviceGroup => {
                    mapServicesGroupData.set(serviceGroup.fullName, serviceGroup)
                })

                mapUser = new Map();
                res[2].users.forEach(user => {
                    mapUser.set(user.loginName, user)
                })

                mapMachine = new Map();
                res[3].machines.forEach(machine => {
                    mapMachine.set(machine.ipv4, machine)
                })
            }

            flylayerClient.getACLPolicy({
                flynetId: flynet.id
            }).then(res => {
                if (res.policy) {
                    setACLPolicy(res.policy);

                    flylayerClient.listAclOrigin({
                        flynetId: flynet.id
                    }).then(res => {
                        if (res.origins) {
                            let list = res.origins;

                            list.sort((a, b) => {
                                // 先按照优先级排序，优先级高的排在前面，最后按照拒绝策略排在前面
                                if (a.priority > b.priority) {
                                    return 1;
                                } else if (a.priority < b.priority) {
                                    return -1;
                                } else {
                                    if (a.action == 'reject' && b.action == 'accept') {
                                        return -1;
                                    } else if (a.action == 'accept' && b.action == 'reject') {
                                        return 1;
                                    } else {
                                        return 0;
                                    }
                                }
                            })


                            setAllACLs(list);
                            setStoredACLs(list);
                            const filteredList = doFilter(list, filter, curGroup);
                            setACLs(filteredList);
                            setTotal(filteredList.length)
                        }
                    }).catch(err => {
                        console.error(err);
                    }).finally(() => {
                        setACLPolicyLoading(false);
                    })

                }
            }).catch(err => {
                console.error(err);
                setACLPolicyLoading(false);
                Notification.error({ content: formatMessage({ id: 'policies.error.fetchFailed' }), position: "bottomRight" })
            })
        });
    };

    // 更新访问控制策略是否正在加载
    const [aclPolicySaveLoading, setACLPolicySaveLoading] = useState(false);

    // 保存访问控制策略
    const saveACLPolicy = (policy: ACLPolicy) => {

        setACLPolicySaveLoading(true);

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: policy
        }).then(() => {
            setACLPolicy(policy);
            Notification.success({ content: formatMessage({ id: 'common.saveSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'common.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setACLPolicySaveLoading(false);
        })
    }


    // 过滤访问控制策略
    const doFilter = (src: Array<AclOrigin>, filter: ACLFilter, group?: AclGroup) => {
        if (!src || src.length == 0) {
            setTotal(0)
            return src;
        }


        if (filter.query == ''
            && filter.services == ''
            && filter.servicesGroup == ''
            && filter.services == ''
            && (!filter.users || filter.users.length == 0)
            && filter.status == ''
            && !group
        ) {
            setTotal(src.length)
            return src;
        }
        const filterdList = src.filter((acl) => {
            let find = true;
            if (filter.query != '') {
                let findName = false;
                let findSrc = false;
                let findDst = false;
                if (caseInsensitiveIncludes(acl.name, filter.query) || caseInsensitiveIncludes(acl.description, filter.query)) {
                    findName = true;
                }

                let findSrcObj = acl.src.find((ar) => {
                    let srcDisplay = getSrcDisplay(ar, false);
                    return caseInsensitiveIncludes(ar.value, filter.query) || caseInsensitiveIncludes(srcDisplay, filter.query)
                })
                if (findSrcObj) {
                    findSrc = true
                }
                let findDstObj = acl.dst.find((ar) => {
                    let dstDisplay = getDstDisplay(ar, false);
                    return caseInsensitiveIncludes(ar.value, filter.query) || caseInsensitiveIncludes(dstDisplay, filter.query)
                })

                if (findDstObj) {
                    findDst = true;
                }

                if (filter.query == formatMessage({ id: 'policies.selector.all' })) {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('*') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('*') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }

                if (filter.query == formatMessage({ id: 'policies.builtinGroup.self' })) {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('autogroup:self') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('autogroup:self') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }
                if (filter.query == formatMessage({ id: 'policies.builtinGroup.members' })) {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('autogroup:members') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('autogroup:members') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }
                if (filter.query == formatMessage({ id: 'policies.builtinGroup.internet' })) {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('autogroup:internet') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('autogroup:internet') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }

                if (!findName && !findSrc && !findDst) {
                    return false;
                }

            }

            if (filter.services != '') {
                let findSrc = false;
                let findDst = false;
                let findSrcServices = acl.src.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE) {
                        return ar.value.indexOf(filter.services) >= 0
                    }
                });

                if (findSrcServices) {
                    findSrc = true;
                }
                let findDstServices = acl.dst.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE) {
                        return ar.value.indexOf(filter.services) >= 0
                    }
                });

                if (findDstServices) {
                    findDst = true;
                }

                if (!findSrc && !findDst) {
                    return false;
                }

            }

            if (filter.servicesGroup != '') {

                let findSrc = false;
                let findDst = false;
                let findSrcServicesGroup = acl.src.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
                        return ar.value.indexOf(filter.servicesGroup) >= 0
                    }
                });
                if (findSrcServicesGroup) {
                    findSrc = true;
                }
                let findDstServicesGroup = acl.dst.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
                        return ar.value.indexOf(filter.servicesGroup) >= 0
                    }
                });

                if (findDstServicesGroup) {
                    findDst = true;
                }

                if (!findSrc && !findDst) {
                    return false;
                }
            }

            if (filter.users && filter.users.length > 0) {
                let findSrc = false;
                let findDst = false;
                let findSrcUser = acl.src.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.USER) {
                        let hasUser = false;
                        filter.users.forEach(user => {
                            if (ar.value.indexOf(user) >= 0) {
                                hasUser = true;
                            }
                        })
                        return hasUser;
                    }
                });
                if (findSrcUser) {
                    findSrc = true;
                }
                let findDstUser = acl.dst.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.USER) {
                        let hasUser = false;
                        filter.users.forEach(user => {
                            if (ar.value.indexOf(user) >= 0) {
                                hasUser = true;
                            }
                        })
                        return hasUser;
                    }
                });

                if (findDstUser) {
                    findDst = true;
                }

                if (!findSrc && !findDst) {
                    return false;
                }
            }

            if (filter.status != '') {
                if (acl.disabled != (filter.status == 'Disable')) {
                    return false;
                }
            }

            if (group) {
                let findGroup = false;
                group.acls.forEach((val: AclOrigin) => {
                    if (acl.id == val.id) {
                        findGroup = true;
                    }
                })

                if (!findGroup) {
                    return false;
                }
            }

            return true;
        })
        setTotal(filterdList.length)
        return filterdList;
    }

    const reloadAclOrigin = async () => {
        getACLPolicy()
    }

    const [syncAclOriginLoading, setSyncAclOriginLoading] = useState(false);
    const syncAclOrigin = () => {
        setSyncAclOriginLoading(true);
        flylayerClient.syncAclOrigin({
            flynetId: flynet.id
        }).then(() => {
            getACLPolicy()
            Notification.success({ content: formatMessage({ id: 'policies.sync.success' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.sync.failed' }), position: "bottomRight" })
        }).finally(() => {
            setSyncAclOriginLoading(false);
        })
    }


    useEffect(() => {
        getACLPolicy();
    }, [])

    const [ipGroups, setIpGroups] = useState<IpGroup[]>([]);

    // 查询资源组
    const queryIpGroup = async () => {
        let res = await getFlynet(flynet.id);

        if (res && res.flynet && res.flynet.ipGroup) {
            if (res.flynet.ipGroup) {
                let data: IpGroup[] = JSON.parse(res.flynet.ipGroup);
                if (data && data.length > 0) {
                    setIpGroups(data);
                } else {
                    setIpGroups([]);
                }
            }
        }
    }

    useEffect(() => {
        queryIpGroup();
    }, [])

    const handleFilterChange = (param: ACLFilter) => {
        setFilter(param);
        setACLs(doFilter(allACLs, param, curGroup));

    }

    useEffect(() => {
        if (aclPolicy) {
            setAllACLs(storedACLs);
            setACLs(doFilter(storedACLs, filter, curGroup));
        }

    }, [aclPolicy])

    const requery = () => {
        queryIpGroup();
        getACLPolicy();
    }

    const handleGroupChange = (group?: AclGroup) => {
        setACLPolicyLoading(true);
        if (!group) {
            setCurGroup(undefined);
            setACLs(storedACLs);
            setACLPolicyLoading(false);
            return;
        }
        setACLs(doFilter(storedACLs, filter, group));
        setACLPolicyLoading(false);
    };

    return {
        aclPolicyLoading,
        aclPolicy,
        acls,
        setACLPolicy,
        userGroups,
        columns,
        saveACLPolicy,
        aclPolicySaveLoading,
        selectedACL,
        setSelectedACL,
        ruleEditVisible,
        setRuleEditVisible,
        ruleDeleteVisible,
        setRuleDeleteVisible,
        ruleEnableVisible,
        setRuleEnableVisible,
        ruleDisableVisible,
        setRuleDisableVisible,
        selectedACLIndex,
        setSelectedACLIndex,
        filter,
        setFilter,
        handleFilterChange,
        getSrcDisplay,
        getDstDisplay,
        storedACLs,
        reloadAclOrigin,
        syncAclOrigin,
        syncAclOriginLoading,
        total,
        ipGroups,
        requery,
        groups,
        curGroup,
        setCurGroup,
        machineGroups,
        handleGroupChange,
        removeFromGroupVisible,
        setRemoveFromGroupVisible,
        editGroupVisible,
        setEditGroupVisible,
    }

}

export default useTable;