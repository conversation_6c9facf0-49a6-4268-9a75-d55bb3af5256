import { FC, useState, useContext, useEffect } from 'react';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import SearchFilter from '@/components/search-filter-combo';
import { AclOrigin_Resource, AclOrigin_ResourceType, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { Toast, Modal, Typography, Space, Select, Spin } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
import UserSelector from './components/user-selector';
// import SerrvicesSelector from './components/services-selector';
// import ServicesGroupSelector from './components/services-group-selector';
import StatusSelector, { getStatusDisplayValue } from './components/status-selector';

import useTable from '@/pages/policies/useTable'
const { Paragraph, Text } = Typography;
interface FilterParam {
    name: string,
    label: string,
    placeholder: string,
    value: string | any,
    fixed?: boolean,
    filterComponent?: FC<{
        value: string | string[] | any,
        onChange: (val: string | string[] | any) => void
    }>,
    funGetDisplayValue?: (val: string | any) => string
}

export type ACLFilter = {
    query: string;
    user: string[];
    // services: string;
    // servicesGroup: string;
    status: string;
    group: string
}
interface Props {
    close: () => void;
    success?: () => void;
}

const Index: FC<Props> = (props) => {

    const { getSrcDisplay } = useTable({
        query: '',
        user: '',
        services: '',
        servicesGroup: '',
        status: '',
        groups: ''
    });

    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);



    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);

    const initFilterParams = (aclGroups: AclGroup[]) => {

        let initParams: FilterParam[] = [{
            name: 'query',
            placeholder: formatMessage({ id: 'policies.filter.searchPlaceholder' }),
            label: formatMessage({ id: 'policies.filter.query' }),
            value: '',
        },
        {
            name: 'user',
            placeholder: formatMessage({ id: 'policies.resource.user' }),
            label: formatMessage({ id: 'policies.resource.user' }),
            value: '',
            filterComponent: UserSelector,
            fixed: true,
            funGetDisplayValue: (val) => {

                return getSrcDisplay(new AclOrigin_Resource({
                    value: val,
                    type: AclOrigin_ResourceType.USER
                }), false)
            }
        },
        // {
        //     name: 'services',
        //     placeholder: formatMessage({ id: 'policies.resource.service' }),
        //     label: formatMessage({ id: 'policies.resource.service' }),
        //     value: '',
        //     filterComponent: SerrvicesSelector,
        //     fixed: true,
        //     funGetDisplayValue: (val) => {
        //         return getSrcDisplay(
        //             new AclOrigin_Resource({
        //                 value: val,
        //                 type: AclOrigin_ResourceType.SERVICE
        //             }), false)
        //     }
        // }, 
        // {
        //     name: 'servicesGroup',
        //     placeholder: formatMessage({ id: 'policies.resource.serviceGroup' }),
        //     label: formatMessage({ id: 'policies.resource.serviceGroup' }),
        //     value: '',
        //     filterComponent: ServicesGroupSelector,
        //     fixed: true,
        //     funGetDisplayValue: (val) => {
        //         return getSrcDisplay(new AclOrigin_Resource({
        //             value: val,
        //             type: AclOrigin_ResourceType.SERVICE_GROUP
        //         }), false)
        //     }
        // }, 
        {
            name: 'status',
            placeholder: formatMessage({ id: 'policies.filter.status' }),
            label: formatMessage({ id: 'policies.filter.status' }),
            value: '',
            fixed: true,
            filterComponent: StatusSelector,
            funGetDisplayValue: getStatusDisplayValue
        }, {
            name: 'group',
            placeholder: '请选择策略组',
            label: '策略组',
            fixed: true,
            value: '',
            filterComponent: ({ value, onChange }) => {
                const [val, setVal] = useState(value ? value.split(',') : []);
                return <Space>
                    <Text type="tertiary" style={{ width: 100 }}>策略组</Text>

                    <Select
                        placeholder='请选择策略组'
                        value={val}
                        onChange={(val) => {
                            setVal(val);
                            onChange((val as any).join(','));
                        }}
                        style={{ width: 290 }}
                        optionList={aclGroups.map(group => {
                            return { value: group.id + '', label: group.alias }
                        })}
                        multiple
                        maxTagCount={1}
                    >
                    </Select>
                </Space>
            },
            funGetDisplayValue: (val: string) => {
                let names: string[] = [];
                if (val) {
                    let values = val.split(',');
                    values.forEach(v => {
                        let group = aclGroups.find(group => group.id + '' == v);
                        if (group) {
                            names.push(group.alias);
                        }
                    })
                }
                return names.join(',');
            }
        }]

        setFilterParams(initParams)
    }


    const query = async () => {
        const res = await flylayerClient.listAclGroups({
            flynetId: flynet.id
        })
        initFilterParams(res.groups)
    }


    useEffect(() => {
        query()
    }, []);

    const [filter, setFilter] = useState<ACLFilter>(
        {
            query: '',
            user: [],
            // services: '',
            // servicesGroup: '',
            status: '',
            group: ''
        } as ACLFilter
    );
    const [saveLoading, setSaveLoading] = useState(false);

    const handleSubmit = async () => {
        setSaveLoading(true);

        let queryArray = [];

        if (filter.user && filter.user.length > 0) {
            queryArray.push(`user=${filter.user}`)
        }

        if (filter.group) {
            queryArray.push(`group=${filter.group}`);
        }


        if (filter.status == 'Enable') {
            queryArray.push(`disabled=false`);
        } else if (filter?.status == 'Disable') {
            queryArray.push('disabled=true');
        }

        // if (filter.temporary == 'true') {
        //     queryArray.push('temporary=true');
        // } else if (filter.temporary == 'false') {
        //     queryArray.push('temporary=false');
        // }

        if (filter.query != "" && filter.query.trim() != "") {
            queryArray.push(`query=${encodeURIComponent(filter.query || '')}`);
        }


        flylayerClient.exportAclOrigins({
            flynetId: flynet.id,
            query: queryArray.join('&')
        }).then((res) => {
            setSaveLoading(false);
            if (res) {
                props.success && props.success();
                props.close();
            }
            window.open(res.downloadUrl, '_blank');
        }
        ).catch((err) => {
            setSaveLoading(false);
            if (err) {
                if (err.response) {
                    if (err.response.status === 403) {
                        Toast.error(formatMessage({ id: 'users.dataExport.noPermission' }));
                    } else {
                        Toast.error(err.response.data.message);
                    }
                }
            }
        });

    }

    return (
        <>
            <Modal
                width={500}
                title={formatMessage({ id: 'policies.dataExport.modal.title' })}
                visible={true}
                onCancel={props.close}
                onOk={handleSubmit}
                okText={formatMessage({ id: 'policies.dataExport.modal.export' })}
                okButtonProps={{ loading: saveLoading }}
                className='semi-modal'
                maskClosable={false}
            >
                {filterParams.length == 0 ? <Spin size='large' /> : <>
                <Paragraph type="tertiary" className='mb20'>
                    {formatMessage({ id: 'policies.dataExport.description' })}
                </Paragraph>
                <SearchFilter onChange={(val: string, filterParam) => {
                    setFilter({ ...filter, [filterParam.name]: val } as ACLFilter)

                    const newFilterParams = filterParams.map((item) => {
                        if (item.name == filterParam.name) {
                            item.value = val;
                        }
                        return item;
                    })
                    setFilterParams(newFilterParams);
                }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
                </>}
            </Modal>

        </>
    );
};

export default Index;