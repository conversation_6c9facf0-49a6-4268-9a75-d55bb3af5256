import React, { FC, useState, useContext } from 'react';
import { Typography, Space, Tree, Tooltip } from '@douyinfe/semi-ui';
import { AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { IconList, IconCheckList } from '@douyinfe/semi-icons';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

const { Text } = Typography;

const Index: FC<{
    value: string,
    onChange: (val: string) => void,
    treeData: any,
    getMapGroupData: () => Map<string, AclGroup>
}> = (props) => {
    const treeData = props.treeData;
    const getMapGroupData = props.getMapGroupData;

    const flynet = useContext(FlynetGeneralContext);

    const queryGroup = async () => {

        const res = await flylayerClient.listAclGroups({
            flynetId: flynet.id
        })

        let curGroup: AclGroup | undefined;
        setGroups(res.groups)
        if (res.groups) {
            res.groups.forEach(g => {
                if (g.name == props.value) {
                    curGroup = g;
                }
            })
        }
        setCurGroup(curGroup)
    }

    // 服务组列表
    const [groups, setGroups] = useState<AclGroup[]>([]);
    // 当前选中服务组名称
    const [curGroup, setCurGroup] = useState<AclGroup>();


    return <>
        <Space>
            <Text type="tertiary" style={{ width: 100 }}>策略组</Text>

            <Tree
                expandAll
                value={curGroup ? curGroup.id + '' : ''}
                style={{ width: 400 }}
                onChange={(value) => {
                    let activeGroup: AclGroup | undefined = undefined;
                    let activeName = '';

                    if (value) {
                        groups.forEach(group => {
                            if (group.id + '' == value as string) {
                                setCurGroup(group);
                                activeGroup = group;
                                activeName = group.name;
                            }
                        })
                        setCurGroup(activeGroup);
                    } else {
                        setCurGroup(undefined);
                    }
                    props.onChange(activeName);
                }}
                treeData={treeData}
                renderLabel={(label, item) => {
                    let id = item?.value as string;
                    let aclGroup = getMapGroupData().get(id);

                    if (!aclGroup) {
                        return <Text ellipsis={{ showTooltip: true }} >{label}</Text>
                    }
                    return <Space style={{ verticalAlign: 'text-top' }} spacing={5}>{aclGroup.type == GroupType.GROUP_DYNAMIC ?
                        <Tooltip content="动态策略组">
                            <IconCheckList style={{ color: 'var(--semi-color-text-2)' }} /></Tooltip>
                        : <Tooltip content="静态策略组">
                            <IconList style={{ color: 'var(--semi-color-text-2)' }} /></Tooltip>}
                        <Tooltip content={aclGroup.name}>
                            <Text>{aclGroup.alias}</Text>
                        </Tooltip></Space>
                }}
            ></Tree>
        </Space>
    </>
}

export default Index;