import React, { FC, useEffect, useState, useContext } from 'react';
import useServicesGroup from '@/pages/services/useServicesGroup'
import { Select, Divider, Button, TreeSelect } from '@douyinfe/semi-ui'
import { useLocale } from '@/locales';



const Index: FC<{
    value: string,
    onChange: (val: string) => void,
    optList?: Array<{
        value: string,
        label: string
    }>
}> = (props) => {
    

    const [currentValue, setCurrentValue] = useState(props.value);
    const { serviceGroupTreeData } = useServicesGroup();
    return <>
        <TreeSelect
            zIndex={9999}
            className='mb10'
            style={{ width: 400 }}
            expandAll
            treeData={
                serviceGroupTreeData
            }
            onChangeWithObject={true}
            onChange={(val) => {
                if (val) {
                    const curValue = 'svg:' + (val as any).fullName; 
                    setCurrentValue(curValue)
                    props.onChange(curValue)
                }
            }}
            position='top'
            filterTreeNode
            showFilteredOnly
            dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
         ></TreeSelect>
    </>
}

export default Index;
