import { FC, useState } from 'react';
import { Typography, Table, Row, Col, Button, Space, Input, Breadcrumb, Divider } from '@douyinfe/semi-ui';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
const { Title } = Typography;
import Export from './export';
import useTable, { RecordFilter } from './useTable';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation } from 'react-router-dom';
import { ExportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import UserSelector from '@/components/user-selector';
import { useLocale } from '@/locales';


const getRecordFilter = (loc: Location): RecordFilter => {
    const keywords: string = getQueryParam('keywords', loc) as string;
    const actorsQuery = getQueryParam('actors', loc);

    let actors: string[] = [];
    if (actorsQuery && Array.isArray(actorsQuery)) {
        actors = actorsQuery as string[];
    }
    if (actorsQuery && typeof actorsQuery == 'string') {
        actors = [actorsQuery as string];
    }
    return {
        keywords: keywords || '',
        actors: actors || []
    };
}

const Index: FC = () => {
    const { formatMessage } = useLocale();
    const location = useLocation();
    const [createVisible, setCreateVisible] = useState(false);
    const initFilter = getRecordFilter(location);

    const {
        loading,
        data,
        total,
        page,
        setPage,
        pageSize,
        columns,
        queryRecord,
        filter,
        setFilter,
        handleFilterChange,
        handleSort,
        setReloadFlag
    } = useTable(initFilter);

    return <><div className='general-page'><Breadcrumb routes={
        [
            {
                path: `${BASE_PATH}/policies`,
                href: `${BASE_PATH}/policies`,
                name: formatMessage({ id: 'policies.allPolicies' })
            },
            {
                name: formatMessage({ id: 'users.breadcrumb.dataExport' }),
            }
        ]
    }>
    </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>{formatMessage({ id: 'users.dataExport.title' })}</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>{formatMessage({ id: 'policies.dataExport.export' })}</Button>
                </Space>
            </div></Col>
        </Row>

        <Divider className='mb20'></Divider>
        <Row className='mb10'>
            <Col span={24}>
                <Space>
                    <Input
                        placeholder={formatMessage({ id: 'users.dataExport.searchPlaceholder' })}
                        value={filter.keywords}
                        onChange={(value: string) => {
                            setFilter({ ...filter, keywords: value });
                            handleFilterChange({ ...filter, keywords: value });
                        }}

                        style={{ width: 400 }}
                        showClear
                        onClear={() => {
                            setFilter({ ...filter, keywords: '' });
                            handleFilterChange({ ...filter, keywords: '' });
                        }}
                    />
                    <UserSelector
                        style={{ width: 260 }}
                        value={filter.actors}
                        onChange={(value: string[]) => {
                            setFilter({ ...filter, actors: value });
                            handleFilterChange({ ...filter, actors: value });
                        }
                        }
                        onLoadingChange={(val) => { }}
                    />
                </Space>
            </Col>
        </Row>

        <Table
            dataSource={data}
            loading={loading}
            rowKey={(record?: ExportRecord) => record ? record.id + '' : ''}
            columns={columns}
            pagination={{
                pageSize: pageSize,
                currentPage: page,
                total: total,
                onPageChange: (page: number) => {
                    setPage(page);
                    queryRecord({ page: page, pageSize: pageSize });
                }
            }}
            // sortOrder={sortOrder}
            // sortField={sortField}
            onChange={handleSort}

            empty={<TableEmpty loading={loading} />}
        // onFilterChange={handleFilterChange}
        // filter={filter}
        // filterDropdownVisible={true}

        />
    </div>
        {createVisible && <Export close={() => setCreateVisible(false)} success={() => setReloadFlag(true)} />}
    </>
}
export default Index;