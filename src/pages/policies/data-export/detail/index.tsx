import { FC, useState } from 'react';
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Tag, Divider, Spin } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;

const Index: FC = () => {
    const { formatMessage } = useLocale();
    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/users`,
                        href: `${BASE_PATH}/users`,
                        name: formatMessage({ id: 'users.allUsers' })
                    },
                    {
                        path: `${BASE_PATH}/users/import`,
                        href: `${BASE_PATH}/users/import`,
                        name: formatMessage({ id: 'dataImport.title' }),
                    }, {
                        name: formatMessage({ id: 'users.dataExport.detail.title' })
                    }
                ]
            }>
            </Breadcrumb>
        </div>
    </>
}

export default Index;