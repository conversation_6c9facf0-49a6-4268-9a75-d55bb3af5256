import React, { useState, useContext, useEffect } from 'react'
import { Typography, Form, Modal, Row, Col, Notification, Popover } from '@douyinfe/semi-ui';
import pinyin from 'tiny-pinyin';
import { useLocale } from '@/locales';

import { getFlynet } from '@/services/flynet';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { flylayerClient } from '@/services/core';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import UserModalSelector from '@/components/acl-modal-selector';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import CodeEditor from '@/components/code-editor';
const { Paragraph, Text } = Typography;
import Expressions from '@/components/expressions';
import { sanitizeLabel } from '@/utils/common';
import { GroupTreeData } from '../useAclGroup';
import { BASE_PATH } from '@/constants/router';
import { AttributeTemplate } from '@/interface/attribute-template';
const { Input, RadioGroup, Radio, Switch, TreeSelect } = Form
interface Props {
    close: () => void,
    parent?: AclGroup,
    success?: (userGroup?: AclOrigin) => void,
    groupTreeData: any[],
    mapTreeData: Map<string, GroupTreeData>
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
        type: GroupType,
        otherUsers: Array<string>,
        expressionsCombo: string,
        // parentId: string[] | string,
        parentId: string
    }>>()

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [users, setUsers] = useState<AclOrigin[]>([]);
    const [userGroupType, setUserGroupType] = useState<GroupType>(GroupType.GROUP_STATIC);



    const [attributeTemplate, setAttributeTemplate] = useState<AttributeTemplate>();

    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet && res.flynet.attributeTemplate) {

            const json = JSON.parse(res.flynet.attributeTemplate);
            const userProperties = json.properties.input.properties.Policy;

            let attributeTemplate: AttributeTemplate = {
                type: 'object',
                title: 'properties',
                description: 'properties',
                properties: {
                    'input': {
                        type: 'object',
                        description: formatMessage({ id: 'policies.group.input' }),
                        title: formatMessage({ id: 'policies.group.input' }),
                        properties: {
                            Policy: userProperties
                        }
                    }
                }

            }
            setAttributeTemplate(attributeTemplate);
        }
    }
    useEffect(() => {
        queryFlynet();
    }, [])


    const [expressions, setExpressions] = useState<Array<Expression>>([]);
    const [advancedDynamicMode, setAdvancedDynamicMode] = useState(false);
    const [expressionAdvanced, setExpressionAdvanced] = useState(`
    package play

    import future.keywords.if
    
    # 请输入OPA表达式脚本
    # 可参见：https://play.openpolicyagent.org/
    
    default hello := false
    
    hello if input.message == "world"
        
    `);

    const [expressionsError, setExpressionsError] = useState(false);

    const [userSelectorVisible, setUserSelectorVisible] = useState(false);

    const handleSubmit = async () => {

        await formApi?.validate();

        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';
        const type = values.type;
        const otherUsers = values.otherUsers;
        const expressionsCombo = values.expressionsCombo;
        let parentId = props.parent ? props.parent.id + '' : values.parentId;

        let fullName = '';
        if (parentId) {
            const mapTreeData = props.mapTreeData;
            const parent = mapTreeData.get(parentId);
            fullName = `${parent?.fullName}/${name}`;
        }

        // const parentIds = values.parentId;

        // let fullName = '';
        // if(props.parent){
        //     parentId = props.parent.id + '';
        //     fullName = `${props.parent.fullName}/${name}`;
        // } else if (parentIds) {
        //     if(parentIds instanceof Array){
        //         parentId = parentIds[parentIds.length - 1];
        //     }else{
        //         parentId = parentIds;
        //     }
        //     const mapTreeData = getMapTreeData();
        //     const parent = mapTreeData.get(parentId);


        //     if (parent) {
        //         fullName = `${parent.fullName}/${name}`;
        //     }

        // }



        if (type == GroupType.GROUP_DYNAMIC) {
            if (advancedDynamicMode) {
                if (expressionAdvanced.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            } else {
                if (expressionsError || expressions.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            }
        }

        setLoading(true);


        flylayerClient.createAclGroup({
            flynetId: flynet.id,
            name: name,
            description: description,
            alias: alias,
            type: type,
            acls: users,
            parentId: parentId ? BigInt(parentId) : undefined,
            fullName: fullName,
            attrs: advancedDynamicMode ? {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_ADVANCED,
                expressionAdvanced: expressionAdvanced
            } : {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_STANDARD,
                expressions: expressions,
                expressionsCombo: expressionsCombo
            }
        }).then((res) => {
            Notification.success({
                title: formatMessage({ id: 'policies.group.createSuccess' }),
                content: formatMessage({ id: 'policies.group.createSuccess' })
            })

            props.success && props.success();
        }).catch((err) => {
            Notification.error({
                title: formatMessage({ id: 'policies.group.createFailed' }),
                content: err.message
            })
        }).finally(() => {
            setLoading(false);
        })

    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'policies.group.addGroup' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >

            <div className={styles.addService}>
                <Form getFormApi={SetFormApi}
                    allowEmpty
                    initValues={{ type: GroupType.GROUP_STATIC }}
                    onValueChange={(values, changedValue) => {

                        if (changedValue.hasOwnProperty('alias')) {
                            formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                        }
                    }}
                >
                    <Row gutter={12}>
                        <Col span={12}>
                            <Input field='alias' label={formatMessage({ id: 'policies.group.name' })} trigger={'blur'} validate={value => {
                                if (!value) {
                                    return formatMessage({ id: 'policies.group.nameRequired' });
                                }
                                return '';
                            }} />
                        </Col>
                        <Col span={12}>
                            <Input field='name'
                                label={<>{formatMessage({ id: 'policies.group.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'policies.group.codeTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}

                                trigger={'blur'} validate={value => {
                                    if (!value) {
                                        return formatMessage({ id: 'policies.group.codeRequired' });
                                    }
                                    // 编码不能以-开头
                                    if (value.trim().startsWith('-')) {
                                        return formatMessage({ id: 'policies.group.codeCannotStartWithDash' });
                                    }
                                    if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                        return formatMessage({ id: 'policies.group.codeInvalidFormat' });
                                    }
                                    return '';
                                }}
                                required />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Input field='description' label={formatMessage({ id: 'policies.group.remark' })} />
                        </Col>
                    </Row>
                    {props.parent ? <Row>
                        <Col span={24}>
                            <Text type='tertiary'>{formatMessage({ id: 'policies.group.parentGroup' })}</Text>
                            <Paragraph className='mb20'>{props.parent.alias}({props.parent.name})</Paragraph>

                        </Col>
                    </Row> : <Row>
                        <Col span={24}>
                            <TreeSelect
                                dropdownStyle={{ width: '100%', maxHeight: 300, overflow: 'auto' }}
                                expandAll
                                treeData={props.groupTreeData}
                                filterTreeNode
                                field='parentId'
                                label={formatMessage({ id: 'policies.group.parentGroup' })}
                                placeholder={formatMessage({ id: 'policies.group.pleaseSelect' })}
                                style={{ width: '100%' }}></TreeSelect>
                        </Col>
                    </Row>}

                    {/* <Divider></Divider>
                    <Row>
                        <Col span={16}>
                            <RadioGroup field='type' label={formatMessage({ id: 'policies.group.groupType' })} onChange={(e) => setUserGroupType(e.target.value)}>
                                <Radio value={GroupType.GROUP_STATIC}>{formatMessage({ id: 'policies.group.static' })}</Radio>
                                <Radio value={GroupType.GROUP_DYNAMIC}>{formatMessage({ id: 'policies.group.dynamic' })}</Radio>
                            </RadioGroup>
                        </Col>
                        <Col span={8} className='btn-right-col' style={{ paddingTop: 24, display: userGroupType == GroupType.GROUP_DYNAMIC ? '' : 'none' }}>

                            <Switch field='advancedDynamicMode' onChange={(checked) => {
                                setAdvancedDynamicMode(checked)
                            }} label={formatMessage({ id: 'policies.group.expertMode' })} labelPosition='left' ></Switch>
                        </Col>
                    </Row> */}
                    {/* {userGroupType == GroupType.GROUP_STATIC && <>

                        <Row className='mb10'>
                            <Col span={20}>
                                <Text type='tertiary'>策略</Text>
                            </Col>
                            <Col span={4} className={styles.rightColumn}>
                                <Button
                                    onClick={() => {
                                        setUserSelectorVisible(true);
                                    }}
                                    icon={<IconPlus></IconPlus>}></Button>
                            </Col>
                        </Row>
                        {
                            users.length == 0 ? <TableEmpty loading={false}></TableEmpty> :
                                <>
                                    {users.map((item, index) => {
                                        return <Row className="mb10" key={index}>
                                            <Col span={20}>
                                                {item.name}{item.description ? `(${item.description})` : ''}
                                            </Col>
                                            <Col span={4} className={styles.rightColumn}>
                                                <Button
                                                    type='danger'
                                                    onClick={() => {
                                                        let newUsers = users.filter((item, i) => i != index);
                                                        setUsers(newUsers);
                                                    }}
                                                    icon={<IconMinusCircle></IconMinusCircle>}></Button>
                                            </Col>
                                        </Row>
                                    })}
                                </>
                        }
                    </>} */}
                    {/* {userGroupType == GroupType.GROUP_DYNAMIC && attributeTemplate && <>
                        {
                            advancedDynamicMode ? <>
                                <CodeEditor value={expressionAdvanced} height='280px' onChange={(value) => setExpressionAdvanced(value || '')} language='systemverilog'></CodeEditor>
                                {expressionsError && <Paragraph type='danger'>表达式不能为空</Paragraph>}
                            </> : <>
                                <Expressions
                                    expressions={expressions}
                                    onChange={(expressions: Array<Expression>) => {
                                        setExpressions(expressions);
                                        setExpressionsError(false);
                                    }}
                                    onError={() => {
                                        setExpressionsError(true);
                                    }}
                                    attributeTemplate={attributeTemplate}
                                ></Expressions>
                                {expressionsError && <Paragraph type='danger'>触发参数错误</Paragraph>}
                                <Row>
                                    <Col span={24}>
                                        <Input
                                            extraText='运算符支持 and(与)、or (或)、or (或)、not (非)、()(括号)，例如：1 and 2，1 or 2, not 1,  1 or (not 1)'
                                            field='expressionsCombo' label='参数组合' />
                                    </Col>
                                </Row>
                            </>
                        }
                    </>} */}
                    {/* {userGroupType == GroupType.GROUP_DYNAMIC && !attributeTemplate && <Card>
                        <Paragraph style={{ textAlign: 'center' }}>动态策略组属性为空, 请前往
                            <a className='link-external' target='_blank' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>
                                设置<IconArrowUpRight />
                            </a>

                        </Paragraph>

                    </Card>} */}

                </Form>
            </div>
        </Modal>
        {
            userSelectorVisible && <UserModalSelector
                multi={true}
                value={users}
                onChange={(value: AclOrigin | AclOrigin[]) => {
                    setUserSelectorVisible(false)

                    let newUsers = users.filter((item) => true);
                    if (value instanceof Array) {
                        value.forEach((item) => {
                            if (!newUsers.some(u => u.id == item.id)) {
                                newUsers.push(item);
                            }
                        })
                    } else {
                        if (!newUsers.some(u => u.id == value.id)) {
                            newUsers.push(value);
                        }
                    }
                    setUsers(newUsers);
                }}
                close={() => setUserSelectorVisible(false)}
            ></UserModalSelector>
        }
    </>
}

export default Index;
