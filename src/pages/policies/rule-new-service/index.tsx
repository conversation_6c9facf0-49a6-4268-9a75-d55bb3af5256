import React, { useState, useContext } from "react";

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Typography, Modal, Form, Row, Col, Checkbox, Notification, Divider, Popover, Tooltip, Space } from "@douyinfe/semi-ui";
import MulteSelect, { SelectItemParam } from "@/components/multi-select";
import { AclOrigin_Resource, AclOrigin_ResourceType, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { IpGroup } from '@/interface/ip-group';
import { ACLPolicy, AclOrigin, SSHRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { IconHelpCircle, IconApartment } from '@douyinfe/semi-icons';

import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import SingleSelector from "../components/single-selector";
import SingleSelectorMulti from '../components/single-selector-multi';
import SingleSelectorPort from "../components/single-selector-port";
import SingleSelectorPortMulti from "../components/single-selector-port-multi";
import IpInput from "../components/ip-input";
import IpInputMulti from "../components/ip-input-multi";
import IpInputPort from "../components/ip-input-port";
import IpInputPortMulti from "../components/ip-input-port-multi";
import ServicesSelector from '../components/services-selector';
import ServicesSelectorMulti from '../components/services-selector-multi';
import ServicesGroupSelector from "../components/services-group-selector";
import ServicesGroupSelectorMulti from '../components/services-group-selector-multi';
import UserSelector from "../components/user-selector";
import UserSelectorMulti from "../components/user-selector-multi";
import UserSelectorPort from "../components/user-selector-port";
import UserSelectorPortMulti from "../components/user-selector-port-multi";
import AllSelector from "../components/all-selector";
import AllSelectorPort from "../components/all-selector-port";
import ExpressionSelectorPort from "../components/expression-selector-port";
import ExpressionSelectorPortMulti from '../components/expression-selector-port-multi';

import { flylayerClient } from '@/services/core';
const { Input, Select, RadioGroup, Radio, InputNumber, TreeSelect } = Form
const { Text } = Typography;

interface Props {
    close: () => void;
    success?: () => void;
    ipGroups: IpGroup[],
    alcs: AclOrigin[],
    aclPolicy: ACLPolicy,
    userGroups: UserGroup[],
    aclGroup?: AclGroup,
    getSrcDisplay: (val: AclOrigin_Resource) => string,
    getDstDisplay: (val: AclOrigin_Resource) => string,
    groupTreeData: any[],
    mapGroupData: Map<string, AclGroup>,
}



const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi>();

    const [loading, setLoading] = useState(false);

    const handleOk = async () => {
    }
    const [aclGroups, setAclGroups] = useState<AclGroup[]>(props.aclGroup ? [props.aclGroup] : []);

    const [src, setSrc] = useState<AclOrigin_Resource[]>([]);
    const [dst, setDst] = useState<AclOrigin_Resource[]>([]);

    const [isServicePolicy, setIsServicePolicy] = useState(false);

    // 表达式
    const expressions: Array<{ value: string, label: string }> = [];
    Object.keys(props.aclPolicy.expressions).forEach(key => {
        expressions.push({
            label: key, value: key
        })
    })
    // 用户组
    const userGroups: Array<{ value: string, label: string }> = [];
    props.userGroups.forEach(userGroup => {
        userGroups.push({
            value: `group:${userGroup.name}`,
            label: userGroup.alias ? `${userGroup.alias}(${userGroup.name})` : userGroup.name
        })
    })


    // 标签
    const tags: Array<{ value: string, label: string }> = [];
    Object.keys(props.aclPolicy.tagowners).forEach(key => {
        tags.push({
            label: key, value: key
        })
    })


    // 主机名
    const hostList: Array<{ value: string, label: string }> = [];
    props.ipGroups.forEach(ipGroup => {
        hostList.push({
            value: ipGroup.name,
            label: ipGroup.description
        })
    })

    return <><Modal
        title="添加策略"
        visible={true}
        onOk={handleOk}
        onCancel={props.close}
        width={802}
        okButtonProps={{ loading, disabled: src.length == 0 || dst.length == 0 }}
        closeOnEsc={true}

        maskClosable={false}
    >

        <Form getFormApi={SetFormApi} initValues={{
            policyType: 0,
            serviceType: 0,
            action: 'accept',
            disabled: 0,
            priority: 1,
            aclGroups: aclGroups.map(item => item.id + '')
        }} render={({ formState, values }) => <>
            <Row gutter={20}>
                <Col span={12}>
                    <Input field="name" validate={value => {
                        if (!value) {
                            return '名称不能为空';
                        }
                        return '';
                    }} label="策略名称" trigger={'blur'}></Input>
                </Col>
                <Col span={6}>
                    <InputNumber field="priority" min={1} max={100} step={1} label={<>优先级&nbsp;<Popover content={<div className='p10'>优先级范围为1-100，默认为1，即最高优先级。</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}></InputNumber>
                </Col>
                <Col span={6}>
                    <RadioGroup field="disabled" label="状态">
                        <Radio value={0}>启用</Radio>
                        <Radio value={1}>禁用</Radio>
                    </RadioGroup>
                </Col>
            </Row>
            <Row>
                <Col span={24}>
                    <Input field="description" label="策略描述"></Input>
                </Col>
            </Row>
            <Row>
                <Col span={12}>
                    <RadioGroup field="policyType" onChange={(val) => {
                        const value = val.target.value as number;
                        if (value == 1) {
                            setIsServicePolicy(true);
                        } else {
                            setIsServicePolicy(false);
                        }
                    }} label="策略类型">
                        <Radio value={0}>一般策略</Radio>
                        <Radio value={1}>服务策略</Radio>
                    </RadioGroup>
                </Col>
                <Col span={12}>
                    {values.policyType == 1 && <RadioGroup field="serviceType" label="服务类型">
                        <Radio value={0}>网络服务</Radio>
                        <Radio value={1}>系统服务</Radio>
                        <Radio value={2}>应用服务</Radio>
                    </RadioGroup>}

                </Col>
            </Row>
            <Row gutter={20}>
                <Col span={24}>
                    <TreeSelect
                        field="aclGroups"
                        label="所属策略组"
                        // zIndex={9999}
                        style={{ width: '100%' }}
                        expandAll
                        treeData={
                            props.groupTreeData
                        }
                        onChange={(val) => {
                            if (val) {
                                let arr = val as string[];
                                let groups: AclGroup[] = [];
                                let mapGroupData = props.mapGroupData;
                                arr.forEach(item => {
                                    let group = mapGroupData.get(item);
                                    if (group) {
                                        groups.push(group);
                                    }
                                })
                                setAclGroups(groups);
                            }
                        }}
                        renderFullLabel={({ className,
                            onExpand,
                            data,
                            onCheck,
                            checkStatus,
                            expandIcon, }) => {
                            const { label, value } = data;
                            let aclGroup = props.mapGroupData.get(value as string);

                            const isLeaf = !(data.children && data.children.length);

                            let labelEle = <></>;
                            if (!aclGroup) {
                                labelEle = <Text ellipsis={{ showTooltip: true }} >{label}</Text>
                            } else {
                                labelEle = <Space>{aclGroup.type == GroupType.GROUP_DYNAMIC ?
                                    <Tooltip content="动态策略组">
                                        <IconApartment /></Tooltip>
                                    : ''}
                                    <Tooltip content={aclGroup.name}>
                                        <Text>{aclGroup.alias}</Text>
                                    </Tooltip></Space>
                            }

                            return (
                                <li
                                    className={className}
                                    role="treeitem"
                                    onClick={aclGroup?.type == GroupType.GROUP_DYNAMIC ? undefined : onCheck}
                                // onClick={isLeaf ? onCheck : onExpand}
                                >
                                    {isLeaf ? null : expandIcon}
                                    <div onClick={aclGroup?.type == GroupType.GROUP_DYNAMIC ? undefined : onCheck} role='checkbox' tabIndex={0} aria-checked={checkStatus.checked}>
                                        <Checkbox
                                            indeterminate={checkStatus.halfChecked}
                                            checked={checkStatus.checked}
                                            disabled={aclGroup?.type == GroupType.GROUP_DYNAMIC}
                                            style={{ marginRight: 8 }}
                                        />
                                    </div>
                                    <span>{labelEle}</span>
                                </li>
                            );
                        }}

                        multiple
                        filterTreeNode
                        showFilteredOnly
                        checkRelation="unRelated"
                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    ></TreeSelect>
                </Col>
            </Row>

            <Divider className="mb10" />

            <div style={{ display: 'flex', width: '100%' }}>
                <div style={{ flexGrow: 1, width: 295 }}>
                    <MulteSelect
                        getDisplayValue={props.getSrcDisplay}
                        label="源"
                        styles={{
                            paddingTop: 12,
                            paddingBottom: 20
                        }}
                        selectItems={[{
                            name: '',
                            label: '全部',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.ALL,
                                value: '*'
                            }),

                            selectComponent: AllSelector
                        }, {
                            name: '',
                            label: 'IP',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.IP,
                                value: ''
                            }),
                            selectComponent: IpInput,
                            multiSelectComponent: IpInputMulti
                        },
                        {
                            name: '',
                            label: '用户',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.USER,
                                value: ''
                            }),
                            selectComponent: UserSelector,
                            multiSelectComponent: UserSelectorMulti
                        }, {
                            name: '',
                            label: '用户组',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.USER_GROUP,
                                value: ''
                            }),
                            optList: userGroups,
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti
                        }, {
                            name: '',
                            label: '内置用户组',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.USER_AUTO_GROUP,
                                value: ''
                            }),
                            optList: [{
                                value: 'autogroup:self',
                                label: '自已'
                            }, {
                                value: 'autogroup:members',
                                label: '普通用户'
                            }, {
                                value: 'autogroup:internet',
                                label: '公网用户'
                            }],
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti

                        }, {
                            name: '',
                            label: '表达式',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.EXPRESSION,
                                value: ''
                            }),
                            optList: expressions,
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti
                        }, {
                            name: '',
                            label: '标签',
                            placeholder: '',
                            value: new AclOrigin_Resource({
                                type: AclOrigin_ResourceType.TAG,
                                value: ''
                            }),
                            optList: tags,
                            selectComponent: SingleSelector,
                            multiSelectComponent: SingleSelectorMulti
                        }]}

                        value={src}
                        existValues={src}
                        onChange={(val: AclOrigin_Resource[], selectItem: SelectItemParam) => {

                            setSrc(val);
                        }}

                    ></MulteSelect>
                </div>
                <div style={{
                    display: 'flex', alignItems: 'center', flexDirection: 'column',
                    borderLeft: '1px solid var(--semi-color-border)'
                    , borderRight: '1px solid var(--semi-color-border)'
                    , paddingLeft: 10, paddingRight: 10,
                    marginLeft: 10, marginRight: 10
                }}  >
                    <div style={{ lineHeight: '52px', width: '100%', textAlign: 'center', borderBottom: '1px solid var(--semi-color-border)' }}>动作 <Popover content={<div className='p10'>优先级相同的情况下，拒绝动作优于允许动作。</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            marginLeft: 10,
                            marginRight: 10,
                            flexGrow: 1,
                        }}
                    >
                        <Select field="action" noLabel style={{ width: 80 }}>
                            <Select.Option value="accept">允许</Select.Option>
                            <Select.Option value="reject">拒绝</Select.Option>
                        </Select>
                    </div>
                </div>
                <div style={{ flexGrow: 1, width: 295 }}>
                    <MulteSelect
                        getDisplayValue={props.getDstDisplay}
                        label="目标"
                        styles={{
                            paddingTop: 12,
                            paddingBottom: 20
                        }}
                        selectItems={
                            isServicePolicy ?
                                [{
                                    name: '',
                                    label: '全部',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.ALL,
                                        value: '*:*'
                                    }),
                                    selectComponent: AllSelectorPort
                                },
                                {
                                    name: '',
                                    label: '服务',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.SERVICE,
                                        value: ''
                                    }),

                                    selectComponent: ServicesSelector,
                                    multiSelectComponent: ServicesSelectorMulti

                                }, {
                                    name: '',
                                    label: '服务组',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.SERVICE_GROUP,
                                        value: ''
                                    }),
                                    selectComponent: ServicesGroupSelector,
                                    multiSelectComponent: ServicesGroupSelectorMulti
                                }] : [{
                                    name: '',
                                    label: '全部',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.ALL,
                                        value: '*:*'
                                    }),
                                    selectComponent: AllSelectorPort
                                },
                                {
                                    name: '',
                                    label: 'IP',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.IP,
                                        value: ''
                                    }),
                                    selectComponent: IpInputPort,
                                    multiSelectComponent: IpInputPortMulti
                                },
                                {
                                    name: '',
                                    label: '资源组',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.IP_GROUP,
                                        value: ''
                                    }),
                                    selectComponent: SingleSelectorPort,
                                    multiSelectComponent: SingleSelectorPortMulti,
                                    optList: hostList
                                },
                                {
                                    name: '',
                                    label: '用户',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.USER,
                                        value: ''
                                    }),
                                    selectComponent: UserSelectorPort,
                                    multiSelectComponent: UserSelectorPortMulti
                                },
                                {
                                    name: '',
                                    label: '用户组',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.USER_GROUP,
                                        value: ''
                                    }),
                                    optList: userGroups,
                                    selectComponent: SingleSelectorPort,
                                    multiSelectComponent: SingleSelectorPortMulti,
                                },
                                {
                                    name: '',
                                    label: '内置用户组',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.USER_AUTO_GROUP,
                                        value: ''
                                    }),
                                    optList: [{
                                        value: 'autogroup:self',
                                        label: '自已'
                                    }, {
                                        value: 'autogroup:members',
                                        label: '普通用户'
                                    }, {
                                        value: 'autogroup:internet',
                                        label: '公网用户'
                                    }],
                                    selectComponent: SingleSelectorPort,
                                    multiSelectComponent: SingleSelectorPortMulti,

                                },
                                {
                                    name: '',
                                    label: '表达式',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.EXPRESSION,
                                        value: ''
                                    }),
                                    optList: expressions,
                                    selectComponent: ExpressionSelectorPort,
                                    multiSelectComponent: ExpressionSelectorPortMulti
                                },
                                {
                                    name: '',
                                    label: '标签',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.TAG,
                                        value: ''
                                    }),
                                    optList: tags,
                                    selectComponent: SingleSelectorPort,
                                    multiSelectComponent: SingleSelectorPortMulti,
                                },
                                {
                                    name: '',
                                    label: '服务',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.SERVICE,
                                        value: ''
                                    }),

                                    selectComponent: ServicesSelector,
                                    multiSelectComponent: ServicesSelectorMulti

                                }, {
                                    name: '',
                                    label: '服务组',
                                    placeholder: '',
                                    value: new AclOrigin_Resource({
                                        type: AclOrigin_ResourceType.SERVICE_GROUP,
                                        value: ''
                                    }),
                                    selectComponent: ServicesGroupSelector,
                                    multiSelectComponent: ServicesGroupSelectorMulti
                                }]}

                        value={dst}
                        existValues={dst}
                        onChange={(val: AclOrigin_Resource[], selectItem: SelectItemParam) => {
                            setDst(val);
                        }}

                    ></MulteSelect>
                </div>
            </div>
        </>}>
        </Form>
    </Modal></>
}

export default Index;
