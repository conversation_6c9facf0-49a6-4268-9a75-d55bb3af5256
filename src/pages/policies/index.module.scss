.tagDst {
    text-wrap: wrap;
    height: auto;
    font-size: 12px!important;
    // width: 230px;
    // top: 50%;
    // transform: translateY(-50%);
}

.tagSrc {
    height: auto;
    font-size: 12px!important;
}
.srcDstText {
    max-width: 280px;
    text-overflow: ellipsis;
    white-space: nowrap; 
    overflow: hidden;
}
.allGroup {
    height: 20px;
    padding-top: 4px;
    // padding-bottom: 4px;
    cursor: pointer;
    padding-left: 30px;
    &:hover {
        background-color: var(--semi-color-fill-0);
    }
}
.allGroupSelected {
    height: 20px;
    padding-top: 4px;
    // padding-bottom: 4px;
    cursor: pointer;
    padding-left: 30px;
    background-color: var(--semi-color-primary-light-default);

}
.groupInfo {
    display: flex;
    
    vertical-align: middle;
    align-items: center;
    margin-bottom: 10px;
}
.nav {
    position: relative;
    min-height: calc(100vh - 260px);
    border-radius: var(--semi-border-radius-small);
    border: 1px solid var(--semi-color-border);
    // border-top-right-radius: var(--semi-border-radius-small);
    // border-bottom-right-radius: var(--semi-border-radius-small);
}
.toggleNavLeft {
    position: absolute;
    z-index: 2;
    left: 165px;
    top: 10px;
}
.toggleNavRight {
    position: absolute;
    z-index: 1;
    left: 10px;
    top: 10px;
}

.navTree {
    max-height: calc(100vh - 260px);
    min-height: calc(100vh - 260px);
    width: 100%;
    padding-top: 10px;
    overflow-y: auto;
    // background-color: var(--semi-color-fill-0);
    // border-top-left-radius: var(--semi-border-radius-small);
    // border-bottom-left-radius: var(--semi-border-radius-small);
    // box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.3), 0px 4px 14px rgba(0, 0, 0, 0.1);
}
.navContent {
    
    min-height: calc(100vh - 230px);
}

.dynamicIcon {
    color: var(--semi-color-text-2);
    
}
.staticIcon {
    color: var(--semi-color-text-2);
    
}