import React, { useState, useContext, useEffect } from 'react'
import { Typo<PERSON>, Modal, Row, Col, Notification, Button, Input, Popover, Space, Tag } from "@douyinfe/semi-ui";
import { ListValue, Value } from '@bufbuild/protobuf';
import { useLocale } from '@/locales';

import { getFlynet } from '@/services/flynet';
import { IconPlus, IconMinusCircle, IconArrowDown, IconArrowUp, IconMore, IconEdit, IconEyeOpened, IconAlignTop, IconAlignBottom } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Paragraph } = Typography;
import styles from './index.module.scss'
import ExpressionList from './expression-list';
import ExpressionEdit from './expression-edit';
import ExpressionPreview from './expression-preview';
import { flylayerClient } from '@/services/core';
interface Props {
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    close: () => void
}

// 无效的名称
const errorNameMessage: Map<number, string> = new Map();

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    const PREFIX = 'exp:';

    const flynet = useContext(FlynetGeneralContext);
    const [aclPolicy, setAclPolicy] = useState<ACLPolicy>(props.aclPolicy);

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const [expressionsTemplate, setExpressionsTemplate] = useState<string>();


    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {
        let expressions: { name: string, values: string }[] = [];
        Object.keys(policy.expressions).forEach(key => {
            const values = policy.expressions[key].values;
            expressions.push({
                name: key.replace(PREFIX, ''),
                values: values.length > 0 ? values[0].kind.value + '' : ''
            })
        })
        return expressions;
    }

    const [templateEditVisible, setTemplateEditVisible] = useState(false);
    const [previewVisible, setPreviewVisible] = useState(false);

    // 初始值
    const initValues = calInitValues(props.aclPolicy);

    const [expressions, setExpressions] = useState<{ name: string, values: string }[]>(initValues);

    const getAclPolicy = () => {
        let newExpressions: { [key: string]: ListValue } = {};
        if (expressions) {
            expressions.forEach((item) => {
                let name = item.name ? item.name.trim() : '';
                if (name) {
                    if (item.values) {
                        newExpressions[PREFIX + name] = new ListValue({
                            values:
                                [new Value({
                                    kind: { value: item.values, case: 'stringValue' }
                                })]
                        });
                    }
                }
            })
        }

        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            expressions: newExpressions,
        })
        return policy;
    }

    const handleOk = () => {
        const aclPolicy = getAclPolicy();
        setAclPolicy(aclPolicy);

        // 保存
        setLoading(true)

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: aclPolicy
        }).then(() => {
            props.onChange(aclPolicy)
            props.close()
            Notification.success({ content: formatMessage({ id: 'policies.saveSuccess' }), position: "bottomRight" })
        }).catch((err: any) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })
    }

    // 无效的行
    const [invalidLines, setInvalidLines] = useState<number[]>([]);
    // 无效的名称
    const [errorNameIndexs, setErrorNameIndexs] = useState<number[]>([]);

    // 无效的表达式
    const [errorExpressionIndexs, setErrorExpressionIndexs] = useState<number[]>([]);

    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet && res.flynet.expressionsTemplate) {
            setExpressionsTemplate(res.flynet.expressionsTemplate);
        }
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    return <>
        <Modal
            title={<>{formatMessage({ id: 'policies.expression.title' })}&nbsp;<Space>
                <Button onClick={() => setTemplateEditVisible(true)} size='small' icon={<IconEdit />}>{formatMessage({ id: 'policies.expression.editPropertyDefinition' })}</Button>
                <Button onClick={() => setPreviewVisible(true)} size='small' icon={<IconEyeOpened />}>{formatMessage({ id: 'policies.expression.preview' })}</Button></Space>
            </>}
            visible={true}
            onOk={handleOk}
            onCancel={props.close}
            width={1080}
            okButtonProps={{ loading, disabled: invalidLines.length > 0 }}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph type='tertiary' className='mb40'>
                {formatMessage({ id: 'policies.expression.description' })}
            </Paragraph>
            <Row className={styles.tableTitle} >
                <Col span={1}>{formatMessage({ id: 'policies.expression.serialNumber' })}</Col>
                <Col span={4}>{formatMessage({ id: 'policies.expression.name' })}</Col>
                <Col span={4}>{formatMessage({ id: 'policies.expression.property' })}</Col>
                <Col span={4}>{formatMessage({ id: 'policies.expression.operation' })}</Col>
                <Col span={9}>{formatMessage({ id: 'policies.expression.value' })}</Col>
                <Col span={2} className='btn-right-col'><Button onClick={() => {
                    setInvalidLines([...invalidLines, expressions.length])
                    setExpressions([...expressions, { name: '', values: '' }])
                }} icon={<IconPlus />} /></Col>
            </Row>
            {expressions.map((value, index) => {
                return <Row className={invalidLines.indexOf(index) >= 0 ? styles.tableBodyError : styles.tableBody} key={index} >
                    <Col span={1}>
                        <Tag style={{ height: 32, width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'} >{index + 1}</Tag>
                    </Col>
                    <Col xs={24} sm={4}>
                        <Input
                            value={value.name}
                            onChange={(input_val) => {
                                const newGroups = expressions.map((val, i) => {
                                    if (i != index) {
                                        return val;
                                    } else return {
                                        name: input_val,
                                        values: val.values
                                    }
                                });
                                if (input_val && value.values) {
                                    setInvalidLines(invalidLines.filter((i) => i != index))
                                }
                                setExpressions(newGroups)

                                // 校验名称是否为空
                                if (!input_val || input_val.trim() == '') {
                                    setErrorNameIndexs([...errorNameIndexs, index])
                                    setInvalidLines([...invalidLines, index])
                                    errorNameMessage.set(index, formatMessage({ id: 'policies.expression.nameRequired' }))
                                } else if (input_val.indexOf(PREFIX) >= 0) {
                                    setErrorNameIndexs([...errorNameIndexs, index])
                                    setInvalidLines([...invalidLines, index])
                                    errorNameMessage.set(index, formatMessage({ id: 'policies.expression.nameCannotContainPrefix' }) + PREFIX)
                                } else {
                                    let exist = false;
                                    // 校验名称是否重复
                                    expressions.forEach((val, i) => {
                                        if (i != index && val.name == input_val) {
                                            setErrorNameIndexs([...errorNameIndexs, index])
                                            setInvalidLines([...invalidLines, index])
                                            errorNameMessage.set(index, formatMessage({ id: 'policies.expression.duplicateName' }))
                                            exist = true;
                                        }
                                    })
                                    if (!exist) {
                                        setErrorNameIndexs(errorNameIndexs.filter((i) => i != index))
                                        errorNameMessage.delete(index)
                                    }
                                }

                            }}
                        >
                        </Input>
                        {errorNameIndexs.indexOf(index) >= 0 && <Paragraph type='danger'>{errorNameMessage.get(index)}</Paragraph>}
                    </Col>

                    <Col xs={24} sm={17}>

                        {expressionsTemplate ?
                            <ExpressionList
                                value={value.values}
                                expressionsTemplate={expressionsTemplate}
                                onChange={(exp_val) => {
                                    const newGroups = expressions.map((val, i) => {
                                        if (i != index) {
                                            return val;
                                        } else return {
                                            name: val.name,
                                            values: exp_val
                                        }
                                    });
                                    if (value.name && exp_val) {
                                        setInvalidLines(invalidLines.filter((i) => i != index))
                                    }
                                    setExpressions(newGroups)
                                }}
                                onerror={() => {
                                    if (!invalidLines.includes(index)) {
                                        setInvalidLines([...invalidLines, index])
                                    }
                                }}
                            ></ExpressionList> : ''}
                    </Col>
                    <Col xs={24} sm={2} className='btn-right-col'>
                        {/* 弹出排序操作 */}
                        <Popover
                            position='left'
                            style={{
                                padding: 5,
                            }}
                            content={<>
                                <Space key={'index'}>
                                    <Button disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconAlignTop />}
                                        onClick={() => {
                                            let newGroups = expressions.map((val, i) => {
                                                return val;
                                            });
                                            let item = newGroups[index];
                                            newGroups[index] = newGroups[0];
                                            newGroups[0] = item;
                                            setExpressions(newGroups)
                                        }}
                                    ></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = expressions.map((val, i) => {
                                                if (i == index) {
                                                    return expressions[index - 1];
                                                } else if (i == index - 1) {
                                                    return expressions[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setExpressions(newGroups)
                                        }} disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconArrowUp />}></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = expressions.map((val, i) => {
                                                if (i == index) {
                                                    return expressions[index + 1];
                                                } else if (i == index + 1) {
                                                    return expressions[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setExpressions(newGroups)
                                        }}
                                        disabled={invalidLines.indexOf(index) >= 0 || index == expressions.length - 1} icon={<IconArrowDown />}></Button>
                                    <Button disabled={invalidLines.indexOf(index) >= 0 || index == expressions.length - 1} icon={<IconAlignBottom />} onClick={() => {
                                        let newGroups = expressions.map((val, i) => {
                                            return val;
                                        });
                                        let item = newGroups[index];
                                        newGroups[index] = newGroups[newGroups.length - 1];
                                        newGroups[newGroups.length - 1] = item;
                                        setExpressions(newGroups)
                                    }}></Button>
                                    <Button
                                        type='danger'
                                        theme='borderless'
                                        icon={<IconMinusCircle />}
                                        onClick={() => {
                                            const newGroups = expressions.filter((g, i) => i != index)
                                            setExpressions(newGroups)
                                            setInvalidLines(invalidLines.filter((i) => i != index))
                                            setErrorNameIndexs(errorNameIndexs.filter((i) => i != index))
                                            setErrorExpressionIndexs(errorExpressionIndexs.filter((i) => i != index))
                                            errorNameMessage.delete(index)

                                        }}

                                    />
                                </Space>
                            </>}><Button icon={<IconMore />}></Button></Popover>

                    </Col>
                </Row>
            })}
        </Modal>
        {templateEditVisible && <ExpressionEdit
            close={() => setTemplateEditVisible(false)}
            success={(template) => { setTemplateEditVisible(false); setExpressionsTemplate(template) }}
        ></ExpressionEdit>}
        {previewVisible && <ExpressionPreview
            close={() => setPreviewVisible(false)}
            aclPolicy={getAclPolicy()}
        ></ExpressionPreview>}
    </>
}

export default Index;