import React, { useState, useRef, useContext, useEffect } from 'react'

import { Modal, Notification } from "@douyinfe/semi-ui";
import styles from './index.module.scss'
import { getFlynet } from '@/services/flynet';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { flylayerClient } from '@/services/core';

import CodeModalEditor from '@/components/code-modal-editor';

import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

const Index: React.FC<{
    close: () => void,
    success: (template: string) => void,
}> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [flynet, setFlynet] = useState<Flynet>(); // flynetGeneral.flynet
    const [loading, setLoading] = useState(false);

    const [templateLoaded, setTemplateLoaded] = useState(false);

    const handleOk = (templateResult:string) => {

        try {
            JSON.parse(templateResult);

        } catch (e) {
            Notification.error({
                title: '表达式模板不是合法的 JSON',
                content: (e as any).message,
                duration: 6000,
            });
            return;
        }


        setLoading(true);
        flylayerClient.saveExpressionsTemplate({
            flynetId: flynet?.id,
            expressionsTemplate: templateResult,
        }).then(res => {

            props.success(templateResult);
            Notification.success({
                content: '保存成功',
            });
        }).catch(err => {
            Notification.error({
                content: '保存失败',
            });
        }).finally(() => {
            setLoading(false);
        })
    }


    const [template, setTemplate] = useState<string>('');

    const queryFlynet = async () => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet);
            if (res && res.flynet && res.flynet.expressionsTemplate) {
                setTemplateLoaded(true);
                if (res.flynet.expressionsTemplate) {

                    setTemplate(res.flynet.expressionsTemplate);
                }
            }
        }).finally(() => {
            setTemplateLoaded(true);
        });


    }

    useEffect(() => {
        queryFlynet();
    }, [])

    return <>
        <CodeModalEditor
            title="编辑表达式模板"
            onCancel={props.close}
            onOk={handleOk}
            value={template}
            language='json'
            width={1080}
            height={620}
            loading={loading}
        ></CodeModalEditor>
        
    </>
}

export default Index;