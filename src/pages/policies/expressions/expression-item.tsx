import React, { useState, useEffect } from 'react'
import { Select, Space, TreeSelect, Input, TagInput, Switch, DatePicker } from '@douyinfe/semi-ui';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';

import moment from 'moment';


interface Props {
    value: string,
    expressionsTemplate: string,
    onChange: ((val: string) => void),
    onError: () => void,
}

// 表达式模板对象
interface ExpressionTemplate {
    type: string,
    description: string,
    title: string,
    format?: string,
    properties: { [key: string]: ExpressionTemplate }
}


const Index: React.FC<Props> = (props) => {
    const isOneOfChar = ' in ';
    

    const calInitVal = () => {

        let initOpt = '';
        let initAttr = '';
        let initVal: string | string[] = '';
    
        let propsValue = props.value;
    
        if (propsValue.indexOf(isOneOfChar) < 0) {
            propsValue = propsValue.split(' ').join('');
        }
        if (propsValue.indexOf('>=') >= 0) {
            initOpt = '>=';
            initAttr = propsValue.split('>=')[0];
            initVal = propsValue.split('>=')[1];
            initVal = initVal.replace(/^"*|"*$/g, '');
            
        } else if (propsValue.indexOf('<=') >= 0) {
            initOpt = '<=';
            initAttr = propsValue.split('<=')[0];
            initVal = propsValue.split('<=')[1];
            initVal = initVal.replace(/^"*|"*$/g, '');
        } else if (propsValue.indexOf('>') >= 0) {
            initOpt = '>';
            initAttr = propsValue.split('>')[0];
            initVal = propsValue.split('>')[1];
            initVal = initVal.replace(/^"*|"*$/g, '');
        } else if (propsValue.indexOf('<') >= 0) {
            initOpt = '<';
            initAttr = propsValue.split('<')[0];
            initVal = propsValue.split('<')[1];
            initVal = initVal.replace(/^"*|"*$/g, '');
        } else if (propsValue.indexOf('==') >= 0) {
            initOpt = '==';
            initAttr = propsValue.split('==')[0];
            initVal = propsValue.split('==')[1];
            // 检查initVal是否包含双引号
            if (initVal.startsWith('"')) {
                initVal = initVal.replace(/^"*|"*$/g, '');
            } else {
                initVal = initVal.replace(/^"*|"*$/g, '');
            }
    
        } else if (propsValue.indexOf('contains') >= 0) {
            initOpt = 'contains';
            let valArr = propsValue.replace('contains(', '').split(',{');
            initAttr = valArr[0];
            initVal = valArr[1].replace("}[_])", "");
            // 替换双引号
            initVal = initVal.replace(/"/g, '');
            initVal = initVal.split(',');
            // initValType = 'array';
        } else if (propsValue.indexOf('!=') >= 0) {
            initOpt = '!=';
            initAttr = propsValue.split('!=')[0];
            initVal = propsValue.split('!=')[1];
            // 检查initVal是否包含双引号
            if (initVal.startsWith('"')) {
                initVal = initVal.replace(/^"*|"*$/g, '');
                // initValType = 'string';
            } else {
                initVal = initVal.replace(/^"*|"*$/g, '');
                // initValType = 'number';
            }
        } else if (propsValue.indexOf(isOneOfChar) >= 0) {
            initOpt = isOneOfChar;
            initAttr = propsValue.split(isOneOfChar)[0];
            initVal = JSON.parse(propsValue.split(isOneOfChar)[1]);
            
        }
    
        if (initAttr.indexOf('.now_ns()') >= 0) {
            initAttr = initAttr.replace('.now_ns()', '');
        }
    
    
        if(typeof initVal == 'string' && initVal.indexOf('parse_rfc3339_ns') >= 0) {
            initVal = initVal.replace('time.parse_rfc3339_ns("', '');
            initVal = initVal.replace('")', '');
            let date = new Date(initVal);
            initVal = moment(date).format('YYYY-MM-DD HH:mm:ss');
        }

        setAttr(initAttr)
        setOpt(initOpt)
        setVal(initVal);
    }

    useEffect(()=>{
        calInitVal();
    }, [props.value])


    // 属性
    const [attr, setAttr] = useState('');
    // 操作符
    const [opt, setOpt] = useState('');
    // 值
    const [val, setVal] = useState<string | string[]>('');
    let initDataType = 'string';

    const getDataType = (attr: string, key: string, expressionsTemplate: ExpressionTemplate): string => {
        let dataType = '';
        if (key == attr) {
            if(expressionsTemplate.type == 'object') {
                if(expressionsTemplate.format == 'date-time') {
                    return 'datetime';
                }
                return 'string';
            } else {
                return expressionsTemplate.type;
            }
            
        }
        if (!expressionsTemplate.properties) {
            return dataType;
        }
        Object.keys(expressionsTemplate.properties).forEach((childKey) => {
            let childDataType = getDataType(attr, key + '.' + childKey, expressionsTemplate.properties[childKey]);
            if (childDataType) {
                dataType = childDataType;
            }
        })
        return dataType;
    }

    const buildTreeData = (key: string, expressionsTemplate: ExpressionTemplate) => {
        let treeData: TreeNodeData = {
            label: expressionsTemplate.title,
            value: key,
            key: key,
            children: []
        }
        if (!expressionsTemplate.properties) {
            return treeData;
        }
        Object.keys(expressionsTemplate.properties).forEach((childKey) => {
            let child = buildTreeData(key + '.' + childKey, expressionsTemplate.properties[childKey]);
            treeData.children?.push(child);
        })

        return treeData;
    }

    useEffect(() => {
        const expressionsTemplate: ExpressionTemplate = JSON.parse(props.expressionsTemplate);
        const initTreeData: TreeNodeData[] = [];
        Object.keys(expressionsTemplate.properties).forEach((key) => {
            let treeDataItem = buildTreeData(key, expressionsTemplate.properties[key]);
            initTreeData.push(treeDataItem);

            let dataType = getDataType(attr, key, expressionsTemplate.properties[key]);
            if (dataType) {
                setValType(dataType as any);
            }
        })
        setTreeData(initTreeData);
    }, [props.expressionsTemplate])

    const expressionsTemplate: ExpressionTemplate = JSON.parse(props.expressionsTemplate);
    const initTreeData: TreeNodeData[] = [];
    Object.keys(expressionsTemplate.properties).forEach((key) => {
        let treeDataItem = buildTreeData(key, expressionsTemplate.properties[key]);
        initTreeData.push(treeDataItem);

        let dataType = getDataType(attr, key, expressionsTemplate.properties[key]);
        if (dataType) {
            initDataType = dataType;
        }
    })



    // 值类型
    const [valType, setValType] = useState<'string' | 'number' | 'boolean' | 'array' | 'datetime'>(initDataType as any);
    const handleChange = (attr: string, opt: string, val: string | string[]) => {
        let exp = '';

        if(valType == 'datetime') {
            exp = `${attr}.now_ns()${opt}time.parse_rfc3339_ns("${val}")`
        } else {

            if (opt == 'contains') {
                if (!val || val.length == 0) {
                    exp = `contains(${attr},{}"}[_])`;
                } else if (typeof val != 'string') {
                    let arr = val;
                    arr = arr.map((item) => {
                        // 转义双引号
                        item = item.replace(/"/g, '\\"');
                        item = '"' + item + '"';
                        return item;
                    })
    
                    exp = `contains(${attr},{${arr.join(',')}}[_])`;
                }
    
            } else if (opt == isOneOfChar) {
                if (!val || val == '' || val.length == 0) {
                    exp = '';
                } else if (typeof val != 'string') {
                    let arr = val;
    
                    arr = arr.map((item) => {
                        // 转义双引号
                        item = item.replace(/"/g, '\\"');
                        item = '"' + item + '"';
                        return item;
                    })
                    exp = `${attr} in [${arr.join(',')}]`;
                }
            } else {
    
                if (valType == 'string') {
                    val = val + '';
                    if (!val.startsWith('"')) {
                        val = '"' + val;
                    }
                    if (!val.endsWith('"')) {
                        val = val + '"';
                    }
                }
    
                exp = `${attr}${opt}${val}`;
            }
        }


        props.onChange(exp);
    }



    const [treeData, setTreeData] = useState<TreeNodeData[]>(initTreeData);




    return <>
        <Space style={{width:666}}>
            <TreeSelect
                value={attr}
                onChange={(value) => {
                    let attr = value as string;
                    setAttr(attr);

                    let initDataType = '';

                    Object.keys(expressionsTemplate.properties).forEach((key) => {

                        let dataType = getDataType(attr, key, expressionsTemplate.properties[key]);
                        if (dataType) {
                            initDataType = dataType;
                        }
                    })

                    if(initDataType) {
                        setValType(initDataType as any);
                    }




                    setOpt('');
                    if(initDataType == 'boolean') {
                        setVal('true');
                     
                    } else {
                        setVal('');
                    }

                    

                    props.onError()
                }}
                style={{ width: 155 }}
                leafOnly
                expandAll
                treeData={treeData}
                placeholder="请选择"
                dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}
            />
            <Select style={{ width: 155 }} value={opt}
                onChange={(value) => {
                    let newopt = value as string;
                    if(valType != 'boolean') {
                        setVal('');
                    }
                    setOpt(newopt);
                    if(attr && newopt && val) {
                        handleChange(attr, newopt, val);
                    } else {
                        props.onError()
                    }
                }}>
                {(valType == 'number' || valType == 'datetime') && <Select.Option value={'>'}>大于</Select.Option>}
                {(valType == 'number' || valType == 'datetime') && <Select.Option value={'<'}>小于</Select.Option>}
                <Select.Option value={'=='}>等于</Select.Option>
                <Select.Option value={'!='}>不等于</Select.Option>
                {(valType == 'number' || valType == 'datetime') && <Select.Option value={'>='}>大于等于</Select.Option>}
                {(valType == 'number' || valType == 'datetime') && <Select.Option value={'<='}>小于等于</Select.Option>}
                {(valType == 'array' || valType == 'string') && <Select.Option value={'contains'}>包含(模糊匹配)</Select.Option>}
                {(valType == 'array' || valType == 'string') && <Select.Option value={isOneOfChar}>包含(精确匹配)</Select.Option>}

            </Select>
            {opt == isOneOfChar || opt == 'contains' ? <>
                <TagInput
                    addOnBlur
                    value={typeof val == 'string' ? [] : val}
                    onChange={(value) => {
                        setVal(value);
                        if(attr && opt && value && value.length > 0) {
                            handleChange(attr, opt, value);
                        } else {
                            props.onError();
                        }
                        
                    }}
                    style={{ width: 340 }}
                ></TagInput>

            </> : valType == 'boolean' ? <Switch
                checked={val == 'true'}
                checkedText={"是"}
                uncheckedText={"否"}
                onChange={(value) => {
                    setVal(value ? 'true' : 'false');
                    handleChange(attr, opt, value ? 'true' : 'false');
                }}
                style={{ marginRight: 300 }}
            ></Switch> : valType == 'datetime' ? 
            <DatePicker
                type='dateTime'
                value={val as string}
                format='yyyy-MM-dd HH:mm:ss'
                onChange={(value) => {
                    setVal(value as string);
                    if(attr && opt && value) {
                        let date = new Date(value as string);
                        
                        handleChange(attr, opt, date.toISOString());
                    } else {
                        props.onError();
                    }
                }}
                style={{ marginRight: 0 }}
            ></DatePicker> :
                <Input value={typeof val == 'string' ? val : val.join(',')}

                    onChange={(value) => {
                        let val = value as string;
                        if(valType == 'number') {
                            val = val.replace(/[^0-9]/g, '');
                        }
                        setVal(val);
                        if(attr && opt && val) {
                            handleChange(attr, opt, val);
                        } else {
                            props.onError();
                        }
                    }} style={{ width: 340 }}></Input>}
        </Space>

    </>
}

export default Index;