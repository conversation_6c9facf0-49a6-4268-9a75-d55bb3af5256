import React, { useState, useRef, useContext, useEffect } from 'react'
import { Modal, Notification } from "@douyinfe/semi-ui";

import { Typography, Row, Col } from '@douyinfe/semi-ui';
import { ArrayField, Form, Button } from '@douyinfe/semi-ui';

import styles from './index.module.scss'

const { Title, Paragraph } = Typography;
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const ExpressionPreview: React.FC<{
    close: () => void,
    aclPolicy: ACLPolicy,

}> = (props) => {
    const [formApi, setFormApi] = useState<FormApi>()
    const aclPolicy = props.aclPolicy;
    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {
        let expressions: { name: string, values: string }[] = [];
        Object.keys(policy.expressions).forEach(key => {
            const values = policy.expressions[key].values;
            expressions.push({
                name: key,
                values: values.length > 0 ? values[0].kind.value + '' : ''
            })
        })

        return { expressions: expressions };
    }


    // 初始值
    const initValues = calInitValues(aclPolicy);
    return <>
        <Modal
            title="表达式预览"
            visible={true}
            onCancel={props.close}
            onOk={props.close}
            hasCancel={false}
            width={1080}
            closeOnEsc={true}
            maskClosable={false}
        ><div className={styles.settingItem}>
      
        {initValues ? <Form getFormApi={setFormApi} initValues={initValues} labelPosition='left' labelWidth='100px' allowEmpty>
            <ArrayField field='expressions' >
                {({ add, arrayFields, addWithInitValue }) => (
                    <React.Fragment>
                      
                        <Row className={styles.tableTitle} >
                            <Col span={6}>名称</Col>
                            <Col span={18}>值</Col>
                        </Row>
                        {
                            arrayFields.map(({ field, key, remove }, i) => (
                                <Row className={styles.tableBody} key={key} >
                                    <Col xs={24} sm={6}>
                                        <Form.Input
                                            field={`${field}[name]`}
                                            noLabel
                                            readonly
                                        >
                                        </Form.Input>
                                    </Col>
                                    <Col xs={24} sm={18}>
                                        <Form.TextArea

                                            field={`${field}[values]`}
                                            noLabel
                                        ></Form.TextArea>
                                    </Col>


                                </Row>
                            ))
                        }
                    </React.Fragment>
                )}
            </ArrayField>

        </Form> : ''}
    </div>
        </Modal></>
}

export default ExpressionPreview