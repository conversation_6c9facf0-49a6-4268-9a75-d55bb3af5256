import React, { useEffect, useState, useContext } from 'react'
import { Typography, Modal, Form, Notification, Skeleton, Popover, Row, Col } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';
import { getFlynet } from '@/services/flynet';
import { AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import Expressions from '@/components/expressions';
import CodeEditor from '@/components/code-editor';

import { GroupTreeData } from '../useAclGroup';
import AclModalSelector from '@/components/acl-modal-selector';
import { BASE_PATH } from '@/constants/router';
import { AttributeTemplate } from '@/interface/attribute-template';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
const { Paragraph, Text } = Typography;

const { Input, RadioGroup, Radio, Switch, TreeSelect } = Form

class AclGroupExt extends AclGroup {
    children: Array<AclGroupExt>
    constructor(children: Array<AclGroupExt>) {
        super();
        this.children = children;
    }
}

interface Props {
    close: () => void,
    success?: (aclGroup?: AclGroup) => void
    groupId: bigint,

    groupTreeData: TreeNodeData[],
    mapTreeData: Map<string, GroupTreeData>
}

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    const traceGroupTreeData = (src: TreeNodeData[], parentDisabled: boolean) : TreeNodeData[] => {
        // 遍历树形数据，如果value等于groupId，则将其及其children 的disabled设置为true,
        // 否则设置为false
        // 这里的src是树形数据
        // 需要遍历每个节点的children
        // 如果有children，则递归调用
        // 如果没有children，则直接返回
        // 这里的src是树形数据
        // 遍历树形数据

        let dst: TreeNodeData[] = [];
        src.forEach((item) => {
            let newItem = { ...item };
            let itemDisabled = false;
            if(parentDisabled) {
                itemDisabled = true;
            } else {
                itemDisabled = (item.value == props.groupId + '');
            }
            
            newItem.disabled = itemDisabled;
            
            if (item.children && item.children.length > 0) {
                newItem.children = traceGroupTreeData(item.children, itemDisabled);
            }
            dst.push(newItem);
        });


        return dst
    }

    const treeData = traceGroupTreeData(props.groupTreeData, false);

    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
        type: GroupType,
        expressionsCombo: string,
        parentId: string,
    }>>()

    const [aclGroup, setAclGroup] = useState<AclGroup>();
    // const [parentName, setParentName] = useState<string>();
    const [parentId, setParentId] = useState<string>();

    const [aclOrigins, setAclOrigins] = useState<AclOrigin[]>([]);
    const [aclSelectorVisible, setAclSelectorVisible] = useState(false);
    const [type, setType] = useState<GroupType>(GroupType.GROUP_STATIC);

    const [attributeTemplate, setAttributeTemplate] = useState<AttributeTemplate>();

    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet && res.flynet.attributeTemplate) {

            const json = JSON.parse(res.flynet.attributeTemplate);
            const userProperties = json.properties.input.properties.Policy;

            let attributeTemplate: AttributeTemplate = {
                type: 'object',
                title: 'properties',
                description: 'properties',
                properties: {
                    'input': {
                        type: 'object',
                        description: '输入',
                        title: '输入',
                        properties: {
                            Policy: userProperties
                        }
                    }
                }

            }
            setAttributeTemplate(attributeTemplate);
        }
    }
    useEffect(() => {
        queryFlynet();
    }, [])

    const [expressions, setExpressions] = useState<Array<Expression>>([]);
    const [advancedDynamicMode, setAdvancedDynamicMode] = useState(false);
    const [expressionAdvanced, setExpressionAdvanced] = useState('');

    const [expressionsError, setExpressionsError] = useState(false);

    const [groupLoading, setGroupLoading] = useState(false);

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleSubmit = async () => {
        await formApi?.validate();

        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';
        const type = values.type;

        let parentId = values.parentId;
        // const parentIds = values.parentId;
        let fullName = '';
        
        if (parentId) {
            const mapTreeData = props.mapTreeData;
            const parent = mapTreeData.get(parentId);
            fullName = `${parent?.fullName}/${name}`;
        }

        // if(parentIds) {
        //     if (parentIds instanceof Array) {
        //         parentId = parentIds[parentIds.length - 1];
        //     } else {
        //         parentId = parentIds;
        //     }
        //     const mapTreeData = getMapTreeData();
        //     const parent = mapTreeData.get(parentId);


        //     if (parent) {
        //         fullName = `${parent.fullName}/${name}`;
        //     }
        // }



        const expressionsCombo = values.expressionsCombo;

        if (type == GroupType.GROUP_DYNAMIC) {
            if (advancedDynamicMode) {
                if (expressionAdvanced.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            } else {
                if (expressionsError || expressions.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            }
        }

        setLoading(true);

        flylayerClient.updateAclGroup({
            flynetId: flynet.id,
            groupId: props.groupId,
            name: name,
            description: description,
            alias: alias,
            type: type,
            acls: aclOrigins,
            parentId: parentId ? BigInt(parentId) : undefined,
            fullName: fullName,
            attrs: advancedDynamicMode ? {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_ADVANCED,
                expressionAdvanced: expressionAdvanced
            } : {
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_STANDARD,
                expressions: expressions,
                expressionsCombo: expressionsCombo
            }
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'policies.group.editSuccess' })
            });
            props.success && props.success();
            props.close();
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'policies.group.editFailed' }),
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        })
    }

    useEffect(() => {
        setGroupLoading(true);
        flylayerClient.getAclGroup({
            groupId: props.groupId
        }).then(res => {
            setAclGroup(res.group);
            if (res.group && res.group.acls) {
                const mapTreeData = props.mapTreeData;
                const parent = mapTreeData.get(res.group.parentId + '');
                setParentId(res.group.parentId ? res.group.parentId + '' : '');
                // if (parent) {
                //     setParentName(parent.label);
                // }

                setAclOrigins(res.group?.acls);
                setType(res.group.type);

                setExpressions(res.group.attrs?.expressions ? res.group.attrs.expressions : []);

                setAdvancedDynamicMode(res.group.attrs?.dynamicGroupMode == DynamicGroupMode.DYNAMIC_GROUP_ADVANCED ? true : false);
                setExpressionAdvanced(res.group.attrs?.expressionAdvanced ? res.group.attrs.expressionAdvanced : '')
            }

            setGroupLoading(false);
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'policies.group.getFailed' }),
                content: err.message
            });
            setGroupLoading(false);
        })
    }, [])

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'policies.group.editGroup' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >

            <Skeleton loading={groupLoading && !aclGroup} placeholder={
                <>
                    {/* <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title> */}
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    {/* <Skeleton.Image style={{ height: 230, marginBottom: 20 }} /> */}
                </>
            }>
                {aclGroup && <div className={styles.addService}>
                    <Form getFormApi={SetFormApi}
                        allowEmpty
                        initValues={
                            {
                                name: aclGroup.name,
                                description: aclGroup.description,
                                alias: aclGroup.alias,
                                type: aclGroup.type,
                                advancedDynamicMode: aclGroup.attrs?.dynamicGroupMode == DynamicGroupMode.DYNAMIC_GROUP_ADVANCED ? true : false,
                                expressionsCombo: aclGroup.attrs && aclGroup.attrs.expressionsCombo ? aclGroup.attrs.expressionsCombo : '',
                                parentId: parentId
                            }
                        }
                    >
                        <Row gutter={12}>
                            <Col span={12}>
                                <Input field='alias' label={formatMessage({ id: 'policies.group.name' })} validate={value => {
                                    if (!value) {
                                        return formatMessage({ id: 'policies.group.nameRequired' });
                                    }
                                    return '';
                                }} />
                            </Col>
                            <Col span={12}>
                                <Input field='name'
                                    label={<>{formatMessage({ id: 'policies.group.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'policies.group.codeTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                                    trigger={'blur'} readonly validate={value => {
                                        if (!value) {
                                            return formatMessage({ id: 'policies.group.codeRequired' });
                                        }
                                        // 编码不能以-开头
                                        if (value.trim().startsWith('-')) {
                                            return formatMessage({ id: 'policies.group.codeCannotStartWithDash' });
                                        }
                                        if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                            return formatMessage({ id: 'policies.group.codeInvalidFormat' });
                                        }
                                        return '';
                                    }}
                                    required />
                            </Col>
                        </Row>
                        <Row>
                            <Col span={24}>
                                <Input field='description' label={formatMessage({ id: 'policies.group.remark' })} />
                            </Col>
                        </Row>
                        <Row>
                            <Col span={24}>
                                <TreeSelect
                                    dropdownStyle={{ width: '100%', maxHeight: 300, overflow: 'auto' }}
                                    expandAll
                                    treeData={treeData}
                                    filterTreeNode
                                    field='parentId'
                                    placeholder={formatMessage({ id: 'policies.group.pleaseSelect' })}
                                    label={formatMessage({ id: 'policies.group.parentGroup' })}
                                    showClear
                                    style={{ width: '100%' }}></TreeSelect>

                            </Col>
                        </Row>
                        
                        {/* <Divider></Divider>
                        <Row>
                            <Col span={16}>
                                <RadioGroup field='type' label="类型" onChange={(e) => setType(e.target.value)}>
                                    <Radio checked={type == GroupType.GROUP_STATIC} value={GroupType.GROUP_STATIC}>静态策略组</Radio>
                                    <Radio checked={type == GroupType.GROUP_DYNAMIC} value={GroupType.GROUP_DYNAMIC}>动态策略组</Radio>
                                </RadioGroup>
                            </Col>
                            <Col span={8} className='btn-right-col' style={{ paddingTop: 24, display: type == GroupType.GROUP_DYNAMIC ? '' : 'none' }}>
                                <Switch field='advancedDynamicMode' onChange={(checked) => {
                                    setAdvancedDynamicMode(checked)
                                }} label="专家模式" labelPosition='left' ></Switch>
                            </Col>
                        </Row>
                        {type == GroupType.GROUP_STATIC && <>
                            <Row className="mb20">
                                <Col span={20}>
                                    <Text type='tertiary'>策略</Text>
                                </Col>
                                <Col span={4} className={styles.rightColumn}>
                                    <Button
                                        onClick={() => {
                                            setAclSelectorVisible(true);
                                        }}
                                        icon={<IconPlus></IconPlus>}></Button>
                                </Col>
                            </Row>
                            {
                                aclOrigins.length == 0 ? <TableEmpty loading={false}></TableEmpty> :

                                    <>
                                        {aclOrigins.map((item, index) => {

                                            return <Row className="mb10" key={index}>
                                                <Col span={20}>
                                                    {item.name}{item.description ? `(${item.description})` : ''}
                                                </Col>
                                                <Col span={4} className={styles.rightColumn}>
                                                    <Button
                                                        type='danger'
                                                        onClick={() => {
                                                            let newAcls = aclOrigins.filter((item, i) => i != index);
                                                            setAclOrigins(newAcls);
                                                        }}
                                                        icon={<IconMinusCircle></IconMinusCircle>}></Button>

                                                </Col>
                                            </Row>
                                        })}
                                    </>
                            }

                        </>}
                        {type == GroupType.GROUP_DYNAMIC && attributeTemplate && <>
                            {
                                advancedDynamicMode ? <>
                                    <CodeEditor value={expressionAdvanced} height='280px' onChange={(value) => setExpressionAdvanced(value || '')} language='systemverilog'></CodeEditor>
                                    {expressionsError && <Paragraph type='danger'>表达式不能为空</Paragraph>}
                                </> : <>
                                    <Expressions
                                        expressions={expressions}
                                        onChange={(expressions: Array<Expression>) => {
                                            setExpressions(expressions);
                                            setExpressionsError(false);
                                        }}
                                        onError={() => {
                                            setExpressionsError(true);
                                        }}
                                        attributeTemplate={attributeTemplate}
                                    ></Expressions>
                                    {expressionsError && <Paragraph type='danger'>触发参数错误</Paragraph>}
                                    <Row>
                                        <Col span={24}>
                                            <Input
                                                extraText='运算符支持 and(与)、or (或)、or (或)、not (非)、()(括号)，例如：1 and 2，1 or 2, not 1,  1 or (not 1)'
                                                field='expressionsCombo' label='参数组合' />
                                        </Col>
                                    </Row>
                                </>
                            }
                        </>}

                        {type == GroupType.GROUP_DYNAMIC && !attributeTemplate && <Card>
                            <Paragraph style={{ textAlign: 'center' }}>动态策略组属性为空, 请前往
                                <a className='link-external' target='_blank' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>
                                    设置<IconArrowUpRight />
                                </a>

                            </Paragraph>

                        </Card>} */}



                    </Form>
                </div>}
            </Skeleton>

        </Modal>
        {
            aclSelectorVisible && <AclModalSelector
                multi={true}
                value={aclOrigins}
                onChange={(value: AclOrigin | AclOrigin[]) => {
                    setAclSelectorVisible(false)

                    let newAcls = aclOrigins.filter((item) => true);
                    if (value instanceof Array) {
                        value.forEach((item) => {
                            if (!newAcls.some(u => u.id == item.id)) {
                                newAcls.push(item);
                            }
                        })
                    } else {
                        if (!newAcls.some(u => u.id == value.id)) {
                            newAcls.push(value);
                        }
                    }
                    setAclOrigins(newAcls);
                }}
                close={() => setAclSelectorVisible(false)}
            ></AclModalSelector>
        }
    </>
}

export default Index;
