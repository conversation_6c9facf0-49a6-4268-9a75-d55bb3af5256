import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification } from '@douyinfe/semi-ui';
import { AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';
import { getSimpleAclGroupName, getSimpleAclOriginName } from '@/utils/acl';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    group: AclGroup,
    record: AclOrigin
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    const flynetGeneral = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const aclName = getSimpleAclOriginName(props.record);
    const aclGroupName = getSimpleAclGroupName(props.group);
    
    const handleSubmit = () => {
        setLoading(true);
        
        flylayerClient.removeAclFromGroup({
            aclId: props.record.id,
            groupId: props.group.id,
            flynetId: flynetGeneral.id
        }).then(_res => {
            Notification.success({
                title: formatMessage({ id: 'policies.removeFromGroup.successTitle' }),
                content: `${formatMessage({ id: 'policies.removeFromGroup.successMessage' })} ${aclName} ${aclGroupName}`
            });
            props.close();
            props.success && props.success();
        }).catch(_err => {
            Notification.error({
                title: formatMessage({ id: 'policies.removeFromGroup.failedTitle' }),
                content: `${formatMessage({ id: 'policies.removeFromGroup.failedMessage' })} ${aclName} ${aclGroupName}`
            });
        }).finally(() => {
            setLoading(false);
        });
        
    }

    return <>
        <Modal
            title={formatMessage({ id: 'policies.removeFromGroup.title' })}
            visible={true}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'policies.removeFromGroup.confirmMessage' })} <b>{aclName}</b> <b>{aclGroupName}</b></Paragraph>
        </Modal></>
}
export default Index;
