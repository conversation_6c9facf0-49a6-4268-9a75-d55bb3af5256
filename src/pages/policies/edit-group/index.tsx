import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Notification, Select } from '@douyinfe/semi-ui';
import { AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';
import { getSimpleAclGroupName, getSimpleAclOriginName } from '@/utils/acl';
const { Paragraph } = Typography;
interface Props {
    close: () => void,
    success?: () => void,
    record: AclOrigin
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 策略组是否正在加载中
    const [loadingGroups, setLoadingGroups] = useState(true);
    // 策略组列表
    const [groups, setGroups] = useState<AclGroup[]>([]);

    const [groupIds, setGroupIds] = useState<string[]>(props.record.aclGroups.map((item) => {
        return item.id + '';
    }));

    // 加载数据
    const query = () => {
        setLoadingGroups(true)

        flylayerClient.listAclGroups({
            flynetId: flynet.id
        }).then((res) => {
            setGroups(res.groups)
        }).finally(() => {
            setLoadingGroups(false);
        })
    }

    useEffect(() => {
        query();
    }, []);

    const handleSubmit = () => {
        setLoading(true);

        flylayerClient.editAclGroups({
            flynetId: flynet.id,
            aclId: props.record.id,
            groupIds: groupIds.map((item) => {
                return BigInt(item);
            })
        }).then((_res) => {
            Notification.success({
                title: formatMessage({ id: 'policies.editGroup.success' })
            })
            props.success && props.success();
        }).catch(() => {
            Notification.error({ content: formatMessage({ id: 'policies.editGroup.failed' }), position: "bottomRight" })

        }).finally(() => {
            setLoading(false);
        })

    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'policies.editGroup.title' })}
            visible={true}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{`${formatMessage({ id: 'policies.editGroup.description' })} ${getSimpleAclOriginName(props.record)}`}</Paragraph>
            <div style={{ minHeight: 200 }}>
                {groups && groups.length > 0 &&
                    <Select size='large' style={{ width: '100%' }}
                        multiple
                        filter
                        optionList={groups.map((item) => {
                            return {
                                label: `${item.alias}(${item.name})`,
                                value: item.id + ''
                            }
                        })}
                        value={groupIds}
                        onChange={(value) => {
                            setGroupIds(value as any);
                        }}
                        placeholder={formatMessage({ id: 'policies.editGroup.placeholder' })}
                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    ></Select>
                }
            </div>
        </Modal></>
}
export default Index;
