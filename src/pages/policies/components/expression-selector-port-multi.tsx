import { FC, useState } from 'react';
import { Select, Divider, Button, Input, Typography, Switch, Popover, Form } from '@douyinfe/semi-ui'
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconMore, IconHelpCircle } from '@douyinfe/semi-icons';
import { isValidPortInput } from '@/utils/validators';
import { useLocale } from '@/locales';
const { Paragraph } = Typography;
const Index: FC<{
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource[]) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    const [currentValue, setCurrentValue] = useState<Array<string>>([]);

    const [currentPort, setCurrentPort] = useState<Array<string>>([]);

    const [isL7, setIsL7] = useState(false);

    const mapedList = props.optList?.map(item => {
        let disabled = false;
        if (props.existValues.find(v => v.value.indexOf(item.value+':') > -1 )) {
            disabled = true;
        }
        return {
            disabled,
            value: item.value,
            label: item.label.replace('exp:', '')
        }
    }) || [];

    const [formApi, SetFormApi] = useState<FormApi>()
    return <><Form getFormApi={SetFormApi} initValues={{
        port: currentPort,
        currentValue: currentValue
    }}>
        <Form.Select
            zIndex={3000}
            noLabel
            multiple
            style={{ width: '100%' }}
            field='currentValue'
            trigger={'blur'}
            optionList={mapedList}
            filter
            position='top'
            validate={(val) => {
                if (!val || val.length == 0) {
                    return '请选择表达式'
                }
                return ''
            }}

            onChange={val => {
                setCurrentValue(val as string[])
            }}>

        </Form.Select>


        <Paragraph type='tertiary'>{formatMessage({ id: 'policies.expression.layer7' })}</Paragraph>
        <Switch aria-label={formatMessage({ id: 'policies.expression.layer7' })} checked={isL7} onChange={(checked) => setIsL7(checked)} className='mb10'></Switch>

        {!isL7 && <><div style={{ color: 'var(--semi-color-text-2)' }} >端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

            端口支持以下格式：
            <div>所有端口：*</div>
            <div>单个端口：22或80</div>
            <div>连续端口：1-65535或100-20000</div>
            <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
        </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>

            <Form.TagInput
                noLabel

                validate={(val) => {
                    if (!val || val.length == 0) {
                        return '端口不能为空'
                    }

                    if (!isValidPortInput(val.join(','))) {
                        return '端口格式不正确';
                    }

                    return ''
                }}
                addOnBlur
                field='port'
                onChange={val => setCurrentPort(val)}
            ></Form.TagInput>


        </>}
        <Divider className='mb10'></Divider>
        <Button block onClick={async () => {
            await formApi?.validate();
            let port = currentPort.join(',');
            let arrList = formApi?.getValue('currentValue');

            let list: AclOrigin_Resource[] = [];
            
            for (let i = 0; i < arrList.length; i++) {
                let val = '';
                if (isL7) {
                    val = 'l7:' + arrList[i];
                } else {
                    val = arrList[i] + ':' + port;
                }

                list.push(new AclOrigin_Resource({
                    value: val,
                    type: AclOrigin_ResourceType.EXPRESSION
                }))
            }

            props.onChange(list)



        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </Form>
    </>
}

export default Index;
