import { FC, useState } from 'react';
import { Select, Divider, Button, Form } from '@douyinfe/semi-ui'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

const SingleSelectorMulti: FC<{
    type: AclOrigin_ResourceType
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource[]) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()

    const mapedList = props.optList?.map(item => {
        let disabled = false;
        if (props.existValues.find(v => v.value === item.value)) {
            disabled = true;
        }
        return {
            disabled,
            value: item.value,
            label: item.label.replace('exp:', '').replace('group:', '').replace('tag:', '')
        }
    }) || [];
    const [currentValue, setCurrentValue] = useState<Array<string>>([]);
    return <><Form getFormApi={SetFormApi} initValues={{currentValue: currentValue}}>
        <Form.Select
            noLabel
            zIndex={3000}
            style={{ width: '100%' }}
            field='currentValue'
            filter
            showClear
            multiple
            optionList={mapedList}
            trigger={'blur'}
            position='top'
            validate={(val) => {
                if (!val || val.length == 0) {
                    return formatMessage({ id: 'components.common.pleaseSelect' })
                }
                return ''
            }}
            onChange={val => {
                setCurrentValue(val as string[])

            }}>

        </Form.Select>
        <Divider className='mb10'></Divider>
        <Button block onClick={async () => {
            await formApi?.validate();

            let list: AclOrigin_Resource[] = [];
            for(let i = 0; i < currentValue.length; i++) {
                list.push(new AclOrigin_Resource({
                    type: props.type,
                    value: currentValue[i]
                }));
            }
            props.onChange(list)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button></Form>
    </>
}

export default SingleSelectorMulti;
