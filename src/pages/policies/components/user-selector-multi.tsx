import { FC, useState, useEffect, useContext, useCallback } from 'react';
import { Form, Divider, Button, Notification, Skeleton } from '@douyinfe/semi-ui'
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { renderCustomOption } from '@/utils/user';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';
import { debounce } from 'lodash';
import { OptionProps } from '@douyinfe/semi-ui/lib/es/select';
import { useLocale } from '@/locales';

const UserSelectorMulti: FC<{
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource[]) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>();
    // Select用户下拉菜单在用户对象中聚值的字段
    const userField = 'loginName'

    const flynet = useContext(FlynetGeneralContext);

    // 当前选择的用户
    const [currentValue, setCurrentValue] = useState<Array<string>>([]);
    // 当前输入的用户
    const [currentValueInput, setCurrentValueInput] = useState<Array<string>>([]);
    // 是否选择用户
    const [isSelectUser, setIsSelectUser] = useState(true);
    // 用户列表
    const [users, setUsers] = useState<User[]>();
    // 所有用户列表
    const [allUsers, setAllUsers] = useState<User[]>();
    // 是否正在加载用户数据 
    const [loadingUser, setLoadingUser] = useState(false);


    const queryUser = async (filterKeywords?: string) => {
        try {
            const res = await flylayerClient.listUsers({
                flynetId: flynet?.id,
                query: filterKeywords ? `keywords=${encodeURIComponent(filterKeywords)}&limit=20&offset=0` : 'limit=20&offset=0'
            })
            setUsers(res.users.filter(user => !user.disabled));
        } catch (err) {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'policies.user.fetchFailed' }) })
        }
    }

    const debounceQuery = useCallback(debounce((queryStr) => queryUser(queryStr), 500), []);

    // 查询用户数据
    const filterActor = (val: string) => {
        debounceQuery(val);
    }

    function filterUser(sugInput:string, option:OptionProps) {
        if (!sugInput) {
            return true
        }
        const u = option as User
        let input = sugInput.trim();
        if (u.loginName.indexOf(input) > -1) {
            return true
        } else if (u.displayName && u.displayName.indexOf(input) > -1) {
            return true
        }
        return false
    }

    useEffect(() => {
        setLoadingUser(true);
        flylayerClient.listUsers({
            flynetId: flynet?.id,
            query: 'limit=20&offset=0'
        }).then((res) => {
            const users = res.users.filter(user => !user.disabled)

            setUsers(users);
            setAllUsers(users)
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: '获取用户列表失败, 请稍后重试' })

        }).finally(() => {
            setLoadingUser(false);

        })
    }, []);

    return <>
        <Skeleton loading={loadingUser} placeholder={<>
            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginTop: '12px',
                marginBottom: '32px',
            }}></Skeleton.Image>
            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginBottom: '20px',
            }}></Skeleton.Image>
            <Divider className='mb10'></Divider>
            <Button block>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </>}>
            <Form getFormApi={SetFormApi} initValues={{ currentValue: currentValue, currentValueInput: currentValueInput }}>
                <Form.Select
                    style={{ width: '100%' }}
                    zIndex={3000}
                    filter
                    remote                    
                    noLabel
                    showClear
                    multiple
                    trigger={'blur'}
                    field='currentValue'
                    placeholder='请选择用户'
                    // renderSelectedItem={renderSelectedItem}
                    onSearch={filterActor}
                    position='top'
                    loading={loadingUser}
                    onChange={(val: any) => {
                        setIsSelectUser(true);
                        setCurrentValue(val as string[])
                        setCurrentValueInput([])
                        formApi?.setValue('currentValueInput', [])
                    }}
                    onClear={() => {
                        setCurrentValue([])
                        setIsSelectUser(false)
                    }}
                    emptyContent={<>暂无用户</>}
                >
                    {users?.map((user, index) => {
                        let isDisabled = false;
                        if (props.existValues.find((value) => value.value == user.loginName)) {
                            isDisabled = true;
                        }
                        return renderCustomOption(user, index, userField, isDisabled)
                    })}
                </Form.Select>
                {/* <Paragraph type='tertiary'>或输入</Paragraph> */}
                <Form.TagInput
                    noLabel
                    placeholder={'或输入用户, 以逗号分隔'}
                    trigger={'blur'}
                    field='currentValueInput'
                    onChange={val => {
                        setCurrentValueInput(val)
                        if (val.length == 0) {
                            setCurrentValueInput([])
                            setIsSelectUser(true)
                        }
                    }}

                    disabled={isSelectUser && currentValue && currentValue.length > 0}
                    showClear
                    addOnBlur
                    validate={(val) => {
                        if (isSelectUser) {
                            return '';
                        }
                        if (!val && !currentValue) {
                            return '用户不能为空'
                        }
                        return ''
                    }}
                ></Form.TagInput>

                <Divider className='mb10'></Divider>
                <Button disabled={
                    !currentValue && !currentValueInput
                } block onClick={async () => {
                    await formApi?.validate();
                    
                    if (currentValueInput && currentValueInput.length > 0) {
                        let list: AclOrigin_Resource[] = [];
                        for (let i = 0; i < currentValueInput.length; i++) {
                            list.push(new AclOrigin_Resource({
                                value: currentValueInput[i],
                                type: AclOrigin_ResourceType.USER
                            }))
                        }

                        props.onChange(list)

                    } else {
                        let list: AclOrigin_Resource[] = [];
                        for (let i = 0; i < currentValue.length; i++) {
                            list.push(new AclOrigin_Resource({
                                value: currentValue[i],
                                type: AclOrigin_ResourceType.USER
                            }))
                        }
                        props.onChange(list)
                    }
                }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
            </Form>
        </Skeleton>


    </>
}

export default UserSelectorMulti;