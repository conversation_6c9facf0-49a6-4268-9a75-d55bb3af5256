import { FC, useState } from 'react';
import { Form, Input, CheckboxGroup, Checkbox, Divider, Dropdown, Button, Space } from '@douyinfe/semi-ui'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { isValidIPOrIpRangeOrCIDR } from '@/utils/validators';
import { useLocale } from '@/locales';

const IpInput: FC<{
    value: AclOrigin_Resource,
    onChange: (val: AclOrigin_Resource) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()
    const [currentValue, setCurrentValue] = useState(props.value.value);
    return <>
        <Form getFormApi={SetFormApi} initValues={{ currentValue: currentValue }}>
            <Form.Input
                noLabel
                extraText={formatMessage({ id: 'policies.ip.cidrSupport' })}
                placeholder={formatMessage({ id: 'policies.ip.placeholder' })}
                trigger={'blur'}
                validate={(val) => {
                    // 验证IP不能为空
                    if (!val) {
                        return formatMessage({ id: 'policies.ip.required' })
                    }
                    // 验证，是否IP格式
                    return isValidIPOrIpRangeOrCIDR(val)

                }}
                field='currentValue'
                onChange={val => setCurrentValue(val)} />
            <Divider className='mb10'></Divider>
            <Button block onClick={async () => {
                await formApi?.validate();
                props.value.value = currentValue;
                props.value.type = AclOrigin_ResourceType.IP;

                props.onChange(props.value)
            }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </Form>
    </>
}

export default IpInput;