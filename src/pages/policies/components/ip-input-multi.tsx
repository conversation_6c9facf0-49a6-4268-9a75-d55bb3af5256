import { FC, useState } from 'react';
import { Form, Divider, Dropdown, Button, Space } from '@douyinfe/semi-ui'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { isValidIPOrIpRangeOrCIDR } from '@/utils/validators';
import { useLocale } from '@/locales';

const IpInputMulti: FC<{
    onChange: (val: AclOrigin_Resource[]) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()
    const [currentValue, setCurrentValue] = useState<Array<string>>([]);
    return <>
        <Form getFormApi={SetFormApi} initValues={{ currentValue: currentValue }}>
            <Form.TagInput
                noLabel
                addOnBlur
                extraText={formatMessage({ id: 'policies.ip.multiSupport' })}
                placeholder={formatMessage({ id: 'policies.ip.placeholder' })}
                validate={(vals: Array<string>) => {
                    // 验证IP不能为空
                    if (!vals) {
                        return formatMessage({ id: 'policies.ip.required' })
                    }

                    for (let i = 0; i < vals.length; i++) {
                        let val = vals[i];

                        let res = isValidIPOrIpRangeOrCIDR(val)
                        if(res) {
                            return res
                        }
                    }

                    return ''
                }}
                field='currentValue'

                onChange={val => setCurrentValue(val)} />
            <Divider className='mb10'></Divider>
            <Button block onClick={async () => {
                await formApi?.validate();
                let arrList = formApi?.getValue('currentValue');



                let list: AclOrigin_Resource[] = [];
                let strList: Array<string> = [];
                for (let i = 0; i < arrList.length; i++) {
                    if (strList.indexOf(arrList[i]) < 0) {
                        strList.push(arrList[i]);
                        list.push(new AclOrigin_Resource({
                            value: arrList[i],
                            type: AclOrigin_ResourceType.IP
                        }))
                    }
                }




                props.onChange(list)
            }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </Form>
    </>
}

export default IpInputMulti;