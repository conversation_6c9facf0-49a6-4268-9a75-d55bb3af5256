import { FC, useState, useEffect, useContext, useCallback } from 'react';
import { Form, Divider, Button, Notification, Skeleton } from '@douyinfe/semi-ui'
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { renderSelectedItem, renderCustomOption } from '@/utils/user';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';
import { debounce } from 'lodash';
import { OptionProps } from '@douyinfe/semi-ui/lib/es/select';
import { useLocale } from '@/locales';

const UserSelector: FC<{
    value: AclOrigin_Resource,
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>();
    // Select用户下拉菜单在用户对象中聚值的字段
    const userField = 'loginName'

    const flynet = useContext(FlynetGeneralContext);


    // 当前选择的用户
    const [currentValue, setCurrentValue] = useState('');
    // 当前输入的用户
    const [currentValueInput, setCurrentValueInput] = useState('');
    // 是否选择用户
    const [isSelectUser, setIsSelectUser] = useState(true);
    // 用户列表
    const [users, setUsers] = useState<User[]>();
    const [selectedUser, setSelectedUser] = useState<User>();
    // 所有用户列表
    // const [allUsers, setAllUsers] = useState<User[]>();
    // 是否正在加载用户数据 
    const [loadingUser, setLoadingUser] = useState(true);

    const queryUser = async (filterKeywords?: string) => {
        try {
            const res = await flylayerClient.listUsers({
                flynetId: flynet?.id,
                query: filterKeywords ? `keywords=${encodeURIComponent(filterKeywords)}&limit=20&offset=0` : 'limit=20&offset=0'
            })
            setUsers(res.users.filter(user => !user.disabled));
        } catch (err) {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'policies.user.fetchFailed' }) })
        }
    }

    const debounceQuery = useCallback(debounce((queryStr) => queryUser(queryStr), 500), []);

    // 查询用户数据
    const queryActor = (val: string) => {
        debounceQuery(val);
    }

    function filterUser(sugInput: string, option: OptionProps) {
        if (!sugInput) {
            return true
        }
        const u = option as User
        let input = sugInput.trim();
        if (u.loginName.indexOf(input) > -1) {
            return true
        } else if (u.displayName && u.displayName.indexOf(input) > -1) {
            return true
        }
        return false
    }

    const querySelectedUser = async (userLoginName: string) => {
        
        try {
            const resUser = await flylayerClient.getUser({
                flynetId: flynet.id,
                identifier: {
                    case: 'loginName',
                    value: userLoginName
                }
            })

            if (resUser && resUser.user) {
                setIsSelectUser(true)
                setSelectedUser(resUser.user);      
                setCurrentValueInput('')
                formApi?.setValue('currentValueInput', "")
                setCurrentValue(userLoginName)
                formApi?.setValues({
                    currentValue: userLoginName,
                    currentValueInput: ''
                })

               
            } else {


                setIsSelectUser(false)
                setCurrentValueInput(userLoginName)
                setCurrentValue('')
                formApi?.setValues({
                    currentValue: "",
                    currentValueInput: userLoginName
                })

            }
        } catch (err) {
            setIsSelectUser(false)
            setCurrentValueInput(userLoginName)
            setCurrentValue('') 
            formApi?.setValues({
                currentValue: "",
                currentValueInput: userLoginName
            })
            console.error(err)

        }
    }

    const queryInitUser = async () => {
        try {
            setLoadingUser(true);
            const resUsers = await flylayerClient.listUsers({
                flynetId: flynet?.id,
                query: 'limit=20&offset=0'
            })
            const users = resUsers.users.filter(user => !user.disabled)
            setUsers(users);

            if (props.value && props.value.value) {
               querySelectedUser(props.value.value)
            } else {
                setCurrentValueInput('')
                setCurrentValue('')
                formApi?.setValue('currentValueInput', "")
                formApi?.setValue('currentValue', "");
            }

            setLoadingUser(false);
        } catch (err) {

            setLoadingUser(false);

            setCurrentValueInput(props.value.value)
            setCurrentValue('')
            formApi?.setValue('currentValue', "");
            formApi?.setValue('currentValueInput', props.value.value);

            console.error(err)
            Notification.error({ content: formatMessage({ id: 'policies.user.fetchFailed' }) })
        }

    }

    useEffect(() => {
        queryInitUser();
    }, []);

    return <>
        <Skeleton loading={loadingUser || (!currentValue && !currentValueInput)} placeholder={<>
            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginTop: '12px',
                marginBottom: '32px',
            }}></Skeleton.Image>
            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginBottom: '20px',
            }}></Skeleton.Image>
            <Divider className='mb10'></Divider>
            <Button block>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </>}>
            {!loadingUser && (currentValue || currentValueInput) &&
            <Form getFormApi={SetFormApi} initValues={{ currentValue: currentValue, currentValueInput: currentValueInput }}>
                <Form.Select
                    style={{ width: '100%' }}
                    zIndex={3000}
                    filter
                    noLabel
                    showClear
                    field='currentValue'
                    placeholder={formatMessage({ id: 'policies.user.placeholder' })}
                    renderSelectedItem={(opt:any) =>renderSelectedItem(opt, selectedUser)}
                    onSearch={queryActor}
                    position='top'
                    loading={loadingUser}
                    onChange={(val: any) => {
                        const user = users?.find(user => user.loginName == val);
                        if (user) {
                            setSelectedUser(user);
                        }    

                        setIsSelectUser(true);
                        setCurrentValue(val as string)
                        setCurrentValueInput("")
                        formApi?.setValue('currentValueInput', "")
                    }}
                    onClear={() => {
                        setCurrentValue('')
                        setIsSelectUser(false)
                    }}
                    emptyContent={<>{formatMessage({ id: 'policies.user.noUsers' })}</>}
                >

                    {users?.map((user, index) => {

                        let isDisabled = false;

                        if (user.loginName == props.value.value) {
                            isDisabled = false;
                        } else if (props.existValues.find((value) => value.value == user.loginName)) {
                            isDisabled = true;
                        }

                        return renderCustomOption(user, index, userField, isDisabled)
                    })}
                </Form.Select>
                {/* <Paragraph type='tertiary'>或输入</Paragraph> */}
                <Form.Input
                    noLabel
                    placeholder={formatMessage({ id: 'policies.user.orEnterUser' })}
                    trigger={'blur'}
                    field='currentValueInput'
                    onChange={val => setCurrentValueInput(val)}
                    onClear={() => {
                        setCurrentValueInput('')
                        setIsSelectUser(true)
                    }}
                    disabled={isSelectUser && !!currentValue}
                    showClear
                    validate={(val) => {
                        if (isSelectUser) {
                            return '';
                        }
                        if (!val && !currentValue) {
                            return formatMessage({ id: 'policies.user.required' })
                        }
                        return ''
                    }}
                ></Form.Input>

                <Divider className='mb10'></Divider>
                <Button disabled={
                    !currentValue && !currentValueInput
                } block onClick={async () => {
                    await formApi?.validate();
                    if (currentValueInput) {
                        
                        props.value.value = currentValueInput;
                        props.value.type = AclOrigin_ResourceType.USER;


                        props.onChange(props.value)


                        return;
                    }
                    props.value.value = currentValue;
                    props.value.type = AclOrigin_ResourceType.USER;

                    props.onChange(props.value)
                }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
            </Form>
}
        </Skeleton>


    </>
}

export default UserSelector;