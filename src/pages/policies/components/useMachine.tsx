import { useState, useEffect, useContext, useCallback } from 'react';
import { Notification } from '@douyinfe/semi-ui'

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { debounce } from 'lodash';
import { useLocale } from '@/locales';
const useMachine = () => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    // 设备列表
    const [machines, setMachines] = useState<Machine[]>();

    // 是否正在加载设备数据
    const [loadingMachine, setLoadingMachine] = useState(false);


    const queryMachine = async (filterKeywords?: string) => {
        try {
            const res = await flylayerClient.listMachines({
                flynetId: flynet?.id,
                query: filterKeywords ? `keywords=${encodeURIComponent(filterKeywords)}&limit=20&offset=0` : 'limit=20&offset=0'
            })
            setMachines(res.machines);
        } catch (err) {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'policies.device.fetchFailed' }) })
        }
    }

    const debounceQuery = useCallback(debounce((queryStr) => queryMachine(queryStr), 500), []);

    // 查询设备数据
    const queryDevices = (val: string) => {
        debounceQuery(val);
    }


    useEffect(() => {
        setLoadingMachine(true);
        flylayerClient.listMachines({
            flynetId: flynet?.id,
            query: 'limit=20&offset=0'
        }).then(res => {
            const machines = res.machines;
            setMachines(machines);
        }).catch(err => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'policies.device.fetchFailed' }) })
        }).finally(() => {
            setLoadingMachine(false);
        })
    }, [])

    return {
        machines,
        loadingMachine,
        queryDevices
    }

};

export default useMachine;