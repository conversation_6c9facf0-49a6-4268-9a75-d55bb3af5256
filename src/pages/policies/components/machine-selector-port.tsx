import { FC, useState } from 'react';
import { Form, Divider, Button, Skeleton, Select, Popover } from '@douyinfe/semi-ui'
import { IconHelpCircle } from '@douyinfe/semi-icons';

import { isValidPortInput } from '@/utils/validators';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import useMachine from './useMachine';
import { useLocale } from '@/locales';

const MachineSelectorPort: FC<{
    value: AclOrigin_Resource,
    onChange: (val: AclOrigin_Resource) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    const arr = props.value.value.split(':');
    const machineIp = arr.length > 0 ? arr[0] : '';
    const port = arr.length > 1 ? arr[1] : ''
    const [formApi, SetFormApi] = useState<FormApi>();

    const { machines, loadingMachine, queryDevices } = useMachine();

    // 当前选择的设备
    const [currentValue, setCurrentValue] = useState(machineIp);


    let ports: Array<string> = [];
    if (port.length > 0) {
        ports = port.split(',');
    }
    const [currentPort, setCurrentPort] = useState(ports);

    return <>
        <Skeleton loading={loadingMachine} placeholder={<>
            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginTop: '12px',
                marginBottom: '32px',
            }}></Skeleton.Image>

            <Divider className='mb10'></Divider>
            <div style={{ color: 'var(--semi-color-text-2)' }}>端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

                端口支持以下格式：
                <div>所有端口：*</div>
                <div>单个端口：22或80</div>
                <div>连续端口：1-65535或100-20000</div>
                <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
            </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>

            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginTop: '12px',
                marginBottom: '20px',
            }}></Skeleton.Image>
            <Button block>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </>}>
            <Form getFormApi={SetFormApi} initValues={{
                port: currentPort,
                currentValue: currentValue
            }}>
                <Form.Select
                    style={{ width: '100%' }}
                    zIndex={3000}
                    filter
                    remote
                    noLabel
                    showClear
                    field='currentValue'
                    placeholder='请选择设备'
                    onSearch={queryDevices}
                    position='top'
                    loading={loadingMachine}
                    onChange={(val: any) => {
                        setCurrentValue(val as string)
                    }}
                    onClear={() => {
                        setCurrentValue('')
                    }}
                    emptyContent={<>暂无设备</>}
                >

                    {machines?.map((machine, index) => {
                        return <Select.Option value={machine.ipv4} showTick={true} key={index + 1}>
                            {machine.autoGeneratedName ? machine.name : machine.givenName}
                        </Select.Option>
                    })}
                </Form.Select>

                <Divider className='mb10'></Divider>
                <div style={{ color: 'var(--semi-color-text-2)' }}>端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

                    端口支持以下格式：
                    <div>所有端口：*</div>
                    <div>单个端口：22或80</div>
                    <div>连续端口：1-65535或100-20000</div>
                    <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
                </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>

                <Form.TagInput
                    noLabel
                    trigger={'blur'}
                    validate={(val) => {
                        if (!val || val.length == 0) {
                            return '端口不能为空'
                        }
                        if (!isValidPortInput(val.join(','))) {
                            return '端口格式不正确';
                        }
                        return ''
                    }}
                    addOnBlur
                    field='port'
                    onChange={val => setCurrentPort(val)}
                ></Form.TagInput>
                <Button disabled={
                    !currentValue
                } block onClick={async () => {
                    await formApi?.validate();

                    props.value.value = currentValue + ':' + currentPort.join(',');
                    props.value.type = AclOrigin_ResourceType.DEVICE;

                    props.onChange(props.value)
                }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
            </Form>
        </Skeleton>

    </>
}

export default MachineSelectorPort;