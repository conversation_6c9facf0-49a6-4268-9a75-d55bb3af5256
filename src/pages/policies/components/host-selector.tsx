import { FC, useState } from 'react';
import { Select, Divider, Button, Form } from '@douyinfe/semi-ui'
import { IconEdit } from '@douyinfe/semi-icons';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

const HostSelector: FC<{
    value: AclOrigin_Resource,
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()

    const [currentValue, setCurrentValue] = useState(props.value.value);
    return <><Form getFormApi={SetFormApi}>
        <Form.Select
            noLabel
            zIndex={3000}
            filter
            style={{ width: '100%' }}
            trigger={'blur'}
            field='currentValue'
            optionList={props.optList?.map(item => {
                let disabled = false;
                if (item.value == props.value.value) {
                    disabled = false;
                } else if (props.existValues.find(v => v.value === item.value)) {
                    disabled = true;
                }
                return {
                    disabled,
                    value: item.value,
                    label: item.label
                }
            }) || []
            }
            position='top'
            validate={(val) => {
                if (!val) {
                    return formatMessage({ id: 'policies.resourceGroup.placeholder' })
                }
                return ''
            }}
            onChange={val => {
                setCurrentValue(val as string)
            }}>

        </Form.Select>
        {/* <Button className='mb10' icon={<IconEdit/>} block>编辑IP别名</Button> */}
        <Divider className='mb10'></Divider>

        <Button block onClick={async () => {
            await formApi?.validate();
            props.value.value = currentValue;
            props.value.type = AclOrigin_ResourceType.IP_GROUP;

            props.onChange(props.value)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </Form>
    </>
}

export default HostSelector;
