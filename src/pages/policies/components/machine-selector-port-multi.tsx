import { FC, useState } from 'react';
import { Form, Divider, Button, Skeleton, Select, Popover } from '@douyinfe/semi-ui'
import { IconHelpCircle } from '@douyinfe/semi-icons';
import { isValidPortInput } from '@/utils/validators';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import useMachine from './useMachine';
import { useLocale } from '@/locales';

const MachineSelectorPortMulti: FC<{
    onChange: (val: AclOrigin_Resource[]) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>();

    const { machines, loadingMachine, queryDevices } = useMachine();

    // 当前选择的设备
    const [currentValue, setCurrentValue] = useState<Array<string>>([]);

    const [currentPort, setCurrentPort] = useState<Array<string>>([]);

    return <>
        <Skeleton loading={loadingMachine} placeholder={<>
            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginTop: '12px',
                marginBottom: '32px',
            }}></Skeleton.Image>

            <Divider className='mb10'></Divider>
            <div style={{ color: 'var(--semi-color-text-2)' }}>端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

                端口支持以下格式：
                <div>所有端口：*</div>
                <div>单个端口：22或80</div>
                <div>连续端口：1-65535或100-20000</div>
                <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
            </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>

            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginTop: '12px',
                marginBottom: '20px',
            }}></Skeleton.Image>

            <Divider className='mb10'></Divider>
            <Button block>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </>}>
            <Form getFormApi={SetFormApi} initValues={{ 
                port: currentPort,
                currentValue: currentValue }}>
                <Form.Select
                    style={{ width: '100%' }}
                    zIndex={3000}
                    filter
                    remote
                    noLabel
                    showClear
                    multiple
                    field='currentValue'
                    placeholder={formatMessage({ id: 'policies.device.placeholder' })}
                    onSearch={queryDevices}
                    position='top'
                    loading={loadingMachine}
                    onChange={(val: any) => {
                        setCurrentValue(val as string[])
                    }}
                    onClear={() => {
                        setCurrentValue([])
                    }}
                    emptyContent={<>{formatMessage({ id: 'policies.device.noDevices' })}</>}
                >

                    {machines?.map((machine, index) => {
                        return <Select.Option value={machine.ipv4} showTick={true} key={index + 1}>
                            {machine.autoGeneratedName ? machine.name : machine.givenName}
                        </Select.Option>
                    })}
                </Form.Select>
                <Divider className='mb10'></Divider>
                <div style={{ color: 'var(--semi-color-text-2)' }}>端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

                    端口支持以下格式：
                    <div>所有端口：*</div>
                    <div>单个端口：22或80</div>
                    <div>连续端口：1-65535或100-20000</div>
                    <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
                </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>

                <Form.TagInput
                    noLabel
                    trigger={'blur'}
                    validate={(val) => {
                        if (!val || val.length == 0) {
                            return formatMessage({ id: 'policies.port.required' })
                        }

                        if (!isValidPortInput(val.join(','))) {
                            return formatMessage({ id: 'policies.port.invalidFormat' });
                        }

                        return ''
                    }}
                    addOnBlur
                    field='port'
                    onChange={val => setCurrentPort(val)}
                ></Form.TagInput>

                <Divider className='mb10'></Divider>
                <Button disabled={
                    !currentValue
                } block onClick={async () => {
                    await formApi?.validate();

                    let list: AclOrigin_Resource[] = [];
                    for (let i = 0; i < currentValue.length; i++) {
                        list.push(new AclOrigin_Resource({
                            value: currentValue[i] + ':' + currentPort.join(','),
                            type: AclOrigin_ResourceType.DEVICE
                        }))
                    }
                    props.onChange(list)

                }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
            </Form>
        </Skeleton>

    </>
}

export default MachineSelectorPortMulti;