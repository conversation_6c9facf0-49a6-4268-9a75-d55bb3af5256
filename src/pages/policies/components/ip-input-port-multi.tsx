import { FC, useState } from 'react';
import { Input, Typography, Checkbox, Divider, Dropdown, Button, Popover, Form } from '@douyinfe/semi-ui'
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { isValidIPOrIpRangeOrCIDR, isValidPortInput } from '@/utils/validators';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconMore, IconHelpCircle } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
const { Paragraph } = Typography;

const IpInputPortMulti: FC<{
    onChange: (val: AclOrigin_Resource[]) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [currentValue, setCurrentValue] = useState<Array<string>>([]);

    const [currentPort, setCurrentPort] = useState<Array<string>>([]);

    const [formApi, SetFormApi] = useState<FormApi>()
    return <><Form getFormApi={SetFormApi} initValues={{
        currentValue: currentValue,
        port: currentPort
    }}>
        <Paragraph type='tertiary'>IP</Paragraph>
        <Form.TagInput noLabel field='currentValue'
            addOnBlur
            placeholder={formatMessage({ id: 'policies.ip.placeholder' })}
            extraText={formatMessage({ id: 'policies.ip.multiSupport' })}
            validate={(vals: Array<string>) => {
                // 验证IP不能为空
                if (!vals) {
                    return formatMessage({ id: 'policies.ip.required' })
                }

                for (let i = 0; i < vals.length; i++) {
                    let val = vals[i];

                    let res = isValidIPOrIpRangeOrCIDR(val)
                    if(res) {
                        return res
                    }
                }

                return ''
            }}
            onChange={val => setCurrentValue(val)} />
        <div style={{ color: 'var(--semi-color-text-2)' }} >端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

            端口支持以下格式：
            <div>所有端口：*</div>
            <div>单个端口：22或80</div>
            <div>连续端口：1-65535或100-20000</div>
            <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
        </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>
        <Form.TagInput
            noLabel
            
            validate={(val) => {
                if (!val || val.length == 0) {
                    return '端口不能为空'
                }

                if (!isValidPortInput(val.join(','))) {
                    return '端口格式不正确';
                }

                return ''
            }}
            addOnBlur
            field='port'
            onChange={val => setCurrentPort(val)}
        ></Form.TagInput>
        <Divider className='mb10'></Divider>
        <Button block onClick={async () => {
            await formApi?.validate();
            let port = currentPort.join(',');
            let arrList = formApi?.getValue('currentValue');

            let list: AclOrigin_Resource[] = [];
            let strList: Array<string> = [];
            for (let i = 0; i < arrList.length; i++) {
                if (strList.indexOf(arrList[i]) < 0) {
                    strList.push(arrList[i]);
                    list.push(new AclOrigin_Resource({
                        value: arrList[i] + ':' + port,
                        type: AclOrigin_ResourceType.IP
                    }))
                }
            }

            props.onChange(list)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </Form>
    </>
}

export default IpInputPortMulti;