import React, { FC, useEffect, useState, useContext } from 'react';
import useServicesGroup from '@/pages/services/useServicesGroup'
import { Form, Divider, Button } from '@douyinfe/semi-ui'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

const Index: FC<{
  onChange: (val: AclOrigin_Resource[]) => void
  existValues: AclOrigin_Resource[],
  optList?: Array<{
    value: string,
    label: string
  }>
}> = (props) => {
  const { formatMessage } = useLocale();
  const { serviceGroupTreeData } = useServicesGroup();
  const [currentValue, setCurrentValue] = useState<Array<string>>([]);


  const [formApi, SetFormApi] = useState<FormApi>()
  return <>
    <Form getFormApi={SetFormApi} initValues={{
      currentValue: currentValue
    }}>
      <Form.Select
        zIndex={9999}
        noLabel
        style={{ width: '100%' }}
        multiple
        trigger={'blur'}
        filter
        position='top'
        optionList={
          serviceGroupTreeData.map((item) => {
            let disabled = false;
            if (props.existValues.find(v => v.value === ('svg:' +item.fullName))) {
                disabled = true;
            }
            return {
              disabled,
              value: 'svg:' + item.fullName,
              label: item.label
            }
          })
        }
        validate={(val) => {
          if (!val || val.length == 0) {
            return formatMessage({ id: 'policies.serviceGroup.placeholder' })
          }
          return ''
        }}
        onChange={(val) => {

          if (val) {
            setCurrentValue(val as string[])
          }

        }}


        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
        field='currentValue' label={formatMessage({ id: 'policies.label.serviceGroup' })}></Form.Select>
      <Divider className='mb10'></Divider>
      <Button block onClick={async () => {
        await formApi?.validate();

        let list: AclOrigin_Resource[] = [];
        for (let i = 0; i < currentValue.length; i++) {
          list.push(new AclOrigin_Resource({
            type: AclOrigin_ResourceType.SERVICE_GROUP,
            value: currentValue[i]
          }));
        }
        props.onChange(list)

      }}>{formatMessage({ id: 'policies.common.apply' })}</Button>

    </Form>
  </>
}

export default Index;
