import { FC, useEffect, useState, useContext } from 'react';
import { Select, Divider, Button, Form } from '@douyinfe/semi-ui'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
const Index: FC<{
    onChange: (val: AclOrigin_Resource[]) => void
    existValues: AclOrigin_Resource[],
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    
    const [currentValue, setCurrentValue] = useState<Array<string>>([]);
    const [optList, setOptList] = useState<Array<{
        value: string,
        label: string
    }>>(props.optList ? props.optList : []);
    const [filteredOptList, setFilteredOptList] = useState<Array<{
        value: string,
        label: string
    }>>(props.optList ? props.optList : []);

    const [servicesLoaded, setServicesLoaded] = useState(false);

    useEffect(() => {
        flylayerClient.listServices({
            flynetId: flynet.id,
        }).then(res => {
            if (res) {
                const optList = res.services.map(service => {
                    let disabled = false;
                    if (props.existValues.find(v => v.value === ('svc:' +service.name))) {
                        disabled = true;
                    }
                    const description = service.description ? `(${service.description})` : ''
                    return {
                        disabled,
                        value: 'svc:' + service.name,
                        label: `${service.name}${description}`
                    }
                });
                setFilteredOptList(optList);
                setOptList(optList);
                setServicesLoaded(true);
            }
        })
    }, []);

    const [filterTxt, setFilterTxt] = useState('');
    const handleFilterTxtChange = (val: string) => {
        setFilterTxt(val);

        const filteredOptList = optList.filter(opt => opt.label.includes(val));
        setFilteredOptList(filteredOptList);


    }
    const [formApi, SetFormApi] = useState<FormApi>()
    return <>
        <Form getFormApi={SetFormApi} initValues={{
            currentValue: currentValue
        }}>
        <Form.Select
            noLabel
            filter
            zIndex={3000}
            style={{ width: '100%' }}
            multiple
            field='currentValue'
            trigger={'blur'}
            position='top'
            optionList={filteredOptList}
            validate={(val) => {
                if (!val || val.length == 0) {
                    return formatMessage({ id: 'policies.service.placeholder' })
                }
                return ''
            }}
            onChange={val => {
                setCurrentValue(val as string[])

            }}>

        </Form.Select>
        <Divider className='mb10'></Divider>
        <Button block onClick={async () => {
            await formApi?.validate();

            let list: AclOrigin_Resource[] = [];
            for(let i = 0; i < currentValue.length; i++) {
                list.push(new AclOrigin_Resource({
                    type: AclOrigin_ResourceType.SERVICE,
                    value: currentValue[i]
                }));
            }
            props.onChange(list)


        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </Form>
    </>
}

export default Index;
