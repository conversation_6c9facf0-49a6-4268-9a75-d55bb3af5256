import { FC, useState } from 'react';
import { Select, Divider, Button, Form } from '@douyinfe/semi-ui'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

const SingleSelector: FC<{
    value: AclOrigin_Resource,
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()

    const mapedList = props.optList?.map(item => {
        let disabled = false;
        if (item.value == props.value.value) {
            disabled = false;
        } else if (props.existValues.find(v => v.value === item.value)) {
            disabled = true;
        }
        return {
            disabled,
            value: item.value,
            label: item.label.replace('exp:', '').replace('group:', '').replace('tag:', '')
        }
    }) || [];
    const [currentValue, setCurrentValue] = useState(props.value.value);
    return <><Form getFormApi={SetFormApi} initValues={{ currentValue: currentValue }}>
        <Form.Select
            noLabel
            zIndex={3000}
            style={{ width: '100%' }}
            field='currentValue'
            filter
            optionList={mapedList}
            trigger={'blur'}
            position='top'
            validate={(val) => {
                if (!val) {
                    return formatMessage({ id: 'components.common.pleaseSelect' })
                }
                return ''
            }}
            onChange={val => {
                setCurrentValue(val as string)
            }}>
        </Form.Select>
        <Divider className='mb10'></Divider>
        <Button block onClick={async () => {
            await formApi?.validate();
            props.value.value = currentValue;


            props.onChange(props.value)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button></Form>
    </>
}

export default SingleSelector;
