import { FC, useState } from 'react';
import { Select, Divider, Button, Input, Typography, Popover, Form } from '@douyinfe/semi-ui'
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconMore, IconHelpCircle } from '@douyinfe/semi-icons';
import { isValidPortInput } from '@/utils/validators';
const { Paragraph } = Typography;
const Index: FC<{
    value: AclOrigin_Resource,
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();
    let dst = props.value.value;
    if (dst.startsWith('tag:')) {
        dst = dst.replace('tag:', '');
    } 
    // else if (dst.startsWith('group:')) {
    //     dst = dst.replace('group:', '');
    // } 

    const arr = dst.split(':');
    
    let value = '';
    let port = '';

    if(dst.startsWith('autogroup:')) {
        
        if (dst.startsWith('autogroup:self')) {
            value = 'autogroup:self'
            port = dst.replace('autogroup:self:', '');
        } else if (dst.startsWith('autogroup:members')) {
            value = 'autogroup:members'
            port = dst.replace('autogroup:members:', '');    
        } else if (dst.startsWith('autogroup:internet')) {
            value = 'autogroup:internet'
            port = dst.replace('autogroup:internet:', '');    
        }
        
    } else if(dst.startsWith('group:')) {
        value = 'group:' + arr[1];
        port = arr.length > 2 ? arr[2] : '';
    } else {
        value = arr.length > 0 ? arr[0] : '';
        port = arr.length > 1 ? arr[1] : '';
    }

    
    const [currentValue, setCurrentValue] = useState(value);

    let ports: Array<string> = [];
    if (port.length > 0) {
        ports = port.split(',');
    }
    const [currentPort, setCurrentPort] = useState(ports);


    const mapedList = props.optList?.map(item => {
        
        let disabled = false;
        if (props.value.value.indexOf(item.value+':') > -1) {
            disabled = false;
        } else if (props.existValues.find(v => v.value.indexOf(item.value+':') > -1 )) {
            disabled = true;
        }
        return {
            disabled,
            value: item.value,
            label: item.label.replace('group:', '').replace('tag:', '')
        }
    }) || [];

    const [formApi, SetFormApi] = useState<FormApi>()
    return <><Form getFormApi={SetFormApi} initValues={{
        port: currentPort,
        currentValue: currentValue
    }}><Paragraph type='tertiary'></Paragraph>

        <Form.Select
            zIndex={3000}
            style={{ width: '100%' }}
            noLabel
            field='currentValue'
            trigger={'blur'}
            filter
            optionList={mapedList}
            position='top'
            validate={(val) => {
                if (!val) {
                    return formatMessage({ id: 'components.common.pleaseSelect' })
                }
                return ''
            }}
            onChange={val => {
                
                setCurrentValue(val as string)
            }}>

        </Form.Select>
        <div style={{ color: 'var(--semi-color-text-2)' }}>端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

            端口支持以下格式：
            <div>所有端口：*</div>
            <div>单个端口：22或80</div>
            <div>连续端口：1-65535或100-20000</div>
            <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
        </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>

        <Form.TagInput
            noLabel
            trigger={'blur'}
            validate={(val) => {
                if (!val || val.length == 0) {
                    return '端口不能为空'
                }
                if (!isValidPortInput(val.join(','))) {
                    return '端口格式不正确';
                }
                return ''
            }}
            addOnBlur
            field='port'
            onChange={val => setCurrentPort(val)}
        ></Form.TagInput>
        <Divider className='mb10'></Divider>
        <Button block onClick={async () => {
            await formApi?.validate();

            let resource = new AclOrigin_Resource();
            resource.type = props.value.type;
            resource.value = currentValue + ':' + currentPort.join(',');
            // if(resource.type == AclOrigin_ResourceType.USER_GROUP) {
            //     resource.value = 'group:' + resource.value;
            // } else if(resource.type == AclOrigin_ResourceType.TAG) {
            //     resource.value = 'tag:' + resource.value;
            // }

            props.onChange(resource)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </Form>
    </>
}

export default Index;
