import { FC, useState } from 'react';
import { Input, Divider, Button, Form, Popover } from '@douyinfe/semi-ui'
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconHelpCircle } from '@douyinfe/semi-icons';
import { isValidPortInput } from '@/utils/validators';
import { useLocale } from '@/locales';
const AllSelectorPort: FC<{
    value: AclOrigin_Resource,
    onChange: (val: AclOrigin_Resource) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const arr = props.value.value.split(':');
    const [currentValue, setCurrentValue] = useState('*');
    let ports: Array<string> = [];
    if (arr.length > 1 && arr[1].length > 0) {
        ports = arr[1].split(',');
    }
    const [currentPort, setCurrentPort] = useState(ports);

    const [formApi, SetFormApi] = useState<FormApi>()

    return <>
        <Form getFormApi={SetFormApi} initValues={{
            port: currentPort
        }}>
            <Input className='mb10' value={currentValue} readOnly />
            <Divider className='mb10'></Divider>
            <div style={{ color: 'var(--semi-color-text-2)' }} className='mb10'>{formatMessage({ id: 'policies.port.label' })}&nbsp;<Popover zIndex={3000} content={<div className='p10'>

                {formatMessage({ id: 'policies.port.formatDescription' })}
                <div>{formatMessage({ id: 'policies.port.allPorts' })}</div>
                <div>{formatMessage({ id: 'policies.port.singlePort' })}</div>
                <div>{formatMessage({ id: 'policies.port.rangePort' })}</div>
                <div>{formatMessage({ id: 'policies.port.multiplePorts' })}</div>
            </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>
            <Form.TagInput
                noLabel
                trigger={'blur'}
                validate={(val) => {
                    if (!val || val.length == 0) {
                        return formatMessage({ id: 'policies.port.required' })
                    }

                    if (!isValidPortInput(val.join(','))) {
                        return formatMessage({ id: 'policies.port.invalidFormat' });
                    }

                    return ''
                }}
                addOnBlur
                field='port'
                onChange={val => setCurrentPort(val)}
            ></Form.TagInput>
            {/* <Form.Input noLabel trigger={'blur'} validate={(val) => {
                if (!val) {
                    return formatMessage({ id: 'policies.port.required' })
                }
                return ''
            }} field='port' onChange={val => setCurrentPort(val)}></Form.Input> */}
            <Divider className='mb10'></Divider>
            <Button block onClick={async () => {
                await formApi?.validate();
                props.value.value = currentValue + ':' + currentPort.join(',');
                props.value.type = AclOrigin_ResourceType.ALL;

                props.onChange(props.value)
            }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </Form>
    </>
}

export default AllSelectorPort;