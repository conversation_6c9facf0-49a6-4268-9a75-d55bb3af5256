import { FC, useState } from 'react';
import { Form, Divider, Button, Skeleton, Select } from '@douyinfe/semi-ui'

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import useMachine from './useMachine';
import { useLocale } from '@/locales';

const MachineSelector: FC<{
    value: AclOrigin_Resource,
    onChange: (val: AclOrigin_Resource) => void
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    console.log('props', props.value)
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>();

    const { machines, loadingMachine, queryDevices } = useMachine();

    // 当前选择的设备
    const [currentValue, setCurrentValue] = useState(props.value.value);


    return <>
        <Skeleton loading={loadingMachine} placeholder={<>
            <Skeleton.Image style={{
                width: '100%',
                height: '32px',
                marginTop: '12px',
                marginBottom: '32px',
            }}></Skeleton.Image>
            
            <Divider className='mb10'></Divider>
            <Button block>{formatMessage({ id: 'policies.common.apply' })}</Button>
        </>}>
            <Form getFormApi={SetFormApi} initValues={{ currentValue: currentValue }}>
                <Form.Select
                    style={{ width: '100%' }}
                    zIndex={3000}
                    filter
                    remote
                    noLabel
                    showClear
                    field='currentValue'
                    placeholder={formatMessage({ id: 'policies.device.placeholder' })}
                    onSearch={queryDevices}
                    position='top'
                    loading={loadingMachine}
                    onChange={(val: any) => {
                        setCurrentValue(val as string)
                    }}
                    onClear={() => {
                        setCurrentValue('')
                    }}
                    emptyContent={<>{formatMessage({ id: 'policies.device.noDevices' })}</>}
                >
                    {machines?.map((machine, index) => {
                        return <Select.Option value={machine.ipv4} showTick={true} key={index + 1}>
                            {machine.autoGeneratedName ? machine.name : machine.givenName}
                        </Select.Option>
                    })}
                </Form.Select>

                <Divider className='mb10'></Divider>
                <Button disabled={
                    !currentValue
                } block onClick={async () => {
                    await formApi?.validate();

                    props.value.value = currentValue;
                    props.value.type = AclOrigin_ResourceType.DEVICE;

                    props.onChange(props.value)
                }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
            </Form>
        </Skeleton>

    </>
}

export default MachineSelector;