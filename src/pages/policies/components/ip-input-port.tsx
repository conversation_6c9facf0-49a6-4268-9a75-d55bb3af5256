import { FC, useState } from 'react';
import { Input, Typography, Checkbox, Divider, Dropdown, Button, Popover, Form } from '@douyinfe/semi-ui'
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { isValidIP, isValidIPOrIpRangeOrCIDR, isValidPortInput } from '@/utils/validators';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconMore, IconHelpCircle } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
const { Paragraph } = Typography;

const IpInput: FC<{
    value: AclOrigin_Resource,
    onChange: (val: AclOrigin_Resource) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const arr = props.value.value.split(':');
    const [currentValue, setCurrentValue] = useState(arr.length > 0 ? arr[0] : '');

    let ports: Array<string> = [];
    if (arr.length > 1 && arr[1].length > 0) {
        ports = arr[1].split(',');
    }
    const [currentPort, setCurrentPort] = useState(ports);

    const [formApi, SetFormApi] = useState<FormApi>()
    return <><Form getFormApi={SetFormApi} initValues={{
        currentValue: currentValue,
        port: currentPort
    }}>
        <Paragraph type='tertiary'>IP</Paragraph>
        <Form.Input noLabel
            placeholder={formatMessage({ id: 'policies.ip.placeholder' })}
            extraText={formatMessage({ id: 'policies.ip.cidrSupport' })}
            field='currentValue'
            trigger={'blur'}
            validate={(value) => {

                // 验证，不能为空
                if (!value || value.trim() == '') {
                    return '不能为空';
                }
                // 验证，是否IP格式
                return isValidIPOrIpRangeOrCIDR(value)

            }}
            onChange={val => setCurrentValue(val)} />
        <div style={{ color: 'var(--semi-color-text-2)' }} >端口&nbsp;<Popover zIndex={3000} content={<div className='p10'>

            端口支持以下格式：
            <div>所有端口：*</div>
            <div>单个端口：22或80</div>
            <div>连续端口：1-65535或100-20000</div>
            <div>多个端口：22,80,443或100-200,300-400或22,3389-3399</div>
        </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></div>
        <Form.TagInput
            noLabel
            trigger={'blur'}
            validate={(val) => {
                if (!val || val.length == 0) {
                    return '端口不能为空'
                }
                if (!isValidPortInput(val.join(','))) {
                    return '端口格式不正确';
                }
                return ''
            }}
            addOnBlur
            field='port'
            onChange={val => setCurrentPort(val)}
        ></Form.TagInput>
        <Divider className='mb10'></Divider>
        <Button block onClick={async () => {
            await formApi?.validate();
            props.value.value = currentValue + ':' + currentPort.join(',');
            props.value.type = AclOrigin_ResourceType.IP;

            props.onChange(props.value)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </Form>
    </>
}

export default IpInput;