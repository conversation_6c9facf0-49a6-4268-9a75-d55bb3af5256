import React, { useState, useContext } from 'react'
import { Typo<PERSON>, Modal, Form, Row, Col, Notification, ArrayField, Button } from "@douyinfe/semi-ui";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Title, Paragraph } = Typography;
import styles from './index.module.scss'

interface Props {
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    close: () => void
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const [aclPolicy, setAclPolicy] = useState<ACLPolicy>(props.aclPolicy);

    const [formApi, setFormApi] = useState<FormApi>()
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);


    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {
        let hosts: { ip: string[], host: string }[] = [];
        Object.keys(policy.hosts).forEach(key => {
            hosts.push({ host: key, ip: policy.hosts[key].split(',') })
        })
        return { hosts };
    }

    // 初始值
    const initValues = calInitValues(props.aclPolicy);


    const handleChange = (values: { hosts: { ip: string[], host: string }[] }) => {

        if (!values || !values.hosts) {
            return;
        }
        const hosts: { [key: string]: string } = {};
        if (values.hosts) {
            values.hosts.forEach((item) => {
                hosts[item.host ? item.host : ''] = item.ip.join(',');
            })
        }
        const policy: any = {
            ...aclPolicy,
            hosts,
        }

        setAclPolicy(policy);
    };

    const handleOk = () => {

        // 保存
        setLoading(true)

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: aclPolicy
        }).then(() => {
            props.onChange(aclPolicy)
            props.close()
            Notification.success({ content: formatMessage({ id: 'policies.saveSuccess' }), position: "bottomRight" })
        }).catch((err: any) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })
    }
    return <>
        <Modal
            title={formatMessage({ id: 'policies.hosts.title' })}
            visible={true}
            onOk={handleOk}
            onCancel={props.close}
            width={800}
            okButtonProps={{ loading }}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'policies.hosts.description' })}</Paragraph>
            {initValues ? <Form getFormApi={setFormApi} onValueChange={handleChange} initValues={initValues} labelPosition='left' labelWidth='100px' allowEmpty>
                <ArrayField field='hosts' >
                    {({ add, arrayFields, addWithInitValue: _addWithInitValue }) => (
                        <React.Fragment>
                            <Button onClick={add} icon={<IconPlusCircle />} className='mb20'>{formatMessage({ id: 'policies.hosts.addAlias' })}</Button>

                            <Row className={styles.tableTitle} >
                                <Col span={12}>{formatMessage({ id: 'policies.hosts.alias' })}</Col>
                                <Col span={10}>{formatMessage({ id: 'policies.hosts.ipOrRange' })}</Col>
                                <Col span={2}></Col>
                            </Row>
                            {
                                arrayFields.map(({ field, key, remove }, _i) => (
                                    <Row className={styles.tableBody} key={key} >
                                        <Col xs={24} sm={12}>
                                            <Form.Input
                                                field={`${field}[host]`}
                                                noLabel
                                            />
                                        </Col>
                                        <Col xs={24} sm={10}>
                                            {/* <Form.Input
                                                field={`${field}[ip]`}
                                                noLabel
                                            /> */}
                                            <Form.TagInput
                                            field={`${field}[ip]`}
                                            noLabel
                                            addOnBlur
                                            ></Form.TagInput>
                                        </Col>
                                        <Col xs={24} sm={2}>
                                            <Button
                                                type='danger'
                                                theme='borderless'
                                                icon={<IconMinusCircle />}
                                                onClick={remove}
                                                style={{ margin: 12 }}
                                            />
                                        </Col>
                                    </Row>
                                ))
                            }
                        </React.Fragment>
                    )}
                </ArrayField>

            </Form> : ''}
        </Modal>
    </>
}

export default Index;