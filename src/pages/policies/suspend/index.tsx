import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification } from '@douyinfe/semi-ui';

import { flylayerClient } from '@/services/core';

import { ACLPolicy, AclOrigin } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Paragraph } = Typography;
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { useLocale } from '@/locales';

interface Props {
    close: () => void,
    success?: () => void,
    aclPolicy: ACLPolicy,
    acl: AclOrigin,
    aclIndex: number,
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const handleOK = () => {
        
        setLoading(true);

        let acl = {
            ...props.acl,
            disabled: !props.acl.disabled,
        }

        setLoading(true);
        flylayerClient.updateAclOrigin({
            flynetId: flynet.id,
            origin: acl,
        }).then(() => {
            props.success && props.success();
            const successKey = props.acl.disabled ? 'policies.suspend.enableSuccess' : 'policies.suspend.disableSuccess';
            Notification.success({ content: formatMessage({ id: successKey }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            const failedKey = props.acl.disabled ? 'policies.suspend.enableFailed' : 'policies.suspend.disableFailed';
            Notification.error({ content: formatMessage({ id: failedKey }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })
    }
    const titleKey = props.acl.disabled ? 'policies.suspend.enableTitle' : 'policies.suspend.disableTitle';
    const descriptionKey = props.acl.disabled ? 'policies.suspend.enableDescription' : 'policies.suspend.disableDescription';

    return <>
        <Modal
            width={500}
            title={`${formatMessage({ id: titleKey })} ${props.acl.name}`}
            visible={true}
            okButtonProps={{ loading, type:'danger' }}
            onOk={handleOK}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>
                {formatMessage({ id: descriptionKey })}
            </Paragraph>
        </Modal></>
}
export default Index;
