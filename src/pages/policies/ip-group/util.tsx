import { IpGroup } from '@/interface/ip-group';

// 计算单个节点资源组
const calNodeIpList = (origin: {
    ip: string,
    memo: string
}[], allRefIpGroups: string[], nodes: IpGroup[]) => {
    const allIpList: {
        ip: string,
        memo: string
    }[] = [];

    origin.forEach((ip) => {
        allIpList.push(ip);
    })
    allRefIpGroups.forEach((name) => {
        let refGroup = nodes.find((item) => {
            return item.name == name;
        })
        if (refGroup && refGroup.ipList) {
            refGroup.ipList.forEach((ip) => {
                let exist = allIpList.find((item) => {
                    return item.ip == ip.ip;
                })
                if (!exist) {
                    allIpList.push(ip);
                }
            })
        }
    })

    return allIpList;
}

// 递归计算所有关联资源组
const calAllRefIpGroups = (self: IpGroup, initData: IpGroup[]): string[] => {
    const allRefIpGroups: string[] = [];

    self.refIpGroups.forEach((name) => {
        let refGroup = initData.find((item) => {
            return item.name == name;
        })

        if (refGroup) {
            if (allRefIpGroups.indexOf(refGroup.name) == -1) {
                allRefIpGroups.push(refGroup.name);
            }
            let find = calAllRefIpGroups(refGroup, initData);
            if (find.length > 0) {
                find.forEach((name) => {
                    if (allRefIpGroups.indexOf(name) == -1) {
                        allRefIpGroups.push(name);
                    }
                })
            }
        }
    })
    return allRefIpGroups;
}

// 清理数据
const clearData = (initData: IpGroup[]) => {
    let resData: IpGroup[] = [];
    initData.forEach((item) => {
        resData.push({
            name: item.name,
            description: item.description,
            ipList: item.ipList,
            refIpGroups: item.refIpGroups,
            allRefIpGroups: [],
            allIpList: []
        })
    })
    return resData;
}

// 初始化数据
const initData = (initdata: IpGroup[]) => {
    let clearedData = clearData(initdata);
    let resData = clearedData.map((item, index) => {
        const allRefIpGroups = calAllRefIpGroups(item, clearedData);
        item.allRefIpGroups = allRefIpGroups;

        item.allIpList = calNodeIpList(item.ipList, allRefIpGroups, clearedData);

        return { ...item };
    })

    return resData;
}

const getRefIpGroup = (ip: string, group: IpGroup, ipGroups: IpGroup[]): string[] => {
    let findSelf = false;
    group.ipList.forEach((item) => {
        if (item.ip == ip) {
            findSelf = true;
            return;
        }
    })

    if (findSelf) {
        return [group.description];
    }

    for (let i = 0; i < group.refIpGroups.length; i++) {
        let name = group.refIpGroups[i];
        let refGroup = ipGroups.find((item) => {
            return item.name == name;
        })
        if (refGroup) {
            let find = getRefIpGroup(ip, refGroup, ipGroups);
            if (find.length > 0) {
                return [group.description, ...find];
            }
        }
    }

    return [];
}

export {
    initData,
    calNodeIpList,
    calAllRefIpGroups,
    clearData,
    getRefIpGroup
}
