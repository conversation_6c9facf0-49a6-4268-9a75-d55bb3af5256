import React, { useState, useContext, useEffect, useRef } from 'react'
import { IpGroup } from '@/interface/ip-group';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconLink, IconFilter, IconChevronRight, IconEdit, IconCopyAdd, IconHelpCircle, IconArrowDown, IconArrowUp, IconMore, IconPlus, IconMinus, IconSearch, IconAlignTop, IconAlignBottom } from '@douyinfe/semi-icons';
import { getUuid } from '@/utils/common';

import { Modal, Layout, Row, Col, Popover, Space, Popconfirm, Tag, Button, Typography, TabPane, Tabs, Form, Notification, Select, Input, Empty } from "@douyinfe/semi-ui";

import styles from './index.module.scss';
import { calAllRefIpGroups, calNodeIpList } from './util';
const { Title, Paragraph } = Typography;

interface Props {
    item: IpGroup;
    index: number;
    selectedNodeIndex: number;
    errorGroup: string[];
    doSelectnode: (node: IpGroup, index: number) => void;
    selectedNode?: IpGroup;
    setSelectedNode: (node: IpGroup) => void;
    setSelectedNodeIndex: (index: number) => void;
    setActiveKey: (key: string) => void;
    ipGroups: IpGroup[];
    setIpGroups: (ipGroups: IpGroup[]) => void;
    isEditName: boolean;
    setIsEditName: (isEditName: boolean) => void;
}
const Index: React.FC<Props> = (props) => {

    const { item, index, selectedNodeIndex, doSelectnode,
        ipGroups, setIpGroups, setActiveKey, 
        isEditName, setIsEditName,
        selectedNode, setSelectedNode, setSelectedNodeIndex } = props;


    const [errorGroup, setErrorGroup] = useState<string[]>([]);


    const nameEditRef = useRef<HTMLInputElement>(null)

    const [nameFormApi, setNameFormApi] = useState<FormApi<{ name: string, description: string }>>();

    useEffect(() => {
        setErrorGroup(props.errorGroup);
    }, [props.errorGroup]);

    const getRowClassName = (record: IpGroup, index: number) => {
        if (errorGroup.includes(record.name)) {
            return styles.nameRowError;
        }
        if (index == selectedNodeIndex) {
            if (isEditName) {
                return styles.nameRowEdit;
            }
            return styles.nameRowSelected;
        }

        return styles.nameRow;
    }


    // 计算资源组是否能被删除
    const calCanDelete = (currentNode: IpGroup, allGroups: IpGroup[]) => {
        let canDelete = true;
        allGroups.forEach((item) => {
            if (item.allRefIpGroups && item.allRefIpGroups.indexOf(currentNode.name) >= 0) {
                canDelete = false;
            }
        })
        return canDelete;
    }

    const finishEditDesciption = async () => {
        const values = nameFormApi?.getValues();

        if (!values?.description) {
            return;
        }

        if (values?.description) {
            // 验证名称是否和已有名称重复
            let exist = ipGroups.find((item, i) => {
                return i != selectedNodeIndex && item.description == values.description;
            })
            if (exist) {
                Notification.error({
                    content: '名称已存在',
                });
                return;
            }
        }
        if (values) {
            const oldName = ipGroups[selectedNodeIndex].name;

            // 修改名称和描述
            setIpGroups(ipGroups.map((item, i) => {
                if (i == selectedNodeIndex) {
                    // item.name = values.name;
                    item.description = values.description;
                    return { ...item, ...values };
                }
                return item;
            }))

        }
        setIsEditName(false);
        let inputs = document.querySelector('.firstIpGroupIp input');
        if (inputs && inputs instanceof HTMLInputElement) {
            inputs.focus();
        }
        setActiveKey('list')
    }
    useEffect(() => {
        if (isEditName) {
            nameEditRef.current?.focus();
        }
    }, [isEditName])

    return <div key={index} className={
        getRowClassName(item, index)
    } onClick={() => {
        if (isEditName) {
            return;
        }
        if (errorGroup.length > 0) {
            return;
        }
        doSelectnode(item, index);
    }}>
        {isEditName && index == selectedNodeIndex ?
            <Form initValues={{ name: item.name, description: item.description }} getFormApi={setNameFormApi} onValueChange={(values) => {
                setSelectedNode({ ...selectedNode, ...values });
            }}>
                <Row>
                    <Col span={24} className={styles.nameField}>
                        <Form.Input size='small' ref={nameEditRef} onBlur={finishEditDesciption} onEnterPress={finishEditDesciption} field='description' noLabel label='描述'></Form.Input>
                    </Col>
                </Row>

            </Form> :
            <Row>
                <Col span={20}>
                    <div style={{ lineHeight: '24px', display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                        <Tag style={{ width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'}>{index + 1}</Tag>&nbsp;{item.description}
                    </div>
                </Col>
                <Col span={4} className={styles.rightColumn}>
                    {index == selectedNodeIndex && <Space>
                        <Popover position='bottomRight' content={<div className='p10'><Space>
                            <Button size='small' disabled={index == 0} icon={<IconAlignTop
                                onClick={(e) => {
                                    if (index == 0) {
                                        return;
                                    }
                                    e.stopPropagation();
                                    // 把数据移到数据的最前面
                                    let newData = [...ipGroups];
                                    let temp = newData[index];
                                    newData.splice(index, 1);
                                    newData.unshift(temp);
                                    setIpGroups([...newData]);
                                    setSelectedNode(newData[0]);
                                    setSelectedNodeIndex(0);
                                }}
                            ></IconAlignTop>} />
                            <Button size='small' disabled={index == 0} icon={<IconArrowUp
                                onClick={(e) => {
                                    if (index == 0) {
                                        return;
                                    }
                                    e.stopPropagation();
                                    let newData = [...ipGroups];

                                    let temp = newData[index - 1];
                                    newData[index - 1] = newData[index];
                                    newData[index] = temp;
                                    setIpGroups([...newData]);
                                    setSelectedNode(newData[index - 1]);
                                    setSelectedNodeIndex(index - 1);
                                }}
                            ></IconArrowUp>}></Button>
                            <Button size='small' disabled={index == ipGroups.length - 1} icon={<IconArrowDown
                                onClick={(e) => {
                                    if (index == ipGroups.length - 1) {
                                        return;
                                    }
                                    e.stopPropagation();
                                    let newData = [...ipGroups];
                                    let temp = newData[index + 1];
                                    newData[index + 1] = newData[index];
                                    newData[index] = temp;
                                    setIpGroups([...newData]);
                                    setSelectedNode(newData[index + 1]);
                                    setSelectedNodeIndex(index + 1);
                                }}
                            ></IconArrowDown>}></Button>
                            <Button size='small' disabled={index == ipGroups.length - 1} icon={<IconAlignBottom
                                onClick={(e) => {
                                    if (index == ipGroups.length - 1) {
                                        return;
                                    }
                                    e.stopPropagation();
                                    let newData = [...ipGroups];
                                    let temp = newData[index];
                                    newData.splice(index, 1);
                                    newData.push(temp);
                                    setIpGroups([...newData]);
                                    setSelectedNode(newData[ipGroups.length - 1]);
                                    setSelectedNodeIndex(ipGroups.length - 1);
                                }}></IconAlignBottom>}></Button>
                            <Popconfirm
                                title="确定要删除吗"
                                content="此修改将不可逆"
                                onConfirm={() => {
                                    setIpGroups(ipGroups.filter((item, i) => {
                                        return i != selectedNodeIndex;
                                    }))
                                    errorGroup.splice(errorGroup.indexOf(item.name), 1);
                                    doSelectnode(ipGroups[0], 0);
                                    setActiveKey('list')
                                }}
                                disabled={isEditName
                                    || !calCanDelete(item, ipGroups)
                                }

                                onCancel={() => { }}
                            >
                                <Button theme='solid'
                                    disabled={isEditName
                                        || !calCanDelete(item, ipGroups)
                                    }
                                    type='danger'
                                    size='small'
                                    icon={<IconMinus></IconMinus>}></Button>
                            </Popconfirm>
                            <Button theme='solid'
                                disabled={isEditName}
                                size='small'
                                onClick={() => {
                                    setIsEditName(true);
                                }}
                                icon={<IconEdit></IconEdit>}></Button>
                            <Button size='small'
                                theme='solid'
                                onClick={() => {
                                    // 复制添加
                                    let newNode: IpGroup = {
                                        name: getUuid(),
                                        description: item.description + '复制',
                                        ipList: item.ipList.map(val => {
                                            return {
                                                ip: val.ip,
                                                memo: val.memo
                                            }
                                        }),
                                        refIpGroups: item.refIpGroups.map(val => val)
                                    }
                                    newNode.allRefIpGroups = calAllRefIpGroups(newNode, ipGroups);

                                    newNode.allIpList = calNodeIpList(newNode.ipList, newNode.allRefIpGroups || [], ipGroups);

                                    setIpGroups([newNode, ...ipGroups]);
                                    setSelectedNode(newNode);
                                    setTimeout(() => {

                                        setSelectedNodeIndex(0);
                                        setIsEditName(true)
                                        setActiveKey('list')
                                    }, 500);
                                }}
                                icon={<IconCopyAdd></IconCopyAdd>}></Button>
                        </Space></div>}>
                            <Button size='small' style={{ cursor: 'default' }} icon={<IconMore></IconMore>}></Button>
                        </Popover>

                    </Space>
                    }
                </Col>
            </Row>}
    </div>
}

export default Index;