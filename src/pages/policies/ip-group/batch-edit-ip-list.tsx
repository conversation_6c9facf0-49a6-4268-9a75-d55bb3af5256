import { FC, useState } from 'react'
import { Typography, Modal, Spin, Form, Notification, TextArea, Row, Col, Button, Card } from '@douyinfe/semi-ui';
import CodeEditor from '@/components/code-editor';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { isValidIPOrIpRangeOrCIDR } from '@/utils/validators';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss';

interface Props {
    close: () => void,
    ipList: {
        ip: string,
        memo: string
    }[],
    onchange: (ipList: {
        ip: string,
        memo: string
    }[]) => void
}

const Index: FC<Props> = (props) => {
    const [formApi, SetFormApi] = useState<FormApi<{ value: string }>>()


    const initCSV = (ipList: {
        ip: string,
        memo: string
    }[]) => {

        const lines: string[] = [];
        ipList.forEach((ip, index) => {
            const ipStr = ip.ip || '';
            const memoStr = ip.memo || '';
            lines.push(`${ipStr},${memoStr}`);
        });

        return lines.join('\n');
    }

    const initValue = initCSV(props.ipList);

    const [value, setValue] = useState<string>(initValue);
    const [error, setError] = useState<string>('');
    const parseIpListFromCsv = (csvStr: string): {
        ip: string,
        memo: string
    }[] => {
        if (!csvStr) {
            return [];
        }

        const lines = csvStr.split('\n');
        let result: {
            ip: string,
            memo: string
        }[] = [];
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            lines[i] = lines[i].trim();
            const [ip, memo] = line.split(',');
            if (ip) {

                result.push({
                    ip: ip,
                    memo: memo
                })
            }
        }
        return result;
    }

    // 点击确定按钮
    const handleOk = () => {

        const error = validateValue(value);
        if (error) {
            setError(error);
            return;
        }

        const ipList = parseIpListFromCsv(value);
        props.onchange(ipList);
        props.close();

    }


    const handleCancel = () => {
        props.close();
    };

    const validateValue = (value: string) => {
        if (!value || value.trim() == '') {
            return '内容不能为空';
        }
        const lines = value.split('\n');
        const mapIP = new Map<string, number>();
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line == '') {
                if (i != lines.length - 1) {
                    return `第${i + 1}行为空行`;
                } else {
                    return '';
                }
            }

            const cols = lines[i].split(',');

            const ip = cols[0];
            const memo = cols.length > 1 ? cols[1] : '';
            if (!ip || ip.trim() == '') {
                return '不能为空';
            }
            if (isValidIPOrIpRangeOrCIDR(ip)) {
                return `第${i + 1}行IP格式错误`;
            }

            const mapValue = mapIP.get(ip);
            if (mapValue !== undefined) {
                return `第${i + 1}行与第${mapValue + 1}行IP重复`;
            } else {
                mapIP.set(ip, i);
            }
        }
        return '';
    }

    return <>
        <Modal
            width={800}
            title={`批量编辑资源组`}
            visible={true}
            onOk={handleOk}
            onCancel={handleCancel}
            maskClosable={false}
        >

<div className={styles.addService}>
            <Paragraph type='secondary' className='mb20'>
                每行一个IP，格式为IP或IP段，描述信息，用逗号分隔。
                IP或IP段，如：**********或**********/24或**********-************或**********-100
            </Paragraph>
            <Row>
                <Col span={24}>
                    <CodeEditor height='280px'
                        language='systemverilog'
                        value={value}
                        onChange={(value) => {
                            setValue(value||'');
                            const error = validateValue(value||'');
                            setError(error);
                        }}
                    ></CodeEditor>

                </Col>
            </Row>
            {error && <Paragraph type='danger'>
                {error}
            </Paragraph>}
</div>

        </Modal>
    </>
}

export default Index;