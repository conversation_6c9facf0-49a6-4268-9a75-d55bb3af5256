import { useState, useEffect, useContext } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { getFlynet } from '@/services/flynet';
import { IpGroup } from '@/interface/ip-group';
import { initData } from './util';
const useIpGroups = () => {

    const flynetGeneral = useContext(FlynetGeneralContext);

    // 资源组数据
    const [ipGroups, setIpGroups] = useState<IpGroup[]>([]);

    // 资源组过滤器
    const [dataFilter, setDataFilter] = useState<string>('');

    // 选中的节点
    const [selectedNode, setSelectedNode] = useState<IpGroup | undefined>();
    // 选中的节点索引
    const [selectedNodeIndex, setSelectedNodeIndex] = useState<number>(-1);

    // 选中节点
    const doSelectnode = (node: IpGroup, index: number) => {
        setSelectedNode(node);
        setSelectedNodeIndex(index);
    };

    // 清除选中节点
    const doClearSelectnode = () => {
        setSelectedNode(undefined);
        setSelectedNodeIndex(-1);
    };

    // 处理资源组数据过滤器变化
    const handleDataFilterChange = (value: string) => {
        setDataFilter(value);
        if (value && value.trim() != '') {
            const filteredText = value.trim();

            let firstFilteredIndex = -1;
            let firstFilteredNode = undefined;

            ipGroups.forEach((item, index) => {
                if (item.description.indexOf(filteredText) >= 0) {
                    if (firstFilteredIndex == -1) {
                        firstFilteredIndex = index;
                        firstFilteredNode = item;
                    }
                    return true;
                }
                return false;
            });

            if (firstFilteredNode) {
                setSelectedNode(firstFilteredNode);
                setSelectedNodeIndex(firstFilteredIndex);
            } else {
                setSelectedNode(undefined);
                setSelectedNodeIndex(-1);
            }

        } else {
            if (ipGroups.length > 0) {
                setSelectedNode(ipGroups[0]);
                setSelectedNodeIndex(0);
            }
        }
    }



    // 查询资源组
    const queryIpGroups = async () => {
        let res = await getFlynet(flynetGeneral.id);

        if (res && res.flynet && res.flynet.ipGroup) {

            if (res.flynet.ipGroup) {
                let initDataRes: IpGroup[] = initData(JSON.parse(res.flynet.ipGroup));
                if (initDataRes && initDataRes.length > 0) {
                    setIpGroups(initDataRes);
                    setSelectedNode(initDataRes[0]);
                    setSelectedNodeIndex(0);
                } else {
                    setIpGroups([]);
                    doClearSelectnode();
                }
            }
        }
    }

    useEffect(() => {
        queryIpGroups();
    }, []);


    return {
        ipGroups, setIpGroups,
        dataFilter, handleDataFilterChange,
        selectedNode, setSelectedNode,
        selectedNodeIndex, setSelectedNodeIndex,
        doSelectnode, doClearSelectnode,
        
    }
}

export default useIpGroups;