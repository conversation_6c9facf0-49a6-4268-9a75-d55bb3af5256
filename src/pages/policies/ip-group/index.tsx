import React, { useState, useContext } from 'react'
import { IconLink, IconFilter, IconChevronRight, IconHelpCircle, IconPlus, IconSearch } from '@douyinfe/semi-icons';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Modal, Layout, Row, Col, Popover, Space, Tag, Button, Typography, TabPane, Tabs, Notification, Select, Input, Empty } from "@douyinfe/semi-ui";
import IpList from './ip-list';
import { IllustrationConstruction, IllustrationConstructionDark } from '@douyinfe/semi-illustrations';
import {
    clearData,
    calNodeIpList,
    getRefIpGroup
} from './util';
import { isIPInRange } from '@/utils/format';
const { Sider, Content } = Layout;
import Preview from './preview';
import styles from './index.module.scss';
import { getUuid } from '@/utils/common';
import { IpGroup } from '@/interface/ip-group';
import IpGroupRow from './ip-group-row';
import useIpGroups from './useIpGroups';

const { Title, Paragraph } = Typography;


interface Props {
    close: () => void,
    success: () => void,
}

const IpGroupRef: React.FC<{ refs: string[] }> = (props) => {
    const { refs } = props;
    if (refs.length < 2) {
        return <></>
    }
    return <>
        <Popover
            trigger='click'
            position='bottomRight'
            content={<div className='p10'><Space style={{ flexWrap: 'wrap' }}>
                {
                    refs.map((groupName, groupIndex, arr) => {
                        return <React.Fragment key={groupIndex}><Tag><span>{groupName}</span></Tag>{groupIndex < arr.length - 1 && <IconChevronRight />}</React.Fragment>
                    })}</Space>
            </div>}
        > <Button size='small' icon={<IconLink />}></Button> </Popover>
    </>
}

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    const {
        dataFilter,
        ipGroups, setIpGroups,
        selectedNode, setSelectedNode,
        selectedNodeIndex, setSelectedNodeIndex,
        doSelectnode,
        handleDataFilterChange
    } = useIpGroups();

    const flynetGeneral = useContext(FlynetGeneralContext);

    // 是否编辑名称
    const [isEditName, setIsEditName] = useState(false);

    // 错误组
    const [errorGroup, setErrorGroup] = useState<string[]>([]);
    // 预览IP
    const [previewVisible, setPreviewVisible] = useState(false);

    // 预览IP
    const [hosts, setHosts] = useState<{ ip: string[], host: string }[]>([]);
    // 激活的Tab
    const [activeKey, setActiveKey] = useState('list');



    // 计算预览IP
    const calPreviewHosts = () => {
        let hosts: { [key: string]: string } = {};
        ipGroups.forEach((item) => {
            if (item.allIpList) {
                hosts[item.description] = item.allIpList.map((ip) => {
                    return ip.ip;
                }).join(',');
            }
        })

        let previewHosts: { ip: string[], host: string }[] = [];
        Object.keys(hosts).forEach(key => {
            previewHosts.push({ host: key, ip: hosts[key].split(',') })
        })
        setHosts(previewHosts);
        setPreviewVisible(true);

    }

    // 保存中
    const [saveLoading, setSaveLoading] = useState(false);
    // 保存资源组
    const handleSave = (ipGroups: IpGroup[]) => {
        setSaveLoading(true);
        let hosts: { [key: string]: string } = {};
        ipGroups.forEach((item) => {
            if (item.allIpList) {
                hosts[item.name] = item.allIpList.map((ip) => {
                    return ip.ip;
                }).join(',');
            }
        })
        flylayerClient.saveIpGroup({
            flynetId: flynetGeneral.id,
            ipGroup: JSON.stringify(clearData(ipGroups)),
            hosts: hosts
        }).then(res => {
            Notification.success({
                content: formatMessage({ id: 'policies.common.save' }),
            });
            props.success();
        }).catch(_err => {
            Notification.error({
                content: formatMessage({ id: 'policies.saveFailed' }),
            });
        }).finally(() => {
            setSaveLoading(false);
        })
    }


    // 取得资源组选项
    const getOptions = (currentNode: IpGroup, allGroups: IpGroup[]) => {
        return allGroups.map((item, i) => {
            let opt = {
                value: item.name,
                label: `${item.description}`,
                disabled: false
            }
            if (item.name == currentNode.name) {
                opt.disabled = true;
            }
            if (item.allRefIpGroups && item.allRefIpGroups.indexOf(currentNode.name) >= 0) {
                opt.disabled = true;
            }
            return opt
        })
    }





    // 关联资源组变化
    const handleRefChange = (val: any) => {

        let value: string[] = val as string[]
        let allIpList: {
            ip: string,
            memo: string
        }[] = [];
        let itemFlushData = ipGroups.map((item, index) => {
            if (index == selectedNodeIndex) {
                item.refIpGroups = value.map(item => item);
                item.allRefIpGroups = value.map(item => item);
                value.forEach((name) => {
                    let refGroup = ipGroups.find((item) => {
                        return item.name == name;
                    })
                    if (refGroup && refGroup.allRefIpGroups) {
                        refGroup.allRefIpGroups.forEach((name) => {
                            if (item.allRefIpGroups?.indexOf(name) == -1) {
                                item.allRefIpGroups?.push(name);
                            }
                        })
                    }
                })

                item.allIpList = calNodeIpList(item.ipList, item.allRefIpGroups, ipGroups);
                allIpList = item.allIpList;
                return { ...item };
            } else {
                return item;
            }

        })

        let allFlushData = itemFlushData.map((item, index) => {
            const allRefIpGroups = item.refIpGroups?.map(item => item);

            // item.allRefIpGroups = item.refIpGroups?.map(item => item);
            item.refIpGroups.forEach((name) => {
                let refGroup = ipGroups.find((item) => {
                    return item.name == name;
                })
                if (refGroup && refGroup.allRefIpGroups) {
                    refGroup.allRefIpGroups.forEach((name) => {
                        if (allRefIpGroups?.indexOf(name) == -1) {
                            allRefIpGroups?.push(name);
                        }
                    })
                }
            })
            item.allRefIpGroups = allRefIpGroups;
            item.allIpList = calNodeIpList(item.ipList, allRefIpGroups || [], ipGroups);
            return { ...item };
        })

        setIpGroups(allFlushData)
        if (selectedNode) {
            setSelectedNode({ ...selectedNode, refIpGroups: value, allIpList: allIpList });
        }


    }



    // 预览过滤
    const [previewFilter, setPreviewFilter] = useState<string>('');
    const isPreviewFilter = (ip: string, memo: string) => {
        if (!previewFilter || previewFilter.trim() == '') {
            return true;
        }
        if (ip.indexOf(previewFilter) == -1 && memo.indexOf(previewFilter) == -1 && isIPInRange(previewFilter, ip) == false) {
            return false;
        }
        return true;
    }


    return <>
        <Modal
            title={<>{formatMessage({ id: 'policies.ipGroup.title' })}&nbsp;
                <Space>
                    <Button size='small' onClick={() => {
                        calPreviewHosts();
                    }}>{formatMessage({ id: 'policies.common.preview' })}</Button>
                </Space>
            </>}
            width={1080}
            onOk={() => handleSave(ipGroups)}
            onCancel={props.close}
            visible={true}
            okButtonProps={{ loading: saveLoading, disabled: errorGroup.length > 0 }}
            closeOnEsc={false}
            maskClosable={false}
            bodyStyle={{ padding: 20, margin: 0 }}
        >
            <Layout className="components-layout-demo">
                <Layout>
                    <Sider className={styles.sider}>
                        <Row className={styles.nameTitle}>
                            <Col span={22}>
                                <Space>
                                    <Title heading={5}>{formatMessage({ id: 'policies.ipGroup.title' })}</Title>
                                    <Input
                                        suffix={<IconFilter />}
                                        size='small'
                                        value={dataFilter}
                                        onChange={(val) => handleDataFilterChange(val)}
                                        style={{ width: 140 }}
                                        placeholder={formatMessage({ id: 'policies.common.searchByName' })}
                                        showClear></Input>
                                </Space>
                            </Col>
                            <Col span={2} className={styles.rightColumn}>
                                <Button size='small'
                                    disabled={isEditName || errorGroup.length > 0}
                                    onClick={() => {
                                        let newNode = {
                                            name: getUuid(),
                                            description: formatMessage({ id: 'policies.ipGroup.description' }) + (ipGroups.length + 1),
                                            ipList: [{
                                                ip: '',
                                                memo: ''
                                            }], refIpGroups: []
                                        }
                                        setErrorGroup([...errorGroup, newNode.name])
                                        setIpGroups([newNode, ...ipGroups]);
                                        setSelectedNode(newNode);
                                        setTimeout(() => {
                                            setSelectedNodeIndex(0);
                                            setIsEditName(true)
                                            setActiveKey('list')
                                        }, 500);

                                    }} icon={<IconPlus></IconPlus>}></Button>
                            </Col>
                        </Row>
                        <div className={styles.sideIpList}>
                            {ipGroups.length > 0 && ipGroups.map((item, index) => {
                                if (dataFilter && item.description.indexOf(dataFilter) == -1) {
                                    return null;
                                }
                                return <IpGroupRow
                                item={item}
                                index={index}
                                key={index}
                                isEditName={isEditName}
                                setIsEditName={setIsEditName}
                                selectedNodeIndex={selectedNodeIndex}
                                errorGroup={errorGroup}
                                doSelectnode={doSelectnode}
                                selectedNode={selectedNode}
                                setSelectedNode={setSelectedNode}
                                setSelectedNodeIndex={setSelectedNodeIndex}
                                setActiveKey={setActiveKey}
                                ipGroups={ipGroups}
                                setIpGroups={setIpGroups}
                                ></IpGroupRow>
                            })}
                        </div>
                    </Sider>
                    <Content className={styles.content}>
                        {selectedNode ?
                            <Tabs onChange={(key) => setActiveKey(key)} type="line" size='small' activeKey={activeKey}>
                                <TabPane tab={formatMessage({ id: 'policies.ipGroup.title' })} itemKey="list">

                                    {selectedNode && <Select style={{ marginTop: 10, width: '100%' }} className='mb10'
                                        showClear
                                        multiple
                                        placeholder={formatMessage({ id: 'policies.ipGroup.title' })}
                                        value={selectedNode?.refIpGroups}
                                        onChange={handleRefChange}
                                        optionList={getOptions(selectedNode, ipGroups)}></Select>}
                                    {selectedNode && <IpList
                                        onerror={() => {
                                            setIsEditName(false)
                                            if (!errorGroup.includes(selectedNode?.name)) {
                                                setErrorGroup([...errorGroup, selectedNode?.name])
                                            }
                                        }}
                                        onAdd={() => {
                                        }}
                                        isCurrent={true}
                                        data={
                                            {
                                                name: selectedNode.name,
                                                ipList: selectedNode.ipList
                                            }
                                        }
                                        onchange={(ipList) => {
                                            setIsEditName(false)
                                            errorGroup.splice(errorGroup.indexOf(selectedNode?.name), 1);
                                            setSelectedNode({ ...selectedNode, ipList: ipList, allIpList: calNodeIpList(ipList, selectedNode?.allRefIpGroups || [], ipGroups) });
                                            let itemFlushData = ipGroups.map((item, index) => {
                                                if (index == selectedNodeIndex) {
                                                    item.ipList = ipList;
                                                    item.allIpList = calNodeIpList(ipList, item.allRefIpGroups || [], ipGroups);
                                                    return { ...item };
                                                } else {
                                                    return item;
                                                }
                                            })

                                            let allFlushData = itemFlushData.map((item, index) => {
                                                item.allIpList = calNodeIpList(item.ipList, item.allRefIpGroups || [], ipGroups);
                                                return { ...item };
                                            })

                                            setIpGroups(allFlushData)
                                        }}
                                    ></IpList>}
                                </TabPane>

                                <TabPane tab={formatMessage({ id: 'policies.common.preview' })} itemKey="preview">

                                    <Row style={{ marginTop: 10 }}>
                                        <Col span={10}>
                                            <Input showClear
                                                placeholder={formatMessage({ id: 'policies.common.searchByIpOrMemo' })}
                                                value={previewFilter} className='mb10' suffix={<IconSearch />} onChange={(val) => {
                                                    setPreviewFilter(val);
                                                }}></Input>
                                        </Col>
                                        <Col span={14} className={styles.rightColumn}>
                                            <Popover position='left' content={<div className='p10'>
                                                <div>资源组和关联的资源组下属所有IP预览</div>
                                            </div>}><IconHelpCircle style={{ verticalAlign: 'text-top' }} /></Popover>
                                        </Col>
                                    </Row>
                                    <Row gutter={10}>
                                        {selectedNode && selectedNode.allIpList?.map((item, index) => {
                                            return <Col span={8} className={styles.previewIP} key={index}>
                                                <Row className={styles.tableBody} style={{ display: isPreviewFilter(item.ip || '', item.memo || '') ? '' : 'none' }}>
                                                    <Col span={6}>
                                                        <Tag style={{ width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'} >{index + 1}</Tag>
                                                    </Col>
                                                    <Col span={18}>
                                                        <Title heading={6}>{item.ip}&nbsp; <IpGroupRef refs={getRefIpGroup(item.ip, selectedNode, ipGroups)}></IpGroupRef></Title>
                                                        <Paragraph title={item.memo} ellipsis style={{ marginBottom: 3 }}> {item.memo}</Paragraph>
                                                    </Col>
                                                </Row></Col>
                                        })}
                                    </Row>
                                </TabPane>
                            </Tabs> :
                            <>
                                <Empty
                                    style={{ marginTop: 100 }}
                                    image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                                    darkModeImage={<IllustrationConstructionDark style={{ width: 150, height: 150 }} />}
                                    title={formatMessage({ id: 'policies.common.notSelected' }) + formatMessage({ id: 'policies.ipGroup.title' })}
                                    description={formatMessage({ id: 'policies.common.pleaseSelect' }) + formatMessage({ id: 'policies.ipGroup.title' })}
                                />
                            </>}
                        

                    </Content>
                </Layout>
            </Layout>

        </Modal>
        {previewVisible && <Preview
            close={() => {
                setPreviewVisible(false);
            }}
            hosts={hosts}
        ></Preview>}
    </>
}

export default Index