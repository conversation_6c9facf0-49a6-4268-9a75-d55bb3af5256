.sider {
    width: 300px;
    border-right: 1px solid var(--semi-color-border);
    margin-right: 16px;
}
.addService {
    
}
.content {
    min-height: 400px;
    width: 590px;
}


.tableBody {
    >div {
        padding-right: 8px;
    }
}


.rightColumn {
    display: flex!important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0!important;
}

.addCenter {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
}



.tableTitle {
    // background-color: var(--semi-color-fill-1);
    
    border-bottom: 1px solid var(--semi-color-border);
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;
    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}
.tableBody {
    >div {
        padding-right: 8px;
    }
}

.sideIpList {
    height: 500px;
    overflow-y: auto;
}

.contentIpList {
    height: 355px;
    overflow-y: auto;
}

.nameTitle {
    border-bottom: 1px solid var(--semi-color-border);
    padding: 10px;
}
.nameRow {
    padding: 10px;
    
}
.nameField {
    cursor: pointer;
    >div {
        margin: 0 0 10px 0 !important;
        padding: 0!important;
    }
}
.nameRowError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);
    padding: 9px 10px;
}
.nameRowSelected {
    background-color: var(--semi-color-fill-1);
    padding: 9px 10px;
    border-bottom: 1px solid var(--semi-color-border);
}
.nameRowEdit {
    padding: 8px 10px 0 10px;
    border-top: 1px solid var(--semi-color-border);
    border-bottom: 1px solid var(--semi-color-border);
}

.previewIP {
    height: 70px;
    >div {
        border: 1px solid var(--semi-color-border);
        border-radius: 4px;
        padding: 10px;
        height: 65px;
    }
    margin-bottom: 10px;
}