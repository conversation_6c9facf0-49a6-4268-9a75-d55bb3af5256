import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';

import { flylayerClient } from '@/services/core';
import { ACLPolicy, AclOrigin } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    aclPolicy: ACLPolicy,
    acl: AclOrigin,
    aclIndex: number,
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');


    const handleSubmit = () => {
        
        
        setLoading(true);

        flylayerClient.deleteAclOrigin({
            flynetId: flynet.id,
            id: props.acl.id,
        }).then(() => {
            props.success && props.success();
            Notification.success({ content: formatMessage({ id: 'policies.delete.deleteSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.delete.deleteFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })
        
        // flylayerClient.setACLPolicy({
        //     flynetId: flynet.id,
        //     policy: policy
        // }).then(() => {
        //     props.success && props.success(policy);
        //     Notification.success({ content: '删除策略成功', position: "bottomRight" })
        // }).catch((err) => {
        //     console.error(err);
        //     Notification.error({ content: '删除策略失败, 请稍后重试', position: "bottomRight" })
        // }).finally(() => {
        //     setLoading(false);
        // })


    }

    return <>
        <Modal
            width={600}
            title={`${formatMessage({ id: 'policies.delete.title' })} ${props.acl.name}`}
            visible={true}
            onOk={handleSubmit}

            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                disabled: props.acl.name != confirmVal,
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'> {formatMessage({ id: 'policies.delete.description' })} <b>{props.acl.name}</b>
            </Paragraph>

            <Paragraph className='mb20'> {formatMessage({ id: 'policies.delete.policyName' })} <b>{props.acl.name}</b>
            </Paragraph>
            <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
