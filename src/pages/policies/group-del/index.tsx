import { useState, useContext } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';
import { AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { flylayerClient } from '@/services/core';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: AclGroup
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={`${formatMessage({ id: 'policies.group.deleteGroup' })} ${props.record.name}`}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

            flylayerClient.deleteAclGroup({
                groupId: props.record.id,
                flynetId: flynet?.id
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'policies.group.deleteSuccess' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'policies.group.deleteFailed' }), position: "bottomRight" })
            }).finally(() => setLoading(false))

        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'> {formatMessage({ id: 'policies.group.deleteConfirmation' })}
        </Paragraph>
        <Paragraph className='mb20'> {formatMessage({ id: 'policies.group.deleteInputConfirm' })} <b>{props.record.name}</b> {formatMessage({ id: 'policies.group.deleteInputConfirmSuffix' })}
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;