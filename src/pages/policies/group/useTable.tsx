import { useEffect, useState, useContext } from 'react';

import { Typo<PERSON>, Dropdown, Button, Divider } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';

import { AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { flylayerClient } from '@/services/core';

import DateFormat from '@/components/date-format';
import { BASE_PATH } from '@/constants/router';
import { useNavigate } from 'react-router-dom';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { caseInsensitiveIncludes } from '@/utils/common';
import { useLocale } from '@/locales';
const { Title, Paragraph, Text } = Typography;
export type GroupFilter = {
    query?: string;
}

class AclGroupExt extends AclGroup {
    children: Array<AclGroupExt>
    constructor(children: Array<AclGroupExt>) {
        super();
        this.children = children;
    }
}

let mapAclGroup: Map<string, AclGroupExt> = new Map();
const useTable = (filterParam: GroupFilter) => {
    const { formatMessage } = useLocale();

    const buildTreeData = (treeData: Array<AclGroupExt>,
        serviceGroups: AclGroup[],
        mapAclGroup: Map<string, AclGroupExt>, isRoot: boolean, depth: number) => {
        if(depth > 10) {
            return;
        }
        const leftServiceGroups: AclGroup[] = [];
        serviceGroups.forEach((item) => {
            

            if (isRoot) {
                if (!item.parentId || item.parentId == BigInt(0)) {
                    const treeItem: AclGroupExt = new AclGroupExt([]);
                    treeItem.id = item.id;
                    treeItem.alias = item.alias;
                    treeItem.name = item.name;
                    treeItem.description = item.description;
                    treeItem.type = item.type;
                    treeItem.createdAt = item.createdAt;
                    treeItem.acls = item.acls;
                    treeItem.children = [];
                    treeData.push(treeItem)
                    mapAclGroup.set(item.id + '', treeItem);
                } else {
                    leftServiceGroups.push(item);
                }
            } else {
                if (mapAclGroup.has(item.parentId + '')) {
                    const parent = mapAclGroup.get(item.parentId + '');
                    // const description = item.description ? `(${item.description})` : ''
                    const treeItem: AclGroupExt = new AclGroupExt([]);
                    treeItem.id = item.id;
                    treeItem.alias = item.alias;
                    treeItem.name = item.name;
                    treeItem.description = item.description;
                    treeItem.type = item.type;
                    treeItem.createdAt = item.createdAt;
                    treeItem.acls = item.acls;
                    treeItem.children = [];
                    parent?.children.push(treeItem);
                    mapAclGroup.set(item.id + '', treeItem);
                } else {
                    leftServiceGroups.push(item);
                }
            }
        })
        if (leftServiceGroups.length > 0) {
            buildTreeData(treeData, leftServiceGroups, mapAclGroup, false, depth + 1);
        }
    }

    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 服务组列表
    const [groups, setGroups] = useState<AclGroup[]>([]);
    // 全部服务组列表
    const [allGroups, setAllGroups] = useState<AclGroup[]>([]);

    const [groupExts, setGroupExts] = useState<AclGroupExt[]>([]);
    const [allGroupExts, setAllGroupExts] = useState<AclGroupExt[]>([]);

    // 编辑弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 删除弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);
    // 新增下级策略组弹出框是否可见
    const [createSubVisible, setCreateSubVisible] = useState(false);

    // 当前菜单选中服务
    const [selectedGroup, setSelectedGroup] = useState<AclGroup>();

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    // 过滤参数
    const [filter, setFilter] = useState<GroupFilter>(filterParam);

    // 表格列
    const columns = [{
        title: formatMessage({ id: 'policies.group.name' }),
        dataIndex: 'name',
        render: (field: string, record: AclGroup, index: number) => {
            return <>
            <div style={{ display: 'inline-flex' }}>
                <div>
                    <Title heading={6}>
                            <a href={`${BASE_PATH}/policies?group=${record.name}`} onClick={(e) => { e.stopPropagation() }}>
                                {record.alias}
                            </a>
                    </Title>
                    <Paragraph size='small'>{record.name}</Paragraph>
                </div>
            </div>
            </>
        },
    },
    {
        title: formatMessage({ id: 'policies.group.remark' }),
        dataIndex: 'description',
        render: (field: string) => {
            return <Text style={{maxWidth: 460}} ellipsis>{field}</Text>
        },
    },  
    {
        width: 160,
        title: formatMessage({ id: 'policies.group.policyCount' }),
        dataIndex: 'acls',
        render: (field: string, record: AclGroup, index: number) => {
            let count = record.acls.length
            return <>{count}</>
        },
    }, {
        width: 200,
        title: formatMessage({ id: 'policies.group.createTime' }),
        dataIndex: 'createdAt',
        render: (field: string, record: AclGroup, index: number) => {
            return <>
                <DateFormat date={record.createdAt}></DateFormat>
            </>
        }
    },
    {
        width: 100,
        title: '',
        dataIndex: 'operate',
        render: (field: string, record: AclGroup) => {
            let delDisable = false;
            if ((record as any).children && (record as any).children.length > 0) {
                delDisable = true;
            }
            return <div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>
                        <Dropdown.Item onClick={() => {
                            navigate(`${BASE_PATH}/policies?group=${record.name}`)
                        }}>{formatMessage({ id: 'policies.group.viewPolicies' })}</Dropdown.Item>
                        <Dropdown.Item onClick={(e) => {
                            e.stopPropagation();
                            setSelectedGroup(record)
                            setCreateSubVisible(true)
                        }}>{formatMessage({ id: 'policies.group.addSubGroup' })}</Dropdown.Item>
                        <Divider />
                        <Dropdown.Item
                            onClick={(e) => {
                                e.stopPropagation();
                                setSelectedGroup(record)
                                setEditVisible(true)
                            }}
                        >{formatMessage({ id: 'policies.group.editGroup' })}</Dropdown.Item>
                        <Dropdown.Divider />
                        <Dropdown.Item type="danger"
                            disabled={delDisable}
                            onClick={(e) => {
                                e.stopPropagation();
                                setSelectedGroup(record)
                                setDelVisible(true)
                            }}
                        >{formatMessage({ id: 'policies.group.deleteGroup' })}</Dropdown.Item>
                    </Dropdown.Menu>
                }
            >
                <Button><IconMore className='align-v-center' /></Button>
            </Dropdown></div>;
        },
    }];

    // 加载数据
    const query = () => {
        setLoading(true)

        flylayerClient.listAclGroups({
            flynetId: flynet.id
        }).then((res) => {
            // setGroups(res.groups)
            // setAllGroups(res.groups)
            const treeData: Array<AclGroupExt> = [];
            mapAclGroup = new Map();
            buildTreeData(treeData, res.groups, mapAclGroup, true, 0);
            
            setAllGroupExts(treeData);
            setGroupExts(doFilter(treeData, filterParam));

        }).finally(() => {
            setLoading(false);
        })
    }

    const filterGroupExts = (query: string, src: Array<AclGroupExt>) => {
        if (!query) {
            return src;
        }
        let dst: Array<AclGroupExt> = [];

        src.forEach((item) => {
            const itemMatch = caseInsensitiveIncludes(item.name, query) || caseInsensitiveIncludes(item.description, query) || caseInsensitiveIncludes(item.alias, query);
            if(item.children && item.children.length > 0) {
                
                let childResult = filterGroupExts(query, item.children);
                
                if(childResult.length > 0) {
                    let newItem = new AclGroupExt(childResult);
                    newItem.id = item.id;
                    newItem.alias = item.alias;
                    newItem.name = item.name;
                    newItem.description = item.description;
                    newItem.type = item.type;
                    newItem.createdAt = item.createdAt;
                    newItem.acls = item.acls;
                    newItem.children = childResult;
                    dst.push(newItem);
                } else if (itemMatch) {
                    let newItem = new AclGroupExt([]);
                    newItem.id = item.id;
                    newItem.alias = item.alias;
                    newItem.name = item.name;
                    newItem.description = item.description;
                    newItem.type = item.type;
                    newItem.createdAt = item.createdAt;
                    newItem.acls = item.acls;
                    newItem.children = [];
                    dst.push(newItem);
                }
            } else if (itemMatch) {
                dst.push(item);
            }
        })

        return dst;
    }

    // 过滤数据
    const doFilter = (src: Array<AclGroupExt>, filter: GroupFilter) => {
        if (!src || src.length == 0) {
            return src;
        }

        
        if (!filter.query) {
            return src;
        }

        let dst: Array<AclGroupExt> = filterGroupExts(filter.query|| '', src);


        return dst;
    }

    useEffect(() => {
        query()
    }, [])

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])


    const handleFilterChange = (newFilter: GroupFilter) => {
        setFilter(newFilter)
        const res = doFilter(allGroupExts, newFilter)

        setGroupExts(res)
    }


    return {
        columns,
        loading,
        allGroups,
        groups,
        selectedGroup,
        setSelectedGroup,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        createSubVisible,
        setCreateSubVisible,
        reloadFlag,
        setReloadFlag,
        filter,
        setFilter,
        groupExts,
        handleFilterChange
    }

}

export default useTable;