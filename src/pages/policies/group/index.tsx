import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Divider } from '@douyinfe/semi-ui';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import { LocaleFormatter, useLocale } from '@/locales';
import GroupAdd from '../group-add';
import GroupEdit from '../group-edit';
import GroupDel from '../group-del';
import useTable from './useTable';
import { GroupFilter } from './useTable';
import SearchFilter, { FilterParam } from '@/components/search-filter';
import { AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import useAclGroup from '../useAclGroup';

const { Title } = Typography;

// 根据URL参数设置过滤参数
const getGroupFilter = (location: Location): GroupFilter => {
    const query: string = getQueryParam('query', location) as string;
    return {
        query: query || ''
    }
}

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const initFilter : GroupFilter = getGroupFilter(useLocation())
    const {
        columns,
        loading,
        groupExts,
        allGroups,
        groups,
        selectedGroup,
        setSelectedGroup,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        createSubVisible,
        setCreateSubVisible,
        reloadFlag,
        setReloadFlag,
        filter,
        setFilter,
        handleFilterChange
    } = useTable(initFilter);
    const navigate = useNavigate();
    // 过滤参数改变时跳转路由
    const doNavigate = (param: GroupFilter) => {
        const { query } = param;
        navigate(`${BASE_PATH}/policies/group/?query=${query}`);
    }
    const { treeData, getMapTreeData, getMapGroupData } = useAclGroup();

    const [createVisible, setCreateVisible] = useState(false);
    
    const [filterParams, setFilterParams] = useState<FilterParam[]>([{
        name: 'query',
        placeholder: formatMessage({ id: 'policies.filter.searchPlaceholder' }),
        label: formatMessage({ id: 'policies.filter.query' }),
        value: initFilter.query || '',
    }]);

    return <><div className='general-page'><Breadcrumb routes={
        [
            {
                path: `${BASE_PATH}/policies`,
                href: `${BASE_PATH}/policies`,
                name: formatMessage({ id: 'policies.title' })
            },
            {
                name: formatMessage({ id: 'policies.group.title' }),
            }
        ]
    }>
    </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}><LocaleFormatter id="policies.group.title" /></Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}><LocaleFormatter id="policies.group.addGroup" /></Button>
                </Space>
            </div></Col>
        </Row>
        <Divider className='mb20'></Divider>
        <SearchFilter onChange={(val: string, filterParam) => {

            doNavigate({ ...filter, [filterParam.name]: val });
            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);
            handleFilterChange({ ...filter, [filterParam.name]: val });

        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
        {/* <div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  服务总数 {services.length}</Tag>} </div> */}
        <Table
            rowKey={(record?: AclGroup) => record ? record.id + '' : ''}
            // expandRowByClick
            // expandAllRows
            defaultExpandAllRows
            expandAllRows={true}
            // expandAllRows={services.length < 10}
            empty={<TableEmpty loading={loading} />} 
            loading={loading} columns={columns} dataSource={groupExts} pagination={false} />
    </div>

        {createVisible && <GroupAdd
            close={() => { setCreateVisible(false); }}
            success={() => {
                setCreateVisible(false)
                setReloadFlag(true)
            }}
            groupTreeData={treeData ? treeData : []}
            mapTreeData={getMapTreeData()}
        ></GroupAdd>
        }
        {createSubVisible && selectedGroup && <GroupAdd
            close={() => { setCreateSubVisible(false); setSelectedGroup(undefined) }}
            success={() => {
                setCreateSubVisible(false)
                setSelectedGroup(undefined)
                setReloadFlag(true)
            }}
            parent={selectedGroup}
            groupTreeData={treeData ? treeData : []}
            mapTreeData={getMapTreeData()}
        ></GroupAdd>}

        {delVisible && selectedGroup && <GroupDel
            close={() => {
                setDelVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setDelVisible(false)
                setSelectedGroup(undefined)
                setReloadFlag(true)
            }}
            record={selectedGroup}
        ></GroupDel>}
        {editVisible && selectedGroup && <GroupEdit
            groupId={selectedGroup.id}
            close={() => {
                setEditVisible(false)
                setSelectedGroup(undefined)

            }}
            groupTreeData={treeData ? treeData : []}
            mapTreeData={getMapTreeData()}
            success={() => {
                setSelectedGroup(undefined)
                setEditVisible(false)
                setReloadFlag(true)
            }}
        ></GroupEdit>}
    </>
}

export default Index;
