import React, { useState, useRef, useContext, useEffect } from 'react'
import { Divider, Modal, Space, Tag, Input, Row, Col, } from "@douyinfe/semi-ui";
import { IconSearch } from '@douyinfe/semi-icons';
import styles from './index.module.scss'
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import { flylayerClient } from '@/services/core';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';

import UserPopover from '@/components/user-popover';

import Expandable from '@/components/expandable/index';
import { values } from 'lodash';

let mapUser: Map<string, User> = new Map();

const Index: React.FC<{
    close: () => void,
    groups: { name: string, values: string[] }[]
}> = (props) => {
    const globalConfig = useContext(GlobalConfigContext);
    
    const templateUserListTitle = globalConfig.template?.userListTitle || ''
    const flynet = useContext(FlynetGeneralContext);
    const [filter, setFilter] = useState<string>('');

    const isPreviewFilter = (name: string) => {
        if (!filter || filter.trim() == '') {
            return true;
        }


        if (name.indexOf(filter) >= 0) {
            return true;
        }

        let user = mapUser.get(name);
        if(user) {
            if(user.displayName.indexOf(filter) >= 0){
                return true;
            }
        }

        return false;
    }

    const isGroupFilter = (values: string[]) => {
        if (!filter || filter.trim() == '') {
            return true;
        }
        for (let i = 0; i < values.length; i++) {
            const name = values[i];
            if (isPreviewFilter(name)) {
                return true;
            }
        }
        return false;
    }

    const [userloaded, setUserLoaded] = useState<boolean>(false);

    useEffect(() =>{
        flylayerClient.listUsers({
            flynetId: flynet.id
        }).then((res) => {
            setUserLoaded(true);
            mapUser.clear();
            res.users.forEach((user) => {
                mapUser.set(user.loginName, user);
            })
        });
    }, [])

    return <><Modal
        title="预览"
        visible={true}
        onCancel={props.close}
        onOk={props.close}
        hasCancel={false}
        footer={null}
        width={1080}

        maskClosable={false}
    ><div style={{ minHeight: 400 }}>

            <Row className='mb10' style={{ marginTop: 10 }}>

                <Col span={10}>
                    <Input showClear
                        placeholder='根据用户搜索'
                        value={filter} className='mb10' suffix={<IconSearch />} onChange={(val) => {
                            setFilter(val);
                        }}></Input>
                </Col>
                <Col span={14} className={styles.rightColumn}>

                </Col>
            </Row>
            {userloaded && props.groups.map((item, i) => {
                if(isGroupFilter(item.values) == false){
                    return ''
                }
                return <div key={i}> <Row className={styles.normalRow}>
                    <Col span={6}>
                        <Tag>{i + 1}</Tag>&nbsp;{item.name}
                    </Col>
                    <Col span={18}>
                        {filter ? <Space style={{ flexWrap: 'wrap' }}>
                            {item.values.map((name, nameIndex) => {
                                if (isPreviewFilter(name || '')) {
                                    
                                    if(mapUser.has(name)){
                                        const user = mapUser.get(name);
                                        return <Tag color='grey' key={nameIndex}><UserPopover loginName={user? user.loginName: ''} displayName={user? `${user.displayName}(${user.loginName})`:''} templateUserListTitle={templateUserListTitle}></UserPopover><Copyable className={styles.nameCopy} content={name} /></Tag>
                                    }
                                    return <Tag color='grey' key={nameIndex}>{name}<Copyable className={styles.nameCopy} content={name} /></Tag>
                                }
                                return ''
                            })}
                        </Space> : <Expandable expand={item.values.length > 5} collapseHeight={60} ><Space style={{ flexWrap: 'wrap' }}>
                            {item.values.map((name, nameIndex) => {
                                if (isPreviewFilter(name || '')) {
                                    if(mapUser.has(name)){
                                        const user = mapUser.get(name);
                                        return <Tag color='grey' key={nameIndex}><UserPopover loginName={user? user.loginName: ''} displayName={user? `${user.displayName}(${user.loginName})`:''} templateUserListTitle={templateUserListTitle}></UserPopover><Copyable className={styles.nameCopy} content={name} /></Tag>
                                    }
                                    return <Tag color='grey' key={nameIndex}>{name}<Copyable className={styles.nameCopy} content={name} /></Tag>
                                }
                                return ''
                            })}
                        </Space>
                        </Expandable>}
                      
                    </Col>
                </Row>
                    {i < props.groups.length - 1 && <Divider className='mb10' />}
                </div>
            })}
        </div></Modal></>
}

export default Index;