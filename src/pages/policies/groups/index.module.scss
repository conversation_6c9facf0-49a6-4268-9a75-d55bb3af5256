
.tableTitle {
    
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 8px;
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;
    >div {
        padding-right: 8px;
        padding-bottom: 8px;
        line-height: 32px;
    }
}
.tableBody {
    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.tableBodyError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);
    
    padding-top: 5px;
    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.rightColumn {
    display: flex!important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0!important;
}

.normalRow {
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.hilightRow {
    // -webkit-mask-image: linear-gradient(black 0%, rgb(0, 0, 0) 60%, rgba(0, 0, 0, 0.2) 80%, transparent 100%);
    // background-image: linear-gradient(black 0%, rgb(0, 0, 0) 60%, rgba(0, 0, 0, 0.2) 80%, transparent 100%);
    background-color:  rgba(var(--semi-blue-5), 0.05);
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.nameCopy {
    span {
        vertical-align: bottom;
    }
}