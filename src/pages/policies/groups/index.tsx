import React, { useState, useContext } from 'react'
import { Typo<PERSON>, Modal, Row, Col, Notification, Input, Button, Popover, Space, Select, TagInput } from "@douyinfe/semi-ui";
import { useLocale } from '@/locales';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { IconPlus, IconMinusCircle, IconArrowDown, IconArrowUp, IconMore, IconAlignTop, IconAlignBottom } from '@douyinfe/semi-icons';
import { ListValue, Value } from '@bufbuild/protobuf';
import Preview from './preview';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Title, Paragraph } = Typography;
import styles from './index.module.scss'
import { flylayerClient } from '@/services/core';
import { set } from 'lodash';

interface Props {
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    close: () => void
}

// 无效的名称
const errorNameMessage: Map<number, string> = new Map();

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const PREFIX = 'group:';
    const flynet = useContext(FlynetGeneralContext);
    const [aclPolicy, setAclPolicy] = useState<ACLPolicy>(props.aclPolicy);

    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);


    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {
        let groups: { name: string, values: string[] }[] = [];
        Object.keys(policy.groups).forEach(key => {
            groups.push({
                name: key.replace(PREFIX, ''), values: policy.groups[key].values.map((item) => {
                    return item.kind.value + ''
                })
            })
        })
        return groups;
    }

    // 初始值
    const initValues = calInitValues(props.aclPolicy);
    const [groups, setGroups] = useState<{ name: string, values: string[] }[]>(initValues);

    const [previewVisible, setPreviewVisible] = useState(false);

    const getAclPolicy = () => {
        let newGroups: { [key: string]: ListValue } = {};

        groups.forEach(item => {
            if (item.name) {
                if (item.values) {
                    newGroups[PREFIX + item.name] = new ListValue({
                        values:
                            item.values.map((item) => {
                                let val = new Value({
                                    kind: { value: item, case: "stringValue" }
                                });
                                return val
                            })
                    });
                }
            }
        })

        const policy: any = {
            ...aclPolicy,
            groups: newGroups,
        }
        return policy;
    }

    const handleOk = () => {
        const aclPolicy = getAclPolicy();
        setAclPolicy(aclPolicy);

        // 保存
        setLoading(true)

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: aclPolicy
        }).then(() => {
            props.onChange(aclPolicy)
            props.close()
            Notification.success({ content: formatMessage({ id: 'policies.saveSuccess' }), position: "bottomRight" })
        }).catch((err: any) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })

    }


    // 无效的行
    const [invalidLines, setInvalidLines] = useState<number[]>([]);
    // 无效的名称
    const [errorNameIndexs, setErrorNameIndexs] = useState<number[]>([]);
    // 无效的值
    const [errorValueIndexs, setErrorValueIndexs] = useState<number[]>([]);

    return <>
        <Modal
            title={<>{formatMessage({ id: 'policies.groups.title' })}&nbsp;<Button size='small' onClick={() => {
                setPreviewVisible(true);
            }}>{formatMessage({ id: 'policies.groups.preview' })}</Button></>}
            visible={true}
            onOk={handleOk}
            onCancel={props.close}
            width={1080}
            okButtonProps={{ loading, disabled: invalidLines.length > 0 }}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'policies.groups.description' })}</Paragraph>
            <Row className={styles.tableTitle} >
                <Col span={6}>{formatMessage({ id: 'policies.groups.name' })}</Col>
                <Col span={16}>{formatMessage({ id: 'policies.groups.users' })}</Col>
                <Col span={2} className='btn-right-col'><Button onClick={() => {
                    setGroups([...groups, { name: '', values: [] }])
                    setInvalidLines([...invalidLines, groups.length])
                }} icon={<IconPlus />} /></Col>
            </Row>
            {groups.map((val, index) => {
                return <Row className={invalidLines.indexOf(index) >= 0 ? styles.tableBodyError : styles.tableBody} key={index} >
                    <Col xs={24} sm={6}>
                        <Input
                            value={val.name}
                            onChange={(input_val) => {
                                const newGroups = groups.map((val, i) => {
                                    if (i != index) {
                                        return val;
                                    } else return {
                                        name: input_val,
                                        values: val.values
                                    }
                                });
                                setGroups(newGroups)

                                // 验证
                                if (!input_val || input_val.trim() == '') {
                                    errorNameMessage.set(index, '名称不能为空');
                                    setErrorNameIndexs([...errorNameIndexs, index])
                                    setInvalidLines([...invalidLines, index])
                                } else if (input_val.indexOf(PREFIX) == 0) {
                                    errorNameMessage.set(index, '名称不能包含前缀' + PREFIX)
                                    setErrorNameIndexs([...errorNameIndexs, index])
                                    setInvalidLines([...invalidLines, index])
                                } else {
                                    let exist = false;
                                    // 验证名称是否重复
                                    groups.forEach((item, i) => {
                                        if (i != index && item.name == input_val) {
                                            exist = true;
                                        }
                                    })
                                    if (exist) {
                                        errorNameMessage.set(index, '名称已存在');
                                        setErrorNameIndexs([...errorNameIndexs, index])
                                        setInvalidLines([...invalidLines, index])
                                    } else {
                                        errorNameMessage.delete(index);
                                        setErrorNameIndexs(errorNameIndexs.filter((i) => i != index))
                                        if (val.values.length > 0) {
                                            setInvalidLines(invalidLines.filter((i) => i != index))
                                        }

                                    }
                                }

                            }}
                        >
                        </Input>
                        {errorNameIndexs.indexOf(index) > -1 && <Paragraph type='danger'>{errorNameMessage.get(index)}</Paragraph>}
                    </Col>
                    <Col xs={24} sm={3}>
                        <Select onChange={(select_val) => {
                            const newGroups = groups.map((val, i) => {
                                if (i != index) {
                                    return val;
                                } else {
                                    if (select_val == '*') {
                                        return {
                                            name: val.name,
                                            values: ['*']
                                        }
                                    } else {
                                        return {
                                            name: val.name,
                                            values: []
                                        }
                                    }
                                }
                            });
                            setGroups(newGroups)
                            if (select_val == '*') {
                                setErrorValueIndexs(errorValueIndexs.filter((i) => i != index))
                                if (errorNameIndexs.indexOf(index) < 0) {
                                    setInvalidLines(invalidLines.filter((i) => i != index))
                                }
                            } else {
                                setErrorValueIndexs([...errorValueIndexs, index])
                                setInvalidLines([...invalidLines, index])
                            }
                        }} value={val.values.length == 1 && val.values[0] == '*' ? '*' : ''}>
                            <Select.Option value={'*'}>全部用户</Select.Option>
                            <Select.Option value={''}>指定用户</Select.Option>
                        </Select>
                    </Col>
                    <Col xs={24} sm={13}>
                        {val.values.length == 1 && val.values[0] == '*' ? '' :
                            <>
                                <TagInput value={val.values}
                                    maxTagCount={2}
                                    allowDuplicates={false}
                                    addOnBlur={true}
                                    onChange={(vals) => {
                                        const newGroups = groups.map((val, i) => {
                                            if (i != index) {
                                                return val;
                                            } else return {
                                                name: val.name,
                                                values: vals
                                            }
                                        });
                                        setGroups(newGroups)
                                        if (!vals || vals.length == 0) {
                                            setErrorValueIndexs([...errorValueIndexs, index])
                                            setInvalidLines([...invalidLines, index])
                                        } else {
                                            setErrorValueIndexs(errorValueIndexs.filter((i) => i != index))
                                            if (errorNameIndexs.indexOf(index) < 0) {
                                                setInvalidLines(invalidLines.filter((i) => i != index))
                                            }
                                        }
                                    }}
                                ></TagInput>
                                {errorValueIndexs.indexOf(index) > -1 && <Paragraph type='danger'>用户不能为空</Paragraph>}
                            </>}

                    </Col>
                    <Col xs={24} sm={2} className='btn-right-col'>
                        <Popover
                            position='left'

                            style={{
                                padding: 5,

                            }}
                            content={<>
                                <Space >
                                    <Button disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconAlignTop />}
                                        onClick={() => {
                                            let newGroups = groups.map((val, i) => {
                                                return val;
                                            });
                                            let item = newGroups[index];
                                            newGroups[index] = newGroups[0];
                                            newGroups[0] = item;
                                            setGroups(newGroups)
                                        }}
                                    ></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = groups.map((val, i) => {
                                                if (i == index) {
                                                    return groups[index - 1];
                                                } else if (i == index - 1) {
                                                    return groups[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setGroups(newGroups)
                                        }} disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconArrowUp />}></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = groups.map((val, i) => {
                                                if (i == index) {
                                                    return groups[index + 1];
                                                } else if (i == index + 1) {
                                                    return groups[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setGroups(newGroups)
                                        }}
                                        disabled={invalidLines.indexOf(index) >= 0 || index == groups.length - 1} icon={<IconArrowDown />}></Button>
                                    <Button disabled={invalidLines.indexOf(index) >= 0 || index == groups.length - 1} icon={<IconAlignBottom />} onClick={() => {
                                        let newGroups = groups.map((val, i) => {
                                            return val;
                                        });
                                        let item = newGroups[index];
                                        newGroups[index] = newGroups[newGroups.length - 1];
                                        newGroups[newGroups.length - 1] = item;
                                        setGroups(newGroups)
                                    }}></Button>
                                    <Button
                                        type='danger'
                                        theme='borderless'
                                        icon={<IconMinusCircle />}
                                        onClick={() => {
                                            const newGroups = groups.filter((g, i) => i != index)
                                            setGroups(newGroups)

                                            // 删除无效行
                                            setInvalidLines(invalidLines.filter((i) => i != index))
                                            setErrorNameIndexs(errorNameIndexs.filter((i) => i != index))
                                            setErrorValueIndexs(errorValueIndexs.filter((i) => i != index))
                                            errorNameMessage.delete(index);
                                        }}

                                    />
                                </Space>
                            </>}><Button icon={<IconMore />}></Button></Popover>

                    </Col>

                </Row>
            })}
        </Modal>
        {previewVisible && <Preview
            close={() => {
                setPreviewVisible(false);
            }}
            groups={groups}
        ></Preview>}
    </>
}

export default Index;