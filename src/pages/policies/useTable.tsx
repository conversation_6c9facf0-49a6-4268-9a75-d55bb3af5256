import { useState, useEffect, useContext } from 'react'
import { Typography, Notification, Dropdown, Button, Divider, Space, Tag, Popover } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { Service, ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";

import { FilterParam } from '@/components/search-filter';
import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { getFlynet } from '@/services/flynet';

import { IpGroup } from '@/interface/ip-group';
import { ACLPolicy, AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Title, Paragraph } = Typography;
import { IconMore, IconHelpCircle } from '@douyinfe/semi-icons';
import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { Machine, MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';

import UserSelector from './components-filter/user-selector';
import SerrvicesSelector from './components-filter/services-selector';
import ServicesGroupSelector from './components-filter/services-group-selector';
import StatusSelector, { getStatusDisplayValue } from './components-filter/status-selector';

import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import Expandable from '@/components/expandable/index';
import styles from './index.module.scss';
import { uniqueAclOriginResource } from '@/utils/acl';
import { getSimpleServiceGroupName, getSimpleServiceName, VITE_LOCAL_PAGER_AND_FILTER, VITE_USE_POLICY_PATCH } from '@/utils/service';
import { GroupTreeData } from './useAclGroup';
import { caseInsensitiveIncludes } from '@/utils/common';


export type ACLFilter = {
    query: string;
    user: string;
    services: string;
    servicesGroup: string;
    status: string;
    groups: string
}

let mapServicesData: Map<string, Service> = new Map();
let mapServicesGroupData: Map<string, ServiceGroup> = new Map();


let mapGroupData: Map<string, AclGroup> = new Map();
let mapUser: Map<string, User> = new Map();

let mapMachine: Map<string, Machine> = new Map();
let mapMachineGroup: Map<string, MachineGroup> = new Map();

const useTable = (initFilter: ACLFilter) => {
    const flynet = useContext(FlynetGeneralContext);
    const { formatMessage } = useLocale();

    // 过滤参数
    const [filter, setFilter] = useState<ACLFilter>(initFilter);
    // 访问控制策略是否在加载中
    const [aclPolicyLoading, setACLPolicyLoading] = useState(true);
    // 访问控制策略
    const [aclPolicy, setACLPolicy] = useState<ACLPolicy>();
    // 访问策略
    const [acls, setACLs] = useState<AclOrigin[]>([]);
    // 所有的访问策略
    const [allACLs, setAllACLs] = useState<AclOrigin[]>([]);

    const [total, setTotal] = useState(0);

    // 存储的访问策略
    const [storedACLs, setStoredACLs] = useState<AclOrigin[]>([]);

    const [treeData, setTreeData] = useState<any[]>();
    const buildTreeData = (treeData: Array<GroupTreeData>,
        serviceGroups: AclGroup[],
        mapTreeData: Map<string, GroupTreeData>, isRoot: boolean, depth: number) => {
        const leftServiceGroups: AclGroup[] = [];
        serviceGroups.forEach((item) => {
            if (isRoot) {
                if (!item.parentId || item.parentId == BigInt(0)) {
                    const treeItem: GroupTreeData = {
                        label: `${item.alias}(${item.name})`,
                        fullName: item.fullName,
                        value: item.id + '',
                        key: item.id + '',
                        children: []
                    }
                    treeData.push(treeItem)
                    mapTreeData.set(item.id + '', treeItem);
                    mapGroupData.set(item.id + '', item);
                } else {
                    leftServiceGroups.push(item);
                }
            } else {
                if (mapTreeData.has(item.parentId + '')) {
                    const parent = mapTreeData.get(item.parentId + '');
                    const description = item.description ? `(${item.description})` : ''
                    const treeItem: GroupTreeData = {
                        parentId: item.parentId + '',
                        fullName: item.fullName,
                        label: `${item.alias}( ${item.name}))`,
                        value: item.id + '',
                        key: item.id + '',
                        children: []
                    }
                    parent?.children.push(treeItem);
                    mapTreeData.set(item.id + '', treeItem);
                    mapGroupData.set(item.id + '', item);
                } else {
                    leftServiceGroups.push(item);
                }
            }
        })

        if (depth > 10) {
            return;
        }
        if (leftServiceGroups.length > 0) {
            buildTreeData(treeData, leftServiceGroups, mapTreeData, false, depth + 1);
        }

    }
    const getMapGroupData = () => { return mapGroupData }

    // 选中的访问策略
    const [selectedACL, setSelectedACL] = useState<AclOrigin>();
    //  选中的访问策略索引
    const [selectedACLIndex, setSelectedACLIndex] = useState<number>(-1);

    // 规则编辑弹窗是否显示
    const [ruleEditVisible, setRuleEditVisible] = useState(false);
    // 规则删除弹窗是否显示
    const [ruleDeleteVisible, setRuleDeleteVisible] = useState(false);
    // 规则启用弹窗是否显示
    const [ruleEnableVisible, setRuleEnableVisible] = useState(false);
    // 规则禁用弹窗是否显示
    const [ruleDisableVisible, setRuleDisableVisible] = useState(false);
    // 用户组
    const [userGroups, setUserGroups] = useState<UserGroup[]>([]);

    // 设备组
    const [machineGroups, setMachineGroups] = useState<MachineGroup[]>([]);
    // 从策略组中删除弹出框是否可见
    const [removeFromGroupVisible, setRemoveFromGroupVisible] = useState(false);
    // 编辑策略组弹出框是否可见
    const [editGroupVisible, setEditGroupVisible] = useState(false);


    const getSrcDstText = (txt: string) => {
        return <span className={styles.srcDstText} title={txt}>{txt}</span>
    }


    const getSrcDisplay = (ar: AclOrigin_Resource, showLabel: boolean) => {
        let src = ar.value;
        let display = src;
        if (ar.type == AclOrigin_ResourceType.ALL) {
            display = formatMessage({ id: 'policies.resource.all' });
        }
        else if (ar.type == AclOrigin_ResourceType.IP) {
            display = formatMessage({ id: 'policies.resource.ip' }) + ":" + src;
        }
        else if (ar.type == AclOrigin_ResourceType.IP_GROUP) {
            display = (showLabel ? formatMessage({ id: 'policies.resource.ipGroup' }) + ":" : "") + src;
        }
        else if (ar.type == AclOrigin_ResourceType.USER) {
            if (VITE_USE_POLICY_PATCH) {

                const user = mapUser.get(src);
                if (user) {
                    display = (showLabel ? "" : "") + user.displayName + '(' + user.loginName + ')';
                } else {
                    display = ''
                }
            } else {
                display = (showLabel ? formatMessage({ id: 'policies.resource.user' }) + "：" : "") + src
            }

        }
        else if (ar.type == AclOrigin_ResourceType.USER_GROUP) {
            let groupName = src.replace('group:', '');

            let displayName = '';
            userGroups.forEach(userGroup => {
                if (userGroup.name == groupName) {
                    displayName = userGroup.alias ? `${userGroup.alias}(${userGroup.name})` : userGroup.name
                }
            })
            if (!displayName) {
                displayName = groupName;
            }
            display = showLabel ? formatMessage({ id: 'policies.resource.userGroup' }) + ":" + displayName : displayName;
        }
        else if (ar.type == AclOrigin_ResourceType.USER_AUTO_GROUP) {
            if (src == 'autogroup:self') {
                display = showLabel ? formatMessage({ id: 'policies.resource.autoGroup' }) + ":" + formatMessage({ id: 'policies.autoGroup.self' }) : formatMessage({ id: 'policies.autoGroup.self' });
            } else if (src == 'autogroup:members') {
                display = showLabel ? formatMessage({ id: 'policies.resource.autoGroup' }) + ":" + formatMessage({ id: 'policies.autoGroup.members' }) : formatMessage({ id: 'policies.autoGroup.members' });
            } else if (src == 'autogroup:internet') {
                display = showLabel ? formatMessage({ id: 'policies.resource.autoGroup' }) + ":" + formatMessage({ id: 'policies.autoGroup.internet' }) : formatMessage({ id: 'policies.autoGroup.internet' });
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE) {
            if (VITE_USE_POLICY_PATCH) {
                const machine = mapMachine.get(src)
                if (machine) {
                    display = (showLabel ? formatMessage({ id: 'policies.resource.device' }) + ":" : "") + machine.name;
                } else {
                    display = ''
                }
            } else {
                display = (showLabel ? formatMessage({ id: 'policies.resource.device' }) + ":" : "") + src;
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE_GROUP) {
            if (VITE_USE_POLICY_PATCH) {
                const machineGroup = mapMachineGroup.get(src);
                if (machineGroup) {
                    display = (showLabel ? formatMessage({ id: 'policies.resource.deviceGroup' }) + ":" : "") + `${machineGroup.alias}(${machineGroup.name})`
                } else {
                    display = ''
                }
            } else {
                display = (showLabel ? formatMessage({ id: 'policies.resource.deviceGroup' }) + ":" : "") + src;
            }

        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE) {
            const service = mapServicesData.get(src.replace('svc:', ''));
            if (service) {
                display = (showLabel ? formatMessage({ id: 'policies.resource.service' }) + ":" : "") + getSimpleServiceName(service);
            }
        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
            const serviceGroup = mapServicesGroupData.get(src.replace('svg:', ''));
            if (serviceGroup) {
                display = (showLabel ? formatMessage({ id: 'policies.resource.serviceGroup' }) + ":" : "") + getSimpleServiceGroupName(serviceGroup);
            }
        }
        else if (ar.type == AclOrigin_ResourceType.EXPRESSION) {
            if (src.startsWith('exp:')) {
                display = src.replace('exp:', showLabel ? formatMessage({ id: 'policies.resource.expression' }) + ":" : "");
            }
            else if (src.startsWith('l7:exp:')) {
                display = src.replace('l7:exp:', showLabel ? formatMessage({ id: 'policies.resource.l7Expression' }) + ":" : "");
            }
        }
        else if (ar.type == AclOrigin_ResourceType.TAG) {
            display = src.replace('tag:', showLabel ? formatMessage({ id: 'policies.resource.tag' }) + ":" : "");
        }



        return display;
    }


    const getDstDisplay = (ar: AclOrigin_Resource, showLabel: boolean) => {
        let dst = ar.value
        let display = '';

        if (ar.type == AclOrigin_ResourceType.ALL) {
            display = formatMessage({ id: 'policies.resource.all' }) + ":" + dst.replace('*:', '');
        }
        else if (ar.type == AclOrigin_ResourceType.IP) {
            if (dst.match(/\d+\.\d+\.\d+\.\d+/)) {
                display = (showLabel ? formatMessage({ id: 'policies.resource.ip' }) + ":" : "") + dst;
            }
            //  是否是IP：后面加文本
            else if (dst.match(/\d+\.\d+\.\d+\.\d+:/)) {
                display = formatMessage({ id: 'policies.resource.ip' }) + ":" + dst;
            }
        }
        else if (ar.type == AclOrigin_ResourceType.IP_GROUP) {
            let displayLable = '';
            let port = '';

            if (ipGroups) {
                if (dst.indexOf(':') > -1) {
                    let arr = dst.split(':');
                    if (arr.length == 2) {
                        let ig = ipGroups.find(ig => ig.name == arr[0])
                        if (ig) {
                            displayLable = ig.description
                        }
                        port = arr[1];
                    }
                } else {
                    let ig = ipGroups.find(ig => ig.name == dst)
                    if (ig) {
                        displayLable = ig.description
                    }
                }

            }
            if (displayLable) {
                display = (showLabel ? formatMessage({ id: 'policies.resource.ipGroup' }) + ":" : "") + displayLable + (port ? ':' + port : '');
            } else {
                display = '';
            }
        }
        else if (ar.type == AclOrigin_ResourceType.USER) {
            if (VITE_USE_POLICY_PATCH) {
                let arr = dst.split(':');
                if (arr.length >= 1) {
                    const user = mapUser.get(arr[0]);
                    if (user) {
                        display = (showLabel ? "" : "") + user.displayName + '(' + user.loginName + ')';
                    } else {
                        
                        display = ''
                    }
                }
                if (arr.length == 2 && display != '') {
                    display += ':' + arr[1];
                }
            } else {
                display = display = (showLabel ? `${formatMessage({ id: 'policies.resource.user' })}:` : "") + ar.value;
            }
        }
        else if (ar.type == AclOrigin_ResourceType.USER_GROUP) {
            let groupName = dst.split(':')[1];
            let displayName = '';
            userGroups.forEach(userGroup => {
                if (userGroup.name == groupName) {
                    displayName = userGroup.alias ? `${userGroup.alias}(${userGroup.name})` : userGroup.name
                }
            })
            if (!displayName) {
                displayName = groupName;
            }
            let groupArr = dst.split(':');
            if (groupArr.length == 3) {
                display = (showLabel ? `${formatMessage({ id: 'policies.resource.userGroup' })}:`  : "") + displayName + ':' + groupArr[2];
            } else {
                display = (showLabel ? `${formatMessage({ id: 'policies.resource.userGroup' })}:` : "") + displayName;
            }
        }
        else if (ar.type == AclOrigin_ResourceType.USER_AUTO_GROUP) {
            if (dst.startsWith('autogroup:self')) {
                display = dst.replace('autogroup:self:', showLabel ? `${formatMessage({ id: 'policies.resource.autoGroup' })}:${formatMessage({ id: 'policies.autoGroup.self' })}:` : "");
            } else if (dst.startsWith('autogroup:members')) {
                display = dst.replace('autogroup:members:', showLabel ? `${formatMessage({ id: 'policies.resource.autoGroup' })}:${formatMessage({ id: 'policies.autoGroup.members' })}:` : "");
            } else if (dst.startsWith('autogroup:internet')) {
                display = dst.replace('autogroup:internet:', showLabel ? `${formatMessage({ id: 'policies.resource.autoGroup' })}:${formatMessage({ id: 'policies.autoGroup.internet' })}:` : "");
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE) {
            
            if (VITE_USE_POLICY_PATCH) {

                let arr = dst.split(':');
                if (arr.length >= 1) {
                    const machine = mapMachine.get(arr[0])
                    if (machine) {
                        display = (showLabel ? `${formatMessage({ id: 'policies.resource.device' })}:` : "") + machine.name;
                        if (arr.length == 2) {
                            display += ':' + arr[1];
                        }
                    }
                }
            } else {

                let arr = dst.split(':');
                if (arr.length >= 1) {
                    display = (showLabel ? `${formatMessage({ id: 'policies.resource.device' })}:` : "") + arr[0];
                    if (arr.length == 2) {
                        display += ':' + arr[1];
                    }
                }
            }
        }
        else if (ar.type == AclOrigin_ResourceType.DEVICE_GROUP) {
            if (VITE_USE_POLICY_PATCH) {

                let arr = dst.split(':');
                if (arr.length >= 1) {
                    const machineGroup = mapMachineGroup.get(arr[0]);
                    if (machineGroup) {
                        display = (showLabel ? `${formatMessage({ id: 'policies.resource.deviceGroup' })}:` : "") + `${machineGroup.alias}(${machineGroup.name})`;
                        if (arr.length == 2) {
                            display += ':' + arr[1];
                        }
                    }
                }
            } else {
                let arr = dst.split(':');
                if (arr.length >= 1) {
                    display = (showLabel ? `${formatMessage({ id: 'policies.resource.deviceGroup' })}:` : "") + arr[0];
                    if (arr.length == 2) {
                        display += ':' + arr[1];
                    }
                }
            }
        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE) {
            let arr = dst.split(':');
            if (arr.length >= 2) {
                const service = mapServicesData.get(arr[1]);
                if (service) {
                    display = (showLabel ? `${formatMessage({ id: 'policies.resource.service' })}:` : "") + getSimpleServiceName(service);
                }
            }
            if (arr.length == 3) {
                display += ':' + arr[2];
            }
        }
        else if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
            let arr = dst.split(':');
            if (arr.length >= 2) {
                const serviceGroup = mapServicesGroupData.get(arr[1]);
                if (serviceGroup) {
                    display = (showLabel ? `${formatMessage({ id: 'policies.resource.serviceGroup' })}:` : "") + getSimpleServiceGroupName(serviceGroup);
                }
            }
            if (arr.length == 3) {
                display += ':' + arr[2];
            }
        }
        else if (ar.type == AclOrigin_ResourceType.EXPRESSION) {
            if (dst.startsWith('exp:')) {
                display = dst.replace('exp:', showLabel ? `${formatMessage({ id: 'policies.resource.expression' })}:` : "");
            } else if (dst.startsWith('l7:exp:')) {
                display = dst.replace('l7:exp:', showLabel ? `${formatMessage({ id: 'policies.resource.l7Expression' })}:` : "");
            }
        }
        else if (ar.type == AclOrigin_ResourceType.TAG) {
            display = dst.replace('tag:', showLabel ? `${formatMessage({ id: 'policies.resource.tag' })}:` : "");
        }


        return display;
    }


    const initFilterParams = (_mapTreeData: any, _getMapGroupDataFunc: any, _groups: AclGroup[]) => {
        let initParams: FilterParam[] = [{
            name: 'query',
            placeholder: formatMessage({ id: 'policies.filter.searchPlaceholder' }),
            label: formatMessage({ id: 'policies.filter.query' }),
            value: initFilter.query || '',
        },     
        {
            name: 'user',
            placeholder: formatMessage({ id: 'policies.resource.user' }),
            label: formatMessage({ id: 'policies.resource.user' }),
            value: initFilter.user || '',
            filterComponent: UserSelector,
            funGetDisplayValue: (val) => {

                return getSrcDisplay(new AclOrigin_Resource({
                    value: val,
                    type: AclOrigin_ResourceType.USER
                }), false)
            }
        },
        {
            name: 'services',
            placeholder: formatMessage({ id: 'policies.resource.service' }),
            label: formatMessage({ id: 'policies.resource.service' }),
            value: initFilter.services || '',
            filterComponent: SerrvicesSelector,
            funGetDisplayValue: (val) => {
                return getSrcDisplay(
                    new AclOrigin_Resource({
                        value: val,
                        type: AclOrigin_ResourceType.SERVICE
                    }), false)
            }
        }, {
            name: 'servicesGroup',
            placeholder: formatMessage({ id: 'policies.resource.serviceGroup' }),
            label: formatMessage({ id: 'policies.resource.serviceGroup' }),
            value: initFilter.servicesGroup || '',
            filterComponent: ServicesGroupSelector,
            funGetDisplayValue: (val) => {
                return getSrcDisplay(new AclOrigin_Resource({
                    value: val,
                    type: AclOrigin_ResourceType.SERVICE_GROUP
                }), false)
            }
        }, {
            name: 'status',
            placeholder: formatMessage({ id: 'policies.filter.status' }),
            label: formatMessage({ id: 'policies.filter.status' }),
            value: initFilter.status || '',
            filterComponent: StatusSelector,
            funGetDisplayValue: getStatusDisplayValue
        }]


        if (VITE_LOCAL_PAGER_AND_FILTER) {
            initParams = [{
                name: 'query',
                placeholder: formatMessage({ id: 'policies.filter.searchPlaceholder' }),
                label: formatMessage({ id: 'policies.filter.query' }),
                value: initFilter.query || '',
            },
           
            {
                name: 'user',
                placeholder: formatMessage({ id: 'policies.filter.user' }),
                label: formatMessage({ id: 'policies.filter.user' }),
                value: initFilter.user || '',
                filterComponent: UserSelector,
                funGetDisplayValue: (val) => {

                    return getSrcDisplay(new AclOrigin_Resource({
                        value: val,
                        type: AclOrigin_ResourceType.USER
                    }), false)
                }
            },
            {
                name: 'status',
                placeholder: formatMessage({ id: 'policies.filter.status' }),
                label: formatMessage({ id: 'policies.filter.status' }),
                value: initFilter.status || '',
                filterComponent: StatusSelector,
                funGetDisplayValue: getStatusDisplayValue
            }]
        }

        setFilterParams(initParams);
    }



    const [filterParams, setFilterParams] = useState<FilterParam[]>([]);


    const columns = [
        {
            title: formatMessage({ id: 'policies.table.policyName' }),
            dataIndex: 'name',
            key: 'name',
            width: 160,
            render: (field: string, acl: AclOrigin, _index: number) => {
                return <>
                    <Popover position='bottomLeft' content={<div className='p10 mw300'><Paragraph>{acl.description}</Paragraph></div>}>
                        <Title heading={6}>
                            {acl.name}
                        </Title>
                    </Popover>
                </>;
            },
        },
        {
            title: <>{formatMessage({ id: 'policies.table.priority' })} <Popover content={<div className='p10'>{formatMessage({ id: 'policies.table.priorityTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover> </>,
            dataIndex: '',
            key: '',
            width: 100,
            render: (field: string, acl: AclOrigin, _index: number) => {
                return <>
                    {acl.priority <= 0 ? '1' : acl.priority}
                </>;
            }
        },
        {
            title: formatMessage({ id: 'policies.table.policyGroup' }),
            dataIndex: 'aclGroups',
            key: 'aclGroups',
            width: 110,
            render: (field: string, acl: AclOrigin, _index: number) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{acl.aclGroups.map((g, i) => <Tag key={i} size='large' style={{ maxWidth: 100 }}>{g.alias}</Tag>)}</Space></div>
            }

        },
        {
            title: formatMessage({ id: 'policies.table.source' }),
            dataIndex: 'src',
            key: 'src',
            width: 300,
            render: (field: string, acl: AclOrigin, _index: number) => {
                return <Expandable expand={acl.src.length > 5} collapseHeight={60} ><Space style={{ flexWrap: 'wrap' }}>
                    {uniqueAclOriginResource(acl.src).map((val, index) => {
                        const srcDisplay = getSrcDisplay(val, true);
                        if (!srcDisplay) {
                            return null
                        }
                        return <Tag key={index} size='small' className={styles.tagSrc}>{getSrcDstText(getSrcDisplay(val, true))}<Copyable content={val.value} /></Tag>
                    })}
                </Space></Expandable>
            }
        },
        {
            title: <>{formatMessage({ id: 'policies.table.action' })} <Popover content={<div className='p10'>{formatMessage({ id: 'policies.table.actionTooltip' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover> </>,
            dataIndex: 'action',
            key: 'action',
            width: 100,
            render: (field: string, acl: AclOrigin) => {
                return <>{acl.action == 'accept' ? <Tag color='green'>{formatMessage({ id: 'policies.action.allow' })}</Tag> : ''}
                    {acl.action == 'reject' ? <Tag color='red'>{formatMessage({ id: 'policies.action.deny' })}</Tag> : ''}
                </>
            }
        },
        {
            title: formatMessage({ id: 'policies.table.target' }),
            dataIndex: 'dst',
            key: 'dst',
            width: 300,
            render: (field: string, acl: AclOrigin, _index: number) => {

                return <Expandable expand={acl.dst.length > 5} collapseHeight={60}><Space style={{ flexWrap: 'wrap' }}>

                    {uniqueAclOriginResource(acl.dst).map((val, index) => {
                        const dstDisplay = getDstDisplay(val, true);
                        if (!dstDisplay) {
                            return null
                        }
                        return <Tag key={index} size='small' className={styles.tagDst}>{getSrcDstText(getDstDisplay(val, true))}<Copyable content={val.value} /></Tag>
                    }
                    )}
                </Space></Expandable>
            }
        },
        {
            title: formatMessage({ id: 'policies.table.status' }),
            dataIndex: 'disabled',
            key: 'disabled',
            width: 80,
            render: (fieldd: string, acl: AclOrigin, _index: number) => {
                return <>
                    {acl.disabled ? <Tag color='red'>{formatMessage({ id: 'policies.action.disabled' })}</Tag> : <Tag color='green'>{formatMessage({ id: 'policies.action.enabled' })}</Tag>}
                </>;
            }
        },

        {
            title: '',
            dataIndex: 'option',
            key: 'option',

            render: (fieldd: string, acl: AclOrigin, index: number) => {
                return <><div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            {acl.disabled ? <Dropdown.Item
                                disabled={acl.sys}
                                onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleEnableVisible(true);
                                    setSelectedACLIndex(index);
                                }}
                            >{formatMessage({ id: 'policies.menu.enablePolicy' })}</Dropdown.Item> :
                                <Dropdown.Item disabled={acl.sys} onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleDisableVisible(true);
                                    setSelectedACLIndex(index);
                                }}>{formatMessage({ id: 'policies.menu.disablePolicy' })}</Dropdown.Item>
                            }

                            <Divider />
                            <Dropdown.Item
                                disabled={acl.sys}
                                onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleEditVisible(true);
                                    setSelectedACLIndex(index);
                                }}
                            >{formatMessage({ id: 'policies.menu.editPolicy' })}</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                disabled={acl.sys}
                                onClick={() => {
                                    setSelectedACL(acl);
                                    setRuleDeleteVisible(true);
                                    setSelectedACLIndex(index);
                                }}
                                type='danger'>{formatMessage({ id: 'policies.menu.deletePolicy' })}</Dropdown.Item>

                        </Dropdown.Menu>}><Button><IconMore className='align-v-center' /></Button>
                </Dropdown>
                </div>
                </>;
            }
        },
    ];


    // 获取访问控制策略
    const getACLPolicy = async () => {
        queryIpGroup();
        flylayerClient.listUserGroups({
            flynetId: flynet.id
        }).then((res) => {
            setUserGroups(res.groups)
        })

        flylayerClient.listMachineGroups({
            flynetId: flynet.id
        }).then((res) => {
            setMachineGroups(res.groups)
            if (res.groups && res.groups.length > 0) {
                res.groups.forEach(g => {
                    mapMachineGroup.set(g.name, g);
                })
            }
        })

        const resAclGroups = await flylayerClient.listAclGroups({
            flynetId: flynet.id
        })

        setGroups(resAclGroups.groups)
        let initCurGroup: AclGroup | undefined;
        if (resAclGroups.groups) {
            resAclGroups.groups.forEach(g => {
                if (g.name == filter.groups) {
                    initCurGroup = g;
                }
            })
        }
        setCurGroup(initCurGroup)

        const treeData: Array<GroupTreeData> = [];
        let mapTreeData = new Map();
        buildTreeData(treeData, resAclGroups.groups, mapTreeData, true, 0);
        setTreeData(treeData);
        initFilterParams(treeData, getMapGroupData, resAclGroups.groups);


        setACLPolicyLoading(true);

        const queryServices = flylayerClient.listServices({
            flynetId: flynet.id
        });
        const queryServicesGroups = flylayerClient.listServiceGroups({
            flynetId: flynet.id
        });


        if (VITE_USE_POLICY_PATCH) {

            const queryUsers = flylayerClient.listUsers({
                flynetId: flynet.id
            });
            const queryMachines = flylayerClient.listMachines({
                flynetId: flynet.id
            });

            const res = await Promise.all([queryServices, queryServicesGroups, queryUsers, queryMachines]);


            if (res) {
                mapServicesData = new Map();
                res[0].services.forEach(service => {
                    mapServicesData.set(service.name, service)
                })

                mapServicesGroupData = new Map();
                res[1].serviceGroups.forEach(serviceGroup => {
                    mapServicesGroupData.set(serviceGroup.fullName, serviceGroup)
                })

                mapUser = new Map();
                res[2].users.forEach(user => {
                    mapUser.set(user.loginName, user)
                })

                mapMachine = new Map();
                res[3].machines.forEach(machine => {
                    mapMachine.set(machine.ipv4, machine);
                })
            }
        } else {
            const res = await Promise.all([queryServices, queryServicesGroups]);


            if (res) {
                mapServicesData = new Map();
                res[0].services.forEach(service => {
                    mapServicesData.set(service.name, service)
                })

                mapServicesGroupData = new Map();
                res[1].serviceGroups.forEach(serviceGroup => {
                    mapServicesGroupData.set(serviceGroup.fullName, serviceGroup)
                })

                mapUser = new Map();

            }
        }


        flylayerClient.getACLPolicy({
            flynetId: flynet.id
        }).then(res => {
            if (res.policy) {
                setACLPolicy(res.policy);

                flylayerClient.listAclOrigin({
                    flynetId: flynet.id
                }).then(res => {
                    if (res.origins) {
                        let list = res.origins;

                        list.sort((a, b) => {
                            // 先按照优先级排序，优先级高的排在前面，最后按照拒绝策略排在前面
                            if (a.priority > b.priority) {
                                return 1;
                            } else if (a.priority < b.priority) {
                                return -1;
                            } else {
                                if (a.action == 'reject' && b.action == 'accept') {
                                    return -1;
                                } else if (a.action == 'accept' && b.action == 'reject') {
                                    return 1;
                                } else {
                                    return 0;
                                }
                            }
                        })


                        setAllACLs(list);
                        setStoredACLs(list);
                        const filteredList = doFilter(list, filter, initCurGroup);
                        setACLs(filteredList);
                        setTotal(filteredList.length)
                    }
                }).catch(err => {
                    console.error(err);
                }).finally(() => {
                    setACLPolicyLoading(false);
                })

            }
        }).catch(err => {
            console.error(err);
            setACLPolicyLoading(false);
            Notification.error({ content: formatMessage({ id: 'policies.getACLPolicyFailed' }), position: "bottomRight" })
        })
    };

    // 更新访问控制策略是否正在加载
    const [aclPolicySaveLoading, setACLPolicySaveLoading] = useState(false);

    // 保存访问控制策略
    const saveACLPolicy = (policy: ACLPolicy) => {

        setACLPolicySaveLoading(true);

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: policy
        }).then(() => {
            setACLPolicy(policy);
            Notification.success({ content: formatMessage({ id: 'policies.saveSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setACLPolicySaveLoading(false);
        })
    }
    const [groups, setGroups] = useState<AclGroup[]>([]);

    // 当前选中服务组名称
    const [curGroup, setCurGroup] = useState<AclGroup>();


    // 过滤访问控制策略
    const doFilter = (src: Array<AclOrigin>, filter: ACLFilter, group?: AclGroup) => {
        if (!src || src.length == 0) {
            setTotal(0)
            return src;
        }



        if (filter.query == ''
            && filter.services == ''
            && filter.servicesGroup == ''
            && filter.services == ''
            && filter.user == ''
            && filter.status == ''
            && filter.groups == '' && !group) {
            setTotal(src.length)
            return src;
        }



        const filterdList = src.filter((acl) => {
            if (filter.query != '') {
                let findName = false;
                let findSrc = false;
                let findDst = false;
                if (caseInsensitiveIncludes(acl.name, filter.query) || caseInsensitiveIncludes(acl.description, filter.query)) {
                    findName = true;
                }

                let findSrcObj = acl.src.find((ar) => {
                    let srcDisplay = getSrcDisplay(ar, false);
                    return caseInsensitiveIncludes(ar.value, filter.query) || caseInsensitiveIncludes(srcDisplay, filter.query)
                })
                if (findSrcObj) {
                    findSrc = true
                }
                let findDstObj = acl.dst.find((ar) => {
                    let dstDisplay = getDstDisplay(ar, false);
                    return caseInsensitiveIncludes(ar.value, filter.query) || caseInsensitiveIncludes(dstDisplay, filter.query)
                })

                if (findDstObj) {
                    findDst = true;
                }

                if (filter.query == formatMessage({ id: 'policies.resource.all' })) {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('*') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('*') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }

                if (filter.query == formatMessage({ id: 'policies.autoGroup.self' })) {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('autogroup:self') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('autogroup:self') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }
                if (filter.query == formatMessage({ id: 'policies.autoGroup.members' })) {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('autogroup:members') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('autogroup:members') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }
                if (filter.query == '公网用户') {
                    let findSrcObj = acl.src.find((ar) => {
                        return ar.value.indexOf('autogroup:internet') >= 0
                    })
                    if (findSrcObj) {
                        findSrc = true;
                    }
                    let findDstObj = acl.dst.find((ar) => {
                        return ar.value.indexOf('autogroup:internet') >= 0
                    })
                    if (findDstObj) {
                        findDst = true;
                    }
                }

                if (!findName && !findSrc && !findDst) {
                    return false;
                }

            }

            if (filter.services != '') {
                let findSrc = false;
                let findDst = false;
                let findSrcServices = acl.src.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE) {
                        return ar.value.indexOf(filter.services) >= 0
                    }
                });

                if (findSrcServices) {
                    findSrc = true;
                }
                let findDstServices = acl.dst.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE) {
                        return ar.value.indexOf(filter.services) >= 0
                    }
                });

                if (findDstServices) {
                    findDst = true;
                }

                if (!findSrc && !findDst) {
                    return false;
                }

            }

            if (filter.servicesGroup != '') {

                let findSrc = false;
                let findDst = false;
                let findSrcServicesGroup = acl.src.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
                        return ar.value.indexOf(filter.servicesGroup) >= 0
                    }
                });
                if (findSrcServicesGroup) {
                    findSrc = true;
                }
                let findDstServicesGroup = acl.dst.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.SERVICE_GROUP) {
                        return ar.value.indexOf(filter.servicesGroup) >= 0
                    }
                });

                if (findDstServicesGroup) {
                    findDst = true;
                }

                if (!findSrc && !findDst) {
                    return false;
                }
            }

            if (filter.user != '') {
                let findSrc = false;
                let findDst = false;
                let findSrcUser = acl.src.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.USER) {
                        return ar.value.indexOf(filter.user) >= 0
                    }
                });
                if (findSrcUser) {
                    findSrc = true;
                }
                let findDstUser = acl.dst.find((ar) => {
                    if (ar.type == AclOrigin_ResourceType.USER) {
                        return ar.value.indexOf(filter.user) >= 0
                    }
                });

                if (findDstUser) {
                    findDst = true;
                }

                if (!findSrc && !findDst) {
                    return false;
                }
            }

            if (filter.status != '') {
                if (acl.disabled != (filter.status == 'Disable')) {
                    return false;
                }
            }

            if (group) {
                let findGroup = false;
                group.acls.forEach((val: AclOrigin) => {
                    if (acl.id == val.id) {
                        findGroup = true;
                    }
                })

                if (!findGroup) {
                    return false;
                }
            }

            return true;
        })
        setTotal(filterdList.length)
        return filterdList;
    }

    const reloadAclOrigin = async () => {
        getACLPolicy()
    }

    const [syncAclOriginLoading, setSyncAclOriginLoading] = useState(false);
    const syncAclOrigin = () => {
        setSyncAclOriginLoading(true);
        flylayerClient.syncAclOrigin({
            flynetId: flynet.id
        }).then(() => {
            getACLPolicy()
            Notification.success({ content: formatMessage({ id: 'policies.syncSuccess' }), position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.syncFailed' }), position: "bottomRight" })
        }).finally(() => {
            setSyncAclOriginLoading(false);
        })
    }


    useEffect(() => {
        getACLPolicy();
    }, [])

    const [ipGroups, setIpGroups] = useState<IpGroup[]>([]);

    // 查询资源组
    const queryIpGroup = async () => {
        let res = await getFlynet(flynet.id);

        if (res && res.flynet && res.flynet.ipGroup) {
            if (res.flynet.ipGroup) {
                let data: IpGroup[] = JSON.parse(res.flynet.ipGroup);
                if (data && data.length > 0) {
                    setIpGroups(data);
                } else {
                    setIpGroups([]);
                }
            }
        }
    }


    const handleFilterChange = (param: ACLFilter) => {
        setFilter(param);
        setACLs(doFilter(allACLs, param, curGroup));

    }


    const requery = () => {
        queryIpGroup();
        getACLPolicy();
    }


    const handleGroupChange = (group?: AclGroup) => {
        setACLPolicyLoading(true);
        if (!group) {
            setCurGroup(undefined);

            setACLs(doFilter(storedACLs, filter, undefined));
            setACLPolicyLoading(false);
            return;
        }
        setACLs(doFilter(storedACLs, filter, group));
        setACLPolicyLoading(false);
    };

    return {
        aclPolicyLoading,
        aclPolicy,
        acls,
        setACLPolicy,
        userGroups,
        columns,
        saveACLPolicy,
        aclPolicySaveLoading,
        selectedACL,
        setSelectedACL,
        ruleEditVisible,
        setRuleEditVisible,
        ruleDeleteVisible,
        setRuleDeleteVisible,
        ruleEnableVisible,
        setRuleEnableVisible,
        ruleDisableVisible,
        setRuleDisableVisible,
        selectedACLIndex,
        setSelectedACLIndex,
        filter,
        setFilter,
        filterParams,
        setFilterParams,
        handleFilterChange,
        getSrcDisplay,
        getDstDisplay,
        storedACLs,
        reloadAclOrigin,
        syncAclOrigin,
        syncAclOriginLoading,
        total,
        ipGroups,
        requery,
        groups,
        curGroup,
        setCurGroup,
        machineGroups,
        handleGroupChange,
        removeFromGroupVisible,
        setRemoveFromGroupVisible,
        editGroupVisible,
        setEditGroupVisible,
        treeData,
        getMapGroupData
    }

}

export default useTable;