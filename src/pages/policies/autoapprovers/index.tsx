import React, { useState, useContext } from 'react'
import { Typo<PERSON>, Modal, Tag, Row, Col, Notification, Popover, Space, Button, Input, Divider } from "@douyinfe/semi-ui";
import { useLocale } from '@/locales';
import { IconPlus, IconPlusCircle, IconUser, IconDesktop, IconMinusCircle, IconArrowDown, IconArrowUp, IconMore, IconPriceTag } from '@douyinfe/semi-icons';
import MachineSelector from '@/components/machine-selector';
import UserSelector from '@/components/user-modal-selector';
import TagSelector from '@/components/tag-modal-selector';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Title, Paragraph } = Typography;
import { ListValue, Value } from '@bufbuild/protobuf';
import styles from './index.module.scss'

import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';
import { User } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { flylayerClient } from '@/services/core';
interface Props {
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    close: () => void
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);


    const [selectExitnode, setSelectExitnode] = useState(false);
    const [curIndex, setCurIndex] = useState(-1);
    const [machineSelectorVisible, setMachineSelectorVisible] = useState(false);
    const [userSelectorVisible, setUserSelectorVisible] = useState(false);
    const [tagSelectorVisible, setTagSelectorVisible] = useState(false);

    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {

        let routes: { name: string, values: string[] }[] = [];
        let exitnode: string[] = []
        if (policy && policy.autoapprovers && policy.autoapprovers.routes) {
            Object.keys(policy.autoapprovers.routes).forEach(key => {
                if (policy && policy.autoapprovers && policy.autoapprovers.routes) {
                    routes.push({
                        name: key, values: policy.autoapprovers.routes[key].values.map((item) => {
                            if (item.kind && item.kind.value) {
                                return item.kind.value + ''
                            } else {
                                return ''
                            }
                        })
                    })
                }
            })
        }
        if (policy && policy.autoapprovers && policy.autoapprovers.exitnode) {
            exitnode = policy.autoapprovers.exitnode;
        }

        return { routes: routes, exitnode: exitnode }

    }


    // 初始值
    const initValues = calInitValues(props.aclPolicy);

    const [routes, setRoutes] = useState<{ name: string, values: string[] }[]>(initValues.routes)
    const [exitnode, setExitnode] = useState<string[]>(initValues.exitnode);

    const [aclPolicy, setAclPolicy] = useState<ACLPolicy>(props.aclPolicy);

    const getAclPolicy = () => {

        let newRoutes: { [key: string]: ListValue } = {};
        if (routes) {

            routes.forEach((item) => {
                if (item.name) {
                    if (item.values) {

                        newRoutes[item.name] = new ListValue({
                            values:
                                item.values.map((item) => {
                                    let val = new Value({
                                        kind: { value: item, case: "stringValue" }
                                    });
                                    return val
                                })
                        });
                    } else {
                        newRoutes[item.name] = new ListValue({
                            values:
                                []
                        });
                    }
                }
            })
        }


        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            autoapprovers: {
                ...aclPolicy.autoapprovers,
                routes: newRoutes,
                exitnode: exitnode
            }
        })
        return policy;
    }


    const handleOk = () => {

        const aclPolicy = getAclPolicy();
        setAclPolicy(aclPolicy);

        // 保存
        setLoading(true)

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: aclPolicy
        }).then(() => {
            props.onChange(aclPolicy)
            props.close()
            Notification.success({ content: formatMessage({ id: 'policies.saveSuccess' }), position: "bottomRight" })
        }).catch((err: any) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })
    }
    return <>
        <Modal
            title={formatMessage({ id: 'policies.autoapprovers.title' })}
            visible={true}
            onOk={handleOk}
            onCancel={props.close}
            width={800}
            okButtonProps={{ loading }}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'policies.autoapprovers.description' })}</Paragraph>
            <Row className={styles.tableTitle} >
                <Col span={6}>{formatMessage({ id: 'policies.autoapprovers.subnetRoute' })}</Col>
                <Col span={16}>{formatMessage({ id: 'policies.autoapprovers.userOrTag' })}</Col>
                <Col span={2} className='btn-right-col'><Button onClick={() => {
                    setRoutes([...routes, { name: '', values: [] }])
                }} icon={<IconPlus />} /></Col>
            </Row>
            {routes.map((value, index) => {
                return <Row className={styles.tableBody} key={index} >
                    <Col xs={24} sm={6}>
                        <Input
                            value={value.name}
                            onChange={(input_val) => {

                                const newGroups = routes.map((val, i) => {
                                    if (i != index) {
                                        return val;
                                    } else return {
                                        name: input_val,
                                        values: val.values
                                    }
                                });
                                setRoutes(newGroups)
                            }}
                        >
                        </Input>
                    </Col>

                    <Col xs={24} sm={16}>
                        <Space style={{ flexWrap: 'wrap' }}>{

                            value.values.map((val, vi) => {
                                return <Tag key={vi} closable style={{ height: 32, paddingLeft: '10px', paddingRight: '10px' }} size='large' onClose={() => {
                                    const newGroups = routes.map((val, i) => {
                                        if (i != index) {
                                            return val;
                                        } else return {
                                            name: val.name,
                                            values: val.values.filter((v, j) => j != index)
                                        }
                                    });
                                    setRoutes(newGroups)
                                }}>{val}</Tag>
                            })

                        }
                            <Popover
                                position='bottom'

                                style={{
                                    padding: 5,

                                }}
                                content={<>
                                    <div className='mb10'>
                                        <Button
                                            icon={<IconUser />}
                                            onClick={() => {
                                                setUserSelectorVisible(true)
                                                setCurIndex(index);
                                                setSelectExitnode(false);
                                            }}
                                        >用户</Button>
                                    </div>
                                    <div>
                                        <Button
                                            icon={<IconPriceTag />}
                                            onClick={() => {
                                                setTagSelectorVisible(true);
                                                setCurIndex(index)
                                                setSelectExitnode(false);
                                            }}
                                        >标签</Button>
                                    </div>
                                </>}> <Button
                                    theme='borderless'
                                    icon={<IconPlusCircle />}
                                    onClick={() => {

                                    }}

                                /></Popover>
                        </Space>
                    </Col>
                    <Col xs={24} sm={2} className='btn-right-col'>
                        <Popover
                            position='left'

                            style={{
                                padding: 5,

                            }}
                            content={<>
                                <Space >
                                    <Button
                                        onClick={() => {
                                            const newGroups = routes.map((val, i) => {
                                                if (i == index) {
                                                    return routes[index + 1];
                                                } else if (i == index + 1) {
                                                    return routes[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setRoutes(newGroups)
                                        }}
                                        disabled={index == routes.length - 1} icon={<IconArrowDown />}></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = routes.map((val, i) => {
                                                if (i == index) {
                                                    return routes[index - 1];
                                                } else if (i == index - 1) {
                                                    return routes[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setRoutes(newGroups)
                                        }} disabled={index == 0} icon={<IconArrowUp />}></Button>
                                    <Button
                                        type='danger'
                                        theme='borderless'
                                        icon={<IconMinusCircle />}
                                        onClick={() => {
                                            const newGroups = routes.filter((g, i) => i != index)
                                            setRoutes(newGroups)
                                        }}

                                    />
                                </Space>
                            </>}><Button icon={<IconMore />}></Button></Popover>

                    </Col>
                </Row>
            })}
            <Divider className='mb20' style={{ marginTop: 20 }} />
            <Row className={styles.tableTitle} >
                <Col span={20}>{formatMessage({ id: 'policies.autoapprovers.exitNode' })}</Col>
                <Col span={4} className='btn-right-col'>
                    <Popover
                        position='bottom'

                        style={{
                            padding: 5,

                        }}
                        content={<>
                            <div className='mb10'>
                                <Button
                                    icon={<IconDesktop />}
                                    onClick={() => {
                                        setMachineSelectorVisible(true);

                                        setSelectExitnode(true);
                                    }}
                                >{formatMessage({ id: 'policies.autoapprovers.device' })}</Button>
                            </div>
                            <div>
                                <Button
                                    icon={<IconPriceTag />}
                                    onClick={() => {
                                        setTagSelectorVisible(true);

                                        setSelectExitnode(true);
                                    }}
                                >{formatMessage({ id: 'policies.autoapprovers.tag' })}</Button>
                            </div>
                        </>}> <Button
                            theme='borderless'
                            icon={<IconPlus />}
                            onClick={() => {
                                setExitnode([...exitnode, ''])
                            }}

                        /></Popover>



                </Col>
            </Row>
            {exitnode.map((value, index) => {
                return <Row className={styles.tableBody} key={index} >
                    <Col xs={24} sm={20}>
                        <Input
                            value={value}
                            readOnly
                        // onChange={(input_val) => {
                        //     const newGroups = exitnode.map((val, i) => {
                        //         if (i != index) {
                        //             return val;
                        //         } else return input_val
                        //     });
                        //     setExitnode(newGroups)
                        // }}
                        >
                        </Input>
                    </Col>

                    <Col xs={24} sm={4} className='btn-right-col'>
                        <Popover
                            position='left'
                            style={{
                                padding: 5,
                            }}
                            content={<>
                                <Space >
                                    <Button
                                        onClick={() => {
                                            const newGroups = exitnode.map((val, i) => {
                                                if (i == index) {
                                                    return exitnode[index + 1];
                                                } else if (i == index + 1) {
                                                    return exitnode[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setExitnode(newGroups)
                                        }}
                                        disabled={index == routes.length - 1} icon={<IconArrowDown />}></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = exitnode.map((val, i) => {
                                                if (i == index) {
                                                    return exitnode[index - 1];
                                                } else if (i == index - 1) {
                                                    return exitnode[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setExitnode(newGroups)
                                        }} disabled={index == 0} icon={<IconArrowUp />}></Button>
                                    <Button
                                        type='danger'
                                        theme='borderless'
                                        icon={<IconMinusCircle />}
                                        onClick={() => {
                                            const newGroups = exitnode.filter((g, i) => i != index)
                                            setExitnode(newGroups)
                                        }}
                                    />
                                </Space>
                            </>}><Button icon={<IconMore />}></Button></Popover>

                    </Col>
                </Row>
            })}
        </Modal>
        {machineSelectorVisible && <MachineSelector
            multi={false}
            onChange={(value => {
                const newList = [...exitnode, (value as Machine).ipv4]
                setExitnode(newList)
                setMachineSelectorVisible(false)
            })}
            close={() => {
                setMachineSelectorVisible(false)
            }}
        ></MachineSelector>}
        {
            tagSelectorVisible && <TagSelector
                multi={false}
                onChange={(value) => {
                    if (selectExitnode) {
                        const newList = [...exitnode, value as string]
                        setExitnode(newList)
                        setMachineSelectorVisible(false)
                    } else {
                        const newList = routes.map((val, i) => {
                            if (i != curIndex) {
                                return val
                            } else {
                                return {
                                    name: val.name,
                                    values: [...val.values, (value as string)]
                                }
                            }
                        });
                        setRoutes(newList)
                    }
                    setTagSelectorVisible(false)

                }}
                close={() => setTagSelectorVisible(false)}
            ></TagSelector>
        }
        {
            userSelectorVisible && <UserSelector
                multi={false}
                onChange={(value => {
                    const newList = routes.map((val, i) => {
                        if (i != curIndex) {
                            return val
                        } else {
                            return {
                                name: val.name,
                                values: [...val.values, (value as User).loginName + '']
                            }
                        }
                    });
                    setRoutes(newList)
                    setUserSelectorVisible(false)
                })}
                close={() => {
                    setUserSelectorVisible(false)
                }}
            ></UserSelector>

        }
    </>
}

export default Index;