import { FC, useState } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';
import { ImportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: ImportRecord
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');


    const handleSubmit = () => {
        if (loading) {
            return;
        }
        setLoading(true);
        flylayerClient.deleteImportRecord({
            id: props.record.id
        }).then(() => {
            Notification.success({ content: formatMessage({ id: 'dataImport.deleteSuccess' }), position: "bottomRight" })
            if (props.success) {
                props.success();
            }
        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'dataImport.deleteFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        });

    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'dataImport.deleteTitle' }).replace('{fileName}', props.record.fileName)}
            visible={true}
            onOk={handleSubmit}

            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                disabled: props.record.fileName != confirmVal,
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >

            <Paragraph className='mb20'> {formatMessage({ id: 'dataImport.deleteConfirm' }).replace('{fileName}', props.record.fileName)}
            </Paragraph>
            <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
