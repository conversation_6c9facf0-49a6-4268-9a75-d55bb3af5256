import { FC, useContext, useState } from 'react';
import { Button, Typography, List, Toast, Tabs, TabPane, Modal, Row, Col, Space, Upload } from '@douyinfe/semi-ui';
import styles from './index.module.scss'
import { AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { IconPaperclip } from '@douyinfe/semi-icons';
import Papa from 'papaparse';
import TableEmpty from '@/components/table-empty';
import { flylayerClient } from '@/services/core';
import { uploadFileWithProgress } from '@/services/file';
import { ImportError } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { useLocale } from '@/locales';

const { Text, Paragraph, Title } = Typography;

interface Props {
    close: () => void;
    success?: () => void;
}

const PolicyOverview: FC<{ acl: AclOrigin }> = ({ acl: policy }) => {
    const { formatMessage } = useLocale();
    return (
        <List.Item className={styles.tabListItem}>
            <div className={styles.tabListItemInner}>
                <Row>
                    <Col span={16}>
                        <Title heading={6}>
                            {policy.name}
                        </Title>
                    </Col>
                    <Col span={8} className='table-last-col'>
                        <Text type='tertiary'></Text>
                    </Col>
                </Row>
                <Paragraph type='tertiary' className='mb10'>{policy.description}</Paragraph>
                <Paragraph>{formatMessage({ id: 'policies.policyGroup' })}</Paragraph>
                {policy.aclGroups.map((group: AclGroup) => (
                    <Space key={group.id + ''} style={{ width: '100%' }}>
                        <Text type='tertiary'>{group.alias}</Text><Text size='small' type='tertiary'>{group.name}</Text>
                    </Space>
                ))}
            </div>
        </List.Item>
    );
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const rootPath = 'policy';
    

    const [isPreviewStep, setIsPreviewStep] = useState(false);

    const [importAcls, setImportAcls] = useState<AclOrigin[]>([]);
    const [updateAcls, setUpdateAcls] = useState<AclOrigin[]>([]);
    const [importErrors, setImportErrors] = useState<ImportError[]>([]);

    const [importId, setImportId] = useState<bigint>();

    const flynet = useContext(FlynetGeneralContext);

    const [saveLoading, setSaveLoading] = useState(false);




    const handleTemplateDown = () => {
        let csv = Papa.unparse([["名称", "描述", "源", "动作", "目标", "禁用", "优先级", "系统", "分组"]]);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `${formatMessage({ id: 'dataImport.template.filename' })}.csv`;
        a.click();
    }

    const checkFileType = (file: any) => {
        if (file.name.startsWith('.')) {
            return true;
        }

        return file.name.endsWith('.csv');
    }


    const [files, setFiles] = useState<FileItem[]>();
    const handleSubmit = async () => {
        setSaveLoading(true);

        flylayerClient.importAclOrigins({
            id: importId,
        }).then(() => {
            setSaveLoading(false);
            props.success && props.success();
            Toast.success(formatMessage({ id: 'dataImport.success' }));
        }
        ).catch(() => {
            setSaveLoading(false);
            Toast.error(formatMessage({ id: 'dataImport.failed' }));
        });
    }


    return (
        <><Modal
            width={960}
            title={formatMessage({ id: 'dataImport.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okText={formatMessage({ id: 'dataImport.import' })}
            okButtonProps={{ loading: saveLoading, disabled: isPreviewStep ? false : true }}
            className='semi-modal'
            maskClosable={false}
        >
            {isPreviewStep ? <>
                <Tabs type="line">
                    <TabPane tab={formatMessage({ id: 'dataImport.newPolicies' })} itemKey="1" className={styles.tabPane}>
                        {importAcls && importAcls.length > 0 ?
                            <List grid={{ gutter: 12, span: 6 }} dataSource={importAcls}
                                renderItem={(item, index) => (
                                    <PolicyOverview key={index} acl={item} />
                                )}></List> : <TableEmpty loading={false} />}
                    </TabPane>
                    <TabPane tab={formatMessage({ id: 'dataImport.updatePolicies' })} itemKey="2" className={styles.tabPane}>
                        {updateAcls && updateAcls.length > 0 ? <List grid={{ gutter: 12, span: 6 }} dataSource={updateAcls}
                            renderItem={(item, index) => (
                                <PolicyOverview key={index} acl={item} />
                            )} /> : <TableEmpty loading={false} />}
                    </TabPane>
                    <TabPane tab={formatMessage({ id: 'dataImport.errorRows' })} itemKey="3" className={styles.tabPane}>
                        <div className={styles.errorWrap}>
                            <List layout="horizontal" className={styles.errorList}>
                                <List.Item className={styles.errorItem}>
                                    <Text type='tertiary'>{formatMessage({ id: 'dataImport.rowNumber' })}</Text>
                                </List.Item>
                                <List.Item className={styles.errorItem}>
                                    <Text type='tertiary'>{formatMessage({ id: 'dataImport.errorMessage' })}</Text>
                                </List.Item>
                            </List>

                            {importErrors && importErrors.length > 0 ? <>

                                {importErrors.map((item, index) => {
                                    return (
                                        <List layout="horizontal" key={index} className={styles.errorList} >
                                            <List.Item className={styles.errorItem}>
                                                {item.line}
                                            </List.Item>
                                            {item.data.map((dataItem, dataIndex) => {
                                                return (
                                                    <List.Item key={dataIndex} className={styles.errorItem}>
                                                        <Text ellipsis>{dataItem}</Text>
                                                    </List.Item>
                                                );
                                            })}
                                            <List.Item className={styles.errorItem}>
                                                {item.message}
                                            </List.Item>
                                        </List>
                                    );
                                })}

                            </> : <Paragraph>{formatMessage({ id: 'dataImport.noErrors' })}</Paragraph>
                            }
                        </div>
                    </TabPane>
                </Tabs>
            </> : <>

                <Space style={{ alignItems: 'flex-start' }}>
                    <Upload
                        beforeUpload={(prop) => {
                            if (!checkFileType(prop.file)) {
                                Toast.error(formatMessage({ id: 'dataImport.uploadCsvOnly' }));
                                return false;
                            }
                            return true;
                        }}
                        style={{ width: 740 }}
                        showUploadList={false}
                        action=''
                        draggable
                        dragMainText={formatMessage({ id: 'dataImport.dragMainText' })}
                        dragSubText={formatMessage({ id: 'dataImport.dragSubText' })}
                        fileList={files}
                        onChange={({ fileList }) => setFiles(fileList)}
                        customRequest={(options) => {
                            let ext = options.file.name.split('.').pop();
                            if (!ext) return;
                            let fileName = options.file.name.split('.').slice(0, -1).join('.');
                            if (fileName.length > 50) {
                                fileName = fileName.slice(0, 50);
                            }
                            fileName = `${fileName}-${new Date().getTime()}.${ext}`;


                            let uploadName = `${rootPath}/${fileName}`;

                            flylayerClient.getUploadUrlWithPathName({
                                path: rootPath,
                                name: fileName,
                            }).then((res) => {
                                uploadFileWithProgress(res.uploadUrl, options.fileInstance, (total, loaded) => {
                                    options.onProgress && options.onProgress({ total, loaded });
                                }).then(() => {
                                    flylayerClient.importAclOriginsPreview({
                                        flynetId: flynet.id,
                                        fileName: uploadName,
                                        fileUrl: res.accessUrl,
                                    }).then((res) => {
                                        setImportAcls(res.importAclOrigins);
                                        setUpdateAcls(res.updateAclOrigins);
                                        setImportErrors(res.errors);
                                        setImportId(res.importRecordId);
                                        setIsPreviewStep(true);
                                    });
                                }).catch((err) => {
                                    options.onError(err);
                                });
                            }).catch((err) => {
                                options.onError(err);
                            });
                        }}
                        uploadTrigger='auto'
                        limit={1}
                        accept='.csv'
                    ></Upload>
                    <Button size='large' type='primary' theme='solid' icon={<IconPaperclip />} onClick={handleTemplateDown}>{formatMessage({ id: 'dataImport.downloadTemplate' })}</Button>
                </Space>
            </>}
        </Modal>
        </>
    );
};

export default Index;