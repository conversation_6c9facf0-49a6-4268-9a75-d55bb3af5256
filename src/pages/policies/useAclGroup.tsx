import React, { FC, useState, useContext, useEffect } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { AclOrigin, AclGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';


import { flylayerClient } from '@/services/core';
import { getSimpleAclGroupName } from '@/utils/acl';

export interface GroupTreeData {
    label: string,
    value: string,
    key: string
    parentId?: string,
    fullName: string
    children: Array<GroupTreeData>
}
let mapTreeData: Map<string, GroupTreeData> = new Map();
let mapGroupData: Map<string, AclGroup> = new Map();
const useAclGroup = () => {
    const getMapTreeData = () => {return mapTreeData}
    const getMapGroupData = () => {return mapGroupData}
    const flynet = useContext(FlynetGeneralContext);
    const [treeData, setTreeData] = useState<any[]>();
    const [aclGroupLoading, setAclGroupLoading] = useState(true);
    // const [stataicTreeData, setStaticTreeData] = useState<any[]>([]);
    const buildTreeData = (treeData: Array<GroupTreeData>,
        serviceGroups: AclGroup[],
        mapTreeData: Map<string, GroupTreeData>, isRoot: boolean, depth: number) => {
        const leftServiceGroups: AclGroup[] = [];
        serviceGroups.forEach((item) => {
            if (isRoot) {
                if (!item.parentId || item.parentId == BigInt(0)) {
                    const treeItem: GroupTreeData = {
                        label: `${item.alias}(${item.name})`,
                        fullName: item.fullName,
                        value: item.id + '',
                        key: item.id + '',
                        children: []
                    }
                    treeData.push(treeItem)
                    mapTreeData.set(item.id + '', treeItem);
                    mapGroupData.set(item.id + '', item);
                } else {
                    leftServiceGroups.push(item);
                }
            } else {
                if (mapTreeData.has(item.parentId+ '')) {
                    const parent = mapTreeData.get(item.parentId+ '');
                    const description = item.description ? `(${item.description})` : ''
                    const treeItem: GroupTreeData = {
                        parentId: item.parentId + '',
                        fullName: item.fullName,
                        label: `${item.alias}( ${item.name}))`,
                        value: item.id + '',
                        key: item.id + '',
                        children: []
                    }
                    parent?.children.push(treeItem);
                    mapTreeData.set(item.id + '', treeItem);
                    mapGroupData.set(item.id + '', item);
                } else {
                    leftServiceGroups.push(item);
                }
            }
        })

        if (depth > 10) {

            return;
        }
        if (leftServiceGroups.length > 0) {
            buildTreeData(treeData, leftServiceGroups, mapTreeData, false, depth + 1);
        }

    }
    useEffect(() => {
        flylayerClient.listAclGroups({
            flynetId: flynet.id,
        }).then((res) => {
            const treeData: Array<GroupTreeData> = [];
            mapTreeData = new Map();
            buildTreeData(treeData, res.groups, mapTreeData, true, 0);
            
            //setMapTreeData(mapTreeData);
            setTreeData(treeData);

        }).finally(()=>{
            setAclGroupLoading(false);
        });
    }, []);

    return { aclGroupLoading, treeData, getMapTreeData, getMapGroupData, buildTreeData }
}

export default useAclGroup;