import React, { useState, useContext } from 'react'

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Typography, Modal, Row, Col, Notification, Space, Button, Input, Popover } from "@douyinfe/semi-ui";
import { useLocale } from '@/locales';
import { IconPlus, IconMinusCircle, IconArrowDown, IconArrowUp, IconMore, IconPlusCircle, IconAlignTop, IconAlignBottom } from '@douyinfe/semi-icons';
import { ListValue, Value } from '@bufbuild/protobuf';
import MachineSelector from '@/components/machine-selector';
import UserSelector from '@/components/user-modal-selector';
import { flylayerClient } from '@/services/core';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
const { Title, Paragraph } = Typography;
import styles from './index.module.scss'
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';
import { User } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import UserRemoteTag from '@/components/user-remote-tag';

interface Props {
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    close: () => void
}

// 无效的名称
const errorNameMessage: Map<number, string> = new Map();

const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const PREFIX = 'tag:';
    const flynet = useContext(FlynetGeneralContext);
    const [aclPolicy, setAclPolicy] = useState<ACLPolicy>(props.aclPolicy);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);


    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {

        let tagowners: { name: string, values: string[] }[] = [];
        Object.keys(policy.tagowners).forEach(key => {
            tagowners.push({
                name: key.replace(PREFIX, ''), values: policy.tagowners[key].values.map((item) => {
                    return item.kind.value + ''
                })
            })
        })
        return tagowners;
    }

    // 初始值
    const initValues = calInitValues(props.aclPolicy);

    const [tagowners, setTagowners] = useState<{ name: string, values: string[] }[]>(initValues);

    const [curIndex, setCurIndex] = useState(-1);
    const [machineSelectorVisible, setMachineSelectorVisible] = useState(false);
    const [userSelectorVisible, setUserSelectorVisible] = useState(false);


    // 无效的行
    const [invalidLines, setInvalidLines] = useState<number[]>([]);
    // 无效的名称
    const [errorNameIndexs, setErrorNameIndexs] = useState<number[]>([]);
    // 无效的值
    const [errorValueIndexs, setErrorValueIndexs] = useState<number[]>([]);


    const getAclPolicy = () => {

        let newTagowners: { [key: string]: ListValue } = {};

        if (tagowners) {
            tagowners.forEach((item) => {
                if (item.name && item.name.length > 0) {
                    if (item.values) {
                        newTagowners[PREFIX + item.name] = new ListValue({
                            values:
                                item.values.map((item) => {
                                    let val = new Value({
                                        kind: { value: item, case: "stringValue" }
                                    });
                                    return val
                                })
                        });
                    }
                }

            })
        }

        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            tagowners: newTagowners,
        })
        return policy;
    }


    const handleOk = () => {

        const aclPolicy = getAclPolicy();
        setAclPolicy(aclPolicy);

        // 保存
        setLoading(true)

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: aclPolicy
        }).then(() => {
            props.onChange(aclPolicy)
            props.close()
            Notification.success({ content: formatMessage({ id: 'policies.saveSuccess' }), position: "bottomRight" })
        }).catch((err: any) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'policies.saveFailed' }), position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        })
    }
    return <>
        <Modal
            title={formatMessage({ id: 'policies.tagowners.title' })}
            visible={true}
            onOk={handleOk}
            onCancel={props.close}
            width={1080}
            okButtonProps={{ loading }}
            closeOnEsc={true}
            maskClosable={false}
        >

            <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'policies.tagowners.description' })}</Paragraph>
            <Row className={styles.tableTitle} >
                <Col span={6}>{formatMessage({ id: 'policies.tagowners.name' })}</Col>
                <Col span={16}>{formatMessage({ id: 'policies.tagowners.users' })}</Col>
                <Col span={2} className='btn-right-col'><Button onClick={() => {
                    setTagowners([...tagowners, { name: '', values: [] }])
                    setInvalidLines([...invalidLines, tagowners.length])
                }} icon={<IconPlus />} /></Col>
            </Row>
            {tagowners.map((val, index) => {
                return <Row className={invalidLines.indexOf(index) >= 0 ? styles.tableBodyError : styles.tableBody} key={index} >
                    <Col xs={24} sm={6}>
                        <Input
                            value={val.name}
                            onChange={(input_val) => {
                                const newGroups = tagowners.map((val, i) => {
                                    if (i != index) {
                                        return val;
                                    } else return {
                                        name: input_val,
                                        values: val.values
                                    }
                                });
                                setTagowners(newGroups)

                                // 验证
                                if (!input_val || input_val.trim() == '') {
                                    if (errorNameIndexs.indexOf(index) < 0) {
                                        errorNameIndexs.push(index);
                                    }
                                    setErrorNameIndexs(errorNameIndexs);
                                    errorNameMessage.set(index, formatMessage({ id: 'policies.tagowners.nameRequired' }));
                                    setInvalidLines([...invalidLines, index]);
                                } else if (input_val.indexOf(PREFIX) >= 0) {
                                    if (errorNameIndexs.indexOf(index) < 0) {
                                        errorNameIndexs.push(index);
                                    }
                                    setErrorNameIndexs(errorNameIndexs);
                                    errorNameMessage.set(index, formatMessage({ id: 'policies.tagowners.nameCannotContainPrefix' }) + PREFIX);
                                    setInvalidLines([...invalidLines, index]);
                                } else {
                                    let exist = false;
                                    tagowners.forEach((item, i) => {
                                        if (i != index && item.name == input_val) {
                                            exist = true;
                                        }
                                    });
                                    if (exist) {
                                        if (errorNameIndexs.indexOf(index) < 0) {
                                            errorNameIndexs.push(index);
                                        }
                                        setErrorNameIndexs(errorNameIndexs);
                                        errorNameMessage.set(index, formatMessage({ id: 'policies.tagowners.nameExists' }));
                                        setInvalidLines([...invalidLines, index]);
                                    } else {
                                        if (errorNameIndexs.indexOf(index) >= 0) {
                                            errorNameIndexs.splice(errorNameIndexs.indexOf(index), 1);
                                        }
                                        setErrorNameIndexs(errorNameIndexs);
                                        errorNameMessage.delete(index);
                                        if (val.values.length > 0) {
                                            setInvalidLines(invalidLines.filter((val) => val != index));
                                        }


                                    }
                                }
                            }}
                        >
                        </Input>
                        {errorNameIndexs.indexOf(index) >= 0 && <Paragraph type='danger'>{errorNameMessage.get(index)}</Paragraph>}
                    </Col>

                    <Col xs={24} sm={16}>
                        <Space style={{ flexWrap: 'wrap' }}>{
                            val.values.map((val, tagIndex) => {
                                return <UserRemoteTag key={tagIndex}
                                onClose={() => {
                                    const newGroups = tagowners.map((val, i) => {

                                        if (i != index) {
                                            return val;
                                        } else {
                                            let newValues: string[] = [];
                                            val.values.forEach((v, j) => {
                                                if (j != tagIndex) {
                                                    newValues.push(v)
                                                }
                                            })

                                            if (newValues.length == 0) {
                                                if (errorValueIndexs.indexOf(index) < 0) {
                                                    errorValueIndexs.push(index);
                                                }
                                                setErrorValueIndexs(errorValueIndexs);
                                                setInvalidLines([...invalidLines, index]);
                                            } else {
                                                if (errorValueIndexs.indexOf(index) >= 0) {
                                                    errorValueIndexs.splice(errorValueIndexs.indexOf(index), 1);
                                                }
                                                setErrorValueIndexs(errorValueIndexs);
                                                if (errorNameIndexs.indexOf(index) < 0) {
                                                    setInvalidLines(invalidLines.filter((val) => val != index));
                                                }

                                            }

                                            return {
                                                name: val.name,
                                                values: newValues
                                            }

                                        }
                                    });
                                    setTagowners(newGroups)
                                }}
                                style={{height: 32, paddingLeft: '10px', paddingRight: '10px'}} loginName={val} />
                                
                            })

                        }

                            <Button
                                theme='borderless'
                                onClick={() => {
                                    setUserSelectorVisible(true);
                                    setCurIndex(index)
                                }}
                                icon={<IconPlusCircle />}
                            />
                        </Space></Col>
                    <Col xs={24} sm={2} className='btn-right-col'>
                        <Popover
                            position='left'

                            style={{
                                padding: 5,

                            }}
                            content={<>
                                <Space >
                                    <Button
                                        onClick={() => {
                                            let newGroups = tagowners.map((val, i) => {
                                                return val;
                                            });
                                            let item = newGroups[index];
                                            newGroups[index] = newGroups[0];
                                            newGroups[0] = item;

                                            setTagowners(newGroups)
                                        }} disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconAlignTop />}></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = tagowners.map((val, i) => {
                                                if (i == index) {
                                                    return tagowners[index - 1];
                                                } else if (i == index - 1) {
                                                    return tagowners[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setTagowners(newGroups)
                                        }} disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconArrowUp />}></Button>
                                    <Button
                                        onClick={() => {
                                            const newGroups = tagowners.map((val, i) => {
                                                if (i == index) {
                                                    return tagowners[index + 1];
                                                } else if (i == index + 1) {
                                                    return tagowners[index]
                                                } else {
                                                    return val
                                                }
                                            });
                                            setTagowners(newGroups)
                                        }}
                                        disabled={invalidLines.indexOf(index) >= 0 || index == tagowners.length - 1} icon={<IconArrowDown />}></Button>
                                    <Button
                                        onClick={() => {
                                            let newGroups = tagowners.map((val, i) => {
                                                return val;
                                            });
                                            let item = newGroups[index];
                                            newGroups[index] = newGroups[newGroups.length - 1];
                                            newGroups[newGroups.length - 1] = item;

                                            setTagowners(newGroups)
                                        }}
                                        disabled={invalidLines.indexOf(index) >= 0 || index == tagowners.length - 1} icon={<IconAlignBottom />}></Button>
                                    <Button
                                        type='danger'
                                        theme='borderless'
                                        icon={<IconMinusCircle />}
                                        onClick={() => {
                                            const newGroups = tagowners.filter((g, i) => i != index)
                                            setTagowners(newGroups)

                                            // 删除无效行
                                            setInvalidLines(invalidLines.filter((val) => val != index));
                                            // 删除无效名称
                                            if (errorNameIndexs.indexOf(index) >= 0) {
                                                errorNameIndexs.splice(errorNameIndexs.indexOf(index), 1);
                                            }
                                            setErrorNameIndexs(errorNameIndexs);
                                            errorNameMessage.delete(index);
                                            // 删除无效值
                                            if (errorValueIndexs.indexOf(index) >= 0) {
                                                errorValueIndexs.splice(errorValueIndexs.indexOf(index), 1);
                                            }
                                            setErrorValueIndexs(errorValueIndexs);

                                        }}

                                    />
                                </Space>
                            </>}><Button icon={<IconMore />}></Button></Popover>

                    </Col>
                </Row>
            })}
        </Modal>
        {machineSelectorVisible && <MachineSelector
            multi={false}
            onChange={(value => {
                const newList = tagowners.map((val, i) => {
                    if (i != curIndex) {
                        return val
                    } else {
                        return {
                            name: val.name,
                            values: [...val.values, (value as Machine).ipv4 + '']
                        }
                    }
                });
                setTagowners(newList)
                setMachineSelectorVisible(false)
            })}
            close={() => {
                setMachineSelectorVisible(false)
            }}
        ></MachineSelector>}
        {
            userSelectorVisible && <UserSelector
                multi={false}
                onChange={(value => {
                    const newList = tagowners.map((val, i) => {
                        if (i != curIndex) {
                            return val
                        } else {
                            setErrorValueIndexs(errorValueIndexs.filter((val) => val != curIndex));
                            if (errorNameIndexs.indexOf(curIndex) < 0) {
                                setInvalidLines(invalidLines.filter((val) => val != curIndex));
                            }

                            let newValues: Array<string> = [...val.values];
                            const newLoginName = (value as User).loginName + '';
                            if (newValues.indexOf(newLoginName) < 0) {
                                newValues.push(newLoginName)
                            }




                            return {
                                name: val.name,
                                values: newValues
                            }
                        }
                    });
                    setTagowners(newList)
                    setUserSelectorVisible(false)
                })}
                close={() => {
                    setUserSelectorVisible(false)
                }}
            ></UserSelector>

        }
    </>
}

export default Index;