import React, { FC, useEffect, useState, useContext } from 'react';
import useServicesGroup from '@/pages/services/useServicesGroup'
import { Select, Divider, Button, TreeSelect } from '@douyinfe/semi-ui'
import { useLocale } from '@/locales';



const Index: FC<{
    value: string,
    onChange: (val: string) => void,
    optList?: Array<{
        value: string,
        label: string
    }>
}> = (props) => {
    const { formatMessage } = useLocale();

    const [currentValue, setCurrentValue] = useState(props.value);
    const { serviceGroupTreeData } = useServicesGroup();
    return <>
        <TreeSelect
            zIndex={9999}
            className='mb10'
            style={{ width: '100%' }}
            expandAll
            treeData={
                serviceGroupTreeData
            }
            onChangeWithObject={true}
            onChange={(val) => {
                if (val) {
                    setCurrentValue('svg:' + (val as any).fullName)
                }

            }}
            position='top'
            filterTreeNode
            showFilteredOnly
            dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
         ></TreeSelect>
            <Divider className='mb10'></Divider>
            <Button block onClick={() => {
                props.onChange(currentValue)
            }}>{formatMessage({ id: 'policies.common.apply' })}</Button></>
}

export default Index;
