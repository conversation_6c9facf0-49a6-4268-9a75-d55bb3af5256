import { FC, useState, useEffect, useContext, useCallback } from 'react';
import { Avatar, Select, Divider, Button, Notification, Typography, Tooltip } from '@douyinfe/semi-ui'
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { useLocale } from '@/locales';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';
import { debounce } from 'lodash';
import { renderCustomOption } from '@/utils/user';

const { Text } = Typography;

const Index: FC<{
    value: string,
    onChange: (val: string) => void,
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();

    const field = 'loginName'
    const flynet = useContext(FlynetGeneralContext);
    const [currentValue, setCurrentValue] = useState(props.value);
    // 用户列表
    const [users, setUsers] = useState<User[]>();
    // 所有用户列表
    const [allUsers, setAllUsers] = useState<User[]>();

    const queryUser = async (filterKeywords?: string) => {
        try {
            const res = await flylayerClient.listUsers({
                flynetId: flynet?.id,
                query: filterKeywords ? `keywords=${encodeURIComponent(filterKeywords)}&limit=20&offset=0` : 'limit=20&offset=0'
            })
            setUsers(res.users.filter(user => !user.disabled));
        } catch (err) {
            console.error(err)
            Notification.error({ content: '获取用户列表失败, 请稍后重试' })
        }
    }

    const debounceQuery = useCallback(debounce((queryStr) => queryUser(queryStr), 500), []);

    // 查询用户数据
    const queryActor = (val: string) => {
        debounceQuery(val);
    }




    // 是否正在加载用户数据 
    const [loading, setLoading] = useState(false);


    useEffect(() => {
        setLoading(true);
        flylayerClient.listUsers({
            flynetId: flynet?.id,
            query: 'limit=20&offset=0'
        }).then((res) => {
            const users = res.users.filter(user => !user.disabled)
            setUsers(users);
            setAllUsers(users)
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: '获取用户列表失败, 请稍后重试' })

        }).finally(() => {
            setLoading(false);

        })
    }, []);

    const renderSelectedItem = (optionNode: any) => {
        const item: User = optionNode as User;
        
        if (!item || !item.loginName) {
            return <div></div>
        }
        

        return <div style={{ display: 'flex', alignItems: 'center' }}><Avatar size="extra-small" src={item.avatarUrl} />
            <div style={{ marginLeft: 8 }}>
                <div style={{ fontSize: 14 }}>{item.displayName}(<Tooltip zIndex={9000} content={item.loginName}><Text ellipsis style={{maxWidth: 120}}>{item.loginName}</Text></Tooltip> )</div>
            </div></div>
    };

    return <>
        <Select
            className='mb10'
            style={{ width: '100%' }}
            zIndex={3000}
            filter
            remote
            showClear
            value={currentValue}

            renderSelectedItem={renderSelectedItem}
            onSearch={queryActor}
            position='bottom'
            loading={loading}
            onChange={(val: any) => {
                setCurrentValue(val as string)
            }}
            onClear={() =>{setCurrentValue('')}}
            emptyContent={<>暂无用户</>}
        >

            {users?.map((user, index) => renderCustomOption(user, index, field, false))}
        </Select>
        <Divider className='mb10'></Divider>
        <Button block onClick={() => {
            props.onChange(currentValue)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </>
}

export default Index;