import { FC, useState } from 'react';
import { Select, Divider, Button } from '@douyinfe/semi-ui'
import { useLocale } from '@/locales';

const Index: FC<{
    value: string,
    onChange: (val: string) => void,
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();

    const [currentValue, setCurrentValue] = useState(props.value);
    return <>
        <Select
            zIndex={3000}
            className='mb10'
            style={{ width: '100%' }}
            value={currentValue}
            optionList={props.optList}
            onChange={val => {
                setCurrentValue(val as string)

            }}>

        </Select>
        <Divider className='mb10'></Divider>
        <Button block onClick={() => {
            props.onChange(currentValue)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </>
}

export default Index;
