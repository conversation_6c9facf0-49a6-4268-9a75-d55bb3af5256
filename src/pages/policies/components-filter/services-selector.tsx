import { FC, useEffect, useState, useContext } from 'react';
import { Select, Divider, Button, Input } from '@douyinfe/semi-ui'
import {Service} from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

import { flylayerClient } from '@/services/core';


const Index: FC<{
    value: string,
    onChange: (val: string) => void,
    optList?: Array<{
        value: string,
        label: string
    }>,
}> = (props) => {
    const { formatMessage } = useLocale();

    const flynet = useContext(FlynetGeneralContext);
    const [currentValue, setCurrentValue] = useState(props.value);
    const [optList, setOptList] = useState<Array<{
        value: string,
        label: string
    }>>(props.optList ? props.optList : []);
    const [filteredOptList, setFilteredOptList] = useState<Array<{
        value: string,
        label: string
    }>>(props.optList ? props.optList : []);

    const [servicesLoaded, setServicesLoaded] = useState(false);

    useEffect(() => {
        flylayerClient.listServices({
            flynetId: flynet.id,
        }).then(res => {
            if(res) {
                const optList = res.services.map(service => {

                    const description = service.description ? `(${service.description})` : ''
                    return {
                        value: 'svc:' + service.name,
                        label: `${service.name}${description}`
                    }
                });
                setFilteredOptList(optList);
                setOptList(optList);
                setServicesLoaded(true);
            }
        })
    }, []);

    const [filterTxt, setFilterTxt] = useState('');
    const handleFilterTxtChange = (val: string) => {
        setFilterTxt(val);

        const filteredOptList = optList.filter(opt => opt.label.includes(val));
        setFilteredOptList(filteredOptList);


    }
    return <>

        <Select
        filter
        zIndex={3000}
            className='mb10'
            style={{width: '100%'}}
            value={currentValue}
            optionList={filteredOptList}
            onChange={val => {
                setCurrentValue(val as string)

            } }>

        </Select>
        <Divider className='mb10'></Divider>
        <Button block onClick={() => {
            props.onChange(currentValue)
        }}>{formatMessage({ id: 'policies.common.apply' })}</Button>
    </>
}

export default Index;
