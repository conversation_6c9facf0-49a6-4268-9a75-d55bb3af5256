import { FC } from 'react';
import { Select } from '@douyinfe/semi-ui'
import { useLocale } from '@/locales';


export const getStatusDisplayValue = (value: string) => {
    // This function is used by useTable.tsx, so we can't use hooks here
    // The translation will be handled in the component that uses this function
    if(value == 'Enable') {
        return '启用';
    } else if(value == 'Disable') {
        return '禁用';
    }
    return value;
}

const StatusSelector: FC<{ value: string, onChange: (val: string) => void }> = (props) => {
    const { value, onChange } = props;
    const { formatMessage } = useLocale();

    return <Select value={value}
    zIndex={9999}
        onChange={(value) => { onChange(value as string) }}
        style={{ width: '100%' }}>
        <Select.Option value='Enable'>{formatMessage({ id: 'policies.action.enabled' })}</Select.Option>
        <Select.Option value='Disable'>{formatMessage({ id: 'policies.action.disabled' })}</Select.Option>
    </Select>
}

export default StatusSelector;