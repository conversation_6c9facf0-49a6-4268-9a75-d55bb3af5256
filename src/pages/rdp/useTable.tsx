import { useEffect, useState, useContext } from 'react';
import { getFlynet } from '@/services/flynet';
import { flylayerClient } from '@/services/core';
import { Typography, Tag, List, Badge, Popover, Notification, Button, Dropdown, Tooltip, Space, Popconfirm } from '@douyinfe/semi-ui';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import DeviceTag from '@/components/device-tag';
import { IconMore, IconGlobeStroke } from '@douyinfe/semi-icons';
import { LicenseContext } from '@/hooks/useLicense';
import { useNavigate } from 'react-router-dom';
import { SaveMachineRdpSettings } from '@/services/device';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { Timestamp } from "@bufbuild/protobuf";

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { RDPMachine, Machine, MachineGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { useLocale } from '@/locales';
import { caseInsensitiveIncludes, getRadioEntitlementVal } from '@/utils/common';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss';
import { formatDefaultTimestamp, formatIPNVersion } from '@/utils/format';
import { compare, sort } from 'semver';

export type DeviceFilter = {
    keywords: string,
    os: Array<string>,
    connectStatus: 'online' | 'offline' | '',
    group: string,
    meshEnabled: 'enable' | 'disable' | ''
}

const useTable = (initFilter: DeviceFilter) => {
    const { formatMessage } = useLocale();

    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();

    const license = useContext(LicenseContext);
    const entitlementAllowRdp = getRadioEntitlementVal('rdp', license.entitlements);


    const navigate = useNavigate();
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 设备列表
    const [devices, setDevices] = useState<RDPMachine[]>([]);
    // 设备列表
    const [allDevices, setAllDevices] = useState<RDPMachine[]>([]);

    const [originData, setOriginData] = useState<Array<RDPMachine>>([]);
    // 当前页码
    const [page, setPage] = useState(1);

    const pageSize = 20;
    // 服务组列表
    const [groups, setGroups] = useState<MachineGroup[]>([]);
    // 当前选中服务组名称
    const [curGroup, setCurGroup] = useState<MachineGroup>();

    const [_sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [_sortField, setSortField] = useState<string>('');


    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filterParam, setFilterParam] = useState<DeviceFilter>(initFilter);

    const columns = [
        {
            // width: 400,
            title: formatMessage({ id: 'devices.table.device' }),
            dataIndex: 'name',
            sorter: true,
            render: (_: string, record: RDPMachine, _index: number) => {
                // 设备名称
                const name = record.machine?.autoGeneratedName ? record.machine?.name : record.machine?.givenName
                return (
                    <div>
                        <Title heading={5}>
                            {name}
                            <Text size='small' type='tertiary'>&nbsp;{record.machine?.description}</Text>
                            &nbsp;
                            {record.machine?.connected ?
                                <span className='mobile-visible'><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /></span> :
                                <span className='mobile-visible'><Badge dot type='tertiary' /></span>}
                        </Title>
                        {record.machine?.tags && record.machine?.tags.length > 0 ? <div>
                            {record.machine?.tags.map((tag, index) => {
                                return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 5, marginBottom: 5 }}>{tag}</Tag>
                            })}
                        </div> : <Paragraph>{record.machine?.user?.displayName} <Text type="tertiary" className={styles.loginName} size='small' ellipsis={{ showTooltip: true }}>{record.machine?.user?.loginName}</Text></Paragraph>
                        }
                        {record?.machine && <DeviceTag record={record.machine}></DeviceTag>}
                    </div>
                );
            },
        },
        {
            title: formatMessage({ id: 'devices.table.deviceGroup' }),
            width: 300,
            dataIndex: 'machineGroups',
            render: (_field: string, r: RDPMachine) => {
                if (!r.machine) {
                    return <></>
                }
                const record = r.machine;
                return <div><Space style={{ flexWrap: 'wrap' }}>{record.machineGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space></div>

            }
        },
        {
            width: 230,
            title: formatMessage({ id: 'devices.table.ip' }),
            dataIndex: 'ipV4',
            sorter: true,
            render: (_field: string, r: RDPMachine) => {
                if (!r.machine) {
                    return <></>
                }
                const record = r.machine;

                return (<>
                    <div className={styles.ipCopyable} style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>

                        <Popover content={<List
                            bordered>
                            {[record.ipv4, record.ipv6].map((val, index) => {
                                return <List.Item key={index}>
                                    <Paragraph copyable>
                                        {val}</Paragraph>
                                </List.Item>
                            })}

                        </List>
                        }>
                            <Text className={styles.ipLine}>{record.ipv4}</Text>
                        </Popover>
                        <Copyable style={{ lineHeight: 1 }} content={record.ipv4}></Copyable>

                    </div>
                    {record.meshEnabled ?
                        <Tooltip content={formatMessage({ id: 'devices.table.meshEnabledTooltip' })}>

                            <Tag
                                color='green'
                                size='large'
                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}
                            >{formatMessage({ id: 'devices.table.meshEnabled' })}</Tag></Tooltip> :
                        <Tooltip
                            content={formatMessage({ id: 'devices.table.meshDisabledTooltip' })}
                        >
                            <Tag
                                size='large'
                                shape='circle'
                                prefixIcon={<IconGlobeStroke />}>{formatMessage({ id: 'devices.table.meshDisabled' })}</Tag>
                        </Tooltip>}

                </>

                );
            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'devices.table.version' }),
            dataIndex: 'os',
            sorter: true,
            render: (field: string, r: RDPMachine) => {
                if (!r.machine) {
                    return <></>
                }
                const record = r.machine;
            
                return (
                    <div className='layout-left-icon'>
                        <div>
                            <Paragraph>{formatIPNVersion(record.clientVersion)}</Paragraph>
                            <Paragraph>{r.machine.os}</Paragraph>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 200,
            title: '无人值守',
            dataIndex: 'lightsOut',
            sorter: true,
            render: (field: boolean, r: RDPMachine) => {
                if (!r.machine) {
                    return <></>
                }
                
                return (
                    <div className='layout-left-icon'>
                        <div>
                            <Paragraph>{r.lightsOut ? '开启' : '关闭'}</Paragraph>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'devices.table.lastOnlineTime' }),
            dataIndex: 'lastSeen',
            sorter: true,
            render: (_field: Timestamp, r: RDPMachine) => {
                if (!r.machine) {
                    return <></>
                }
                const record = r.machine;

                if (record.connected) {
                    return <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {formatMessage({ id: 'devices.table.online' })}</span>
                }

                if (record.lastSeen) {
                    return <><span><Badge dot type='tertiary' /> {formatDefaultTimestamp(record.lastSeen)}</span></>
                }
                return <></>
            }
        },
        {
            title: '',
            width: 100,
            dataIndex: 'operate',
            render: (_: string, r: RDPMachine) => {
                if (!r.machine) {
                    return <></>
                }
                const record = r.machine;

                let isExpiry = false;
                if (!record.keyExpiryDisabled) {

                    if (record.expiresAt) {
                        if (record.createdAt && record.createdAt.toDate().getTime() > record.expiresAt.toDate().getTime()) {
                            // 创建时间大于过期时间，说明是旧数据，未过期
                        } else {
                            const expiry = record.expiresAt.toDate()
                            const now = new Date()
                            isExpiry = expiry < now;
                        }
                    }
                }
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            {entitlementAllowRdp && (record.os == 'windows' || record.os == 'macOS') ?


                                flynet?.rdpEnabled ? record.user?.rdpEnabled ? record.rdpEnabled ?
                                    <Popconfirm
                                        onConfirm={() => {
                                            SaveMachineRdpSettings(record.id, false).then(() => {
                                                setReloadFlag(true)
                                            })
                                        }}
                                        title="确定要关闭远程桌面吗？"
                                        content="关闭后请在设备中重新开启"
                                    >
                                        <Dropdown.Item>{formatMessage({ id: 'devices.menu.disableRemoteDesktop' })}</Dropdown.Item>
                                    </Popconfirm>
                                    :
                                    <Dropdown.Item
                                        onClick={() => {
                                            SaveMachineRdpSettings(record.id, true).then(() => {
                                                setReloadFlag(true)
                                            })
                                        }}>{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item> :
                                    <Popover zIndex={3001} content={<div className='p10' dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.menu.remoteDesktopUserNotEnabled' }) }} />}>
                                        <Dropdown.Item disabled >{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item>
                                    </Popover> : <Popover zIndex={3001} content={<div className='p10' dangerouslySetInnerHTML={{ __html: formatMessage({ id: 'devices.menu.remoteDesktopNotEnabled' }) }} />}>
                                    <Dropdown.Item disabled >{formatMessage({ id: 'devices.menu.enableRemoteDesktop' })}</Dropdown.Item>
                                </Popover> : ''
                            }

                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];


    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;


        let sortedAllDate = [...allDevices];

        if (sortOrder == 'ascend') {
            setSortOrder('ascend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: RDPMachine, b?: RDPMachine) => (a && b && a.machine && b.machine && a.machine?.name > b.machine?.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((aa?: RDPMachine, bb?: RDPMachine) => {
                    const a = aa?.machine;
                    const b = bb?.machine;
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return 1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return -1;
                            }
                        }
                    }
                    return -1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((aa?: RDPMachine, bb?: RDPMachine) => {

                    const a = aa?.machine;
                    const b = bb?.machine;
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(a.clientVersion.split('-')[0], b.clientVersion.split('-')[0])
                    }
                    return -1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((aa?: RDPMachine, bb?: RDPMachine) => {
                    const a = aa?.machine;
                    const b = bb?.machine;
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return 1;
                    }
                    if (!a.connected && b.connected) {
                        return -1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds > b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return 1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return -1;
                    }
                    return -1;
                });
            }

            if (dataIndex == "lightsOut") {
                sortedAllDate.sort((a?: RDPMachine, b?: RDPMachine) => {
                    
                    if (!a || !b) return -1;
                    if (a.lightsOut && b.lightsOut) {
                        return 1;
                    }
                    if (a.lightsOut && !b.lightsOut) {
                        return 1;
                    }
                    if (!a.lightsOut && b.lightsOut) {
                        return -1;
                    }
                    return -1;
                });
            }
            setAllDevices(sortedAllDate);

            setDevices(doFilter(1, sortedAllDate, filterParam, curGroup))
        } else if (sortOrder == 'descend') {
            setSortOrder('descend');
            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: RDPMachine, b?: RDPMachine) => (a && b && a.machine && b.machine && a.machine.name < b.machine.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((aa?: RDPMachine, bb?: RDPMachine) => {
                    const a = aa?.machine;
                    const b = bb?.machine;
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return -1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return 1;
                            }
                        }
                    }
                    return 1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((aa?: RDPMachine, bb?: RDPMachine) => {
                    const a = aa?.machine;
                    const b = bb?.machine;
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(b.clientVersion.split('-')[0], a.clientVersion.split('-')[0])
                    }
                    return 1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((aa?: RDPMachine, bb?: RDPMachine) => {
                    const a = aa?.machine;
                    const b = bb?.machine;
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return -1;
                    }
                    if (!a.connected && b.connected) {
                        return 1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds < b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return -1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return 1;
                    }
                    return 1;
                });
            }

            if (dataIndex == "lightsOut") {
                sortedAllDate.sort((a?: RDPMachine, b?: RDPMachine) => {
                    
                    if (!a || !b) return -1;
                    if (a.lightsOut && b.lightsOut) {
                        return 1;
                    }
                    if (a.lightsOut && !b.lightsOut) {
                        return -1;
                    }
                    if (!a.lightsOut && b.lightsOut) {
                        return 1;
                    }
                    return 1;
                });
            }

            setAllDevices(sortedAllDate);
            setDevices(doFilter(1, sortedAllDate, filterParam, curGroup))
        } else {
            setSortOrder(undefined)
            setAllDevices(originData)
            setDevices(doFilter(1, originData, filterParam, curGroup))

        }
        setSortField(dataIndex)



    }
    // 过滤结果
    const doFilter = (page: number, src: Array<RDPMachine>, filter: DeviceFilter, group?: MachineGroup): Array<RDPMachine> => {
        if (!src || src.length == 0) {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }

        if (filter.keywords == '' && filter.os.length == 0 && filter.connectStatus == '' && filter.meshEnabled == '' && !group) {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }
        const filteredList = src.filter(r => {
            const record = r.machine;
            if (!record) {
                return false;
            }
            let { keywords: query, os, connectStatus } = filter;
            if (query) query = query.trim();
            let passQuery = true, passOs = true, passConnectStatus = true, passMeshEnabled = true;
            let passGroup = true;
            if (query) {
                var containsTag = false;
                if (record.tags && record.tags.length > 0) {
                    record.tags.forEach(tag => {
                        if (caseInsensitiveIncludes(tag, query)) {
                            containsTag = true;
                        }
                    })
                }

                if (caseInsensitiveIncludes(record.givenName, query) ||
                    record.user && caseInsensitiveIncludes(record.user.displayName, query) ||
                    record.user && caseInsensitiveIncludes(record.user.loginName, query) ||
                    caseInsensitiveIncludes(record.ipv4, query) ||
                    caseInsensitiveIncludes(record.ipv6, query) ||
                    caseInsensitiveIncludes(record.clientVersion, query) ||
                    caseInsensitiveIncludes(record.description, query) ||
                    containsTag
                ) {
                    passQuery = true;
                } else {
                    passQuery = false;
                }
            }

            if (record.meshEnabled && filter.meshEnabled == 'disable') {
                passMeshEnabled = false;
            } else if (!record.meshEnabled && filter.meshEnabled == 'enable') {
                passMeshEnabled = false;
            }

            if (group) {
                let findGroup = false;
                group.machines.forEach((val: Machine) => {
                    if (record.id == val.id) {
                        findGroup = true;
                    }
                })

                if (findGroup) {
                    passGroup = true;
                } else {
                    passGroup = false;
                }
            }



            if (os.length > 0) {
                passOs = os.indexOf(record.os) >= 0;
            }

            if (connectStatus == 'online') {
                passConnectStatus = record.connected;
            } else if (connectStatus == 'offline') {
                passConnectStatus = !record.connected;
            }

            return passQuery && passOs && passConnectStatus && passGroup && passMeshEnabled;
        })

        setTotal(filteredList.length)

        return filteredList.slice(0, page * pageSize)
    }

    const query = async () => {
        const res = await flylayerClient.listMachineGroups({
            flynetId: flynetGeneral.id
        })
        let curGroup: MachineGroup | undefined;
        setGroups(res.groups)
        if (res.groups) {
            res.groups.forEach(g => {
                if (g.id + '' == initFilter.group) {
                    curGroup = g;
                }
            })
        }

        setCurGroup(curGroup)

        setLoading(true)

        flylayerClient.listRDPMachines({
            flynetId: flynetGeneral.id,
            query: ''
        }).then(res => {

            res.machines.sort((aa?: RDPMachine, bb?: RDPMachine) => {
                const a = aa?.machine;
                const b = bb?.machine;
                if (!a || !b) return -1;
                if (!a.authorized && b.authorized) {
                    return -1;
                }
                if (a.authorized && !b.authorized) {
                    return 1;
                }

                return 1;
            });

            setAllDevices(res.machines);
            let copyedData: any = []
            res.machines.forEach(user => {
                copyedData.push({ ...user })
            })
            setOriginData(copyedData);
            setDevices(doFilter(page, res.machines, filterParam, curGroup))
        }).catch(e => {
            console.error(e)
            Notification.error({ content: formatMessage({ id: 'devices.error.getDeviceListFailed' }) })
        }).finally(() => setLoading(false))
    }
    const addPage = () => {
        setDevices(doFilter(page + 1, allDevices, filterParam, curGroup));
        setPage(page + 1)

    }

    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])

    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        if (reloadFlag) {
            setReloadFlag(false)
            setPage(1)
            query()
        }
    }, [reloadFlag])
    useEffect(() => {
        setPage(1)
        setDevices(doFilter(1, allDevices, filterParam))

    }, [filterParam])

    return {
        loading,
        allDevices,
        devices,
        curGroup,
        setCurGroup,
        reloadFlag,
        setReloadFlag,
        filterParam,
        setFilterParam,
        page,
        total,
        addPage,
        pageSize,
        handleSort,
        columns
    }

}

export default useTable;