import React, { FC, useState, useContext } from 'react'
import {
  Select,
  Avatar,
  Typography,
  Modal,
  Steps,
  Space,
  Button,
  Row,
  Col,
  Popover,
  List,
  Divider,
  RadioGroup,
  Radio,
  Notification, Form
} from '@douyinfe/semi-ui'
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'
import {
  IconTick,
  IconPlus,
  IconArrowDown,
  IconArrowUp,
  IconAlignTop, IconAlignBottom,
  IconMinusCircle,
  IconMore
} from '@douyinfe/semi-icons'
import MachineCard from '../../../components/machine-card';
import MachineViewer from '@/components/machine-viewer';
import { Service, ServiceType, ServiceNode, ServiceOrigin, ServiceGroup, ServiceProto, ServicePort, ServiceRouteMode, ServiceNodeType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import styles from './index.module.scss'
import { flylayerClient } from '@/services/core';
import gatewayValidate from '../gateway-validate';
import { IconArrowUpRight } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';


interface Props {
  close: () => void,
  success?: (service?: Service) => void,
  mapService: Map<string, Service>, // 服务map, key为IP + 端口，value为服务
}

import typeRemoteDesktop from '@/assets/remote-desktop.png'
import typeServer from '@/assets/server.png'
import typeService from '@/assets/service.png'

import useServicesGroup from '../useServicesGroup';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import MachineSelector from '@/components/machine-selector'
import { ArrayField, FormApi } from '@douyinfe/semi-ui/lib/es/form'
import { LicenseContext } from '@/hooks/useLicense';
import { getRadioEntitlementVal } from '@/utils/common';



const {
  Title,
  Text
} = Typography

// 远程桌面端口
const RDP_PORT = 21118;

const Index: FC<Props> = (props) => {
  const { formatMessage } = useLocale();

  const flynet = useContext(FlynetGeneralContext);
  const license = useContext(LicenseContext);
  const entitlementAllowRdp = getRadioEntitlementVal('rdp', license.entitlements);

  // 设备节点
  const [machine, setMachine] = useState<Machine>()

  // 远程桌面节点选择
  const [machineSelectorVisible, setMachineSelectorVisible] = useState(false);
  // 网关节点选择
  const [gatewaySelectorVisible, setGatewaySelectorVisible] = useState(false);
  // 直连节点选择
  const [directSelectorVisible, setDirectSelectorVisible] = useState(false);

  // 系统服务模式
  const [daemonMode, setDaemonMode] = useState<ServiceRouteMode>(ServiceRouteMode.DIRECT)


  const [step, setStep] = useState(0)

  const [serviceType, setServiceType] = useState<ServiceType>(ServiceType.SYSTEM_DAEMON)


  const { serviceGroupTreeData } = useServicesGroup();

  const [saveLoading, setSaveLoading] = useState(false);

  // 完成第一步
  const finishStep1 = () => {
    setStep2FinishAble(calStep2FinishAble({
      serviceType,
      daemonMode
    }))
    setStep(1)
  }
  // 完成第二步
  const finishStep2 = async () => {
    if (serviceType == ServiceType.REMOTE_DESKTOP) {
      setStep(2)
      return;
    } else if (serviceType == ServiceType.SYSTEM_DAEMON) {
      await formApi?.validate();
      const values = formApi?.getValues();

      if (daemonMode == ServiceRouteMode.FORWARD) {
        if (values?.gatewayNodes && values?.subnetNodes) {

          gatewayValidate(values?.gatewayNodes, values?.subnetNodes).then(() => {

            setSubnetNodes(values?.subnetNodes ? values?.subnetNodes : [])
            setGatewayNodes(values?.gatewayNodes ? values?.gatewayNodes : [])

            setStep(2)
          }, (unmatchNodes: Array<string>) => {
            Notification.error({
              title: formatMessage({ id: 'services.guide.connectorMismatch' }),
              content: formatMessage({ id: 'services.guide.connectorMismatchContent' }) + unmatchNodes.join(','),
            })
          })
        }
      } else if (daemonMode == ServiceRouteMode.DIRECT) {
        setDirectNodes(values?.directNodes ? values.directNodes : [])
        setStep(2)
      }
      return;
    }

    setStep(2)
  }


  const validateIpPort = (service: Service, mapService: Map<string, Service>): string => {
    let errMsg = '';

    if (service.serviceNodes) {
      service.serviceNodes.forEach((node) => {
        if (node.ipv4 && node.type != ServiceNodeType.GATEWAY && service.ports) {
          service.ports.forEach((port) => {
            let key = node.ipv4 + ':' + port.port;
            if (mapService.has(key)) {
              let s = mapService.get(key);
              const description = s?.description ? `(${s?.description})` : '';
              errMsg += `IP地址${node.ipv4}端口${port.port}已被添加到服务${s?.name}${description};`
            }
          })
        }
      })
    }

    return errMsg
  }

  // 完成创建
  const finishStep3 = async () => {
    if (serviceType == ServiceType.REMOTE_DESKTOP) {
      await rdpFormApi?.validate();

      const values = rdpFormApi?.getValues();
      let serviceGroups: Array<ServiceGroup> = []
      if (values?.serviceGroups) {

        serviceGroups = values?.serviceGroups.map((s, _i) => {
          return new ServiceGroup({
            id: BigInt(s)
          })
        })
      }
      let service = new Service({
        name: values?.name.trim(),
        description: values?.description.trim(),
        type: serviceType,
        origin: ServiceOrigin.SYSTEM_CONFIG,
        routeMode: ServiceRouteMode.DIRECT,
        ports: [new ServicePort({
          name: '',
          description: '',
          port: RDP_PORT,
          proto: ServiceProto.TCP
        })],
        serviceNodes: [
          new ServiceNode({
            name: machine?.name,
            ipv4: machine?.ipv4,
            rank: 1,
            machine: machine,
            type: ServiceNodeType.VIRTUAL
          })
        ],
        serviceGroups: serviceGroups
      });

      let errIpMsg = validateIpPort(service, props.mapService);
      if (errIpMsg) {
        Notification.error({
          title: formatMessage({ id: 'services.guide.ipPortOccupied' }),
          content: errIpMsg
        })
        return;
      }

      setSaveLoading(true);
      flylayerClient.createService({
        service: service,
        flynetId: flynet.id,
      }).then((_res) => {
        Notification.success({
          title: formatMessage({ id: 'services.create.success' }),
          content: formatMessage({ id: 'services.create.success' })
        });
        props.success && props.success(service);
      }).catch((err) => {
        Notification.error({
          title: formatMessage({ id: 'services.create.failed' }),
          content: err.message
        })
      }).finally(() => {
        setSaveLoading(false);
      })
    }
    else if (serviceType == ServiceType.SYSTEM_DAEMON) {
      await daemonFormApi?.validate();
      const values = daemonFormApi?.getValues();
      if (!values?.ports || values.ports.length == 0) {
        Notification.error({
          content: '协议不能为空'
        })
        return;
      }

      let ports: Array<ServicePort> = []
      values?.ports.forEach((item) => {
        ports.push(new ServicePort({
          name: item.name.trim(),
          description: item.description.trim(),
          port: Number(item.port),
          proto: item.proto
        }))
      })


      let serviceGroups: ServiceGroup[] = [];
      if (values && values.serviceGroups) {
        serviceGroups = values?.serviceGroups.map((s, _i) => {
          return new ServiceGroup({
            id: BigInt(s)
          })
        })
      }

      var serviceNodes: Array<ServiceNode> = [];
      if (daemonMode == ServiceRouteMode.FORWARD) {
        gatewayNodes?.forEach((item, index) => {
          item.rank = index + 1;
          serviceNodes.push(item)
        })

        subnetNodes?.forEach((item, index) => {
          item.rank = index + 1;
          item.name = item.name.trim();
          item.ipv4 = item.ipv4.trim();
          serviceNodes.push(item)
        })

      } else if (daemonMode == ServiceRouteMode.DIRECT) {
        directNodes?.forEach((item, index) => {
          item.rank = index + 1;
          serviceNodes.push(item)
        })
      }
      let service = new Service({
        name: values?.name.trim(),
        description: values?.description.trim(),
        type: serviceType,
        origin: ServiceOrigin.SYSTEM_CONFIG,
        ports: ports,
        routeMode: daemonMode,
        serviceNodes: serviceNodes,
        serviceGroups: serviceGroups
      });
      let errIpMsg = validateIpPort(service, props.mapService);
      if (errIpMsg) {
        Notification.error({
          title: formatMessage({ id: 'services.guide.ipPortOccupied' }),
          content: errIpMsg
        })
        return;
      }
      setSaveLoading(true);
      flylayerClient.createService({
        service: service,
        flynetId: flynet.id,
      }).then((_res) => {
        Notification.success({
          title: formatMessage({ id: 'services.create.success' }),
          content: formatMessage({ id: 'services.create.success' })
        });
        props.success && props.success(service);
      }).catch((err) => {
        Notification.error({
          title: formatMessage({ id: 'services.create.failed' }),
          content: err.message
        })
      }).finally(() => {
        setSaveLoading(false);
      })
    }

  }

  // 从第三步返回第二步,保存数据
  const backStep2 = () => {
    if (serviceType == ServiceType.REMOTE_DESKTOP) {
      setRdpName(rdpFormApi?.getValue('name') as string)
      setRdpDescription(rdpFormApi?.getValue('description') as string)
      setRdpServiceGroups(rdpFormApi?.getValue('serviceGroups') as Array<string>)
    } else if (serviceType == ServiceType.SYSTEM_DAEMON) {
      setDaemonName(daemonFormApi?.getValue('name') as string)
      setDaemonDescription(daemonFormApi?.getValue('description') as string)
      setDaemonServiceGroups(daemonFormApi?.getValue('serviceGroups') as Array<string>)


      let ports: Array<ServicePort> = []
      daemonFormApi?.getValues()?.ports.forEach((item) => {
        ports.push(new ServicePort({
          name: item.name,
          description: item.description,
          port: Number(item.port),
          proto: item.proto
        }))
      })

      setDaemonServicePorts(ports)
    }


    setStep(1)
  }

  const [step2FinishAble, setStep2FinishAble] = useState(false);

  const calStep2FinishAble = (opts: {
    serviceType: ServiceType,
    daemonMode: ServiceRouteMode
  }) => {
    const { serviceType, daemonMode } = opts;
    if (serviceType == ServiceType.REMOTE_DESKTOP) {
      return machine != undefined
    } else if (serviceType == ServiceType.SYSTEM_DAEMON) {
      const values = formApi?.getValues();
      if (values) {
        if (daemonMode == ServiceRouteMode.DIRECT) {
          return values?.directNodes?.length > 0
        } else if (daemonMode == ServiceRouteMode.FORWARD) {
          return values.gatewayNodes?.length > 0 && values.subnetNodes?.length > 0
        }
      }
      return false
    }
    return false
  }


  const data = [
    {
      title: formatMessage({ id: 'services.guide.remoteDesktop.title' }),
      content: formatMessage({ id: 'services.guide.remoteDesktop.content' }),
      value: ServiceType.REMOTE_DESKTOP,
      icon: typeRemoteDesktop,
      disabled: entitlementAllowRdp ? false : true
    }, {
      title: formatMessage({ id: 'services.guide.systemService.title' }),
      content: formatMessage({ id: 'services.guide.systemService.content' }),
      value: ServiceType.SYSTEM_DAEMON,
      icon: typeService,
      disabled: false
    }, {
      title: formatMessage({ id: 'services.guide.webApp.title' }),
      content: formatMessage({ id: 'services.guide.webApp.content' }),
      value: ServiceType.WEB_APP,
      icon: typeServer,
      disabled: true
    },
  ]

  // 直连节点
  const [directNodes, setDirectNodes] = useState<Array<ServiceNode>>([]);
  // 网关节点
  const [gatewayNodes, setGatewayNodes] = useState<Array<ServiceNode>>([])
  // 子网节点
  const [subnetNodes, setSubnetNodes] = useState<Array<ServiceNode>>([new ServiceNode({
    name: '',
    ipv4: '',
    rank: 1,
    machine: new Machine({ id: BigInt(0) }),
    type: ServiceNodeType.SUBNET
  })])


  const [formApi, SetFormApi] = useState<FormApi<{
    directNodes: Array<ServiceNode>,
    gatewayNodes: Array<ServiceNode>,
    subnetNodes: Array<ServiceNode>
  }>>()

  const [rdpFormApi, SetRdpFormApi] = useState<FormApi<{
    name: string,
    description: string,
    serviceGroups: Array<string>
  }>>();
  const [daemonFormApi, SetDaemonFormApi] = useState<FormApi<{
    name: string,
    description: string,
    serviceGroups: Array<string>,
    proto: ServiceProto,
    ports: Array<ServicePort>,
  }>>();

  // 从设备添加网关结点
  const addGatewayNodeFromMachine = () => {
    setGatewaySelectorVisible(true)
  }

  // 直接添加子网结点
  const addSubnetNodeDirect = () => {

    let newList: Array<ServiceNode> = []

    formApi?.getValue('subnetNodes')?.forEach((item: ServiceNode) => {
      newList.push(item)
    })
    newList.push(new ServiceNode({
      name: '',
      ipv4: '',
      rank: newList.length,
      machine: new Machine({ id: BigInt(0) }),
      type: ServiceNodeType.SUBNET
    }))

    formApi?.setValue('subnetNodes', newList);
    setStep2FinishAble(calStep2FinishAble({
      serviceType,
      daemonMode
    }))
  }
  // 从设备添加子网结点
  const addSubnetNodeFromMachine = () => {
    setDirectSelectorVisible(true)
  }

  const [gatewayIp, setGatewayIp] = useState('');


  const [gatewayNodeViewerVisible, setGatewayNodeViewerVisible] = useState(false);
  const viewSubnet = (index: number) => {

    var gatewayNode = formApi?.getValues().gatewayNodes[index];

    if (gatewayNode) {
      if (!gatewayNode.ipv4) {

        Notification.error({ content: 'IP地址为空' })
        return;
      } else {
        setGatewayIp(gatewayNode.ipv4);
        setGatewayNodeViewerVisible(true);
      }
    }
  }
  const [directNodeViewerVisible, setDirectNodeViewerVisible] = useState(false);
  const viewDirectNode = (index: number) => {
    var subnetNode = formApi?.getValues().directNodes[index];
    if (subnetNode) {
      if (!subnetNode.ipv4) {
        Notification.error({ content: 'IP地址为空' })
        return;
      } else {
        setGatewayIp(subnetNode.ipv4);
        setDirectNodeViewerVisible(true)
      }
    }
  }

  const [rdpName, setRdpName] = useState('');
  const [rdpDescription, setRdpDescription] = useState('');
  const [rdpServiceGroups, setRdpServiceGroups] = useState<Array<string>>([]);

  const [daemonName, setDaemonName] = useState('');
  const [daemonDescription, setDaemonDescription] = useState('');
  const [daemonServiceGroups, setDaemonServiceGroups] = useState<Array<string>>([]);
  const [daemonServicePorts, setDaemonServicePorts] = useState<Array<ServicePort>>([new ServicePort({
    proto: ServiceProto.TCP,
    port: 80,
  })]);


  return <>
    <Modal
      width={830}
      title="添加服务"
      visible={true}
      onOk={() => {
      }}
      onCancel={props.close}
      footer={null}
      maskClosable={false}
      className={styles.addServiceModal}
    >
      <Steps type="basic" className="mb20" current={step} onChange={(i) => console.log(i)}>
        <Steps.Step title="服务类型" description="选择需要新建的服务类型"></Steps.Step>
        <Steps.Step title="服务节点" description="选择服务的节点"></Steps.Step>
        <Steps.Step title="完成" description="完成服务创建"></Steps.Step>
      </Steps>
      {step === 0 && <div>
        <List
          className="mb40 guide-list"
          style={{ width: '100%' }}
          dataSource={data}
          layout="horizontal"
          renderItem={item => (
            <List.Item
              className={item.disabled ? styles.typeItemDisabled : serviceType === item.value ? styles.typeItemActive + ' ' + styles.typeItem : styles.typeItem}
              onClick={() => {
                if (item.disabled) {
                  return
                }
                setServiceType(item.value as any)
              }}
              main={
                <div className={styles.typeItemContent}>
                  {serviceType === item.value ? <IconTick className={styles.iconTick} /> : null}
                  <Avatar size="extra-large" shape="square" src={item.icon}></Avatar>
                  <div>
                    <Title heading={4} className="mb20">
                      {item.title} {item.disabled &&
                        <Text type="tertiary" style={{ fontWeight: 'normal' }}>(即将上线)</Text>}</Title>
                    <p style={{
                      color: 'var(--semi-color-text-2)',
                      margin: '4px 0'
                    }}>
                      {item.content}
                    </p>
                  </div>
                </div>
              }
            />
          )}
        >

        </List>
        <Space>
          <Button onClick={() => props.close()}>取消</Button>
          <Button theme="solid" disabled={serviceType == ServiceType.UNKNOWN_TYPE} onClick={() => finishStep1()}>下一步</Button>
        </Space>
      </div>}
      {step === 1 && <div>
        {serviceType == ServiceType.REMOTE_DESKTOP && <div className={styles.rdpContainer}>
          <div style={{ paddingTop: 20 }}>
            {machine ?
              <MachineCard
                onClear={() => {
                  setMachine(undefined)
                  setStep2FinishAble(false)
                }}
                machine={machine}></MachineCard>
              : <Button
                theme="solid"
                iconPosition="right"
                onClick={() => {
                  setMachineSelectorVisible(true)
                }}
              >请选择设备</Button>
            }
          </div>
        </div>}
        {serviceType == ServiceType.SYSTEM_DAEMON && <div className={styles.daemonContainer}>
          <Form
            getFormApi={SetFormApi}
            allowEmpty
            initValues={{
              gatewayNodes: gatewayNodes,
              subnetNodes: subnetNodes,
              directNodes: directNodes,
            }}
            style={{ width: '100%' }}
          >
            <div style={{
              paddingTop: 20,
              width: '100%'
            }}>
              <Space className="mb10">
                <RadioGroup name="mode" value={daemonMode} onChange={(e) => {
                  let newDaemonMode: ServiceRouteMode = e.target.value;
                  if (daemonMode == ServiceRouteMode.DIRECT && newDaemonMode == ServiceRouteMode.FORWARD) {
                    setDirectNodes(formApi?.getValue('directNodes') as any)
                    formApi?.setValue('gatewayNodes', gatewayNodes)
                    formApi?.setValue('subnetNodes', subnetNodes)
                  } else if (daemonMode == ServiceRouteMode.FORWARD && newDaemonMode == ServiceRouteMode.DIRECT) {
                    setGatewayNodes(formApi?.getValue('gatewayNodes') as any)
                    setSubnetNodes(formApi?.getValue('subnetNodes') as any)
                    formApi?.setValue('directNodes', directNodes)
                  }
                  setDaemonMode(newDaemonMode)
                  setStep2FinishAble(calStep2FinishAble({
                    serviceType,
                    daemonMode: newDaemonMode
                  }))
                }} direction="horizontal">
                  <Radio checked={daemonMode == ServiceRouteMode.DIRECT} value={ServiceRouteMode.DIRECT}>直连模式</Radio>
                  <Radio checked={daemonMode == ServiceRouteMode.FORWARD} value={ServiceRouteMode.FORWARD}>路由模式</Radio>
                </RadioGroup>
              </Space>
              <Divider className="mb10" />

              {daemonMode == ServiceRouteMode.FORWARD && <>

                <ArrayField field='gatewayNodes'>
                  {({
                    add: _add,
                    arrayFields
                  }) => <React.Fragment>
                      <Row className="mb20">
                        <Col span={20}>
                          <Title heading={6} className="mb10">连接器</Title>
                        </Col>
                        <Col span={4} className={styles.rightColumn}>
                          {arrayFields.length > 0 &&
                            <Button
                              onClick={addGatewayNodeFromMachine}
                              icon={<IconPlus />}
                            ></Button>
                          }

                        </Col>
                      </Row>
                      {arrayFields.length > 0 ? <>
                        <Row className={styles.tableTitle} >
                          <Col span={10}>名称</Col>
                          <Col span={9}>地址</Col>
                          <Col span={3}>&nbsp;</Col>
                          <Col span={2}>&nbsp;</Col>
                        </Row>
                        {arrayFields.map(({ field, key, remove }, i) => (
                          <Row className={styles.tableBody} key={key} >
                            <Col xs={24} sm={10}>
                              <Form.Input
                                readonly
                                field={`${field}[name]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '名称不能为空';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            <Col xs={24} sm={9}>
                              <Form.Input
                                readonly
                                field={`${field}[ipv4]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '地址不能为空';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            <Col xs={24} sm={3}>
                              {/* <a className='link-external' target='_blank' href={getGatewayNodeUrl(i)} onClick={(e) => { e.stopPropagation() }}>
                                <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }}></Button>
                              </a> */}
                              <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }} onClick={() => viewSubnet(i)}></Button>
                            </Col>
                            <Col xs={24} sm={2} className={styles.rightColumn}>
                              <Popover
                                position='left'

                                style={{
                                  padding: 5,
                                }}
                                content={<>
                                  <Space >
                                    <Button icon={<IconAlignTop />}
                                      disabled={i == 0}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let gatewayNodes = formApi?.getValue('gatewayNodes');
                                        gatewayNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i - 1];
                                        newList[i - 1] = temp;
                                        formApi?.setValue('gatewayNodes', newList);
                                        setGatewayNodes(newList)
                                      }}
                                    ></Button>
                                    <Button icon={<IconArrowUp />}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let gatewayNodes = formApi?.getValue('gatewayNodes');
                                        gatewayNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i - 1];
                                        newList[i - 1] = temp;
                                        formApi?.setValue('gatewayNodes', newList);
                                        setGatewayNodes(newList)
                                      }}

                                      disabled={i == 0}
                                    ></Button>
                                    <Button
                                      icon={<IconArrowDown />}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let gatewayNodes = formApi?.getValue('gatewayNodes');
                                        gatewayNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i + 1];
                                        newList[i + 1] = temp;
                                        formApi?.setValue('gatewayNodes', newList);
                                        setGatewayNodes(newList)
                                      }}
                                      disabled={i == arrayFields.length - 1}
                                    ></Button>
                                    <Button icon={<IconAlignBottom />} disabled={i == arrayFields.length - 1}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let gatewayNodes = formApi?.getValue('gatewayNodes');
                                        gatewayNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i + 1];
                                        newList[i + 1] = temp;
                                        formApi?.setValue('gatewayNodes', newList);
                                        setGatewayNodes(newList)
                                      }}
                                    ></Button>
                                    <Button
                                      type='danger'
                                      theme='borderless'
                                      icon={<IconMinusCircle />}
                                      onClick={() => {
                                        remove()
                                        setGatewayNodes(formApi?.getValue('gatewayNodes') as any)
                                      }}

                                    />
                                  </Space>
                                </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover>


                            </Col>

                          </Row>
                        ))}</> : <div className={styles.addCenter}>
                        <Button
                          size='large'
                          onClick={addGatewayNodeFromMachine}
                          icon={<IconPlus />}
                        ></Button>
                      </div>}

                    </React.Fragment>}

                </ArrayField>

                <Divider className="mb20" />
                <ArrayField field='subnetNodes'>
                  {({
                    add: _add,
                    arrayFields
                  }) => <React.Fragment>

                      <Row className="mb20">
                        <Col span={20}>
                          <Title heading={6} className="mb10">子网节点</Title>
                        </Col>
                        {/* <Col span={4} className={styles.rightColumn}>
                          {arrayFields.length > 0 ? <Button icon={<IconPlus />} onClick={addSubnetNodeDirect}></Button> : ''}
                        </Col> */}
                      </Row>
                      {arrayFields.length > 0 ? <>
                        <Row className={styles.tableTitle} >
                          <Col span={12}>名称</Col>
                          <Col span={12}>地址</Col>
                          {/* <Col span={3}>端口</Col> */}
                          {/* <Col span={2}>&nbsp;</Col> */}
                        </Row>
                        {arrayFields.map(({ field, key, remove: _remove }, _i) => (
                          <Row className={styles.tableBody} key={key} >
                            <Col xs={24} sm={12}>
                              <Form.Input
                                field={`${field}[name]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '名称不能为空';
                                  }

                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            <Col xs={24} sm={12}>
                              <Form.Input
                                field={`${field}[ipv4]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '地址不能为空';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            {/* <Col xs={24} sm={3}>
                              <Form.Input
                                field={`${field}[port]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '端口不能为空';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col> */}
                            {/* <Col xs={24} sm={2} className={styles.rightColumn}>
                              <Popover
                                position='left'

                                style={{
                                  padding: 5,
                                }}
                                content={<>
                                  <Space >
                                    <Button
                                      icon={<IconArrowDown />}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let subnetNodes = formApi?.getValue('subnetNodes');
                                        subnetNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i + 1];
                                        newList[i + 1] = temp;
                                        formApi?.setValue('subnetNodes', newList);
                                        setSubnetNodes(newList)
                                      }}
                                      disabled={i == arrayFields.length - 1}
                                    ></Button>
                                    <Button icon={<IconArrowUp />}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let subnetNodes = formApi?.getValue('subnetNodes');
                                        subnetNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i - 1];
                                        newList[i - 1] = temp;
                                        formApi?.setValue('subnetNodes', newList);
                                        setSubnetNodes(newList)
                                      }}
                                      disabled={i == 0}
                                    ></Button>
                                    <Button
                                      type='danger'
                                      theme='borderless'
                                      icon={<IconMinusCircle />}
                                      onClick={() => {
                                        remove()
                                        setSubnetNodes(formApi?.getValue('subnetNodes') as any)
                                      }}

                                    />
                                  </Space>
                                </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover>


                            </Col> */}

                          </Row>
                        ))}
                      </> :
                        <div className={styles.addCenter}>
                          <Button
                            size='large'
                            onClick={addSubnetNodeDirect}
                            icon={<IconPlus />}
                          ></Button>
                        </div>}

                    </React.Fragment>}

                </ArrayField>
              </>}
              {daemonMode == ServiceRouteMode.DIRECT && <>

                <ArrayField field='directNodes'>
                  {({
                    add: _add,
                    arrayFields
                  }) => <React.Fragment>

                      <Row className="mb20">
                        <Col span={20}>
                          <Title heading={6} className="mb10">节点</Title>
                        </Col>
                        {/* <Col span={4} className={styles.rightColumn}>
                          {arrayFields.length > 0 ?
                            <Button icon={<IconPlus />} onClick={addSubnetNodeFromMachine}></Button> : ''
                          }

                        </Col> */}
                      </Row>
                      {arrayFields.length > 0 ? <>
                        <Row className={styles.tableTitle} >
                          <Col span={10}>名称</Col>
                          <Col span={9}>地址</Col>
                          <Col span={3}>&nbsp;</Col>
                          {/* <Col span={3}>端口</Col> */}
                          <Col span={2}>&nbsp;</Col>
                        </Row>
                        {arrayFields.map(({ field, key, remove }, i) => (
                          <Row className={styles.tableBody} key={key} >
                            <Col xs={24} sm={10}>
                              <Form.Input
                                field={`${field}[name]`}
                                readonly
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '名称不能为空';
                                  }

                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            <Col xs={24} sm={9}>
                              <Form.Input
                                field={`${field}[ipv4]`}
                                noLabel
                                readonly
                                validate={value => {
                                  if (!value) {
                                    return '地址不能为空';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            <Col xs={24} sm={3}>
                              {/* <a className='link-external' target='_blank' href={getDirectNodeUrl(i)} onClick={(e) => { e.stopPropagation() }}>
                                <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }}></Button>
                              </a> */}
                              <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }} onClick={() => viewDirectNode(i)}></Button>

                            </Col>
                            {/* <Col xs={24} sm={3}>
                              <Form.Input
                                field={`${field}[port]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '端口不能为空';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col> */}
                            <Col xs={24} sm={2} className={styles.rightColumn}>
                              {/* <Popover
                                position='left'

                                style={{
                                  padding: 5,
                                }}
                                content={<>
                                  <Space >
                                    <Button
                                      icon={<IconArrowDown />}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let directNodes = formApi?.getValue('directNodes');
                                        directNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i + 1];
                                        newList[i + 1] = temp;
                                        formApi?.setValue('directNodes', newList);
                                        setDirectNodes(newList)
                                      }}
                                      disabled={i == arrayFields.length - 1}
                                    ></Button>
                                    <Button icon={<IconArrowUp />}
                                      onClick={() => {
                                        let newList: Array<ServiceNode> = []
                                        let directNodes = formApi?.getValue('directNodes');
                                        directNodes?.forEach((item: ServiceNode) => {
                                          newList.push(item)
                                        })
                                        let temp = newList[i];
                                        newList[i] = newList[i - 1];
                                        newList[i - 1] = temp;
                                        formApi?.setValue('directNodes', newList);
                                        setDirectNodes(newList)
                                      }}
                                      disabled={i == 0}
                                    ></Button>
                                    <Button
                                      type='danger'
                                      theme='borderless'
                                      icon={<IconMinusCircle />}
                                      onClick={() => {
                                        remove()
                                        setDirectNodes(formApi?.getValue('directNodes') as any)
                                      }}

                                    />
                                  </Space>
                                </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover> */}

                              <Button style={{ marginTop: 12 }}
                                type='danger'
                                theme='borderless'
                                icon={<IconMinusCircle />}
                                onClick={() => {
                                  remove()
                                  setDirectNodes(formApi?.getValue('directNodes') as any)
                                }}

                              />

                            </Col>

                          </Row>
                        ))}
                      </> :
                        <div className={styles.addCenter}>
                          <Button
                            size='large'
                            onClick={addSubnetNodeFromMachine}
                            icon={<IconPlus />} />
                        </div>}
                    </React.Fragment>}
                </ArrayField>
              </>}

            </div>

          </Form>
        </div>

        }
        <Space>
          <Button onClick={() => setStep(0)}>上一步</Button>
          <Button theme="solid" disabled={!step2FinishAble} onClick={() => finishStep2()}>下一步</Button>
        </Space>
      </div>}
      {step === 2 && <div>
        {serviceType == ServiceType.REMOTE_DESKTOP &&
          <Form
            getFormApi={SetRdpFormApi}
            style={{ width: '100%' }}
            initValues={{
              serviceGroups: rdpServiceGroups,
              name: rdpName,
              description: rdpDescription
            }}
          >
            <div className={styles.rdpContainer}>
              <div style={{ paddingTop: 20, width: "100%" }}>
                <Row className="mb20">
                  <Col span={24}>
                    <Title heading={6} className="mb10">服务名称</Title>
                  </Col>
                  <Col span={24}>
                    <Form.Input field='name'
                      validate={value => {
                        if (!value.trim()) {
                          return '服务名称不能为空';
                        }

                        if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                          return "名称只能包含字母、数字和'-'";
                        }
                        return '';
                      }}
                      noLabel required trigger={'blur'} />
                  </Col>
                </Row>
                <Row className="mb20">
                  <Col span={24}>
                    <Title heading={6} className="mb10">备注</Title>
                  </Col>
                  <Col span={24}>
                    <Form.Input noLabel field='description' />
                  </Col>
                </Row>
                <Row className="mb20">
                  <Col span={24}>
                    <Title heading={6} className="mb10">服务组</Title>
                  </Col>
                  <Col span={24}>
                    <Form.TreeSelect
                      multiple
                      expandAll
                      checkRelation='unRelated'
                      field='serviceGroups' noLabel style={{ width: '100%' }}
                      treeData={
                        serviceGroupTreeData
                      }
                      filterTreeNode
                      showFilteredOnly
                      dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    ></Form.TreeSelect>
                  </Col>
                </Row>
              </div>
            </div></Form>}
        {serviceType == ServiceType.SYSTEM_DAEMON && <Form
          getFormApi={SetDaemonFormApi}
          allowEmpty
          initValues={{
            name: daemonName,
            description: daemonDescription,
            serviceGroups: daemonServiceGroups,
            ports: daemonServicePorts,
          }}
          style={{ width: '100%' }}
        ><div className={styles.rdpContainer}>
            <div style={{ paddingTop: 20, width: "100%" }}>
              <Row className="mb20">
                <Col span={24}>
                  <Title heading={6} className="mb10">服务名称</Title>
                </Col>
                <Col span={24}>
                  <Form.Input field='name'
                    validate={value => {
                      if (!value.trim()) {
                        return '服务名称不能为空';
                      }
                      if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                        return "名称只能包含字母、数字和'-'";
                      }
                      return '';
                    }}
                    noLabel required />
                </Col>
              </Row>
              <Row className="mb20">
                <Col span={24}>
                  <Title heading={6} className="mb10">备注</Title>
                </Col>
                <Col span={24}>
                  <Form.Input noLabel field='description' required />
                </Col>
              </Row>
              <Row className="mb20">
                <Col span={24}>
                  <Title heading={6} className="mb10">服务组</Title>
                </Col>
                <Col span={24}>
                  <Form.TreeSelect field='serviceGroups'
                    multiple
                    expandAll
                    checkRelation='unRelated'
                    noLabel style={{ width: '100%' }}
                    treeData={
                      serviceGroupTreeData
                    }
                    filterTreeNode
                    showFilteredOnly
                    dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                  ></Form.TreeSelect>
                </Col>
              </Row>

              <ArrayField field='ports'>
                {({
                  add,
                  arrayFields
                }) => <React.Fragment>
                    <Row>
                      <Col span={20}>
                        <Title heading={6} className="mb10">协议/端口</Title>
                      </Col>
                      {/* <Col span={4} className={styles.rightColumn}>
                                                            {arrayFields.length > 0 && <Button icon={<IconPlus />} onClick={() => {
                                                                add()
                                                            }}></Button>}
                                                        </Col> */}
                    </Row>
                    {arrayFields.length > 0 ?
                      <>
                        <Row className={styles.tableTitle} >
                          <Col span={7}>名称</Col>
                          <Col span={9}>描述</Col>
                          <Col span={4}>协议</Col>
                          <Col span={4}>端口</Col>
                          {/* <Col span={2}>&nbsp;</Col> */}
                        </Row>
                        {arrayFields.map(({ field, key: _key, remove: _remove }, i) => (
                          <Row className={styles.tableBody} key={i}>
                            <Col xs={24} sm={7}>
                              <Form.Input
                                field={`${field}[name]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '名称不能为空';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            <Col xs={24} sm={9}>
                              <Form.Input
                                field={`${field}[description]`}
                                noLabel
                              >
                              </Form.Input>
                            </Col>
                            <Col xs={24} sm={4}>
                              <Form.Select
                                field={`${field}[proto]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '协议不能为空';
                                  }
                                  return '';
                                }}
                                style={{ width: '100%' }}
                              >
                                <Select.Option value={ServiceProto.TCP}>TCP</Select.Option>
                                <Select.Option value={ServiceProto.UDP}>UDP</Select.Option>
                              </Form.Select>
                            </Col>
                            <Col xs={24} sm={4}>
                              <Form.Input
                                field={`${field}[port]`}
                                noLabel
                                validate={value => {
                                  if (!value) {
                                    return '端口不能为空';
                                  }
                                  // 只能为端口号
                                  if (!/^\d+$/.test(value)) {
                                    return '端口号只能为数字';
                                  }
                                  return '';
                                }}
                              >
                              </Form.Input>
                            </Col>
                            {/* <Col xs={24} sm={2} className={styles.rightColumn}>
                              <Popover
                                position='left'
                                style={{
                                  padding: 5,
                                }}
                                content={<>
                                  <Space >
                                    <Button icon={<IconArrowDown />} onClick={() => {
                                      let newList: Array<ServicePort> = []
                                      let ports = daemonFormApi?.getValue('ports');
                                      ports?.forEach((item: ServicePort) => {
                                        newList.push(item)
                                      })
                                      let item = newList[i];
                                      newList[i] = newList[i + 1];
                                      newList[i + 1] = item;
                                      daemonFormApi?.setValue('ports', newList);
                                    }}
                                      disabled={i == arrayFields.length - 1}
                                    ></Button>
                                    <Button icon={<IconArrowUp />} onClick={() => {
                                      let newList: Array<ServicePort> = []
                                      let ports = daemonFormApi?.getValue('ports');
                                      ports?.forEach((item: ServicePort) => {
                                        newList.push(item)
                                      })
                                      let item = newList[i];
                                      newList[i] = newList[i - 1];
                                      newList[i - 1] = item;
                                      daemonFormApi?.setValue('ports', newList);
                                    }
                                    }
                                      disabled={i == 0}
                                    ></Button>
                                    <Button
                                      type='danger'
                                      theme='borderless'
                                      icon={<IconMinusCircle />}
                                      onClick={() => {
                                        remove()
                                      }}

                                    />
                                  </Space>
                                </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover>
                            </Col> */}
                          </Row>

                        ))}
                      </> :
                      <>
                        <div className={styles.addCenter}>
                          <Button
                            size='large'
                            icon={<IconPlus />}
                            onClick={() => {
                              add()
                            }}
                          ></Button>
                        </div>

                      </>}
                  </React.Fragment>}
              </ArrayField>

            </div>
          </div></Form>}
        <Space>
          <Button onClick={() => backStep2()}>上一步</Button>
          <Button theme="solid" loading={saveLoading} onClick={finishStep3}>确定</Button>
        </Space>
      </div>}
    </Modal>


    {
      // 选择远程桌面
      machineSelectorVisible && <MachineSelector
        multi={false}
        value={machine}
        avilibleOs={['windows', 'macOS']}
        onChange={(value => {
          setMachineSelectorVisible(false)
          setMachine(value as Machine)
          setStep2FinishAble(true)
        })}
        close={() => setMachineSelectorVisible(false)}
      ></MachineSelector>}

    {directSelectorVisible && <MachineSelector
      multi={false}
      value={directNodes.map(node => node.ipv4)}
      onChange={(value => {
        let newList: Array<ServiceNode> = []
        let directNodes = formApi?.getValue('directNodes');
        directNodes?.forEach((item: ServiceNode) => {
          newList.push(item)
        })
        if (value instanceof Machine) {
          let exist = false;
          directNodes?.forEach(node => {
            if (node.ipv4 == value?.ipv4) {
              exist = true;
            }
          })

          if (!exist) {
            newList.push(new ServiceNode({
              ipv4: value.ipv4,
              ipv6: value.ipv6,
              machine: value,
              name: value.name,
              type: ServiceNodeType.SUBNET
            }))

          }
        } else {
          value.forEach((item) => {
            let exist = false;
            directNodes?.forEach(node => {
              if (node.ipv4 == item?.ipv4) {
                exist = true;
              }
            })
            if (!exist) {

              newList.push(new ServiceNode({
                ipv4: item.ipv4,
                ipv6: item.ipv6,
                machine: item,
                name: item.name,
                type: ServiceNodeType.SUBNET
              }))
            }
          })
        }
        formApi?.setValue('directNodes', newList);
        setDirectNodes(newList)
        setDirectSelectorVisible(false)
        setStep2FinishAble(calStep2FinishAble({
          serviceType,
          daemonMode
        }))
      })}
      close={() => setDirectSelectorVisible(false)}
    ></MachineSelector>}


    {gatewaySelectorVisible && <MachineSelector
      multi={true}
      value={gatewayNodes.map(node => node.ipv4)}
      gateway={true}
      onChange={(value => {
        setGatewaySelectorVisible(false)

        let newList: Array<ServiceNode> = []
        let subnetNodes = formApi?.getValue('gatewayNodes');
        subnetNodes?.forEach((item: ServiceNode) => {
          newList.push(item)
        })
        if (value instanceof Machine) {

          let exist = false;
          subnetNodes?.forEach(node => {
            if (node.ipv4 == value?.ipv4) {
              exist = true;
            }
          })
          if (!exist) {
            newList.push(new ServiceNode({
              ipv4: value.ipv4,
              ipv6: value.ipv6,
              machine: value,
              name: value.name,
              type: ServiceNodeType.GATEWAY
            }))
          }
        } else {
          value.forEach((item) => {

            let exist = false;
            subnetNodes?.forEach(node => {
              if (node.ipv4 == item?.ipv4) {
                exist = true;
              }
            })
            if (!exist) {
              newList.push(new ServiceNode({
                ipv4: item.ipv4,
                ipv6: item.ipv6,
                machine: item,
                name: item.name,
                type: ServiceNodeType.GATEWAY
              }))
            }
          })
        }
        formApi?.setValue('gatewayNodes', newList);
        setGatewayNodes(newList)
        setStep2FinishAble(calStep2FinishAble({
          serviceType,
          daemonMode
        }))
      })}
      close={() => setGatewaySelectorVisible(false)}
    ></MachineSelector>}
    {gatewayNodeViewerVisible && gatewayIp && <MachineViewer ip={gatewayIp} close={() => setGatewayNodeViewerVisible(false)}></MachineViewer>}

    {directNodeViewerVisible && gatewayIp && <MachineViewer ip={gatewayIp} close={() => setDirectNodeViewerVisible(false)}></MachineViewer>}
  </>
}

export default Index

