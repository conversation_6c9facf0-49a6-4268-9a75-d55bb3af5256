import React, { FC, useContext, useEffect, useState } from 'react'
import { Breadcrumb, Row, Col, Button, Typography, Tag, Notification, Dropdown, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List } from '@douyinfe/semi-ui';

import { useNavigate, useParams } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { Service, ServiceNode, ServiceNodeType, ServiceGroup, ServiceType, ServiceOrigin, ServiceProto, ServiceRouteMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import MachineCard from '../../../components/machine-card';
import TableEmpty from '@/components/table-empty';
import typeRemoteDesktop from '@/assets/remote-desktop.png'
import typeServer from '@/assets/server.png'
import styles from './index.module.scss';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
const { Title, Paragraph, Text } = Typography;

const Index: FC = () => {
    const { formatMessage } = useLocale();
    const params = useParams<{ id: string }>()
    const servicesId = params.id ? params.id : ''
    const flynet = useContext(FlynetGeneralContext);
    // 详情加载标识，用于骨架屏
    const [detailLoading, setDetailLoading] = useState(false);

    // 详情数据
    const [services, setServices] = useState<Service>();

    const [gatewayNodes, setGatewayNodes] = useState<Array<ServiceNode>>([])
    const [subnetNodes, setSubnetNodes] = useState<Array<ServiceNode>>([])


    useEffect(() => {
        flylayerClient.getService({
            serviceId: BigInt(servicesId)
        }).then(res => {
            setServices(res.service)
            
            if (res.service) {
                if (res.service.type == ServiceType.SYSTEM_DAEMON) {
                    let gatewayNodes: Array<ServiceNode> = [];
                    let subnetNodes: Array<ServiceNode> = [];
                    res.service.serviceNodes.forEach((item) => {
                        if (item.type == ServiceNodeType.GATEWAY) {
                            gatewayNodes.push(item)
                        } else if (item.type == ServiceNodeType.SUBNET) {
                            subnetNodes.push(item)
                        }
                    })

                    subnetNodes.sort((a, b) => {
                        return a.rank - b.rank
                    })
                    gatewayNodes.sort((a, b) => {
                        return a.rank - b.rank
                    })
                    setGatewayNodes(gatewayNodes)
                    setSubnetNodes(subnetNodes)
                }

            }

        }, err => {
            Notification.error({
                content: formatMessage({ id: 'services.detail.error.loadDetailFailed' }),
                position: 'bottomRight'
            })
        })
    }
        , [])

    return (
        <><Skeleton placeholder={<div className='general-page'>
            <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>
            <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
            <Skeleton.Image style={{ height: 60 }} className='mb40' />
            <Skeleton.Image style={{ height: 200 }} />
        </div>} loading={detailLoading}>
            <div className='general-page'>
                <Breadcrumb routes={
                    [
                        {
                            path: `${BASE_PATH}/services`,
                            href: `${BASE_PATH}/services`,
                            name: '所有服务'
                        },
                        {
                            name: services?.name,
                        }
                    ]
                }>
                </Breadcrumb>

                <Divider style={{ marginBottom: 10 }} />
                <div className={styles.detailTable}>
                    <Descriptions>
                        <Descriptions.Item key="type" itemKey="服务类型">{
                            services && services.type == ServiceType.SYSTEM_DAEMON && <>
                                <div className={styles.servicesLogo}>
                                    <img src={typeServer}></img>
                                    <span>系统服务</span>
                                </div>
                            </>
                        }
                            {
                                services && services.type == ServiceType.REMOTE_DESKTOP && <>
                                    <div className={styles.servicesLogo}>
                                        <img src={typeRemoteDesktop}></img>
                                        <span>远程桌面</span>
                                    </div>

                                </>
                            }</Descriptions.Item>

                    </Descriptions>
                </div>
                <Divider style={{ marginBottom: 10 }} />
                <div className={styles.detailTable}>
                    <Descriptions>
                        <Descriptions.Item key="name" itemKey="服务名称">{services?.name}</Descriptions.Item>
                        <Descriptions.Item key="description" itemKey="服务描述">{services?.description}</Descriptions.Item>

                        <Descriptions.Item key="reserviceGroupsmark" itemKey="服务组">
                            {
                                services?.serviceGroups.map((item, index) => {
                                    return <Tag size='large' style={{ marginRight: 10 }} key={index}>{item.alias}({item.name})</Tag>
                                })
                            }

                        </Descriptions.Item>
                        <Descriptions.Item key="origin" itemKey="服务来源">
                            {
                                services?.origin == ServiceOrigin.SYSTEM_CONFIG && <Tag size='large'>系统配置</Tag>
                            }
                            {
                                services?.origin == ServiceOrigin.AUTO_DISCOVER && <Tag size='large'>自动发现</Tag>
                            }
                        </Descriptions.Item>

                    </Descriptions>
                </div>

                {services?.type == ServiceType.REMOTE_DESKTOP && <>
                    <Divider style={{ marginBottom: 20 }}></Divider>
                    {
                        services.serviceNodes.length > 0 && services.serviceNodes[0].machine &&
                        <MachineCard hideDelete machine={services.serviceNodes[0].machine}></MachineCard>}
                </>}

                {services?.type == ServiceType.SYSTEM_DAEMON && <>

                    <Divider style={{ marginBottom: 10 }}></Divider>
                    <div className={styles.detailTable}>
                        <Descriptions>
                       
                            <Descriptions.Item key="routeMode" itemKey="路由模式">
                                {
                                    services?.routeMode == ServiceRouteMode.DIRECT && <Tag size='large'>直连</Tag>

                                }
                                {
                                    services?.routeMode == ServiceRouteMode.FORWARD && <Tag size='large'>转发</Tag>

                                }
                            </Descriptions.Item>

                        </Descriptions>
                    </div>

                    <Divider style={{ marginBottom: 40 }}></Divider>

                    <Title heading={6} className="mb10">协议/端口</Title>
                    {services.ports.length > 0 ? <div style={{ marginBottom: 20, width: 400 }}>
                        <Row className={styles.tableTitle} >
                            <Col span={8}>名称</Col>
                            <Col span={8}>描述</Col>
                            <Col span={4}>协议</Col>
                            <Col span={4}>端口</Col>
                        </Row>
                        {services.ports.map((item, index) => {
                            return <Row className={styles.tableBody} key={index}>
                                <Col xs={24} sm={8}>
                                    {item.name}
                                </Col>
                                <Col xs={24} sm={8}>
                                    {item.description}
                                </Col>
                                <Col xs={24} sm={4}>
                                    {item.proto == ServiceProto.TCP && 'TCP'}
                                    {item.proto == ServiceProto.UDP && 'UDP'}
                                </Col>
                                <Col xs={24} sm={4}>
                                    {item.port}
                                </Col>
                            </Row>

                        }
                        )}
                        </div> : <TableEmpty loading={false}></TableEmpty>}


                    {services?.routeMode == ServiceRouteMode.FORWARD && <>
                        <Divider style={{ marginBottom: 10 }}></Divider>


                        <Title heading={6} className="mb10">连接器</Title>
                        {gatewayNodes.length > 0 ?
                            <div style={{ marginBottom: 40, width: 400 }}>
                                <Row className={styles.tableTitle} >
                                    <Col span={12}>名称</Col>
                                    <Col span={12}>地址</Col>
                                </Row>
                                {gatewayNodes.map((item, index) => {
                                    return <Row className={styles.tableBody} key={index}>
                                        <Col xs={24} sm={12}>
                                            {item.name}
                                        </Col>
                                        <Col xs={24} sm={12}>
                                            {item.ipv4}
                                        </Col>
                                    </Row>

                                })}

                            </div> : <TableEmpty loading={false}></TableEmpty>}


                    </>}



                    <Divider className="mb20" />

                    <Title heading={6} className="mb10">

                        {services.routeMode == ServiceRouteMode.DIRECT && formatMessage({ id: 'services.detail.nodes' })}
                        {services.routeMode == ServiceRouteMode.FORWARD && formatMessage({ id: 'services.detail.subnetNodes' })}
                    </Title>
                    {
                        subnetNodes.length > 0 ?
                            <div style={{ marginBottom: 40, width: 400 }}>
                                <Row className={styles.tableTitle} >
                                    <Col span={12}>名称</Col>
                                    <Col span={12}>地址</Col>
                                </Row>
                                {subnetNodes.map((item, index) => {
                                    return <Row className={styles.tableBody} key={index}>
                                        <Col xs={24} sm={12}>
                                            {item.name}
                                        </Col>
                                        <Col xs={24} sm={12}>
                                            {item.ipv4}
                                        </Col>
                                    </Row>

                                })}
                            </div> :
                            <TableEmpty loading={false}></TableEmpty>
                    }

                </>}



            </div>
        </Skeleton>
        </>
    )
}

export default Index;