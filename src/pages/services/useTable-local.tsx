import { useEffect, useState, useContext } from 'react';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Typography, Dropdown, Space, Notification, Button, Tag, Divider } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';

import { flylayerClient } from '@/services/core';
import { Service, ServiceType, ServiceProto, ServiceRouteMode, ServiceNodeType, ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { BASE_PATH } from '@/constants/router';
import { useNavigate } from 'react-router-dom';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { FilterParam } from '@/components/search-filter';
import TypeSelector, { getTypeDisplayValueI18n } from './components/type-selector';
import ProtoSelector, { getProtoDisplayValue } from './components/proto-selector';



import { getServiceNodeDisplay } from '@/utils/service';
import { useLocale } from '@/locales';
export type ServicesFilter = {
    query?: string;
    source?: string;
    proto?: string;
    type?: string;
    lastSeen?: string;
    group?: string;
}

const { Title, Paragraph } = Typography;
const useTable = (initFilter: ServicesFilter) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 服务列表
    const [services, setServices] = useState<Service[]>([]);
    // 全部服务列表
    const [allServices, setAllServices] = useState<Service[]>([]);

    // 编辑弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 删除弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);
    // 当前菜单选中服务
    const [selectedService, setSelectedService] = useState<Service>();
    // 服务组列表
    const [groups, setGroups] = useState<ServiceGroup[]>([]);
    // 当前选中的服务组
    const [curGroup, setCurGroup] = useState<ServiceGroup>();

    // 从本组删除是否可见
    const [removeFromGroupVisible, setRemoveFromGroupVisible] = useState(false);

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    // 过滤参数
    const [filter, setFilter] = useState<ServicesFilter>(initFilter);

    // 当前页码
    const [page, setPage] = useState(1);
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    const pageSize = 20;

    // 服务map, key为IP + 端口，value为服务
    const [mapService, setMapService] = useState<Map<string, Service>>(new Map());

    const buildMapService = (services: Service[]) => {
        const map = new Map();
        services.forEach((service) => {
            service.serviceNodes.forEach((node) => {
                if (node.type != ServiceNodeType.GATEWAY) {
                    service.ports.forEach((port) => {
                        map.set(node.ipv4 + ':' + port.port, service);
                    })
                }
            })
        })
        setMapService(map);
    }

    // 表格列
    const columns = [
        {
            width: 240,
            title: formatMessage({ id: 'services.table.serviceName' }),
            dataIndex: 'name',
            sorter: true,
            render: (field: string, record: Service, _index: number) => {
                return <>
                    <Space>
                        <Title heading={5}>
                            <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/services/${record.id}`), 10)}>{field}</a>
                        </Title>
                    </Space>
                    <Paragraph size='small'>{record.description}</Paragraph>
                </>
            },
        },
        {
            width: 160,
            title: formatMessage({ id: 'services.table.serviceType' }),
            dataIndex: 'type',
            sorter: true,
            render: (_field: string, record: Service) => {
                return <>
                    <Paragraph style={{ marginBottom: 5 }}>
                        {/* {record.type == ServiceType.REMOTE_DESKTOP && '远程桌面'} */}
                        {record.type == ServiceType.SYSTEM_DAEMON && formatMessage({ id: 'services.type.systemDaemon' })}
                        {record.type == ServiceType.WEB_APP && formatMessage({ id: 'services.type.webApp' })}
                    </Paragraph>

                    {record.type == ServiceType.SYSTEM_DAEMON && <>
                        {record.routeMode == ServiceRouteMode.DIRECT && <Tag>{formatMessage({ id: 'services.routeMode.direct' })}</Tag>}
                        {record.routeMode == ServiceRouteMode.FORWARD && <Tag>{formatMessage({ id: 'services.routeMode.forward' })}</Tag>}
                    </>}
                </>
            },
        },
        {
            width: 160,
            title: formatMessage({ id: 'services.table.protocolPort' }),
            dataIndex: 'ports',
            render: (_field: string, record: Service, _index: number) => {
                return <>
                    {record.ports.map((port, i) => {
                        return <div key={i} style={{ marginBottom: 5 }}>{port.proto == ServiceProto.TCP ? <Tag size='small'>TCP/{port.port}</Tag> : <Tag size='small'>UDP/{port.port}</Tag>}</div>
                    })}
                </>
            }
        },
        {
            title: formatMessage({ id: 'services.table.serviceGroup' }),
            dataIndex: 'serviceGroups',
            render: (_: any, record: Service) => {
                return <div><Space style={{ flexWrap: 'wrap' }}>{record.serviceGroups.map((g, i) => <Tag key={i} size='large'>{g.alias} ({g.name})</Tag>)}</Space></div>
            }
        },
        {
            width: 360,
            title: formatMessage({ id: 'services.table.serviceNodes' }),
            dataIndex: 'servicesNodes',
            render: (_field: string, record: Service, _index: number) => {
                return getServiceNodeDisplay(record);
            }
        },
        {
            width: 100,
            title: '',
            dataIndex: 'operate',
            render: (_field: string, record: Service) => {
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => {
                                setSelectedService(record)
                                setEditVisible(true)
                            }}>{formatMessage({ id: 'services.action.edit' })}</Dropdown.Item>
                            <Dropdown.Divider />

                            <Dropdown.Item type="danger" onClick={() => {
                                setDelVisible(true)
                                setSelectedService(record)
                            }} >{formatMessage({ id: 'services.action.delete' })}</Dropdown.Item>

                            {curGroup && curGroup.type == GroupType.GROUP_STATIC && <>
                                <Divider/>
                                <Dropdown.Item onClick={() => {
                                    setRemoveFromGroupVisible(true)
                                    setSelectedService(record)
                                }} >{formatMessage({ id: 'services.action.removeFromGroup' })}</Dropdown.Item>
                            </>}
                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];

    // 过滤数据
    const doFilter = (page: number, src: Array<Service>, filter: ServicesFilter, group?:ServiceGroup) => {

        if (!src || src.length == 0) {
            setTotal(src.length)
            return src.slice(0, pageSize * page);
        }
        if (filter.query == '' && filter.source == '' && filter.type == '' && filter.proto == '' && filter.lastSeen == '' && !group) {
            setTotal(src.length)
            return src.slice(0, pageSize * page);
        }

        const filteredList = src.filter((item) => {
            if (filter.query) {
                if (item.name.indexOf(filter.query) < 0 && item.description.indexOf(filter.query) < 0) {
                    return false;
                }
            }
            if (filter.type) {

                const types = filter.type.split(',');
                if (types.indexOf(item.type.toString()) < 0) {
                    return false;
                }
            }

            if (filter.proto) {
                const protos = filter.proto.split(',');
                let find = false;
                for (let i = 0; i < protos.length; i++) {
                    const p = protos[i];
                    item.ports.forEach((port) => {
                        if (port.proto.toString() == p) {
                            find = true;
                        }
                    })
                }
                if (!find) {
                    return false;
                }

            }

            // if (filter.group) {
            //     const groups = filter.group.split(',');
            //     let found = false;
            //     for (let i = 0; i < groups.length; i++) {
            //         const g = groups[i];
            //         for (let j = 0; j < item.serviceGroups.length; j++) {
            //             const sg = item.serviceGroups[j];
            //             if (sg.id + '' == g) {
            //                 found = true;
            //                 break;
            //             }
            //         }
            //         if (found) {
            //             break;
            //         }
            //     }
            //     if (!found) {
            //         return false;
            //     }
            // }
            if (group) {
                let findGroup = false;
                item.serviceGroups.forEach((val) => {
                    if(group.id == val.id) {
                        findGroup = true;
                    }
                });
                if(!findGroup) {
                    return false;
                } else {
                    return true;
                }

            }

            return true;
        });
        setTotal(filteredList.length)
        return filteredList.slice(0, pageSize * page);
    }

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;

        let sortedAllDate = [...allServices];

        if (sortOrder == 'descend' || sortOrder == 'ascend') {


            if (dataIndex == 'name') {

                sortedAllDate.sort((a, b) => {
                    if (a.name > b.name) {
                        return sortOrder == 'ascend' ? 1 : -1;
                    } else {
                        return sortOrder == 'ascend' ? -1 : 1;
                    }
                })
            }
            if (dataIndex == 'type') {

                sortedAllDate.sort((a, b) => {
                    if (a.type > b.type) {
                        return sortOrder == 'ascend' ? 1 : -1;
                    } else {
                        return sortOrder == 'ascend' ? -1 : 1;
                    }
                })
            }

            if (dataIndex == 'createdAt') {

                sortedAllDate.sort((a, b) => {
                    if (!a || !b) return -1;
                    if (a.createdAt && b.createdAt) {
                        return 1;
                    }
                    if (a.createdAt && !b.createdAt) {
                        return -1;
                    }
                    if (!a.createdAt && b.createdAt) {
                        return 1;
                    }
                    if (b.createdAt && a.createdAt && a.createdAt.seconds > b.createdAt.seconds) {
                        return -1;
                    }
                    return -1;
                })
            }
            if (dataIndex == 'serviceGroups') {

                sortedAllDate.sort((a, b) => {
                    if (a.serviceGroups.length > b.serviceGroups.length) {
                        return sortOrder == 'ascend' ? 1 : -1;
                    } else {
                        return sortOrder == 'ascend' ? -1 : 1;
                    }
                })
            }
            if (dataIndex == 'servicesNodes') {

                sortedAllDate.sort((a, b) => {
                    if (a.serviceNodes.length > b.serviceNodes.length) {
                        return sortOrder == 'ascend' ? 1 : -1;
                    } else {
                        return sortOrder == 'ascend' ? -1 : 1;
                    }
                })
            }
        } else {
            sortedAllDate = [...allServices];

        }

        setAllServices(sortedAllDate);
        setServices(doFilter(page, sortedAllDate, initFilter, curGroup));



    }


    const [filterParams, setFilterParams] = useState<FilterParam[]>([{
        name: 'query',
        placeholder: formatMessage({ id: 'services.filter.searchPlaceholder' }),
        label: formatMessage({ id: 'services.filter.query' }),
        value: initFilter.query || '',
    }, {
        name: 'type',
        placeholder: formatMessage({ id: 'services.filter.serviceType' }),
        label: formatMessage({ id: 'services.filter.serviceType' }),
        value: initFilter.type || '',
        filterComponent: TypeSelector,
        funGetDisplayValue: (value: string) => getTypeDisplayValueI18n(value, formatMessage),

    },
    // {
    //     name: 'source',
    //     placeholder: formatMessage({ id: 'services.filter.source' }),
    //     label: formatMessage({ id: 'services.filter.source' }),
    //     value: initFilter.source || '',
    //     filterComponent: SourceSelector,
    //     funGetDisplayValue: getSourceDisplayValue
    // },
    {
        name: 'proto',
        placeholder: formatMessage({ id: 'services.filter.protocol' }),
        label: formatMessage({ id: 'services.filter.protocol' }),
        value: initFilter.proto || '',
        filterComponent: ProtoSelector,
        funGetDisplayValue: getProtoDisplayValue
    },
    // {
    //     name: 'group',
    //     placeholder: formatMessage({ id: 'services.filter.serviceGroup' }),
    //     label: formatMessage({ id: 'services.filter.serviceGroup' }),
    //     value: initFilter.group || '',
    //     filterComponent: GroupSelector,
    //     funGetDisplayValue: (val) => {
    //         return getGroupDisplayValue(val, getMapTreeData())
    //     }
    // }

    ]);

    // 加载数据
    const query = async () => {

        const resGroup = await flylayerClient.listServiceGroups({
            flynetId: flynet.id
        });

        let curGroup: ServiceGroup | undefined;
        setGroups(resGroup.serviceGroups);
        if(resGroup.serviceGroups) {
            resGroup.serviceGroups.forEach((item) => {
                if (item.name == filter.group) {
                    curGroup = item;
                }
            })
        }
        setCurGroup(curGroup);

        setLoading(true);

        flylayerClient.listServices({
            flynetId: flynet.id
        }).then((resp) => {
            const services = resp.services;
            setServices(doFilter(page, services, initFilter, curGroup));
            setAllServices(services);
            buildMapService(services);
            setLoading(false);
        }).catch((err) => {
            Notification.error({
                title: formatMessage({ id: 'services.error.loadServicesFailed' }),
                content: err.message,
            });
            setLoading(false);
        })

        setLoading(false);
    }

    const addPage = () => {

        setServices(doFilter(page + 1, allServices, initFilter, curGroup));
        setPage(page + 1)

    }
    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setPage(1)
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])



    const handleFilterChange = (newFilter: ServicesFilter) => {
        setFilter(newFilter)
        setServices(doFilter(1, allServices, newFilter, curGroup))
    }


    const handleGroupChange = (group?: ServiceGroup) => {
        const list = doFilter(1, allServices, filter, group);
        setServices(list);
        setCurGroup(group);
    };

    return {
        columns, loading,
        allServices, services, selectedService, setSelectedService,
        delVisible, setDelVisible,
        editVisible, setEditVisible,
        reloadFlag, setReloadFlag,
        filter, setFilter,
        page, setPage, pageSize, addPage, total, handleSort,
        filterParams, setFilterParams,
        handleFilterChange,
        mapService,
        groups, curGroup, setCurGroup, handleGroupChange,
        removeFromGroupVisible, setRemoveFromGroupVisible

    }
}


export default useTable;