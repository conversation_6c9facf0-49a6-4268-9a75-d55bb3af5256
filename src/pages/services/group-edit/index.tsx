import React, { FC, useEffect, useState, useContext } from 'react'
import { Typography, Modal, Form, Notification, Skeleton, Popover, Button, Row, Col, Spin, List, Divider } from '@douyinfe/semi-ui';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Service, ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons';

import ServicesSelector from '@/components/services-modal-selector';
const { Title, Paragraph, Text } = Typography;

const { Switch, Input, Select } = Form
import useServicesGroup from '../useServicesGroup';
import { getSimpleServiceName } from '@/utils/service';

interface Props {
    close: () => void,
    success?: (service?: ServiceGroup) => void
    servicesGroupId: bigint
}
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        parentId: bigint,
        alias: string,
        type: GroupType,
    }>>()

    const [servicesGroup, setServicesGroup] = useState<ServiceGroup>()

    const { serviceGroupTreeData, getMapTreeData } = useServicesGroup();

    // 服务
    const [service, setService] = useState<Service>()
    const [services, setServices] = useState<Service[]>()
    const [servicesSelectorVisible, setServicesSelectorVisible] = useState(false);

    const [groupLoading, setGroupLoading] = useState(false);

    useEffect(() => {
        setGroupLoading(true);
        flylayerClient.getServiceGroup({
            serviceGroupId: props.servicesGroupId,
        }).then((res) => {
            setServicesGroup(res.serviceGroup);
            setServices(res.serviceGroup?.services)
        }).catch((err) => {
            Notification.error({
                title: formatMessage({ id: 'services.group.edit.failed' }),
                content: err.message
            });
        }).finally(() => {
            setGroupLoading(false);
        })
    }, [])


    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleChange = (values: any) => {

    }

    const handleSubmit = async () => {
        await formApi?.validate();
        setLoading(true);
        let values = formApi?.getValues();
        if (values) {
            let parentId = values.parentId && values.parentId > 0 ? values.parentId : BigInt(0);
            const name = values.name.trim();
            let fullName = name;
            const mapTreeData = getMapTreeData();
            if (mapTreeData) {
                let parent = mapTreeData.get(parentId + '');
                if (parent) {
                    fullName = parent.fullName + '/' + fullName;
                }
            }
            let _servicesGroup = {
                ...servicesGroup, ...{
                    name: name,
                    fullName: fullName,
                    alias: values.alias.trim(),
                    type: GroupType.GROUP_STATIC,
                    description: values.description.trim(),
                    serviceCount: services ? services.length : 0,
                    services: services,
                    parentId: values.parentId
                        ? values.parentId : BigInt(0),
                }
            };

            flylayerClient.updateServiceGroups({
                serviceGroup: _servicesGroup,
                flynetId: flynet.id,
            }).then((res) => {
                Notification.success({
                    title: formatMessage({ id: 'services.group.edit.success' }),
                    content: formatMessage({ id: 'services.group.edit.success' })
                });
                props.success && props.success();
            }).catch((err) => {
                Notification.error({
                    title: formatMessage({ id: 'services.group.edit.failed' }),
                    content: err.message
                });
            }).finally(() => {
                setLoading(false);
            })
        }
    }


    return <>
        <Modal
            width={600}
            title='编辑服务组'
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >
            <Skeleton loading={groupLoading} placeholder={
                <>
                    <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title>
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />

                </>
            }>
                {servicesGroup && <div className={styles.addService}>
                
                    <Form getFormApi={SetFormApi}
                        onValueChange={handleChange}
                        allowEmpty
                        initValues={
                            {
                                name: servicesGroup.name,
                                description: servicesGroup.description,
                                alias: servicesGroup.alias,
                                parentId: servicesGroup.parentId,
                            }
                        }
                    >
                        <>
                            <Row style={{ display: 'none' }}>
                                <Col span={24}>
                                    <Form.TreeSelect
                                        placeholder='请选择服务组'
                                        showClear
                                        style={{ width: '100%' }}
                                        expandAll
                                        treeData={
                                            serviceGroupTreeData
                                        }

                                        field='parentId' label='上级服务组'></Form.TreeSelect>
                                </Col>
                            </Row>
                            <Row gutter={12}>
                            <Col span={12}>
                                <Input field='alias' label='名称' validate={value => {
                                    if (!value) {
                                        return '名称不能为空';
                                    }
                                    return '';
                                }} />
                            </Col>
                            <Col span={12}>
                                <Input field='name' 
                                label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{fontSize:14, verticalAlign: 'text-top'}}/></Popover></>} 
                                
                                 trigger={'blur'} readonly validate={value => {
                                    if (!value) {
                                        return '编码不能为空';
                                    }
                                    // 编码不能以-开头
                                    if (value.trim().startsWith('-')) {
                                        return '编码不能以-开头'
                                    }
                                    if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                        return "编码只能包含字母、数字和'-'";
                                    }
                                    return '';
                                }}
                                    required />
                            </Col>
                        </Row>
                            <Row>
                                <Col span={24}>
                                    <Input field='description' label='备注' />

                                </Col>
                            </Row>
                            <Divider className='mb20'></Divider>

                            <Row className="mb20">
                                <Col span={20}>
                                    <Title heading={6} className="mb10">服务</Title>
                                </Col>
                                <Col span={4} className={styles.rightColumn}>

                                    <Button
                                        onClick={() => {
                                            setServicesSelectorVisible(true);
                                        }}
                                        icon={<IconPlus></IconPlus>}></Button>

                                </Col>
                            </Row>
                            {
                                !services || services.length == 0 ? <TableEmpty loading={false}></TableEmpty> :
                                    <>
                                        {services && services.map((item, index) => {
                                            return <Row className="mb10" key={index}>
                                                <Col span={20}>
                                                    {getSimpleServiceName(item)}
                                                </Col>
                                                <Col span={4} className={styles.rightColumn}>

                                                    <Button
                                                        type='danger'
                                                        onClick={() => {
                                                            let newServices = services.filter((item, i) => i != index);
                                                            setServices(newServices);
                                                        }}
                                                        icon={<IconMinusCircle></IconMinusCircle>}></Button>

                                                </Col>
                                            </Row>
                                        })}
                                    </>
                            }

                        </>

                    </Form>
                </div>}
            </Skeleton>

        </Modal>
        {
            servicesSelectorVisible && <ServicesSelector
                multi={true}
                value={services}
                onChange={(value) => {
                    setServicesSelectorVisible(false)
                    let newServices = services ? services.filter((item) => true) : [];

                    if (value instanceof Array) {
                        value.forEach((item) => {
                            let find = false;
                            newServices.forEach((s) => {
                                if (s.id == item.id) {
                                    find = true;
                                }
                            })
                            if (!find) {
                                newServices.push(item);
                            }
                        })
                    } else {

                        let find = false;
                        newServices.forEach((s) => {
                            if (s.id == value.id) {
                                find = true;
                            }
                        })
                        if (!find) {
                            newServices.push(value);
                        }
                    }
                    setServices(newServices);
                }}
                close={() => {
                    setServicesSelectorVisible(false);
                }}
            ></ServicesSelector>
        }
    </>
}

export default Index