import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { flylayerClient } from '@/services/core';

import {Service} from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Service
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
  
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <>
        <Modal
            width={400}
            title={`${formatMessage({ id: 'services.action.delete' })} ${props.record.name}`}
            visible={true}
            okButtonProps={{ 
                disabled: props.record.name !== confirmVal,
                loading, type: 'danger' }}
            onOk={() => {
                setLoading(true)

                flylayerClient.deleteService({
                    serviceId: props.record.id,
                    flynetId: flynet.id
                }).then(() => {
                    Notification.success({ content: formatMessage({ id: 'services.delete.success' }), position: "bottomRight" })
                    if (props.success) {
                        props.success();
                    }
                }).catch((err) => {
                    console.error(err);
                    Notification.error({ content: formatMessage({ id: 'services.delete.failed' }), position: "bottomRight" })
                }).finally(() => setLoading(false))

                // flylayerClient.deleteMachine({
                //     machineId: props.record.id
                // }).then(() => {
                //     Notification.success({ content: "删除服务成功", position: "bottomRight" })
                //     if (props.success) {
                //         props.success();
                //     }
                // }).catch((err) => {
                //     console.error(err);
                //     Notification.error({ content: "删除服务失败，请稍后重试", position: "bottomRight" })
                // }).finally(() => setLoading(false))
            }}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'services.delete.warning' })}
            </Paragraph><Paragraph className='mb20'>{formatMessage({ id: 'services.delete.confirmInput' }).replace('{name}', props.record.name)}
            </Paragraph>
            <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
