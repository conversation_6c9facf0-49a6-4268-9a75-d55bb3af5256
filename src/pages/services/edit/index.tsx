import React, { FC, useEffect, useState, useContext } from 'react'
import { Typography, Modal, Form, ArrayField, Space, Button, Row, Col, Popover, Divider, Notification, Skeleton } from '@douyinfe/semi-ui';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import MachineSelector from '@/components/machine-selector'
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Service, ServiceType, ServiceNode, ServiceGroup, ServiceProto, ServicePort, ServiceRouteMode, ServiceNodeType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import useServicesGroup from '../useServicesGroup';

import MachineCard from '../../../components/machine-card';
import typeRemoteDesktop from '@/assets/remote-desktop.png'
import typeServer from '@/assets/server.png'
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'
import { flylayerClient } from '@/services/core';

import MachineViewer from '@/components/machine-viewer';
const { Input, Select } = Form
const {
    Title
} = Typography
import { IconArrowUpRight, IconMinusCircle, IconArrowDown, IconArrowUp, IconAlignTop, IconAlignBottom, IconMore, IconPlus } from '@douyinfe/semi-icons';
import gatewayValidate from '../gateway-validate';
import { BASE_PATH } from '@/constants/router';
interface Props {
    close: () => void,
    success?: () => void,
    mapService: Map<string, Service>, // 服务map, key为IP + 端口，value为服务
    recordId: bigint
}
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formValue, setFormValue] = useState<{
        name: string,
        description: string,
        serviceGroups: Array<string>
        gatewayNodes: Array<ServiceNode>,
        subnetNodes: Array<ServiceNode>,
        ports: Array<ServicePort>,
    }>();

    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        serviceGroups: Array<string>
        gatewayNodes: Array<ServiceNode>,
        subnetNodes: Array<ServiceNode>,
        proto: ServiceProto,
        ports: Array<ServicePort>,
    }>>()

    const [serviceLoading, setServiceLoading] = useState(true);
    const [service, setService] = useState<Service>();

    useEffect(() => {
        flylayerClient.getService({
            serviceId: props.recordId
        }).then((res) => {
            setService(res.service)
            if (res.service) {
                let gatewayNodes: Array<ServiceNode> = [];
                let subnetNodes: Array<ServiceNode> = [];

                if (res.service.type == ServiceType.SYSTEM_DAEMON) {
                    res.service.serviceNodes.forEach((item) => {
                        if (item.type == ServiceNodeType.GATEWAY) {
                            gatewayNodes.push(item)
                        } else if (item.type == ServiceNodeType.SUBNET) {
                            subnetNodes.push(item)
                        }
                    })
                }

                let serviceGroups = res.service.serviceGroups.map(sg => {
                    return sg.id + ''
                })

                subnetNodes.sort((a, b) => {
                    return a.rank - b.rank
                })
                gatewayNodes.sort((a, b) => {
                    return a.rank - b.rank
                })

                let formValue = {
                    name: res.service.name,
                    description: res.service.description,
                    serviceGroups: serviceGroups,
                    gatewayNodes: gatewayNodes,
                    subnetNodes: subnetNodes,
                    ports: res.service.ports,
                    routeMode: res.service.routeMode
                }
                setDaemonMode(res.service.routeMode)
                setFormValue(formValue);
                formApi?.setValues(formValue);

                setSubnetNodes(subnetNodes)
                setGatewayNodes(gatewayNodes)
            }
            if (res.service && res.service.type == ServiceType.REMOTE_DESKTOP && res.service.serviceNodes) {
                if (res.service.serviceNodes.length > 0) {
                    setMachine(res.service.serviceNodes[0].machine)
                }
            }

        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'services.edit.error.loadServiceFailed' }), position: "bottomRight" })
        }).finally(() => {
            setServiceLoading(false)
        })

    }, []);

    const [opt, setOpt] = useState<'' | 'SELECT_RDP_MACHINE' | 'SELECT_DAEMON_GATEWAY' | 'SELECT_DAEMON_SUBNET'>('')

    // 系统服务模式  
    const [daemonMode, setDaemonMode] = useState<ServiceRouteMode>(ServiceRouteMode.DIRECT)

    const [machineSelectorVisible, setMachineSelectorVisible] = useState(false)
    const [machine, setMachine] = useState<Machine>();

    const [subnetNodes, setSubnetNodes] = useState<Array<ServiceNode>>([])
    const [gatewayNodes, setGatewayNodes] = useState<Array<ServiceNode>>([])
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    const handleChange = (values: any) => {
        setFormValue(values)
    }

    const { serviceGroupTreeData } = useServicesGroup();

    // 从设备添加网关结点
    const addGatewayNodeFromMachine = () => {
        setMachineSelectorVisible(true)
        setOpt('SELECT_DAEMON_GATEWAY')
    }

    // 直接添加子网结点
    const addSubnetNodeDirect = () => {
        let newList: Array<ServiceNode> = []

        formApi?.getValue('subnetNodes')?.forEach((item: ServiceNode) => {
            newList.push(item)
        })
        newList.push(
            new ServiceNode({
                name: '',
                ipv4: '',
                type: ServiceNodeType.SUBNET
            }))

        formApi?.setValue('subnetNodes', newList);
        setSubnetNodes(newList)
    }
    // 从设备添加子网结点
    const addSubnetNodeFromMachine = () => {
        setMachineSelectorVisible(true)
        setOpt('SELECT_DAEMON_SUBNET')
    }


    const validateIpPort = (service: Service, mapService: Map<string, Service>): string => {
        let errMsg = '';
        if (service.serviceNodes) {
            service.serviceNodes.forEach((node) => {
                if (node.ipv4 && node.type != ServiceNodeType.GATEWAY && service.ports) {
                    service.ports.forEach((port) => {
                        let key = node.ipv4 + ':' + port.port;
                        if (mapService.has(key)) {
                            let s = mapService.get(key);
                            if (s && s.id != service.id) {

                                errMsg += `IP地址${node.ipv4}端口${port.port}已被添加到服务${s?.name}(${s?.description});`
                            }
                        }
                    })
                }
            })
        }

        return errMsg
    }

    const handleSubmit = async () => {
        if (service?.type == ServiceType.REMOTE_DESKTOP) {
            await formApi?.validate();
            if (!machine) {
                Notification.error({ content: formatMessage({ id: 'services.edit.error.selectDevice' }), position: "bottomRight" })
                return
            }
            const values = formApi?.getValues();
            const serviceGroups = values?.serviceGroups.map((s, i) => {
                return new ServiceGroup({
                    id: BigInt(s)
                })
            })

            let serviceNodes: Array<ServiceNode> = [];
            if (service.serviceNodes.length > 0) {
                var serviceNode = service.serviceNodes[0];
                serviceNodes.push(new ServiceNode({
                    ipv4: machine?.ipv4,
                    ipv6: machine?.ipv6,
                    name: machine?.name,
                    id: serviceNode.id,
                    machine: machine,
                    type: ServiceNodeType.VIRTUAL
                }))
            }

            let s = new Service({
                id: service.id,
                name: values?.name.trim(),
                description: values?.description.trim(),
                routeMode: service.routeMode,
                type: service.type,
                ports: service.ports,
                origin: service.origin,
                serviceNodes: serviceNodes,
                serviceGroups: serviceGroups
            });


            let errIpMsg = validateIpPort(s, props.mapService);
            if (errIpMsg) {
                Notification.error({
                    title: formatMessage({ id: 'services.guide.ipPortOccupied' }),
                    content: errIpMsg
                })
                return;
            }

            setLoading(true)
            flylayerClient.updateService({
                service: s,
                flynetId: flynet.id
            }).then((res) => {
                props.success && props.success()
                Notification.success({ content: formatMessage({ id: 'services.edit.success.updateService' }), position: "bottomRight" });
            }).catch((err) => {
                console.error(err)
                Notification.error({ content: formatMessage({ id: 'services.edit.error.updateServiceFailed' }), position: "bottomRight" })
            }).finally(() => {
                setLoading(false)
            })
        } else if (service?.type == ServiceType.SYSTEM_DAEMON) {
            await formApi?.validate();
            const values = formApi?.getValues();

            const serviceGroups = values?.serviceGroups?.map((s, i) => {
                return new ServiceGroup({
                    id: BigInt(s)
                })
            })


            var serviceNodes: Array<ServiceNode> = [];
            if (values) {

                values.gatewayNodes?.forEach((item, index) => {
                    item.rank = index + 1;
                    serviceNodes.push(item)
                })

                values.subnetNodes?.forEach((item, index) => {
                    item.name = item.name.trim();
                    item.ipv4 = item.ipv4.trim();
                    item.rank = index + 1;
                    serviceNodes.push(item)
                })


                let ports: Array<ServicePort> = []
                values?.ports.forEach((item) => {
                    ports.push(new ServicePort({
                        name: item.name.trim(),
                        description: item.description.trim(),
                        port: Number(item.port),
                        proto: item.proto
                    }))
                })

                if (daemonMode == ServiceRouteMode.DIRECT) {
                    if (!values.subnetNodes || values.subnetNodes.length == 0) {
                        Notification.error({ content: formatMessage({ id: 'services.edit.error.addNodes' }), position: "bottomRight" })
                        return
                    }


                    let s = new Service({
                        id: service.id,
                        name: values?.name.trim(),
                        description: values?.description.trim(),
                        routeMode: daemonMode,
                        type: service.type,
                        ports: ports,
                        origin: service.origin,
                        serviceNodes: serviceNodes,
                        serviceGroups: serviceGroups
                    });

                    let errIpMsg = validateIpPort(s, props.mapService);
                    if (errIpMsg) {
                        Notification.error({
                            title: formatMessage({ id: 'services.guide.ipPortOccupied' }),
                            content: errIpMsg
                        })
                        return;
                    }
                    setLoading(true)
                    flylayerClient.updateService({
                        service: s,
                        flynetId: flynet.id
                    }).then((res) => {
                        props.success && props.success()
                        Notification.success({ content: formatMessage({ id: 'services.edit.success.updateService' }), position: "bottomRight" });
                    }).catch((err) => {
                        console.error(err)
                        Notification.error({ content: formatMessage({ id: 'services.edit.error.updateServiceFailed' }), position: "bottomRight" })
                    }).finally(() => {
                        setLoading(false)
                    })


                } else if (daemonMode == ServiceRouteMode.FORWARD) {
                    if (!values.gatewayNodes || values.gatewayNodes.length == 0) {
                        Notification.error({ content: formatMessage({ id: 'services.edit.error.addConnector' }), position: "bottomRight" })
                        return
                    }
                    if (!values.subnetNodes || values.subnetNodes.length == 0) {
                        Notification.error({ content: formatMessage({ id: 'services.edit.error.addSubnetNodes' }), position: "bottomRight" })
                        return
                    }

                    gatewayValidate(values.gatewayNodes, values.subnetNodes).then((res) => {

                        let s = new Service({
                            id: service.id,
                            name: values?.name.trim(),
                            description: values?.description.trim(),
                            routeMode: daemonMode,
                            type: service.type,
                            ports: ports,
                            origin: service.origin,
                            serviceNodes: serviceNodes,
                            serviceGroups: serviceGroups
                        });

                        let errIpMsg = validateIpPort(s, props.mapService);
                        if (errIpMsg) {
                            Notification.error({
                                title: formatMessage({ id: 'services.guide.ipPortOccupied' }),
                                content: errIpMsg
                            })
                            return;
                        }
                        setLoading(true)
                        flylayerClient.updateService({
                            service: s,
                            flynetId: flynet.id
                        }).then((res) => {
                            props.success && props.success()
                            Notification.success({ content: formatMessage({ id: 'services.edit.success.updateService' }), position: "bottomRight" });
                        }).catch((err) => {
                            console.error(err)
                            Notification.error({ content: formatMessage({ id: 'services.edit.error.updateServiceFailed' }), position: "bottomRight" })
                        }).catch(() => {
                            setLoading(false)
                        })
                    }, (unmatchNodes) => {
                        Notification.error({
                            title: formatMessage({ id: 'services.guide.connectorMismatch' }),
                            content: formatMessage({ id: 'services.guide.connectorMismatchContent' }) + unmatchNodes.join(','),
                        })

                    })

                }
            }

        }
    }


    const [gatewayIp, setGatewayIp] = useState('');

    const [machineViewerVisible, setMachineViewerVisible] = useState(false);

    const viewGateway = (index: number) => {

        var gatewayNode = formApi?.getValues().gatewayNodes[index];

        if (gatewayNode) {
            if (!gatewayNode.ipv4) {
                Notification.error({ content: 'IP地址为空' })
                return;
            } else {
                setGatewayIp(gatewayNode.ipv4);
                setMachineViewerVisible(true);
            }
        }
    }


    const viewSubnet = (index: number) => {
        var subnetNode = formApi?.getValues().subnetNodes[index];
        if (subnetNode) {
            if (!subnetNode.ipv4) {
                Notification.error({ content: 'IP地址为空' })
                return;
            } else {
                setGatewayIp(subnetNode.ipv4);
                setMachineViewerVisible(true)
            }
        }
    }


    return <>
        <Modal
            width={700}
            title={formatMessage({ id: 'services.edit.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{
                loading: loading,
                disabled: service?.type == ServiceType.REMOTE_DESKTOP && !machine || service?.type == ServiceType.SYSTEM_DAEMON && (service?.routeMode == ServiceRouteMode.DIRECT && (!formApi?.getValue('subnetNodes') || formApi?.getValue('subnetNodes')?.length == 0)
                    || service?.routeMode == ServiceRouteMode.FORWARD && (!formApi?.getValue('gatewayNodes') || formApi?.getValue('gatewayNodes')?.length == 0 || !formApi?.getValue('subnetNodes') || formApi?.getValue('subnetNodes')?.length == 0))
            }}
            className='semi-modal'
            maskClosable={false}
        >
            <Skeleton loading={serviceLoading} placeholder={
                <>
                    <Skeleton.Image style={{ height: 32, marginBottom: 50, marginTop: 36 }} />
                    <Skeleton.Image style={{ height: 32, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />
                    <Divider className='mb20' />
                    <Skeleton.Image style={{ height: 152 }} />
                </>
            }>
                {formValue && service &&
                    <div className={styles.addService}>
                        <Form getFormApi={SetFormApi}
                            onValueChange={handleChange}
                            allowEmpty
                            initValues={formValue}
                        >
                            <>
                                <Row>
                                    <Col span={24}>
                                        {
                                            service.type == ServiceType.SYSTEM_DAEMON && <>
                                                <div className={styles.servicesLogo}>
                                                    <img src={typeServer}></img>
                                                    <span>{formatMessage({ id: 'services.components.type.systemService' })}</span>
                                                </div>
                                            </>
                                        }
                                        {
                                            service.type == ServiceType.REMOTE_DESKTOP && <>
                                                <div className={styles.servicesLogo}>
                                                    <img src={typeRemoteDesktop}></img>
                                                    <span>{formatMessage({ id: 'services.components.type.remoteDesktop' })}</span>
                                                </div>
                                            </>
                                        }

                                    </Col>
                                </Row>
                                <Row>
                                    <Col span={24}>
                                        <Input field='name' label={formatMessage({ id: 'services.field.name' })}
                                            validate={value => {
                                                if (!value.trim()) {
                                                    return formatMessage({ id: 'services.validation.nameRequired' });
                                                }
                                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                                    return formatMessage({ id: 'services.validation.nameFormat' });
                                                }
                                                return '';
                                            }}
                                            required />
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span={24}>
                                        <Input field='description' label={formatMessage({ id: 'common.description' })}></Input>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span={24}>
                                        <Form.TreeSelect style={{ width: '100%' }}
                                            multiple
                                            expandAll
                                            checkRelation='unRelated'
                                            treeData={
                                                serviceGroupTreeData
                                            }
                                            field='serviceGroups' label={formatMessage({ id: 'services.field.selectServiceGroup' })}
                                            filterTreeNode
                                            showFilteredOnly
                                            dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                                        ></Form.TreeSelect>

                                    </Col>
                                </Row>
                                {
                                    service.type == ServiceType.REMOTE_DESKTOP && <>
                                        <Row>
                                            <Col span={24}>
                                                {machine ?
                                                    <MachineCard onClear={() => { setMachine(undefined) }} machine={machine}></MachineCard>
                                                    : <Button block
                                                        onClick={() => {
                                                            setMachineSelectorVisible(true)
                                                            setOpt('SELECT_RDP_MACHINE')
                                                        }}
                                                    >{formatMessage({ id: 'services.action.selectDevice' })}</Button>
                                                }
                                            </Col>
                                        </Row>
                                    </>
                                }
                                {
                                    service.type == ServiceType.SYSTEM_DAEMON && <>

                                        <ArrayField field='ports'>
                                            {({
                                                add,
                                                arrayFields
                                            }) => <React.Fragment>
                                                    <Row>
                                                        <Col span={20}>
                                                            <Title heading={6} className="mb10">协议/端口</Title>
                                                        </Col>
                                                        {/* <Col span={4} className={styles.rightColumn}>
                                                            {arrayFields.length > 0 && <Button icon={<IconPlus />} onClick={() => {
                                                                add()
                                                            }}></Button>}
                                                        </Col> */}
                                                    </Row>
                                                    {arrayFields.length > 0 ?
                                                        <>
                                                            <Row className={styles.tableTitle} >
                                                                <Col span={6}>名称</Col>
                                                                <Col span={8}>描述</Col>
                                                                <Col span={4}>协议</Col>
                                                                <Col span={4}>端口</Col>
                                                                <Col span={2}>&nbsp;</Col>
                                                            </Row>
                                                            {arrayFields.map(({ field, key, remove }, i) => (
                                                                <Row className={styles.tableBody} key={i}>
                                                                    <Col xs={24} sm={6}>
                                                                        <Form.Input
                                                                            field={`${field}[name]`}
                                                                            noLabel
                                                                            validate={value => {
                                                                                if (!value) {
                                                                                    return '名称不能为空';
                                                                                }
                                                                                return '';
                                                                            }}
                                                                        >
                                                                        </Form.Input>
                                                                    </Col>
                                                                    <Col xs={24} sm={8}>
                                                                        <Form.Input
                                                                            field={`${field}[description]`}
                                                                            noLabel
                                                                        >
                                                                        </Form.Input>
                                                                    </Col>
                                                                    <Col xs={24} sm={4}>
                                                                        <Form.Select
                                                                            field={`${field}[proto]`}
                                                                            noLabel
                                                                            validate={value => {
                                                                                if (!value) {
                                                                                    return '协议不能为空';
                                                                                }
                                                                                return '';
                                                                            }}
                                                                            style={{ width: '100%' }}
                                                                        >
                                                                            <Select.Option value={ServiceProto.TCP}>TCP</Select.Option>
                                                                            <Select.Option value={ServiceProto.UDP}>UDP</Select.Option>
                                                                        </Form.Select>
                                                                    </Col>
                                                                    <Col xs={24} sm={4}>
                                                                        <Form.Input
                                                                            field={`${field}[port]`}
                                                                            noLabel
                                                                            validate={value => {
                                                                                if (!value) {
                                                                                    return '端口不能为空';
                                                                                }
                                                                                // 只能为端口号
                                                                                if (!/^\d+$/.test(value)) {
                                                                                    return '端口号只能为数字';
                                                                                }
                                                                                return '';
                                                                            }}
                                                                        >
                                                                        </Form.Input>
                                                                    </Col>
                                                                    <Col xs={24} sm={2} className={styles.rightColumn}>
                                                                        <Popover
                                                                            position='left'
                                                                            style={{
                                                                                padding: 5,
                                                                            }}
                                                                            content={<>
                                                                                <Space >
                                                                                    <Button icon={<IconArrowDown />} onClick={() => {
                                                                                        let newList: Array<ServicePort> = []
                                                                                        let ports = formApi?.getValue('ports');
                                                                                        ports?.forEach((item: ServicePort) => {
                                                                                            newList.push(item)
                                                                                        })
                                                                                        let item = newList[i];
                                                                                        newList[i] = newList[i + 1];
                                                                                        newList[i + 1] = item;
                                                                                        formApi?.setValue('ports', newList);
                                                                                    }}
                                                                                        disabled={i == arrayFields.length - 1}
                                                                                    ></Button>
                                                                                    <Button icon={<IconArrowUp />} onClick={() => {
                                                                                        let newList: Array<ServicePort> = []
                                                                                        let ports = formApi?.getValue('ports');
                                                                                        ports?.forEach((item: ServicePort) => {
                                                                                            newList.push(item)
                                                                                        })
                                                                                        let item = newList[i];
                                                                                        newList[i] = newList[i - 1];
                                                                                        newList[i - 1] = item;
                                                                                        formApi?.setValue('ports', newList);
                                                                                    }
                                                                                    }
                                                                                        disabled={i == 0}
                                                                                    ></Button>
                                                                                    <Button
                                                                                        type='danger'
                                                                                        theme='borderless'
                                                                                        icon={<IconMinusCircle />}
                                                                                        onClick={() => {
                                                                                            remove()
                                                                                        }}

                                                                                    />
                                                                                </Space>
                                                                            </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover>
                                                                    </Col>
                                                                </Row>

                                                            ))}
                                                        </> :
                                                        <>
                                                            <div className={styles.addCenter}>
                                                                <Button
                                                                    size='large'
                                                                    icon={<IconPlus />}
                                                                    onClick={() => {
                                                                        add()
                                                                    }}
                                                                ></Button>
                                                            </div>

                                                        </>}
                                                </React.Fragment>}
                                        </ArrayField>


                                        <div style={{
                                            paddingTop: 20,
                                        }}>
                                            <Title heading={6} className='mb3'>{daemonMode == ServiceRouteMode.DIRECT && formatMessage({ id: 'services.edit.mode.direct' })}
                                                {daemonMode == ServiceRouteMode.FORWARD && formatMessage({ id: 'services.edit.mode.forward' })}</Title>
                                            {/* <Space className="mb10">
                                                <RadioGroup
                                                    value={daemonMode}
                                                    name='routeMode'
                                                    disabled
                                                    onChange={(e) => {
                                                        setDaemonMode(e.target.value as any)
                                                    }}
                                                    direction="horizontal">
                                                    <Radio checked={daemonMode == ServiceRouteMode.DIRECT} value={ServiceRouteMode.DIRECT}>直连模式</Radio>
                                                    <Radio checked={daemonMode == ServiceRouteMode.FORWARD} value={ServiceRouteMode.FORWARD}>路由模式</Radio>
                                                </RadioGroup>
                                            </Space> */}
                                            <Divider className="mb10" />

                                            {daemonMode == ServiceRouteMode.FORWARD && <>
                                                <ArrayField field='gatewayNodes'>
                                                    {({
                                                        add,
                                                        arrayFields
                                                    }) => <React.Fragment>
                                                            <Row className="mb20">
                                                                <Col span={20}>
                                                                    <Title heading={6} className="mb10">连接器</Title>
                                                                </Col>
                                                                {/* <Col span={4} className={styles.rightColumn}>
                                                                    {arrayFields.length > 0 &&
                                                                        <Button icon={<IconPlus />} onClick={addGatewayNodeFromMachine}></Button>
                                                                        // <Dropdown
                                                                        //     position={'bottomRight'}
                                                                        //     render={
                                                                        //         <Dropdown.Menu>
                                                                        //             <Dropdown.Item icon={<IconPlus />}
                                                                        //                 onClick={addGatewayNodeDirect}
                                                                        //             >直接添加</Dropdown.Item>
                                                                        //             <Dropdown.Item icon={<IconCopyAdd />}
                                                                        //                 onClick={addGatewayNodeFromMachine}
                                                                        //             >从设备添加</Dropdown.Item>
                                                                        //         </Dropdown.Menu>
                                                                        //     }
                                                                        // >
                                                                        //     <Button
                                                                        //         icon={<IconPlus />}
                                                                        //     ></Button>
                                                                        // </Dropdown>
                                                                    }

                                                                </Col> */}
                                                            </Row>
                                                            {arrayFields.length > 0 ? <>

                                                                <Row className={styles.tableTitle} >

                                                                    <Col span={10}>名称</Col>
                                                                    <Col span={9}>地址</Col>
                                                                    <Col span={3}>&nbsp;</Col>
                                                                    <Col span={2}>&nbsp;</Col>
                                                                </Row>
                                                                {arrayFields.map(({ field, key, remove }, i) => (
                                                                    <Row className={styles.tableBody} key={key} >
                                                                        <Col xs={24} sm={10}>
                                                                            <Form.Input
                                                                                field={`${field}[name]`}
                                                                                noLabel
                                                                                readonly
                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '名称不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col>
                                                                        <Col xs={24} sm={9}>
                                                                            <Form.Input
                                                                                field={`${field}[ipv4]`}
                                                                                noLabel
                                                                                readonly
                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '地址不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col>
                                                                        <Col xs={24} sm={3}>
                                                                            {/* <a className='link-external' target='_blank' href={getGatewayNodeUrl(i)} onClick={(e) => { e.stopPropagation() }}>
                                                                                <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }}></Button>
                                                                            </a> */}
                                                                            <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }}
                                                                                onClick={() => {
                                                                                    viewGateway(i)
                                                                                }}
                                                                            ></Button>
                                                                        </Col>

                                                                        <Col xs={24} sm={2} className={styles.rightColumn}>
                                                                            <Popover
                                                                                position='left'
                                                                                style={{
                                                                                    padding: 5,
                                                                                }}
                                                                                content={<>
                                                                                    <Space >
                                                                                        <Button icon={<IconAlignTop />}
                                                                                            disabled={i == 0}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let gatewayNodes = formApi?.getValue('gatewayNodes');
                                                                                                gatewayNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[0];
                                                                                                newList[0] = item;
                                                                                                formApi?.setValue('gatewayNodes', newList);
                                                                                                setGatewayNodes(newList)
                                                                                            }}
                                                                                        ></Button>
                                                                                        <Button icon={<IconArrowUp />}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let gatewayNodes = formApi?.getValue('gatewayNodes');
                                                                                                gatewayNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[i - 1];
                                                                                                newList[i - 1] = item;
                                                                                                formApi?.setValue('gatewayNodes', newList);
                                                                                            }
                                                                                            }
                                                                                            disabled={i == 0}
                                                                                        ></Button>
                                                                                        <Button icon={<IconArrowDown />}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let gatewayNodes = formApi?.getValue('gatewayNodes');
                                                                                                gatewayNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[i + 1];
                                                                                                newList[i + 1] = item;
                                                                                                formApi?.setValue('gatewayNodes', newList);
                                                                                            }}
                                                                                            disabled={i == arrayFields.length - 1}
                                                                                        ></Button>
                                                                                        <Button icon={<IconAlignBottom />}
                                                                                            disabled={i == arrayFields.length - 1}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let gatewayNodes = formApi?.getValue('gatewayNodes');
                                                                                                gatewayNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[arrayFields.length - 1];
                                                                                                newList[arrayFields.length - 1] = item;
                                                                                                formApi?.setValue('gatewayNodes', newList);
                                                                                                setGatewayNodes(newList)
                                                                                            }}
                                                                                        ></Button>

                                                                                        <Button
                                                                                            type='danger'
                                                                                            theme='borderless'
                                                                                            icon={<IconMinusCircle />}
                                                                                            onClick={() => {
                                                                                                remove()
                                                                                                setGatewayNodes(formApi?.getValue('gatewayNodes') as any || [])
                                                                                            }}

                                                                                        />
                                                                                    </Space>
                                                                                </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover>


                                                                        </Col>

                                                                    </Row>
                                                                ))}
                                                            </> : <>
                                                                <div className={styles.addCenter}>
                                                                    {/* <Dropdown
                                                                        position={'bottomRight'}
                                                                        render={
                                                                            <Dropdown.Menu>
                                                                                <Dropdown.Item icon={<IconPlus />}
                                                                                    onClick={addGatewayNodeDirect}
                                                                                >直接添加</Dropdown.Item>
                                                                                <Dropdown.Item icon={<IconCopyAdd />}
                                                                                    onClick={addGatewayNodeFromMachine}
                                                                                >从设备添加</Dropdown.Item>
                                                                            </Dropdown.Menu>
                                                                        }
                                                                    >
                                                                        <Button
                                                                            size='large'
                                                                            icon={<IconPlus />}
                                                                        ></Button>
                                                                    </Dropdown> */}

                                                                    <Button
                                                                        onClick={addGatewayNodeFromMachine}
                                                                        size='large'
                                                                        icon={<IconPlus />}
                                                                    ></Button>
                                                                </div>
                                                            </>}
                                                        </React.Fragment>}

                                                </ArrayField>


                                                <Divider className="mb20" />

                                                <ArrayField field='subnetNodes'>
                                                    {({
                                                        add,
                                                        arrayFields
                                                    }) => <React.Fragment>
                                                            <Row className="mb20">
                                                                <Col span={20}>
                                                                    <Title heading={6} className="mb10">子网节点</Title>
                                                                </Col>
                                                                {/* <Col span={4} className={styles.rightColumn}>
                                                                    {arrayFields.length > 0 &&
                                                                        <>
                                                                            <Button
                                                                                onClick={() => { addSubnetNodeDirect() }}
                                                                                icon={<IconPlus />}
                                                                            ></Button>
                                                                            
                                                                        </>
                                                                    }

                                                                </Col> */}
                                                            </Row>
                                                            {arrayFields.length > 0 ? <>

                                                                <Row className={styles.tableTitle} >
                                                                    <Col span={12}>名称</Col>
                                                                    <Col span={12}>地址</Col>
                                                                    {/* <Col span={3}>端口</Col> */}
                                                                    {/* <Col span={2}>&nbsp;</Col> */}
                                                                </Row>
                                                                {arrayFields.map(({ field, key, remove }, i) => (
                                                                    <Row className={styles.tableBody} key={key} >
                                                                        <Col xs={24} sm={12}>
                                                                            <Form.Input
                                                                                field={`${field}[name]`}
                                                                                noLabel

                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '名称不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col>
                                                                        <Col xs={24} sm={12}>
                                                                            <Form.Input
                                                                                field={`${field}[ipv4]`}
                                                                                noLabel
                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '地址不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col>
                                                                        {/* <Col xs={24} sm={3}>
                                                                            <Form.Input
                                                                                field={`${field}[port]`}
                                                                                noLabel
                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '端口不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col> */}
                                                                        {/* <Col xs={24} sm={2} className={styles.rightColumn}>
                                                                            <Popover
                                                                                position='left'

                                                                                style={{
                                                                                    padding: 5
                                                                                }}
                                                                                content={<>
                                                                                    <Space >
                                                                                        <Button icon={<IconArrowDown />}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let subnetNodes = formApi?.getValue('subnetNodes');
                                                                                                subnetNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[i + 1];
                                                                                                newList[i + 1] = item;
                                                                                                formApi?.setValue('subnetNodes', newList);
                                                                                                setSubnetNodes(newList)
                                                                                            }}
                                                                                            disabled={i == arrayFields.length - 1}
                                                                                        ></Button>
                                                                                        <Button icon={<IconArrowUp />}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let subnetNodes = formApi?.getValue('subnetNodes');
                                                                                                subnetNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[i - 1];
                                                                                                newList[i - 1] = item;
                                                                                                formApi?.setValue('subnetNodes', newList);
                                                                                                setSubnetNodes(newList)
                                                                                            }
                                                                                            }
                                                                                            disabled={i == 0}

                                                                                        ></Button>
                                                                                        <Button
                                                                                            type='danger'
                                                                                            theme='borderless'
                                                                                            icon={<IconMinusCircle />}
                                                                                            onClick={() => {
                                                                                                remove()
                                                                                                setSubnetNodes(formApi?.getValue('subnetNodes') as any || [])
                                                                                            }}

                                                                                        />
                                                                                    </Space>
                                                                                </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover>


                                                                        </Col> */}

                                                                    </Row>
                                                                ))}
                                                            </> : <>
                                                                <div className={styles.addCenter}>
                                                                    <Button
                                                                        size='large'
                                                                        onClick={() => { addSubnetNodeDirect() }}
                                                                        icon={<IconPlus />}
                                                                    ></Button>
                                                                    {/* <Dropdown
                                                                    position={'bottomRight'}
                                                                    render={
                                                                        <Dropdown.Menu>
                                                                            <Dropdown.Item icon={<IconPlus />}
                                                                                onClick={addSubnetNodeDirect}
                                                                            >直接添加</Dropdown.Item>
                                                                            <Dropdown.Item icon={<IconCopyAdd />}
                                                                                onClick={addSubnetNodeFromMachine}
                                                                            >从设备添加</Dropdown.Item>
                                                                        </Dropdown.Menu>
                                                                    }
                                                                >
                                                                    <Button
                                                                        size='large'
                                                                        icon={<IconPlus />}
                                                                    ></Button>
                                                                </Dropdown> */}
                                                                </div>
                                                            </>}
                                                        </React.Fragment>}
                                                </ArrayField>
                                            </>}
                                            {daemonMode == ServiceRouteMode.DIRECT && <>
                                                <ArrayField field='subnetNodes'>
                                                    {({
                                                        add,
                                                        arrayFields
                                                    }) => <React.Fragment>
                                                            <Row className="mb20">
                                                                <Col span={20}>
                                                                    <Title heading={6} className="mb10">节点</Title>
                                                                </Col>
                                                                {/* <Col span={4} className={styles.rightColumn}>
                                                                    {arrayFields.length > 0 &&
                                                                        <>
                                                                            <Button
                                                                                onClick={() => { daemonMode == ServiceRouteMode.DIRECT ? addSubnetNodeFromMachine() : addSubnetNodeDirect() }}
                                                                                icon={<IconPlus />}
                                                                            ></Button>
                                                                        </>
                                                                    }
                                                                </Col> */}
                                                            </Row>
                                                            {arrayFields.length > 0 ? <>

                                                                <Row className={styles.tableTitle} >
                                                                    <Col span={10}>名称</Col>
                                                                    <Col span={9}>地址</Col>
                                                                    <Col span={3}>&nbsp;</Col>
                                                                    <Col span={2}>&nbsp;</Col>
                                                                </Row>
                                                                {arrayFields.map(({ field, key, remove }, i) => (
                                                                    <Row className={styles.tableBody} key={key} >
                                                                        <Col xs={24} sm={10}>
                                                                            <Form.Input
                                                                                field={`${field}[name]`}
                                                                                noLabel

                                                                                readonly={daemonMode == ServiceRouteMode.DIRECT}
                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '名称不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col>
                                                                        <Col xs={24} sm={9}>
                                                                            <Form.Input
                                                                                field={`${field}[ipv4]`}
                                                                                readonly={daemonMode == ServiceRouteMode.DIRECT}
                                                                                noLabel
                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '地址不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col>
                                                                        <Col xs={24} sm={3}>
                                                                            {/* <a className='link-external' target='_blank' href={getDirectNodeUrl(i)} onClick={(e) => { e.stopPropagation() }}>
                                                                                <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }}></Button>
                                                                            </a> */}
                                                                            <Button icon={<IconArrowUpRight />} style={{ marginTop: 12, width: 32 }}
                                                                                onClick={() => {
                                                                                    viewSubnet(i)
                                                                                }}
                                                                            ></Button>
                                                                        </Col>
                                                                        {/* <Col xs={24} sm={3}>
                                                                            <Form.Input
                                                                                field={`${field}[port]`}
                                                                                noLabel
                                                                                validate={value => {
                                                                                    if (!value) {
                                                                                        return '端口不能为空';
                                                                                    }
                                                                                    return '';
                                                                                }}
                                                                            >
                                                                            </Form.Input>
                                                                        </Col> */}
                                                                        <Col xs={24} sm={2} className={styles.rightColumn}>
                                                                            {/* <Popover
                                                                                position='left'

                                                                                style={{
                                                                                    padding: 5,
                                                                                }}
                                                                                content={<>
                                                                                    <Space >
                                                                                        <Button icon={<IconArrowDown />}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let subnetNodes = formApi?.getValue('subnetNodes');
                                                                                                subnetNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[i + 1];
                                                                                                newList[i + 1] = item;
                                                                                                formApi?.setValue('subnetNodes', newList);
                                                                                                setSubnetNodes(newList)
                                                                                            }}
                                                                                            disabled={i == arrayFields.length - 1}
                                                                                        ></Button>
                                                                                        <Button icon={<IconArrowUp />}
                                                                                            onClick={() => {
                                                                                                let newList: Array<ServiceNode> = []
                                                                                                let subnetNodes = formApi?.getValue('subnetNodes');
                                                                                                subnetNodes?.forEach((item: ServiceNode) => {
                                                                                                    newList.push(item)
                                                                                                })
                                                                                                let item = newList[i];
                                                                                                newList[i] = newList[i - 1];
                                                                                                newList[i - 1] = item;
                                                                                                formApi?.setValue('subnetNodes', newList);
                                                                                                setSubnetNodes(newList)
                                                                                            }
                                                                                            }
                                                                                            disabled={i == 0}

                                                                                        ></Button>
                                                                                        <Button
                                                                                            type='danger'
                                                                                            theme='borderless'
                                                                                            icon={<IconMinusCircle />}
                                                                                            onClick={() => {
                                                                                                remove()
                                                                                                setSubnetNodes(formApi?.getValue('subnetNodes') as any || [])
                                                                                            }}

                                                                                        />
                                                                                    </Space>
                                                                                </>}><Button style={{ marginTop: 12 }} icon={<IconMore />}></Button></Popover> */}
                                                                            <Button
                                                                                style={{ marginTop: 12 }}
                                                                                type='danger'
                                                                                theme='borderless'
                                                                                icon={<IconMinusCircle />}
                                                                                onClick={() => {
                                                                                    remove()
                                                                                    setSubnetNodes(formApi?.getValue('subnetNodes') as any || [])
                                                                                }}

                                                                            />

                                                                        </Col>

                                                                    </Row>
                                                                ))}
                                                            </> : <>
                                                                <div className={styles.addCenter}>
                                                                    <Button
                                                                        size='large'
                                                                        onClick={() => { daemonMode == ServiceRouteMode.DIRECT ? addSubnetNodeFromMachine() : addSubnetNodeDirect() }}
                                                                        icon={<IconPlus />}
                                                                    ></Button>
                                                                    {/* <Dropdown
                                                                    position={'bottomRight'}
                                                                    render={
                                                                        <Dropdown.Menu>
                                                                            <Dropdown.Item icon={<IconPlus />}
                                                                                onClick={addSubnetNodeDirect}
                                                                            >直接添加</Dropdown.Item>
                                                                            <Dropdown.Item icon={<IconCopyAdd />}
                                                                                onClick={addSubnetNodeFromMachine}
                                                                            >从设备添加</Dropdown.Item>
                                                                        </Dropdown.Menu>
                                                                    }
                                                                >
                                                                    <Button
                                                                        size='large'
                                                                        icon={<IconPlus />}
                                                                    ></Button>
                                                                </Dropdown> */}
                                                                </div>
                                                            </>}
                                                        </React.Fragment>}
                                                </ArrayField>
                                            </>}

                                        </div>
                                    </>
                                }

                            </>

                        </Form>
                    </div>
                }
            </Skeleton>
        </Modal>


        {machineSelectorVisible && <MachineSelector
            multi={opt == 'SELECT_RDP_MACHINE' || opt == 'SELECT_DAEMON_SUBNET' ? false : true}
            avilibleOs={opt == 'SELECT_RDP_MACHINE' ? ['windows', 'macOS'] : undefined}
            value={opt == 'SELECT_RDP_MACHINE' ?
                machine :
                opt == 'SELECT_DAEMON_SUBNET' ? formApi?.getValue('subnetNodes')?.map(node => node.ipv4) :
                    formApi?.getValue('gatewayNodes')?.map(node => node.ipv4)}
            gateway={opt == 'SELECT_DAEMON_GATEWAY' ? true : false}
            onChange={(value => {
                setMachineSelectorVisible(false)
                if (opt == 'SELECT_RDP_MACHINE') {
                    setMachine(value as Machine)
                } else if (opt == 'SELECT_DAEMON_GATEWAY') {
                    let newList: Array<ServiceNode> = []
                    let subnetNodes = formApi?.getValue('gatewayNodes');
                    subnetNodes?.forEach((item: ServiceNode) => {
                        newList.push(item)
                    })
                    if (value instanceof Machine) {

                        let exist = false;
                        subnetNodes?.forEach(node => {
                            if (node.ipv4 == value?.ipv4) {
                                exist = true;
                            }
                        })
                        if (!exist) {

                            newList.push(
                                new ServiceNode({
                                    ipv4: value.ipv4,
                                    ipv6: value.ipv6,
                                    name: value.givenName,
                                    type: ServiceNodeType.GATEWAY,
                                    machine: value
                                }))
                        }
                    } else {
                        value.forEach((item) => {
                            let exist = false;
                            subnetNodes?.forEach(node => {
                                if (node.ipv4 == item?.ipv4) {
                                    exist = true;
                                }
                            })
                            if (!exist) {

                                newList.push(new ServiceNode({
                                    ipv4: item.ipv4,
                                    ipv6: item.ipv6,
                                    name: item.givenName,
                                    type: ServiceNodeType.GATEWAY,
                                    machine: item
                                }))
                            }
                        })
                    }
                    formApi?.setValue('gatewayNodes', newList);
                    setGatewayNodes(newList)
                } else if (opt == 'SELECT_DAEMON_SUBNET') {
                    let newList: Array<ServiceNode> = []
                    let subnetNodes = formApi?.getValue('subnetNodes');
                    subnetNodes?.forEach((item: ServiceNode) => {
                        newList.push(item)
                    })
                    if (value instanceof Machine) {
                        let exist = false;
                        subnetNodes?.forEach(node => {
                            if (node.ipv4 == value?.ipv4) {
                                exist = true;
                            }
                        })
                        if (!exist) {

                            newList.push(
                                new ServiceNode({
                                    ipv4: value.ipv4,
                                    ipv6: value.ipv6,
                                    name: value.givenName,
                                    type: ServiceNodeType.SUBNET,
                                    machine: value
                                }))
                        }
                    } else {
                        value.forEach((item) => {

                            let exist = false;
                            subnetNodes?.forEach(node => {
                                if (node.ipv4 == item?.ipv4) {
                                    exist = true;
                                }
                            })
                            if (!exist) {

                                newList.push(new ServiceNode({
                                    ipv4: item.ipv4,
                                    ipv6: item.ipv6,
                                    name: item.givenName,
                                    type: ServiceNodeType.SUBNET,
                                    machine: item
                                }))
                            }
                        })
                    }
                    formApi?.setValue('subnetNodes', newList);
                    setSubnetNodes(newList)
                }
            })}
            close={() => setMachineSelectorVisible(false)}
        ></MachineSelector>}
        {machineViewerVisible && gatewayIp && <MachineViewer ip={gatewayIp} close={() => setMachineViewerVisible(false)}></MachineViewer>}
    </>
}

export default Index