import { Service, ServiceType, ServiceNode, ServiceOrigin, ServiceGroup, ServiceProto, ServiceRouteMode, ServiceNodeType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';
import { getMachine, setMachineKeyExpiry, authorizeMachine } from '@/services/device';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { AdvertisedRoute } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";
import { isIPInRange } from '@/utils/format';

function gatewayValidate(gatewayNodes: Array<ServiceNode>, sunnetNodes: Array<ServiceNode>   ) {
    const promises = gatewayNodes.map((node: ServiceNode) => {
        return getMachine(node.ipv4)
    })

    return new Promise((resolve, reject) => {
        Promise.all(promises).then((machines: Array<Machine>) => {
            let routes : Array<string> = [];
            machines.forEach((machine: Machine) => {
                if(machine.advertisedRoutes) {
                    machine.advertisedRoutes.forEach((route: string) => {  
                        routes.push(route)
                    });
                }
            })
    
            let unmatchNodes : Array<string> = [];
            
            sunnetNodes.forEach((node: ServiceNode) => {
                let matched = false;
                for (let i = 0; i < routes.length; i++) {
                    if(isIPInRange(node.ipv4, routes[i])) {
                        matched = true;
                        break;
                    }
                }
                if(!matched) {
                    unmatchNodes.push(node.ipv4)
                }
                
            })
    
            if(unmatchNodes.length > 0) {
                reject(unmatchNodes)
            }
            resolve("")
        })
    
    });

    
}

export default gatewayValidate;