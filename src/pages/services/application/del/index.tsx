import { useState, useContext } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';
import { Application } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/application_pb";
import { flylayerClient } from '@/services/core';



import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: Application
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const _flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={formatMessage({ id: 'services.application.delete.title' }).replace('{name}', props.record.name)}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

            flylayerClient.deleteApplication({
                id: props.record.id
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'services.application.delete.success' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'services.application.delete.failed' }), position: "bottomRight" })
            }).finally(() => setLoading(false))

        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'>
            {formatMessage({ id: 'services.application.delete.description' })
                .replace('{name}', `${props.record.name}`)
                .replace('{description}', props.record.description ? `(${props.record.description})` : '')}
        </Paragraph>
        <Paragraph className='mb20'>
            {formatMessage({ id: 'services.application.delete.confirmText' }).replace('{name}', props.record.name)}
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;