import { FC } from 'react';
import { Breadcrumb } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';

import Application from './index';
import { useLocale } from '@/locales';

const Index: FC = () => {
    const { formatMessage } = useLocale();
    return <>
        <div className='general-page'><Breadcrumb routes={
            [
                {
                    path: `${BASE_PATH}/services`,
                    href: `${BASE_PATH}/services`,
                    name: formatMessage({ id: 'services.group.breadcrumb.allServices' })
                },
                {
                    name: formatMessage({ id: 'services.application.breadcrumb.application' }),
                }
            ]
        }>
        </Breadcrumb>
            <Application />
        </div>
    </>
}

export default Index;