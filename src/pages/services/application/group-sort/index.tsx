import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Notification, Space, Button, Table, Skeleton, Row, Col, Popconfirm, Input, Popover } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { IconArrowDown, IconArrowUp, IconAlignTop, IconAlignBottom, IconDelete, IconEdit, IconTick, IconClear } from '@douyinfe/semi-icons';
import pinyin from 'tiny-pinyin';
import { sanitizeLabel } from '@/utils/common';
import { Application, ApplicationGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/application_pb";

import styles from './index.module.scss';

import { flylayerClient } from '@/services/core';

const { Text, Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void
}


const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [loading, setLoading] = useState(false);

    const flynet = useContext(FlynetGeneralContext);
    const [data, setData] = useState<ApplicationGroup[]>([]);
    const [applications, setApplications] = useState<Application[]>([]);

    const [newName, setNewName] = useState<string>('');
    const [newVisible, setNewVisible] = useState<boolean>(false);
    const [newLoading, setNewLoading] = useState<boolean>(false);

    const [editId, setEditId] = useState<bigint>();
    const [editName, setEditName] = useState<string>('');
    const [editLoading, setEditLoading] = useState<boolean>(false);

    const [mapGroupAppCount, setMapGroupAppCount] = useState<{ [key: string]: number }>({})

    const query = async () => {
        setLoading(true);
        try {
            const res = await flylayerClient.listApplicationGroups({
                flynetId: flynet.id
            })
            setData(res.applicationGroups);
            const resApplications = await flylayerClient.listApplications({
                flynetId: flynet.id
            })
            setApplications(resApplications.applications);
            let map: { [key: string]: number } = {};
            resApplications.applications.forEach(item => {
                if (item.group?.id) {
                    let key = item.group.id + ''
                    map[key] = (map[key] || 0) + 1;
                }
            })
            setMapGroupAppCount(map);
        } catch (e) {
            Notification.error({ title: formatMessage({ id: 'services.application.groupSort.error.loadCategoryFailed' }), content: e });
        } finally {
            setLoading(false);
        }
    }

    const [saveLoading, setSaveLoading] = useState(false);
    const [delLoading, setDelLoading] = useState(false);

    const [sortModified, setSortModified] = useState(false);

    useEffect(() => {
        query();
    }, [])

    const handleSubmit = () => {
        setSaveLoading(true);
        flylayerClient.sortApplicationGroups({
            flynetId: flynet.id,
            applicationGroups: data
        }).then(() => {
            Notification.success({ title: formatMessage({ id: 'services.application.groupSort.success.saveSuccess' }) });
            props.close();
            props.success && props.success();
        }).catch((e) => {
            Notification.error({ title: formatMessage({ id: 'services.application.groupSort.error.saveFailed' }), content: e.message });
        }).finally(() => {
            setSaveLoading(false);
        })
    }

    const columns = [
        {
            title: formatMessage({ id: 'services.application.table.name' }),
            dataIndex: 'name',
            render: (field: string, record: ApplicationGroup) => {
                return <>
                    {editId == record.id ?
                        <Space>
                            <Input value={editName} autoFocus size='small' onChange={(val) => setEditName(val)}></Input>
                            <Button size='small' theme='solid' onClick={() => {
                                if (!editName || editName == '' || editName.trim() == '') {
                                    Notification.error({ title: formatMessage({ id: 'services.application.groupSort.error.nameRequired' }) });
                                    return;
                                }
                                setEditLoading(true);
                                flylayerClient.updateApplicationGroup({
                                    flynetId: flynet.id,

                                    applicationGroup: {
                                        ...record,
                                        alias: editName,
                                        name: sanitizeLabel(pinyin.convertToPinyin(editName, '', true)),
                                    }
                                }).then(() => {
                                    Notification.success({ title: formatMessage({ id: 'services.application.groupSort.success.modifySuccess' }) });
                                    let newData = [...data];
                                    let index = newData.findIndex(item => item.id == record.id);
                                    newData[index].alias = editName;
                                    setData(newData);
                                    setEditId(undefined);

                                }).catch((e) => {
                                    Notification.error({ title: formatMessage({ id: 'services.application.groupSort.error.modifyFailed' }), content: e.message });
                                }).finally(() => {
                                    setEditLoading(false);
                                })
                            }} loading={editLoading} icon={<IconTick />}></Button>
                            <Button size='small' theme='solid' type='primary' onClick={() => {
                                setEditId(undefined);
                                setEditName('');
                            }} icon={<IconClear />}></Button>
                        </Space> :
                        <>
                            <Text>{record.alias} ({field})</Text>
                            <Button size='small' icon={<IconEdit />} onClick={() => {
                                setEditId(record.id);
                                setEditName(record.alias);
                            }}></Button>
                        </>}

                </>
            }
        },

        {
            title: '',
            dataIndex: 'operate',
            width: 180,
            render: (field: string, record: ApplicationGroup, index: number) => {
                return <div className='btn-right-column'><Space>
                    <Button size='small' disabled={index == 0}

                        title={formatMessage({ id: 'services.application.groupSort.button.moveToTop' })}
                        theme='solid'
                        icon={<IconAlignTop

                            onClick={(e) => {
                                setSortModified(true);
                                if (index == 0) {
                                    return;
                                }
                                e.stopPropagation();
                                // 把数据移到数据的最前面
                                let newData = [...data];
                                let temp = newData[index];
                                newData.splice(index, 1);
                                newData.unshift(temp);
                                setData([...newData]);
                            }}
                        ></IconAlignTop>} />
                    <Button size='small' disabled={index == 0}

                        title={formatMessage({ id: 'services.application.groupSort.button.moveUp' })}
                        icon={<IconArrowUp
                            onClick={(e) => {
                                setSortModified(true);
                                if (index == 0) {
                                    return;
                                }
                                e.stopPropagation();
                                let newData = [...data];

                                let temp = newData[index - 1];
                                newData[index - 1] = newData[index];
                                newData[index] = temp;
                                setData([...newData]);
                            }}
                        ></IconArrowUp>}></Button>
                    <Button size='small' disabled={index == data.length - 1}

                        title={formatMessage({ id: 'services.application.groupSort.button.moveDown' })}
                        icon={<IconArrowDown
                            onClick={(e) => {
                                setSortModified(true);
                                if (index == data.length - 1) {
                                    return;
                                }
                                e.stopPropagation();
                                let newData = [...data];
                                let temp = newData[index + 1];
                                newData[index + 1] = newData[index];
                                newData[index] = temp;
                                setData([...newData]);
                            }}

                        ></IconArrowDown>}></Button>
                    <Button size='small' theme='solid'
                        title={formatMessage({ id: 'services.application.groupSort.button.moveToBottom' })} disabled={index == data.length - 1} icon={<IconAlignBottom
                            onClick={(e) => {
                                setSortModified(true);
                                if (index == data.length - 1) {
                                    return;
                                }
                                e.stopPropagation();
                                let newData = [...data];
                                let temp = newData[index];
                                newData.splice(index, 1);
                                newData.push(temp);
                                setData([...newData]);
                            }}></IconAlignBottom>}></Button>
                    {mapGroupAppCount[record.id + ''] > 0 ? <Button size='small' type='danger'
                        theme='solid'
                        disabled
                        icon={<IconDelete></IconDelete>}
                    ></Button> : <Popconfirm
                        title={formatMessage({ id: 'services.application.groupSort.confirmDelete' })}
                        okButtonProps={{ loading: delLoading }}
                        onConfirm={() => {
                            flylayerClient.deleteApplicationGroup({
                                id: record.id
                            }).then(() => {
                                Notification.success({ title: formatMessage({ id: 'services.application.groupSort.success.deleteSuccess' }) });
                                let newData = [...data];
                                newData.splice(index, 1);
                                setData(newData);
                            }).catch((e) => {
                                Notification.error({ title: formatMessage({ id: 'services.application.groupSort.error.deleteFailed' }), content: e.message });
                            }).finally(() => {
                                setDelLoading(false);
                            })

                        }}

                    >
                        <Button size='small' type='danger'
                            theme='solid'
                            icon={<IconDelete></IconDelete>}
                        ></Button>

                    </Popconfirm>



                    }


                </Space></div>
            }
        }
    ]

    return <><Modal
        width={600}
        title={formatMessage({ id: 'services.application.groupSort.title' })}
        visible={true}
        onCancel={props.close}
        hasCancel={false}
        onOk={handleSubmit}
        okButtonProps={{ loading: saveLoading, disabled: !sortModified }}
        className='semi-modal'
        maskClosable={false}
        bodyStyle={{ padding: '20px 0' }}
    ><Skeleton loading={loading} placeholder={
        <>
            <Row>
                <Col span={24}>
                    <Skeleton.Image style={{ height: 400 }} />
                </Col>
            </Row>
        </>
    }>
            <Row className='mb10'>
                <Col span={12}>
                    {sortModified &&
                    <Paragraph type='warning'>{formatMessage({ id: 'services.application.groupSort.sortWarning' })}</Paragraph>}
                </Col>
                <Col span={12} style={{ display: 'flex', justifyContent: 'end' }}>

                    <Popover trigger='click' position='bottomRight' visible={newVisible} content={<div className='p10'>
                        <Space>

                            <Input value={newName} onChange={(val) => setNewName(val)} placeholder={formatMessage({ id: 'services.application.groupSort.form.namePlaceholder' })} />
                            <Button onClick={() => {
                                setNewLoading(true);
                                flylayerClient.createApplicationGroup({
                                    flynetId: flynet.id,
                                    applicationGroup: {
                                        name: sanitizeLabel(pinyin.convertToPinyin(newName, '', true)),
                                        alias: newName
                                    }
                                }).then(() => {
                                    Notification.success({ title: formatMessage({ id: 'services.application.groupSort.success.createSuccess' }) });
                                    query();

                                    setNewVisible(false);
                                }).catch((e) => {
                                    Notification.error({ title: formatMessage({ id: 'services.application.groupSort.error.createCategoryFailed' }), content: e.message });
                                }).finally(() => {
                                    setNewLoading(false);
                                })
                            }} theme='solid' loading={newLoading} disabled={!newName || newName == ''}>{formatMessage({ id: 'services.application.groupSort.button.confirm' })}</Button>
                        </Space>

                    </div>}>
                        <Button theme='solid' onClick={() => setNewVisible(true)}>{formatMessage({ id: 'components.common.new' })}</Button>
                    </Popover>
                </Col>
            </Row>
            <Table
                columns={columns}
                dataSource={data}
                rowKey={(record?: ApplicationGroup) => record ? record.id + '' : ''}
                pagination={false}
                size='small'
            ></Table>
        </Skeleton>
    </Modal></>
}

export default Index;