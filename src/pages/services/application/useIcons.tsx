import {
    IconAbsoluteStroked,
    IconActivity,
    IconAlarm,
    IconAlertCircle,
    IconAlertTriangle,
    IconAlignBottom,
    IconAlignCenter,
    IconAlignCenterVertical,
    IconAlignHCenterStroked,
    IconAlignHLeftStroked,
    IconAlignHRightStroked,
    IconAlignJustify,
    IconAlignLeft,
    IconAlignRight,
    IconAlignTop,
    IconAlignVBotStroked,
    IconAlignVBottomStroked,
    IconAlignVCenterStroked,
    IconAlignVTopStroked,
    IconApartment,
    IconAppCenter,
    IconApps,
    IconArchive,
    IconArrowDown,
    IconArrowDownLeft,
    IconArrowDownRight,
    IconArrowLeft,
    IconArrowRight,
    IconArrowUp,
    IconArrowUpLeft,
    IconArrowUpRight,
    IconArticle,
    IconAscend,
    IconAt,
    IconBackTop,
    IconBackward,
    IconBarChartHStroked,
    IconBarChartVStroked,
    IconBeaker,
    IconBell,
    IconBellStroked,
    IconBold,
    IconBolt,
    IconBookH5Stroked,
    IconBookOpenStroked,
    IconBookStroked,
    IconBookmark,
    IconBookmarkAddStroked,
    IconBookmarkDeleteStroked,
    IconBottomCenterStroked,
    IconBottomLeftStroked,
    IconBottomRightStroked,
    IconBox,
    IconBrackets,
    IconBranch,
    IconBriefStroked,
    IconBriefcase,
    IconBulb,
    IconButtonStroked,
    IconBytedanceLogo,
    IconCalendar,
    IconCalendarClock,
    IconCalendarStroked,
    IconCamera,
    IconCandlestickChartStroked,
    IconCaretdown,
    IconCaretup,
    IconCarouselStroked,
    IconCart,
    IconCaseSensitive,
    IconCenterLeftStroked,
    IconCenterRightStroked,
    IconChainStroked,
    IconCheckChoiceStroked,
    IconCheckCircleStroked,
    IconCheckList,
    IconCheckboxIndeterminate,
    IconCheckboxTick,
    IconChecklistStroked,
    IconChevronDown,
    IconChevronDownStroked,
    IconChevronLeft,
    IconChevronRight,
    IconChevronRightStroked,
    IconChevronUp,
    IconChevronUpDown,
    IconClear,
    IconClock,
    IconClose,
    IconCloud,
    IconCloudStroked,
    IconCloudUploadStroked,
    IconCode,
    IconCodeStroked,
    IconCoinMoneyStroked,
    IconColorPalette,
    IconColumnsStroked,
    IconCommand,
    IconComment,
    IconCommentStroked,
    IconComponent,
    IconComponentPlaceholderStroked,
    IconComponentStroked,
    IconConfigStroked,
    IconConnectionPoint1,
    IconConnectionPoint2,
    IconContrast,
    IconCopy,
    IconCopyAdd,
    IconCopyStroked,
    IconCornerRadiusStroked,
    IconCreditCard,
    IconCrop,
    IconCrossCircleStroked,
    IconCrossStroked,
    IconCrown,
    IconCustomerSupport,
    IconCustomerSupportStroked,
    IconCustomize,
    IconDelete,
    IconDeleteStroked,
    IconDescend,
    IconDescend2,
    IconDesktop,
    IconDisc,
    IconDislikeThumb,
    IconDivide,
    IconDongchediLogo,
    IconDoubleChevronLeft,
    IconDoubleChevronRight,
    IconDownCircleStroked,
    IconDownload,
    IconDownloadStroked,
    IconDuration,
    IconEdit,
    IconEdit2Stroked,
    IconEditStroked,
    IconElementStroked,
    IconEmoji,
    IconExit,
    IconExpand,
    IconExport,
    IconExternalOpen,
    IconExternalOpenStroked,
    IconEyeClosed,
    IconEyeClosedSolid,
    IconEyeOpened,
    IconFacebook,
    IconFaceuLogo,
    IconFastForward,
    IconFastFoward,
    IconFavoriteList,
    IconFeishuLogo,
    IconFemale,
    IconFigma,
    IconFile,
    IconFillStroked,
    IconFilledArrowDown,
    IconFilledArrowUp,
    IconFilpVertical,
    IconFilter,
    IconFingerLeftStroked,
    IconFixedStroked,
    IconFlag,
    IconFlipHorizontal,
    IconFlowChartStroked,
    IconFolder,
    IconFolderOpen,
    IconFolderStroked,
    IconFollowStroked,
    IconFont,
    IconFontColor,
    IconForward,
    IconForwardStroked,
    IconFullScreenStroked,
    IconGallery,
    IconGift,
    IconGiftStroked,
    IconGit,
    IconGithubLogo,
    IconGitlabLogo,
    IconGlobe,
    IconGlobeStroke,
    IconGridRectangle,
    IconGridSquare,
    IconGridStroked,
    IconGridView,
    IconGridView1,
    IconH1,
    IconH2,
    IconH3,
    IconH4,
    IconH5,
    IconH6,
    IconH7,
    IconH8,
    IconH9,
    IconHandle,
    IconHash,
    IconHeartStroked,
    IconHelm,
    IconHelpCircle,
    IconHelpCircleStroked,
    IconHistogram,
    IconHistory,
    IconHn,
    IconHome,
    IconHomeStroked,
    IconHorn,
    IconHourglass,
    IconHourglassStroked,
    IconIdCard,
    IconIdentity,
    IconImage,
    IconImageStroked,
    IconImport,
    IconInbox,
    IconIndenpentCornersStroked,
    IconIndentLeft,
    IconIndentRight,
    IconIndependentCornersStroked,
    IconInfoCircle,
    IconInherit,
    IconInheritStroked,
    IconInnerSectionStroked,
    IconInstagram,
    IconInteractiveStroked,
    IconInviteStroked,
    IconIssueStroked,
    IconItalic,
    IconJianying,
    IconKanban,
    IconKey,
    IconKeyStroked,
    IconLanguage,
    IconLayers,
    IconLeftCircleStroked,
    IconLightningStroked,
    IconLikeHeart,
    IconLikeThumb,
    IconLineChartStroked,
    IconLineHeight,
    IconLink,
    IconList,
    IconListView,
    IconLive,
    IconLoading,
    IconLock,
    IconLockStroked,
    IconLoopTextStroked,
    IconMail,
    IconMailStroked,
    IconMailStroked1,
    IconMale,
    IconMapPin,
    IconMapPinStroked,
    IconMarginLeftStroked,
    IconMarginStroked,
    IconMark,
    IconMaximize,
    IconMember,
    IconMenu,
    IconMicrophone,
    IconMicrophoneOff,
    IconMinimize,
    IconMinus,
    IconMinusCircle,
    IconMinusCircleStroked,
    IconMinusStroked,
    IconModalStroked,
    IconMoneyExchangeStroked,
    IconMonitorStroked,
    IconMoon,
    IconMore,
    IconMoreStroked,
    IconMusic,
    IconMusicNoteStroked,
    IconMute,
    IconNineGridStroked,
    IconNoteMoneyStroked,
    IconOption,
    IconOrderedList,
    IconOrderedListStroked,
    IconPaperclip,
    IconPause,
    IconPercentage,
    IconPhone,
    IconPhoneStroke,
    IconPieChart2Stroked,
    IconPieChartStroked,
    IconPiechartH5Stroked,
    IconPipixiaLogo,
    IconPlay,
    IconPlayCircle,
    IconPlus,
    IconPlusCircle,
    IconPlusCircleStroked,
    IconPlusStroked,
    IconPriceTag,
    IconPrint,
    IconPrizeStroked,
    IconPulse,
    IconPuzzle,
    IconQingyan,
    IconQrCode,
    IconQuit,
    IconQuote,
    IconRadio,
    IconRankingCardStroked,
    IconRealSizeStroked,
    IconRedo,
    IconRedoStroked,
    IconRefresh,
    IconRefresh2,
    IconRegExp,
    IconReply,
    IconReplyStroked,
    IconResso,
    IconRestart,
    IconRingChartStroked,
    IconRotate,
    IconRotationStroked,
    IconRoute,
    IconRowsStroked,
    IconSafe,
    IconSave,
    IconSaveStroked,
    IconScan,
    IconScissors,
    IconSearch,
    IconSearchStroked,
    IconSectionStroked,
    IconSemiLogo,
    IconSend,
    IconSendMsgStroked,
    IconSendStroked,
    IconServer,
    IconServerStroked,
    IconSetting,
    IconSettingStroked,
    IconShareMoneyStroked,
    IconShareStroked,
    IconShield,
    IconShieldStroked,
    IconShift,
    IconShoppingBag,
    IconShrink,
    IconShrinkScreenStroked,
    IconSidebar,
    IconSignal,
    IconSimilarity,
    IconSmallTriangleDown,
    IconSmallTriangleLeft,
    IconSmallTriangleRight,
    IconSmallTriangleTop,
    IconSmartphoneCheckStroked,
    IconSmartphoneStroked,
    IconSong,
    IconSonicStroked,
    IconSort,
    IconSortStroked,
    IconSourceControl,
    IconSpin,
    IconStackBarChartStroked,
    IconStar,
    IconStarStroked,
    IconStop,
    IconStopwatchStroked,
    IconStoryStroked,
    IconStrikeThrough,
    IconSun,
    IconSync,
    IconTabArrowStroked,
    IconTabsStroked,
    IconTaskMoneyStroked,
    IconTemplate,
    IconTemplateStroked,
    IconTerminal,
    IconTestScoreStroked,
    IconText,
    IconTextRectangle,
    IconTextStroked,
    IconThumbUpStroked,
    IconTick,
    IconTickCircle,
    IconTicketCodeExchangeStroked,
    IconTicketCodeStroked,
    IconTiktokLogo,
    IconTop,
    IconTopCenterStroked,
    IconTopLeftStroked,
    IconTopRightStroked,
    IconTopbuzzLogo,
    IconToutiaoLogo,
    IconTransparentStroked,
    IconTreeTriangleDown,
    IconTreeTriangleRight,
    IconTriangleArrow,
    IconTriangleArrowVertical,
    IconTriangleDown,
    IconTriangleUp,
    IconTrueFalseStroked,
    IconTvCheckedStroked,
    IconTwitter,
    IconTypograph,
    IconUnChainStroked,
    IconUnderline,
    IconUndo,
    IconUnlink,
    IconUnlock,
    IconUnlockStroked,
    IconUpload,
    IconUploadError,
    IconUser,
    IconUserAdd,
    IconUserCardPhone,
    IconUserCardVideo,
    IconUserCircle,
    IconUserCircleStroked,
    IconUserGroup,
    IconUserListStroked,
    IconUserSetting,
    IconUserStroked,
    IconVennChartStroked,
    IconVerify,
    IconVersionStroked,
    IconVideo,
    IconVideoDouyinStroked,
    IconVideoListStroked,
    IconVideoStroked,
    IconVideoUrlStroked,
    IconVigoLogo,
    IconVolume1,
    IconVolume2,
    IconVolumnSilent,
    IconVoteStroked,
    IconVoteVideoStroked,
    IconWeibo,
    IconWholeWord,
    IconWifi,
    IconWindowAdaptionStroked,
    IconWrench,
    IconXiguaLogo,
    IconYoutube,
} from '@douyinfe/semi-icons';

const useIcons = () => {
    const icons = [
        { name: 'IconAbsoluteStroked', component: <IconAbsoluteStroked /> },
        { name: 'IconActivity', component: <IconActivity /> },
        { name: 'IconAlarm', component: <IconAlarm /> },
        { name: 'IconAlertCircle', component: <IconAlertCircle /> },
        { name: 'IconAlertTriangle', component: <IconAlertTriangle /> },
        { name: 'IconAlignBottom', component: <IconAlignBottom /> },
        { name: 'IconAlignCenter', component: <IconAlignCenter /> },
        { name: 'IconAlignCenterVertical', component: <IconAlignCenterVertical /> },
        { name: 'IconAlignHCenterStroked', component: <IconAlignHCenterStroked /> },
        { name: 'IconAlignHLeftStroked', component: <IconAlignHLeftStroked /> },
        { name: 'IconAlignHRightStroked', component: <IconAlignHRightStroked /> },
        { name: 'IconAlignJustify', component: <IconAlignJustify /> },
        { name: 'IconAlignLeft', component: <IconAlignLeft /> },
        { name: 'IconAlignRight', component: <IconAlignRight /> },
        { name: 'IconAlignTop', component: <IconAlignTop /> },
        { name: 'IconAlignVBotStroked', component: <IconAlignVBotStroked /> },
        { name: 'IconAlignVBottomStroked', component: <IconAlignVBottomStroked /> },
        { name: 'IconAlignVCenterStroked', component: <IconAlignVCenterStroked /> },
        { name: 'IconAlignVTopStroked', component: <IconAlignVTopStroked /> },
        { name: 'IconApartment', component: <IconApartment /> },
        { name: 'IconAppCenter', component: <IconAppCenter /> },
        { name: 'IconApps', component: <IconApps /> },
        { name: 'IconArchive', component: <IconArchive /> },
        { name: 'IconArrowDown', component: <IconArrowDown /> },
        { name: 'IconArrowDownLeft', component: <IconArrowDownLeft /> },
        { name: 'IconArrowDownRight', component: <IconArrowDownRight /> },
        { name: 'IconArrowLeft', component: <IconArrowLeft /> },
        { name: 'IconArrowRight', component: <IconArrowRight /> },
        { name: 'IconArrowUp', component: <IconArrowUp /> },
        { name: 'IconArrowUpLeft', component: <IconArrowUpLeft /> },
        { name: 'IconArrowUpRight', component: <IconArrowUpRight /> },
        { name: 'IconArticle', component: <IconArticle /> },
        { name: 'IconAscend', component: <IconAscend /> },
        { name: 'IconAt', component: <IconAt /> },
        { name: 'IconBackTop', component: <IconBackTop /> },
        { name: 'IconBackward', component: <IconBackward /> },
        { name: 'IconBarChartHStroked', component: <IconBarChartHStroked /> },
        { name: 'IconBarChartVStroked', component: <IconBarChartVStroked /> },
        { name: 'IconBeaker', component: <IconBeaker /> },
        { name: 'IconBell', component: <IconBell /> },
        { name: 'IconBellStroked', component: <IconBellStroked /> },
        { name: 'IconBold', component: <IconBold /> },
        { name: 'IconBolt', component: <IconBolt /> },
        { name: 'IconBookH5Stroked', component: <IconBookH5Stroked /> },
        { name: 'IconBookOpenStroked', component: <IconBookOpenStroked /> },
        { name: 'IconBookStroked', component: <IconBookStroked /> },
        { name: 'IconBookmark', component: <IconBookmark /> },
        { name: 'IconBookmarkAddStroked', component: <IconBookmarkAddStroked /> },
        { name: 'IconBookmarkDeleteStroked', component: <IconBookmarkDeleteStroked /> },
        { name: 'IconBottomCenterStroked', component: <IconBottomCenterStroked /> },
        { name: 'IconBottomLeftStroked', component: <IconBottomLeftStroked /> },
        { name: 'IconBottomRightStroked', component: <IconBottomRightStroked /> },
        { name: 'IconBox', component: <IconBox /> },
        { name: 'IconBrackets', component: <IconBrackets /> },
        { name: 'IconBranch', component: <IconBranch /> },
        { name: 'IconBriefStroked', component: <IconBriefStroked /> },
        { name: 'IconBriefcase', component: <IconBriefcase /> },
        { name: 'IconBulb', component: <IconBulb /> },
        { name: 'IconButtonStroked', component: <IconButtonStroked /> },
        { name: 'IconBytedanceLogo', component: <IconBytedanceLogo /> },
        { name: 'IconCalendar', component: <IconCalendar /> },
        { name: 'IconCalendarClock', component: <IconCalendarClock /> },
        { name: 'IconCalendarStroked', component: <IconCalendarStroked /> },
        { name: 'IconCamera', component: <IconCamera /> },
        { name: 'IconCandlestickChartStroked', component: <IconCandlestickChartStroked /> },
        { name: 'IconCaretdown', component: <IconCaretdown /> },
        { name: 'IconCaretup', component: <IconCaretup /> },
        { name: 'IconCarouselStroked', component: <IconCarouselStroked /> },
        { name: 'IconCart', component: <IconCart /> },
        { name: 'IconCaseSensitive', component: <IconCaseSensitive /> },
        { name: 'IconCenterLeftStroked', component: <IconCenterLeftStroked /> },
        { name: 'IconCenterRightStroked', component: <IconCenterRightStroked /> },
        { name: 'IconChainStroked', component: <IconChainStroked /> },
        { name: 'IconCheckChoiceStroked', component: <IconCheckChoiceStroked /> },
        { name: 'IconCheckCircleStroked', component: <IconCheckCircleStroked /> },
        { name: 'IconCheckList', component: <IconCheckList /> },
        { name: 'IconCheckboxIndeterminate', component: <IconCheckboxIndeterminate /> },
        { name: 'IconCheckboxTick', component: <IconCheckboxTick /> },
        { name: 'IconChecklistStroked', component: <IconChecklistStroked /> },
        { name: 'IconChevronDown', component: <IconChevronDown /> },
        { name: 'IconChevronDownStroked', component: <IconChevronDownStroked /> },
        { name: 'IconChevronLeft', component: <IconChevronLeft /> },
        { name: 'IconChevronRight', component: <IconChevronRight /> },
        { name: 'IconChevronRightStroked', component: <IconChevronRightStroked /> },
        { name: 'IconChevronUp', component: <IconChevronUp /> },
        { name: 'IconChevronUpDown', component: <IconChevronUpDown /> },
        { name: 'IconClear', component: <IconClear /> },
        { name: 'IconClock', component: <IconClock /> },
        { name: 'IconClose', component: <IconClose /> },
        { name: 'IconCloud', component: <IconCloud /> },
        { name: 'IconCloudStroked', component: <IconCloudStroked /> },
        { name: 'IconCloudUploadStroked', component: <IconCloudUploadStroked /> },
        { name: 'IconCode', component: <IconCode /> },
        { name: 'IconCodeStroked', component: <IconCodeStroked /> },
        { name: 'IconCoinMoneyStroked', component: <IconCoinMoneyStroked /> },
        { name: 'IconColorPalette', component: <IconColorPalette /> },
        { name: 'IconColumnsStroked', component: <IconColumnsStroked /> },
        { name: 'IconCommand', component: <IconCommand /> },
        { name: 'IconComment', component: <IconComment /> },
        { name: 'IconCommentStroked', component: <IconCommentStroked /> },
        { name: 'IconComponent', component: <IconComponent /> },
        { name: 'IconComponentPlaceholderStroked', component: <IconComponentPlaceholderStroked /> },
        { name: 'IconComponentStroked', component: <IconComponentStroked /> },
        { name: 'IconConfigStroked', component: <IconConfigStroked /> },
        { name: 'IconConnectionPoint1', component: <IconConnectionPoint1 /> },
        { name: 'IconConnectionPoint2', component: <IconConnectionPoint2 /> },
        { name: 'IconContrast', component: <IconContrast /> },
        { name: 'IconCopy', component: <IconCopy /> },
        { name: 'IconCopyAdd', component: <IconCopyAdd /> },
        { name: 'IconCopyStroked', component: <IconCopyStroked /> },
        { name: 'IconCornerRadiusStroked', component: <IconCornerRadiusStroked /> },
        { name: 'IconCreditCard', component: <IconCreditCard /> },
        { name: 'IconCrop', component: <IconCrop /> },
        { name: 'IconCrossCircleStroked', component: <IconCrossCircleStroked /> },
        { name: 'IconCrossStroked', component: <IconCrossStroked /> },
        { name: 'IconCrown', component: <IconCrown /> },
        { name: 'IconCustomerSupport', component: <IconCustomerSupport /> },
        { name: 'IconCustomerSupportStroked', component: <IconCustomerSupportStroked /> },
        { name: 'IconCustomize', component: <IconCustomize /> },
        { name: 'IconDelete', component: <IconDelete /> },
        { name: 'IconDeleteStroked', component: <IconDeleteStroked /> },
        { name: 'IconDescend', component: <IconDescend /> },
        { name: 'IconDescend2', component: <IconDescend2 /> },
        { name: 'IconDesktop', component: <IconDesktop /> },
        { name: 'IconDisc', component: <IconDisc /> },
        { name: 'IconDislikeThumb', component: <IconDislikeThumb /> },
        { name: 'IconDivide', component: <IconDivide /> },
        { name: 'IconDongchediLogo', component: <IconDongchediLogo /> },
        { name: 'IconDoubleChevronLeft', component: <IconDoubleChevronLeft /> },
        { name: 'IconDoubleChevronRight', component: <IconDoubleChevronRight /> },
        { name: 'IconDownCircleStroked', component: <IconDownCircleStroked /> },
        { name: 'IconDownload', component: <IconDownload /> },
        { name: 'IconDownloadStroked', component: <IconDownloadStroked /> },
        { name: 'IconDuration', component: <IconDuration /> },
        { name: 'IconEdit', component: <IconEdit /> },
        { name: 'IconEdit2Stroked', component: <IconEdit2Stroked /> },
        { name: 'IconEditStroked', component: <IconEditStroked /> },
        { name: 'IconElementStroked', component: <IconElementStroked /> },
        { name: 'IconEmoji', component: <IconEmoji /> },
        { name: 'IconExit', component: <IconExit /> },
        { name: 'IconExpand', component: <IconExpand /> },
        { name: 'IconExport', component: <IconExport /> },
        { name: 'IconExternalOpen', component: <IconExternalOpen /> },
        { name: 'IconExternalOpenStroked', component: <IconExternalOpenStroked /> },
        { name: 'IconEyeClosed', component: <IconEyeClosed /> },
        { name: 'IconEyeClosedSolid', component: <IconEyeClosedSolid /> },
        { name: 'IconEyeOpened', component: <IconEyeOpened /> },
        { name: 'IconFacebook', component: <IconFacebook /> },
        { name: 'IconFaceuLogo', component: <IconFaceuLogo /> },
        { name: 'IconFastForward', component: <IconFastForward /> },
        { name: 'IconFastFoward', component: <IconFastFoward /> },
        { name: 'IconFavoriteList', component: <IconFavoriteList /> },
        { name: 'IconFeishuLogo', component: <IconFeishuLogo /> },
        { name: 'IconFemale', component: <IconFemale /> },
        { name: 'IconFigma', component: <IconFigma /> },
        { name: 'IconFile', component: <IconFile /> },
        { name: 'IconFillStroked', component: <IconFillStroked /> },
        { name: 'IconFilledArrowDown', component: <IconFilledArrowDown /> },
        { name: 'IconFilledArrowUp', component: <IconFilledArrowUp /> },
        { name: 'IconFilpVertical', component: <IconFilpVertical /> },
        { name: 'IconFilter', component: <IconFilter /> },
        { name: 'IconFingerLeftStroked', component: <IconFingerLeftStroked /> },
        { name: 'IconFixedStroked', component: <IconFixedStroked /> },
        { name: 'IconFlag', component: <IconFlag /> },
        { name: 'IconFlipHorizontal', component: <IconFlipHorizontal /> },
        { name: 'IconFlowChartStroked', component: <IconFlowChartStroked /> },
        { name: 'IconFolder', component: <IconFolder /> },
        { name: 'IconFolderOpen', component: <IconFolderOpen /> },
        { name: 'IconFolderStroked', component: <IconFolderStroked /> },
        { name: 'IconFollowStroked', component: <IconFollowStroked /> },
        { name: 'IconFont', component: <IconFont /> },
        { name: 'IconFontColor', component: <IconFontColor /> },
        { name: 'IconForward', component: <IconForward /> },
        { name: 'IconForwardStroked', component: <IconForwardStroked /> },
        { name: 'IconFullScreenStroked', component: <IconFullScreenStroked /> },
        { name: 'IconGallery', component: <IconGallery /> },
        { name: 'IconGift', component: <IconGift /> },
        { name: 'IconGiftStroked', component: <IconGiftStroked /> },
        { name: 'IconGit', component: <IconGit /> },
        { name: 'IconGithubLogo', component: <IconGithubLogo /> },
        { name: 'IconGitlabLogo', component: <IconGitlabLogo /> },
        { name: 'IconGlobe', component: <IconGlobe /> },
        { name: 'IconGlobeStroke', component: <IconGlobeStroke /> },
        { name: 'IconGridRectangle', component: <IconGridRectangle /> },
        { name: 'IconGridSquare', component: <IconGridSquare /> },
        { name: 'IconGridStroked', component: <IconGridStroked /> },
        { name: 'IconGridView', component: <IconGridView /> },
        { name: 'IconGridView1', component: <IconGridView1 /> },
        { name: 'IconH1', component: <IconH1 /> },
        { name: 'IconH2', component: <IconH2 /> },
        { name: 'IconH3', component: <IconH3 /> },
        { name: 'IconH4', component: <IconH4 /> },
        { name: 'IconH5', component: <IconH5 /> },
        { name: 'IconH6', component: <IconH6 /> },
        { name: 'IconH7', component: <IconH7 /> },
        { name: 'IconH8', component: <IconH8 /> },
        { name: 'IconH9', component: <IconH9 /> },
        { name: 'IconHandle', component: <IconHandle /> },
        { name: 'IconHash', component: <IconHash /> },
        { name: 'IconHeartStroked', component: <IconHeartStroked /> },
        { name: 'IconHelm', component: <IconHelm /> },
        { name: 'IconHelpCircle', component: <IconHelpCircle /> },
        { name: 'IconHelpCircleStroked', component: <IconHelpCircleStroked /> },
        { name: 'IconHistogram', component: <IconHistogram /> },
        { name: 'IconHistory', component: <IconHistory /> },
        { name: 'IconHn', component: <IconHn /> },
        { name: 'IconHome', component: <IconHome /> },
        { name: 'IconHomeStroked', component: <IconHomeStroked /> },
        { name: 'IconHorn', component: <IconHorn /> },
        { name: 'IconHourglass', component: <IconHourglass /> },
        { name: 'IconHourglassStroked', component: <IconHourglassStroked /> },
        { name: 'IconIdCard', component: <IconIdCard /> },
        { name: 'IconIdentity', component: <IconIdentity /> },
        { name: 'IconImage', component: <IconImage /> },
        { name: 'IconImageStroked', component: <IconImageStroked /> },
        { name: 'IconImport', component: <IconImport /> },
        { name: 'IconInbox', component: <IconInbox /> },
        { name: 'IconIndenpentCornersStroked', component: <IconIndenpentCornersStroked /> },
        { name: 'IconIndentLeft', component: <IconIndentLeft /> },
        { name: 'IconIndentRight', component: <IconIndentRight /> },
        { name: 'IconIndependentCornersStroked', component: <IconIndependentCornersStroked /> },
        { name: 'IconInfoCircle', component: <IconInfoCircle /> },
        { name: 'IconInherit', component: <IconInherit /> },
        { name: 'IconInheritStroked', component: <IconInheritStroked /> },
        { name: 'IconInnerSectionStroked', component: <IconInnerSectionStroked /> },
        { name: 'IconInstagram', component: <IconInstagram /> },
        { name: 'IconInteractiveStroked', component: <IconInteractiveStroked /> },
        { name: 'IconInviteStroked', component: <IconInviteStroked /> },
        { name: 'IconIssueStroked', component: <IconIssueStroked /> },
        { name: 'IconItalic', component: <IconItalic /> },
        { name: 'IconJianying', component: <IconJianying /> },
        { name: 'IconKanban', component: <IconKanban /> },
        { name: 'IconKey', component: <IconKey /> },
        { name: 'IconKeyStroked', component: <IconKeyStroked /> },
        { name: 'IconLanguage', component: <IconLanguage /> },
        { name: 'IconLayers', component: <IconLayers /> },
        { name: 'IconLeftCircleStroked', component: <IconLeftCircleStroked /> },
        { name: 'IconLightningStroked', component: <IconLightningStroked /> },
        { name: 'IconLikeHeart', component: <IconLikeHeart /> },
        { name: 'IconLikeThumb', component: <IconLikeThumb /> },
        { name: 'IconLineChartStroked', component: <IconLineChartStroked /> },
        { name: 'IconLineHeight', component: <IconLineHeight /> },
        { name: 'IconLink', component: <IconLink /> },
        { name: 'IconList', component: <IconList /> },
        { name: 'IconListView', component: <IconListView /> },
        { name: 'IconLive', component: <IconLive /> },
        { name: 'IconLoading', component: <IconLoading /> },
        { name: 'IconLock', component: <IconLock /> },
        { name: 'IconLockStroked', component: <IconLockStroked /> },
        { name: 'IconLoopTextStroked', component: <IconLoopTextStroked /> },
        { name: 'IconMail', component: <IconMail /> },
        { name: 'IconMailStroked', component: <IconMailStroked /> },
        { name: 'IconMailStroked1', component: <IconMailStroked1 /> },
        { name: 'IconMale', component: <IconMale /> },
        { name: 'IconMapPin', component: <IconMapPin /> },
        { name: 'IconMapPinStroked', component: <IconMapPinStroked /> },
        { name: 'IconMarginLeftStroked', component: <IconMarginLeftStroked /> },
        { name: 'IconMarginStroked', component: <IconMarginStroked /> },
        { name: 'IconMark', component: <IconMark /> },
        { name: 'IconMaximize', component: <IconMaximize /> },
        { name: 'IconMember', component: <IconMember /> },
        { name: 'IconMenu', component: <IconMenu /> },
        { name: 'IconMicrophone', component: <IconMicrophone /> },
        { name: 'IconMicrophoneOff', component: <IconMicrophoneOff /> },
        { name: 'IconMinimize', component: <IconMinimize /> },
        { name: 'IconMinus', component: <IconMinus /> },
        { name: 'IconMinusCircle', component: <IconMinusCircle /> },
        { name: 'IconMinusCircleStroked', component: <IconMinusCircleStroked /> },
        { name: 'IconMinusStroked', component: <IconMinusStroked /> },
        { name: 'IconModalStroked', component: <IconModalStroked /> },
        { name: 'IconMoneyExchangeStroked', component: <IconMoneyExchangeStroked /> },
        { name: 'IconMonitorStroked', component: <IconMonitorStroked /> },
        { name: 'IconMoon', component: <IconMoon /> },
        { name: 'IconMore', component: <IconMore /> },
        { name: 'IconMoreStroked', component: <IconMoreStroked /> },
        { name: 'IconMusic', component: <IconMusic /> },
        { name: 'IconMusicNoteStroked', component: <IconMusicNoteStroked /> },
        { name: 'IconMute', component: <IconMute /> },
        { name: 'IconNineGridStroked', component: <IconNineGridStroked /> },
        { name: 'IconNoteMoneyStroked', component: <IconNoteMoneyStroked /> },
        { name: 'IconOption', component: <IconOption /> },
        { name: 'IconOrderedList', component: <IconOrderedList /> },
        { name: 'IconOrderedListStroked', component: <IconOrderedListStroked /> },
        { name: 'IconPaperclip', component: <IconPaperclip /> },
        { name: 'IconPause', component: <IconPause /> },
        { name: 'IconPercentage', component: <IconPercentage /> },
        { name: 'IconPhone', component: <IconPhone /> },
        { name: 'IconPhoneStroke', component: <IconPhoneStroke /> },
        { name: 'IconPieChart2Stroked', component: <IconPieChart2Stroked /> },
        { name: 'IconPieChartStroked', component: <IconPieChartStroked /> },
        { name: 'IconPiechartH5Stroked', component: <IconPiechartH5Stroked /> },
        { name: 'IconPipixiaLogo', component: <IconPipixiaLogo /> },
        { name: 'IconPlay', component: <IconPlay /> },
        { name: 'IconPlayCircle', component: <IconPlayCircle /> },
        { name: 'IconPlus', component: <IconPlus /> },
        { name: 'IconPlusCircle', component: <IconPlusCircle /> },
        { name: 'IconPlusCircleStroked', component: <IconPlusCircleStroked /> },
        { name: 'IconPlusStroked', component: <IconPlusStroked /> },
        { name: 'IconPriceTag', component: <IconPriceTag /> },
        { name: 'IconPrint', component: <IconPrint /> },
        { name: 'IconPrizeStroked', component: <IconPrizeStroked /> },
        { name: 'IconPulse', component: <IconPulse /> },
        { name: 'IconPuzzle', component: <IconPuzzle /> },
        { name: 'IconQingyan', component: <IconQingyan /> },
        { name: 'IconQrCode', component: <IconQrCode /> },
        { name: 'IconQuit', component: <IconQuit /> },
        { name: 'IconQuote', component: <IconQuote /> },
        { name: 'IconRadio', component: <IconRadio /> },
        { name: 'IconRankingCardStroked', component: <IconRankingCardStroked /> },
        { name: 'IconRealSizeStroked', component: <IconRealSizeStroked /> },
        { name: 'IconRedo', component: <IconRedo /> },
        { name: 'IconRedoStroked', component: <IconRedoStroked /> },
        { name: 'IconRefresh', component: <IconRefresh /> },
        { name: 'IconRefresh2', component: <IconRefresh2 /> },
        { name: 'IconRegExp', component: <IconRegExp /> },
        { name: 'IconReply', component: <IconReply /> },
        { name: 'IconReplyStroked', component: <IconReplyStroked /> },
        { name: 'IconResso', component: <IconResso /> },
        { name: 'IconRestart', component: <IconRestart /> },
        { name: 'IconRingChartStroked', component: <IconRingChartStroked /> },
        { name: 'IconRotate', component: <IconRotate /> },
        { name: 'IconRotationStroked', component: <IconRotationStroked /> },
        { name: 'IconRoute', component: <IconRoute /> },
        { name: 'IconRowsStroked', component: <IconRowsStroked /> },
        { name: 'IconSafe', component: <IconSafe /> },
        { name: 'IconSave', component: <IconSave /> },
        { name: 'IconSaveStroked', component: <IconSaveStroked /> },
        { name: 'IconScan', component: <IconScan /> },
        { name: 'IconScissors', component: <IconScissors /> },
        { name: 'IconSearch', component: <IconSearch /> },
        { name: 'IconSearchStroked', component: <IconSearchStroked /> },
        { name: 'IconSectionStroked', component: <IconSectionStroked /> },
        { name: 'IconSemiLogo', component: <IconSemiLogo /> },
        { name: 'IconSend', component: <IconSend /> },
        { name: 'IconSendMsgStroked', component: <IconSendMsgStroked /> },
        { name: 'IconSendStroked', component: <IconSendStroked /> },
        { name: 'IconServer', component: <IconServer /> },
        { name: 'IconServerStroked', component: <IconServerStroked /> },
        { name: 'IconSetting', component: <IconSetting /> },
        { name: 'IconSettingStroked', component: <IconSettingStroked /> },
        { name: 'IconShareMoneyStroked', component: <IconShareMoneyStroked /> },
        { name: 'IconShareStroked', component: <IconShareStroked /> },
        { name: 'IconShield', component: <IconShield /> },
        { name: 'IconShieldStroked', component: <IconShieldStroked /> },
        { name: 'IconShift', component: <IconShift /> },
        { name: 'IconShoppingBag', component: <IconShoppingBag /> },
        { name: 'IconShrink', component: <IconShrink /> },
        { name: 'IconShrinkScreenStroked', component: <IconShrinkScreenStroked /> },
        { name: 'IconSidebar', component: <IconSidebar /> },
        { name: 'IconSignal', component: <IconSignal /> },
        { name: 'IconSimilarity', component: <IconSimilarity /> },
        { name: 'IconSmallTriangleDown', component: <IconSmallTriangleDown /> },
        { name: 'IconSmallTriangleLeft', component: <IconSmallTriangleLeft /> },
        { name: 'IconSmallTriangleRight', component: <IconSmallTriangleRight /> },
        { name: 'IconSmallTriangleTop', component: <IconSmallTriangleTop /> },
        { name: 'IconSmartphoneCheckStroked', component: <IconSmartphoneCheckStroked /> },
        { name: 'IconSmartphoneStroked', component: <IconSmartphoneStroked /> },
        { name: 'IconSong', component: <IconSong /> },
        { name: 'IconSonicStroked', component: <IconSonicStroked /> },
        { name: 'IconSort', component: <IconSort /> },
        { name: 'IconSortStroked', component: <IconSortStroked /> },
        { name: 'IconSourceControl', component: <IconSourceControl /> },
        { name: 'IconSpin', component: <IconSpin /> },
        { name: 'IconStackBarChartStroked', component: <IconStackBarChartStroked /> },
        { name: 'IconStar', component: <IconStar /> },
        { name: 'IconStarStroked', component: <IconStarStroked /> },
        { name: 'IconStop', component: <IconStop /> },
        { name: 'IconStopwatchStroked', component: <IconStopwatchStroked /> },
        { name: 'IconStoryStroked', component: <IconStoryStroked /> },
        { name: 'IconStrikeThrough', component: <IconStrikeThrough /> },
        { name: 'IconSun', component: <IconSun /> },
        { name: 'IconSync', component: <IconSync /> },
        { name: 'IconTabArrowStroked', component: <IconTabArrowStroked /> },
        { name: 'IconTabsStroked', component: <IconTabsStroked /> },
        { name: 'IconTaskMoneyStroked', component: <IconTaskMoneyStroked /> },
        { name: 'IconTemplate', component: <IconTemplate /> },
        { name: 'IconTemplateStroked', component: <IconTemplateStroked /> },
        { name: 'IconTerminal', component: <IconTerminal /> },
        { name: 'IconTestScoreStroked', component: <IconTestScoreStroked /> },
        { name: 'IconText', component: <IconText /> },
        { name: 'IconTextRectangle', component: <IconTextRectangle /> },
        { name: 'IconTextStroked', component: <IconTextStroked /> },
        { name: 'IconThumbUpStroked', component: <IconThumbUpStroked /> },
        { name: 'IconTick', component: <IconTick /> },
        { name: 'IconTickCircle', component: <IconTickCircle /> },
        { name: 'IconTicketCodeExchangeStroked', component: <IconTicketCodeExchangeStroked /> },
        { name: 'IconTicketCodeStroked', component: <IconTicketCodeStroked /> },
        { name: 'IconTiktokLogo', component: <IconTiktokLogo /> },
        { name: 'IconTop', component: <IconTop /> },
        { name: 'IconTopCenterStroked', component: <IconTopCenterStroked /> },
        { name: 'IconTopLeftStroked', component: <IconTopLeftStroked /> },
        { name: 'IconTopRightStroked', component: <IconTopRightStroked /> },
        { name: 'IconTopbuzzLogo', component: <IconTopbuzzLogo /> },
        { name: 'IconToutiaoLogo', component: <IconToutiaoLogo /> },
        { name: 'IconTransparentStroked', component: <IconTransparentStroked /> },
        { name: 'IconTreeTriangleDown', component: <IconTreeTriangleDown /> },
        { name: 'IconTreeTriangleRight', component: <IconTreeTriangleRight /> },
        { name: 'IconTriangleArrow', component: <IconTriangleArrow /> },
        { name: 'IconTriangleArrowVertical', component: <IconTriangleArrowVertical /> },
        { name: 'IconTriangleDown', component: <IconTriangleDown /> },
        { name: 'IconTriangleUp', component: <IconTriangleUp /> },
        { name: 'IconTrueFalseStroked', component: <IconTrueFalseStroked /> },
        { name: 'IconTvCheckedStroked', component: <IconTvCheckedStroked /> },
        { name: 'IconTwitter', component: <IconTwitter /> },
        { name: 'IconTypograph', component: <IconTypograph /> },
        { name: 'IconUnChainStroked', component: <IconUnChainStroked /> },
        { name: 'IconUnderline', component: <IconUnderline /> },
        { name: 'IconUndo', component: <IconUndo /> },
        { name: 'IconUnlink', component: <IconUnlink /> },
        { name: 'IconUnlock', component: <IconUnlock /> },
        { name: 'IconUnlockStroked', component: <IconUnlockStroked /> },
        { name: 'IconUpload', component: <IconUpload /> },
        { name: 'IconUploadError', component: <IconUploadError /> },
        { name: 'IconUser', component: <IconUser /> },
        { name: 'IconUserAdd', component: <IconUserAdd /> },
        { name: 'IconUserCardPhone', component: <IconUserCardPhone /> },
        { name: 'IconUserCardVideo', component: <IconUserCardVideo /> },
        { name: 'IconUserCircle', component: <IconUserCircle /> },
        { name: 'IconUserCircleStroked', component: <IconUserCircleStroked /> },
        { name: 'IconUserGroup', component: <IconUserGroup /> },
        { name: 'IconUserListStroked', component: <IconUserListStroked /> },
        { name: 'IconUserSetting', component: <IconUserSetting /> },
        { name: 'IconUserStroked', component: <IconUserStroked /> },
        { name: 'IconVennChartStroked', component: <IconVennChartStroked /> },
        { name: 'IconVerify', component: <IconVerify /> },
        { name: 'IconVersionStroked', component: <IconVersionStroked /> },
        { name: 'IconVideo', component: <IconVideo /> },
        { name: 'IconVideoDouyinStroked', component: <IconVideoDouyinStroked /> },
        { name: 'IconVideoListStroked', component: <IconVideoListStroked /> },
        { name: 'IconVideoStroked', component: <IconVideoStroked /> },
        { name: 'IconVideoUrlStroked', component: <IconVideoUrlStroked /> },
        { name: 'IconVigoLogo', component: <IconVigoLogo /> },
        { name: 'IconVolume1', component: <IconVolume1 /> },
        { name: 'IconVolume2', component: <IconVolume2 /> },
        { name: 'IconVolumnSilent', component: <IconVolumnSilent /> },
        { name: 'IconVoteStroked', component: <IconVoteStroked /> },
        { name: 'IconVoteVideoStroked', component: <IconVoteVideoStroked /> },
        { name: 'IconWeibo', component: <IconWeibo /> },
        { name: 'IconWholeWord', component: <IconWholeWord /> },
        { name: 'IconWifi', component: <IconWifi /> },
        { name: 'IconWindowAdaptionStroked', component: <IconWindowAdaptionStroked /> },
        { name: 'IconWrench', component: <IconWrench /> },
        { name: 'IconXiguaLogo', component: <IconXiguaLogo /> },
        { name: 'IconYoutube', component: <IconYoutube /> },];

    const getIcon = (name: string) => {
        const icon = icons.find(icon => icon.name === name);
        return icon ? icon.component : null;
    }

    return { icons, getIcon };
}

export default useIcons;