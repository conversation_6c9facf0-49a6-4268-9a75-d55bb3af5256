import { FC, useState, useEffect } from 'react';
import { Typography, Table, BackTop, Spin, Tag, Button, Layout, Input, Breadcrumb, Row, Col, Divider, Select, Space } from '@douyinfe/semi-ui';
import InfiniteScroll from 'react-infinite-scroll-component';
import TableEmpty from '@/components/table-empty';
import { IconSearch } from '@douyinfe/semi-icons';
import qs from 'query-string';
import Switch from './switch'
import { Application } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/application_pb";
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { getQueryParam } from '@/utils/query';
const { Sider, Content } = Layout;
const { Title, Paragraph, Text } = Typography;

import New from './new';
import Edit from './edit';
import Del from './del';
import Import from './import';
import GroupSort from './group-sort';

import styles from './index.module.scss'
import useTable, { ApplicationFilter } from './useTable';
import { BASE_PATH } from '@/constants/router';
import { useLocale } from '@/locales';

// 根据URL参数设置过滤参数
const getApplicationFilter = (location: Location): ApplicationFilter => {
    const query: string = getQueryParam('query', location) as string;
    const tagQuery = getQueryParam('tag', location);
    let tag: string[] = [];
    if (tagQuery && Array.isArray(tagQuery)) {
        tagQuery.forEach(item => {
            if (item && typeof item == 'string') {
                tag.push(item);
            }
        })
    }
    if (tagQuery && typeof tagQuery == 'string') {
        tag = [tagQuery];
    }
    return {
        query: query,
        tag: tag,
    }
}

const Index: FC = () => {
    const { formatMessage } = useLocale();
    const location = useLocation();
    const navigate = useNavigate();

    // 过滤参数
    const initFilter = getApplicationFilter(location);

    // 过滤参数改变时跳转路由
    const doNavigate = (filter: ApplicationFilter) => {
        let query = '';
        if (filter.query || filter.tag.length > 0) {
            query = qs.stringify(filter, {
                skipEmptyString: true
            });
        }
        console.log('location', location.pathname)
        if (query) {
            navigate(`${location.pathname}?${query}`)
        } else {
            navigate(`${location.pathname}`)
        }
    }

    // 查询参数
    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        // 查询参数从有值变化为无值时，重新加载数据
        if (location.search == '' && search != '') {
            setFilterParam(initFilter);
        }
        setSearch(location.search);
    }, [location])

    const [importVisible, setImportVisible] = useState(false);
    const [createVisiable, setCreateVisiable] = useState(false);

    const [groupSortVisible, setGroupSortVisible] = useState(false);

    const { columns, loading, applications, tags, reloadFlag, setReloadFlag, curApplication, setCurApplication, delVisible, setDelVisible, editVisible, setEditVisible, addPage, query, filterParam, setFilterParam, total, pageSize, handleExport } = useTable(initFilter);

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, query: value })
        doNavigate({ ...filterParam, query: value })
    }

    const handleTagChange = (value: any) => {
        setFilterParam({ ...filterParam, tag: value })
        doNavigate({ ...filterParam, tag: value });
    }

    return <><div >
        <Row className='mb10'>
            <Col span={20}>
                <div><Title heading={3} className="mb2">{formatMessage({ id: 'services.application.index.title.applicationPanel' })}</Title>
                </div></Col>
            <Col span={4}><div className='btn-right-col'>
            </div></Col>
        </Row>
        {/* <Divider className='mb20'></Divider> */}
        <Paragraph className='mb40' type='tertiary'>{formatMessage({ id: 'services.application.index.description.applicationPanel' })}</Paragraph>
        <Switch></Switch>
        <Divider className='mb40'></Divider>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={4} className="mb2">{formatMessage({ id: 'services.application.index.title.application' })}</Title>
                <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'services.application.index.description.application' })}</Paragraph>

            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>

                    <Button onClick={() => setImportVisible(true)}>{formatMessage({ id: 'services.application.index.button.dataImport' })}</Button>
                    <Button onClick={() => handleExport()}>{formatMessage({ id: 'services.application.index.button.dataExport' })}</Button>
                    <Button onClick={() => setGroupSortVisible(true)}>{formatMessage({ id: 'services.application.index.button.categoryManagement' })}</Button>
                    <Button theme='solid' onClick={() => setCreateVisiable(true)}>{formatMessage({ id: 'services.application.index.button.newApplication' })}</Button>
                </Space>
            </div></Col>
        </Row>
        <Layout className='mb20 search-bar'>

            <Layout>
                <Content className='pr10'>
                    <Input value={filterParam.query}
                        onChange={handleQueryChange} style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={formatMessage({ id: 'services.application.filter.searchPlaceholder' })}></Input>
                </Content>
                <Sider>
                    <Select multiple
                        maxTagCount={1}
                        style={{ width: 200 }}
                        optionList={tags}
                        insetLabel={formatMessage({ id: 'services.application.index.filter.category' })}
                        onChange={handleTagChange}
                        value={filterParam.tag}></Select>
                </Sider>
            </Layout>

        </Layout>
        <div style={{ height: 20 }} className='mb10'>
            <Row>
                <Col span={12}>
                    {!loading && <Tag> {formatMessage({ id: 'services.application.index.stats.totalApplications' })} {loading ? <Spin size='small' /> : total}</Tag>}
                </Col>
            </Row>


        </div>
        <InfiniteScroll
            dataLength={applications.length} //This is important field to render the next data
            next={addPage}

            hasMore={applications.length < total}
            loader={<div><Spin></Spin></div>}
            endMessage={
                <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                    {applications.length > pageSize && <Paragraph type='tertiary'>{formatMessage({ id: 'services.application.index.endMessage' })}</Paragraph>}
                </div>
            }

        >
            <Table
                rowKey={(record?: Application) => record ? record.id + '' : ''}

                empty={<TableEmpty loading={loading} />} columns={columns} loading={loading} dataSource={applications} pagination={false} />
        </InfiniteScroll>
        <BackTop style={{ right: 10 }} />
    </div>
        {createVisiable && <New close={() => setCreateVisiable(false)} success={() => setReloadFlag(true)}></New>}
        {editVisible && curApplication && <Edit close={() => setEditVisible(false)} success={() => {
            setReloadFlag(true)
            setEditVisible(false)
            setCurApplication(undefined)
        }} id={curApplication?.id}></Edit>}
        {delVisible && curApplication && <Del close={() => setDelVisible(false)} success={() => {
            setReloadFlag(true)
            setDelVisible(false)
            setCurApplication(undefined)
        }} record={curApplication}></Del>}
        {importVisible && <Import close={() => setImportVisible(false)} success={() => setReloadFlag(!reloadFlag)}></Import>}
        {groupSortVisible && <GroupSort close={() => {

            setGroupSortVisible(false)
            setReloadFlag(true)
        }} success={() => {
            setReloadFlag(true)
        }}></GroupSort>}
    </>
}

export default Index;
