import { useEffect, useState, useContext } from "react";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { Typography, Dropdown, Button, Tag, Image } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';
import <PERSON> from 'papaparse';

import { Application, ApplicationIconType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/application_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import useIcons from './useIcons';
import { caseInsensitiveIncludes } from "@/utils/common";
export type ApplicationFilter = {
    query: string;
    tag: Array<string>,
}

const { Title, Paragraph, Text } = Typography;
const useTable = (initFilter: ApplicationFilter) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const { icons, getIcon } = useIcons();
    const columns = [
        {
            title: formatMessage({ id: 'services.application.table.name' }),
            dataIndex: 'name',
            render: (field: string, record: Application) => {
                return <>{field}
                    <Paragraph size='small'>{record.description}</Paragraph>
                </>
            }
        },
        {
            title: formatMessage({ id: 'services.application.table.category' }),
            dataIndex: 'tag',
            render: (field: string, record: Application) => {
                return <Tag >{record.group?.alias}</Tag>
            }
        },
        {
            title: formatMessage({ id: 'services.application.table.icon' }),
            dataIndex: 'icon',
            render: (field: string, record: Application) => {
                if(record.iconType == ApplicationIconType.NAME) {
                    return <>{getIcon(record.icon)}</>
                }
                return <Image src={field} width={20} height={20} />
            }
        },
        {
            title: 'URL',
            dataIndex: 'url',
            render: (field: string, record: Application) => {
                return <>
                    <Paragraph size='small'>{record.url}</Paragraph>
                </>
            }
        },
        {
            title: formatMessage({ id: 'services.application.table.urlMasquerade' }),
            dataIndex: 'urlMasquerade',
            render: (field: boolean, record: Application) => {
                return <>{record.urlMasquerade ? <Tag color="green">{formatMessage({ id: 'services.application.table.yes' })}</Tag> : <Tag>{formatMessage({ id: 'services.application.table.no' })}</Tag>}</>
            }
        },
        {
            width: 100,
            title: '',
            dataIndex: 'operate',
            render: (field: string, record: Application) => {
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => {
                                setCurApplication(record)
                                setEditVisible(true)
                            }}>{formatMessage({ id: 'services.application.dropdown.editApplication' })}</Dropdown.Item>
                            <Dropdown.Divider />

                            <Dropdown.Item type="danger" onClick={() => {
                                setDelVisible(true)
                                setCurApplication(record)
                            }} >{formatMessage({ id: 'services.application.dropdown.deleteApplication' })}</Dropdown.Item>


                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];


    // 列表数据是否正在加载中
    const [loading, setLoading] = useState(true);

    const [tags, setTags] = useState<{
        value: string,
        label: string
    }[]>([]);
    const [applications, setApplications] = useState<Application[]>([]);
    const [allApplications, setAllApplications] = useState<Application[]>([]);
    // 重新加载标志
    const [reloadFlag, setReloadFlag] = useState(false);
    const [curApplication, setCurApplication] = useState<Application>();
    const [delVisible, setDelVisible] = useState(false);
    const [editVisible, setEditVisible] = useState(false);

    // 当前页码
    const [page, setPage] = useState(1);
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    const pageSize = 20;

    // 过滤参数
    const [filterParam, setFilterParam] = useState(initFilter);

    // 过滤结果
    const doFilter = (page: number, src: Array<Application>, filter: ApplicationFilter): Array<Application> => {
        if (!src || src.length == 0) {
            setTotal(0);
            return [];
        }
        
        if (!filter.query && filter.tag.length == 0) {
            setTotal(src.length);
            return src.slice(0, page * pageSize);
        }
        let result = src.filter((item) => {
            let { query, tag } = filter;
            if (query) query = query.trim();
            let passQuery = true;
            let passTag = true;
            if (query) {
                passQuery = caseInsensitiveIncludes(item.name, query) || caseInsensitiveIncludes(item.description, query) || caseInsensitiveIncludes(item.url, query);
            }
            if (tag.length > 0) {
                passTag = tag.includes(item.group?.name || '');
            }

            return passQuery && passTag;

        });
        setTotal(result.length);

        return result.slice(0, page * pageSize);
        // return result.slice(0, page * pageSize);
    }

    const query = () => {
        flylayerClient.listApplications({
            flynetId: flynetGeneral.id
        }).then((res) => {
            setAllApplications(res.applications);
            const names:string[] = [];
            const tags = new Set<{
                value: string,
                label: string
            }>();
            res.applications.forEach((item) => {
                if(!names.includes(item.group?.name || '')) {

                    tags.add({
                        value: item.group?.name || '',
                        label: item.group?.alias || ''
                    });
                    names.push(item.group?.name || '');
                }
            });
            setTags(Array.from(tags));
            setApplications(doFilter(page, res.applications, filterParam));
            setLoading(false);
        }).catch((err) => {
            console.error(err);
            setLoading(false);
        });
    }

    useEffect(() => {
        query()
    }, [])

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    const addPage = () => {
        setApplications(doFilter(page + 1, allApplications, filterParam));
        setPage(page + 1)
    }

    useEffect(() => {
        setPage(1)
        const list = doFilter(1, allApplications, filterParam);
        setApplications(list);
    }, [filterParam])

    const handleExport = () => {
        const data = [[
            formatMessage({ id: 'services.application.export.name' }),
            formatMessage({ id: 'services.application.export.description' }),
            formatMessage({ id: 'services.application.export.category' }),
            formatMessage({ id: 'services.application.export.iconType' }),
            formatMessage({ id: 'services.application.export.icon' }),
            formatMessage({ id: 'services.application.export.url' }),
            formatMessage({ id: 'services.application.export.urlMasquerade' })
        ]]
        

        allApplications.forEach((item) => {
            
            
            data.push([item.name, item.description, item.group?.alias || '', item.iconType.toString(), item.icon, item.url, item.urlMasquerade ? 'true': 'false']);
        });

        let csv = Papa.unparse(data);

        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = formatMessage({ id: 'services.application.export.filename' });
        a.click();
    } 

    return {
        columns, loading, applications, tags, reloadFlag, setReloadFlag, curApplication, setCurApplication, delVisible, setDelVisible, editVisible, setEditVisible,
        addPage, query, filterParam, setFilterParam, total, pageSize,
        handleExport
    }


}

export default useTable
