import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Form, Input, Notification, Dropdown, Row, Col, Divider, Space, Button, List, RadioGroup, Radio, Upload, Avatar, Spin, Image } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { Application, ApplicationIconType, ApplicationGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/application_pb";
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { IconCamera, IconUploadError } from '@douyinfe/semi-icons';
import pinyin from 'tiny-pinyin';
import { isIPUrl, sanitizeLabel } from '@/utils/common';

import styles from './index.module.scss';
import { flylayerClient } from '@/services/core';
import useIcons from '../useIcons';
import { uploadFile } from '@/services/file';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void
}


const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [groupSaveLoading, setGroupSaveLoading] = useState(false);

    const flynet = useContext(FlynetGeneralContext);

    const { icons, getIcon } = useIcons();

    

    const [inputTag, setInputTag] = useState<string>('');

    const inputTagAddDisable = (tag:string, groups: ApplicationGroup[]) => {
        if (!inputTag || inputTag == '' || inputTag.trim() == '') {
            return true;
        }

        let exist = false;
        groups.forEach(group=>{
            if(group.alias == tag.trim()) {
                exist = true;
                return;
            }
        })
        return exist;
    }

    const [hasUrlError, setHasUrlError] = useState(false);

    const [groups, setGroups] = useState<ApplicationGroup[]>([]);

    const query = () => {
        flylayerClient.listApplicationGroups({
            flynetId: flynet.id
        }).then((res) => {
            setGroups(res.applicationGroups);
        }).catch((e) => {
            Notification.error({ title: formatMessage({ id: 'services.application.edit.error.loadCategoryFailed' }), content: e.message });
        })
    }

    useEffect(() => {
        query();
    }, [])

    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        url: string,
        icon: string,
        iconType: ApplicationIconType,
        tag: string,
        urlMasquerade: boolean
    }>>()

    const [saveLoading, setSaveLoading] = useState(false);

    const [fileUploading, setFileUploading] = useState(false);

    const [icon, setIcon] = useState<string>('');

    const [enableUrlMasquerade, setEnableUrlMasquerade] = useState(false);

    const handleSubmit = async () => {
        await formApi?.validate();


        const values = formApi?.getValues();
        if (!values) return;

        if (!values.icon) {
            setHasUrlError(true);
            return;
        }
        
        const application = new Application();
        application.name = values.name;
        application.description = values.description || '';
        application.url = values.url;
        application.icon = values.icon;
        application.iconType = values.iconType;
        if(enableUrlMasquerade) {
            application.urlMasquerade = values.urlMasquerade ? true : false;
        } else {
            application.urlMasquerade = false;
        }
        
        
        groups.forEach(val=>{
            if(val.name == values.tag) {
                application.group = val;
            } ;
        }) 


        setSaveLoading(true);
        flylayerClient.createApplication({
            application: application,
            flynetId: flynet.id
        }).then(() => {
            Notification.success({ title: formatMessage({ id: 'services.application.new.success.createSuccess' }) });
            props.success?.();
            props.close();
        }).catch((e) => {
            Notification.error({ title: formatMessage({ id: 'services.application.new.error.createFailed' }), content: e.message });
        }).finally(() => {
            setSaveLoading(false);
        })
    }

    return <><Modal
        width={600}
        title={formatMessage({ id: 'services.application.new.title' })}
        visible={true}
        onCancel={props.close}
        onOk={handleSubmit}
        okButtonProps={{ loading: saveLoading }}
        className='semi-modal'
        maskClosable={false}
    >
        <Form
            getFormApi={SetFormApi}
            initValues={{
                name: '',
                description: '',
                url: '',
                icon: '',
                iconType: ApplicationIconType.NAME,
                tag: ''
            }}
        >
            {({ values }) => (<>
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Input
                            field='name'
                            name='name'
                            label={formatMessage({ id: 'services.application.edit.form.name' })}
                            rules={[{ required: true, message: formatMessage({ id: 'services.application.edit.form.nameRequired' }) }]}
                            placeholder={formatMessage({ id: 'services.application.edit.form.namePlaceholder' })}
                        />
                    </Col>
                    <Col span={12}>
                        <Form.Select
                            field='tag'
                            name='tag'
                            label={formatMessage({ id: 'services.application.edit.form.category' })}
                            style={{ width: '100%' }}
                            rules={[{ required: true, message: formatMessage({ id: 'services.application.edit.form.categoryRequired' }) }]}
                            placeholder={formatMessage({ id: 'services.application.edit.form.categoryPlaceholder' })}
                            optionList={groups.map(group => {
                                return { value: group.name, label: group.alias }
                            })}
                            outerBottomSlot={
                                <Space className='p10'><Input
                                    value={inputTag}
                                    onChange={(e) => setInputTag(e)}
                                    placeholder={formatMessage({ id: 'services.application.edit.form.inputCategory' })} /><Button
                                        loading={groupSaveLoading}
                                        onClick={() => {
                                            setGroupSaveLoading(true);
                                            flylayerClient.createApplicationGroup({
                                                flynetId: flynet.id,
                                                applicationGroup: {
                                                    name: sanitizeLabel(pinyin.convertToPinyin(inputTag, '', true)),
                                                    alias: inputTag
                                                }
                                            }).then(() => {
                                                query();
                                            }).catch((e) => {
                                                Notification.error({ title: formatMessage({ id: 'services.application.edit.error.createCategoryFailed' }), content: e.message });
                                            }).finally(() => {
                                                setGroupSaveLoading(false);
                                            })
                                            
                                            setInputTag('');
                                        }}
                                        disabled = {inputTagAddDisable(inputTag, groups)}
                                    >{formatMessage({ id: 'components.common.add' })}</Button></Space>
                            }
                        />

                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Form.Input
                            field='description'
                            name='description'
                            label={formatMessage({ id: 'services.application.edit.form.description' })}

                            placeholder={formatMessage({ id: 'services.application.edit.form.descriptionPlaceholder' })}
                        />
                    </Col>
                </Row>
                <Divider />
                <Row>
                    <Col span={24}>
                        <Space>
                            <Form.Select
                                labelPosition='left'
                                field='iconType'
                                name='iconType'
                                label={formatMessage({ id: 'services.application.edit.form.icon' })}
                                rules={[{ required: true, message: formatMessage({ id: 'services.application.edit.form.iconRequired' }) }]}
                                placeholder={formatMessage({ id: 'services.application.edit.form.iconPlaceholder' })}
                                style={{ width: 80 }}
                            >
                                <Form.Select.Option value={ApplicationIconType.NAME}>{formatMessage({ id: 'services.application.edit.form.iconName' })}</Form.Select.Option>
                                <Form.Select.Option value={ApplicationIconType.URL}>{formatMessage({ id: 'services.application.edit.form.iconUpload' })}</Form.Select.Option>
                            </Form.Select>

                            {values.iconType == ApplicationIconType.NAME ? <><Dropdown
                                
                                content={<div className='p10' >
                                    <RadioGroup value={icon} onChange={e => {
                                        setHasUrlError(false);
                                        const val = e.target.value;
                                        formApi?.setValue('icon', val);
                                        setIcon(val);
                                    }}>
                                        <List dataSource={icons}
                                            grid={{ gutter: 0, span: 2 }}
                                            className={styles.list}
                                            renderItem={item => <List.Item className={styles.listItem}>
                                                <Radio className={styles.radio} value={item.name}><span className={styles.radioIcon}>{item.component}</span></Radio>
                                            </List.Item>}
                                        ></List>
                                    </RadioGroup>
                                </div>}
                            >
                                {values.icon ? <Button style={{ marginTop: 12, marginBottom: 20 }} icon={getIcon(values.icon)}></Button> :
                                    <Button style={{ marginTop: 12, marginBottom: 20 }}>{formatMessage({ id: 'components.common.pleaseSelect' })}</Button>
                                }

                            </Dropdown>

                            </> : <> {icon && <Image src={icon} width={32} height={32} style={{marginTop:12, marginBottom:20}} />}<Upload
                                
                                beforeUpload={(prop) => {
                                    const file = prop.file;
                                    if(!file.fileInstance) {
                                        return false;
                                    }
                                    const isJpgOrPng = file.fileInstance?.type === 'image/jpeg' || file.fileInstance?.type === 'image/png';
                                    if (!isJpgOrPng) {
                                        Notification.error({title: formatMessage({ id: 'services.application.edit.error.fileFormat' })});
                                    }
                                    
                                    const isLt2M = file.fileInstance.size / 1024 / 1024 < 2;
                                    if (!isLt2M) {
                                        Notification.error({title: formatMessage({ id: 'services.application.edit.error.fileSize' })});
                                    }
                                    return isJpgOrPng && isLt2M;
                                }}
                                customRequest={(options) => {
                                    setFileUploading(true);
                                    let ext = options.file.name.split('.').pop();
                                    if (!ext) return;
                                    flylayerClient.getUploadUrl({ ext }).then((res) => {
                                        uploadFile(res.uploadUrl, options.fileInstance).then(() => {

                                            formApi?.setValue('icon', res.accessUrl);
                                            setIcon(res.accessUrl);
                                        }).catch((e) => {
                                            options.onError(e);
                                        }).finally(() => {
                                            setFileUploading(false);
                                        })
                                    }).catch((e) => {
                                        options.onError(e);
                                    })
                                }}
                                className={styles.upload}
                                disabled={fileUploading}
                                
                                onError={() => Notification.error({ content: formatMessage({ id: 'services.application.edit.error.uploadFailed' }), position: "bottomRight" })}
                                showUploadList={false} accept='image/*' action=''
                            ><Avatar size='small' shape="square" ><div className={styles.uploadMask}>
                            {fileUploading ? <Spin/> : <IconCamera size='large' />} 
                        </div></Avatar></Upload></>}
                            {hasUrlError && <Paragraph type='danger' style={{ paddingTop: 12, paddingBottom: 20, display: 'flex', justifyContent: 'center', alignItems: 'center' }} ><IconUploadError />&nbsp;{formatMessage({ id: 'services.application.edit.error.selectIcon' })}</Paragraph>}
                        </Space>

                    </Col>
                </Row>
                <Divider />

                <Row>
                    <Col span={24}>
                        <Form.Input
                            field='url'
                            name='url'
                            label={formatMessage({ id: 'services.application.edit.form.url' })}
                            rules={[{ required: true, message: formatMessage({ id: 'services.application.edit.form.urlRequired' }) }]}
                            placeholder={formatMessage({ id: 'services.application.edit.form.urlPlaceholder' })}
                            onChange={(val)=>{
                                setEnableUrlMasquerade(isIPUrl(val));
                            }}
                        />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Form.Checkbox
                            disabled={!enableUrlMasquerade}
                            field='urlMasquerade'
                            name='urlMasquerade'
                            label={formatMessage({ id: 'services.application.edit.form.urlMasquerade' })}
                        />
                    </Col>
                </Row>

            </>)}
        </Form>
    </Modal></>
}

export default Index;