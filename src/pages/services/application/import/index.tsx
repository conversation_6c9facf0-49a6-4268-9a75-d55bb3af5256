import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Banner, Input, Notification, Table, Tag, Row, Col, Divider, Space, Button, List, RadioGroup, Radio, Upload, Avatar, Spin, Image } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { Application, ApplicationIconType, ApplicationGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/application_pb";
import { isUTF8Encoding } from '@/utils/file';
import Papa from 'papaparse';
import { Model } from './model';
import TableEmpty from '@/components/table-empty';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { IconPaperclip, IconAlertCircle } from '@douyinfe/semi-icons';
import styles from './index.module.scss';
import { flylayerClient } from '@/services/core';
import pinyin from 'tiny-pinyin';
import { sanitizeLabel } from '@/utils/common';
interface Props {
    close: () => void,
    success?: () => void
}

const { Text, Paragraph } = Typography;

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [saveLoading, setSaveLoading] = useState(false);

    // 无效数据
    const [invalidData, setInvalidData] = useState<Array<Model>>([]);
    // 有效数据
    const [validData, setValidData] = useState<Array<Model>>([]);
    const handleSubmit = async () => {
        setSaveLoading(true);
        let applications: Array<Application> = [];

        validData.forEach(item => {
            let application = new Application();
            application.name = item.name;
            application.description = item.description;
            application.group = new ApplicationGroup({
                name: sanitizeLabel(pinyin.convertToPinyin(item.tag, '', true)),
                alias: item.tag
            });
            application.iconType = item.iconType == '0' ? ApplicationIconType.URL : ApplicationIconType.NAME;
            application.icon = item.icon;
            application.url = item.url;
            application.urlMasquerade = item.urlMasquerade;
            applications.push(application);
        });
        console.log(applications);
        flylayerClient.importApplications({
            flynetId: flynet.id,
            applications: applications
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'services.application.import.success.importSuccess' }),
                content: formatMessage({ id: 'services.application.import.success.importSuccess' }),
                duration: 2000,
            });
            props.success && props.success();
            props.close();
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'services.application.import.error.importFailed' }),
                content: err.message,
                duration: 2000,
            });
        }).finally(()=>{
            setSaveLoading(false);
        })
        
    }

    // 表格内容数据
    const [data, setData] = useState<Array<Model>>([]);

    const columns: Array<ColumnProps> = [{
        title: formatMessage({ id: 'services.application.edit.form.name' }),
        dataIndex: 'name',
        width: 160,
        render: (text, record, index) => {
            return <>
                <Text>{text}</Text>
            </>
        }
    }, {
        title: formatMessage({ id: 'services.application.import.table.resourceDescription' }),
        dataIndex: 'description',
        width: 200,
        render: (text, record, index) => {
            return <>
                <Text>{text}</Text>
            </>
        }
    }, {
        title: formatMessage({ id: 'services.application.import.table.resourceCategory' }), // 资源类型(0为WEB应用，1为TCP应用，2为L3VPN)
        dataIndex: 'tag',
        width: 100
    }, {
        title: formatMessage({ id: 'services.application.import.table.iconType' }), // 协议类型(-1为全部协议，0为TCP，1为UDP，2为ICMP)
        dataIndex: 'iconType',
        width: 100,
        render: (text, record, index) => {
            return <>
                <Text>{record.iconType == 0 ? formatMessage({ id: 'services.application.import.table.iconTypeUrl' }) : formatMessage({ id: 'services.application.import.table.iconTypeName' })}</Text>
            </>
        }
    }, {
        title: formatMessage({ id: 'services.application.edit.form.icon' }), // 服务类型(如HTTP)
        dataIndex: 'icon',
        width: 160
    }, {
        title: 'URL',
        dataIndex: 'url',

    },
    {
        title: formatMessage({ id: 'services.application.table.urlMasquerade' }),
        dataIndex: 'urlMasquerade',
        render: (field: boolean, record: Application) => {
            return <>{record.urlMasquerade ? <Tag color="green">{formatMessage({ id: 'services.application.table.yes' })}</Tag> : <Tag>{formatMessage({ id: 'services.application.table.no' })}</Tag>}</>
        },
        width: 120
    }, {
        title: formatMessage({ id: 'services.application.import.table.isValid' }),
        width: 100,
        dataIndex: 'invalid',
        render: (text, record) => {
            return <>{record.invalid ? <Text type='danger'>{record.invalidReason}</Text> : formatMessage({ id: 'services.application.import.table.yes' })}</>
        }
    }
    ];

    // 文件
    const [file, setFile] = useState<File>();
    // 文件
    const [files, setFiles] = useState<FileItem[]>();
    // 计算数据是否有效
    const calModelValid = (model: Model) => {
        if (!model.name) {
            model.invalid = true;
            model.invalidReason = formatMessage({ id: 'services.application.import.validation.nameRequired' });
        } else if (!model.url) {
            model.invalid = true;
            model.invalidReason = formatMessage({ id: 'services.application.import.validation.urlRequired' });
        } else if (!model.tag) {
            model.invalid = true;
            model.invalidReason = formatMessage({ id: 'services.application.import.validation.categoryRequired' });
        } else if (!model.icon) {
            model.invalid = true;
            model.invalidReason = formatMessage({ id: 'services.application.import.validation.iconRequired' });
        } else if (model.iconType != '0' && model.iconType != '1') {
            model.invalid = true;
            model.invalidReason = formatMessage({ id: 'services.application.import.validation.iconTypeError' });
        } else {
            model.invalid = false;
            model.invalidReason = '';
        }
    }

    const handleFileSelect = async (files: File[]) => {
        if (files.length > 0) {
            const isUTF8 = await isUTF8Encoding(files[0]);

            Papa.parse(files[0] as any, {
                encoding: isUTF8 ? 'UTF-8' : 'GBK',
                complete(result, file) {
                    if (result.errors && result.errors.length > 0) {
                        Notification.error({
                            title: formatMessage({ id: 'services.application.import.error.importFailed' }),
                            content: formatMessage({ id: 'services.application.import.error.checkFileFormat' }),
                            duration: 2000,
                        });
                        return;
                    }
                    let modelIndex = 0;
                    if (result.data && result.data.length > 2) {
                        let pureData = result.data.slice(1);
                        let data: Array<Model> = [];
                        let invalidData: Array<Model> = [];
                        let validData: Array<Model> = [];
                        let existUrl: Array<string> = [];
                        for (let i = 0; i < pureData.length - 1; i++) {
                            let item: string[] = pureData[i] as string[];

                            let name = item[0].trim();
                            
                            let dataItem: Model = {
                                index: modelIndex++,
                                name: name,
                                description: item[1] ? item[1].trim() : name,
                                tag: item[2].trim(),
                                iconType: item[3].trim(),
                                icon: item[4].trim(),
                                url: item[5].trim(),
                                urlMasquerade: item[6] == 'true',
                                invalid: false,
                                invalidReason: ''
                            }

                            calModelValid(dataItem);
                            if (!dataItem.invalid) {

                                if (existUrl.indexOf(dataItem.url) >= 0) {
                                    dataItem.invalid = true;
                                    dataItem.invalidReason = formatMessage({ id: 'services.application.import.validation.duplicateUrl' });
                                } else {
                                    existUrl.push(dataItem.url);
                                }
                            }

                            data.push(dataItem);
                            if (dataItem.invalid) {
                                invalidData.push(dataItem);
                            } else {
                                validData.push(dataItem);
                            }

                        }
                        setData(data)
                        setInvalidData(invalidData);
                        setValidData(validData);

                    }
                    setFile(file);
                },
            });
        }
        return false
    };

    // 下载模板
    const handleTemplateDown = () => {
        let csv = Papa.unparse([[
            formatMessage({ id: 'services.application.export.name' }),
            formatMessage({ id: 'services.application.export.description' }),
            formatMessage({ id: 'services.application.export.category' }),
            formatMessage({ id: 'services.application.export.iconType' }),
            formatMessage({ id: 'services.application.export.icon' }),
            formatMessage({ id: 'services.application.export.url' }),
            formatMessage({ id: 'services.application.export.urlMasquerade' })
        ]]);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = formatMessage({ id: 'services.application.import.templateFilename' });
        a.click();
    }
    // 下载无效数据
    const handleInvalidDataDown = () => {
        let csv = Papa.unparse(invalidData);
        let blob = new Blob(["\ufeff" + csv], { type: 'text/csv,charset=UTF-8' });
        let a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = formatMessage({ id: 'services.application.import.invalidDataFilename' });
        a.click();
    }
    return <><Modal
        width={1280}
        title={formatMessage({ id: 'services.application.import.title' })}
        visible={true}
        onCancel={props.close}
        onOk={handleSubmit}
        okText={formatMessage({ id: 'services.application.import.button.import' })}
        okButtonProps={{ loading: saveLoading }}
        className='semi-modal'
        maskClosable={false}
    >
        
        <Row className='mb20' gutter={20}>
            <Col span={12}>
                <Space align='start'>
                    <Upload

                        style={{ width: 300 }}
                        action=''
                        draggable
                        dragMainText={formatMessage({ id: 'services.application.import.upload.dragMainText' })}
                        dragSubText={formatMessage({ id: 'services.application.import.upload.dragSubText' })}
                        onFileChange={handleFileSelect}
                        fileList={files}
                        onChange={({ fileList }) => setFiles(fileList)}
                        uploadTrigger='custom'
                        limit={1}
                        accept='.csv'
                    ></Upload>
                </Space>
            </Col>
            <Col span={12}>
                <div className='btn-right-col'>
                    <Space>
                        <Button size='large' icon={<IconPaperclip />} onClick={handleTemplateDown}>{formatMessage({ id: 'services.application.import.button.templateDownload' })}</Button>
                        <Button size='large' disabled={!invalidData || invalidData.length == 0} onClick={handleInvalidDataDown}>{formatMessage({ id: 'services.application.import.button.invalidDataDownload' })}</Button>
                    </Space>
                </div>

            </Col>
        </Row>

        <Divider className='mb20'></Divider>

        {file && <>
            <Space className='mb20'>
                <Tag style={{ height: 32 }}>{formatMessage({ id: 'services.application.import.stats.totalData' })}{data.length}</Tag>
                <Tag style={{ height: 32 }}>{formatMessage({ id: 'services.application.import.stats.validData' })}{validData.length}</Tag>
                <Tag style={{ height: 32 }}>{formatMessage({ id: 'services.application.import.stats.invalidData' })}{invalidData.length}</Tag>
                <Paragraph type='warning' style={{display: 'flex', alignContent: 'center', alignItems: 'center'}}><IconAlertCircle />&nbsp;{formatMessage({ id: 'services.application.import.warning' })}</Paragraph>
            </Space>

            <Table
                columns={columns}
                dataSource={data}
                pagination={false}
                virtualized
                scroll={{ y: 600 }}
                bordered
                rowKey={'index'}
                empty={<TableEmpty loading={false}></TableEmpty>}></Table></>}
    </Modal>
    </>
}

export default Index;