import { FC, useState, useEffect, useContext } from 'react';
import { Typography, Table, Switch, Breadcrumb, Row, Col, Divider, Tooltip, Space } from '@douyinfe/semi-ui';
import { setApplicationPanelEnabled, getFlynet } from '@/services/flynet';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

const { Title, Paragraph, Text } = Typography;

const Index: FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const queryFlynet = async () => {
        setFlynetLoading(true);

        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
            setApplicationEnabled(res.flynet.applicationEnabled)
        }).catch((e) => {
            console.error(e)
        }).finally(() => {
            setFlynetLoading(false)
        })
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    const [applicationLoading, setApplicationLoading] = useState(false);
    const [applicationEnabled, setApplicationEnabled] = useState(false);
    const [flynetLoading, setFlynetLoading] = useState(false)

    const [flynet, setFlynet] = useState<Flynet>();

    const handleApplicationEnabledChange = (checked: boolean) => {

        if (!flynet) {
            return;
        }

        setApplicationLoading(true)
        setApplicationPanelEnabled(flynet?.id, checked).then(() => {
            setFlynet(Object.assign({}, flynet, { applicationEnabled: checked }))
            setApplicationLoading(false)
            setApplicationEnabled(checked)
        })
    }
    return <>
        <Title heading={4} className="mb2">{formatMessage({ id: 'services.application.switch.title' })}</Title>
        <Paragraph className='mb20' type='tertiary'>{formatMessage({ id: 'services.application.switch.description' })}</Paragraph>
        <Row style={{ maxWidth: 400 }} className='mb40'>
            <Col span={12}>
                <Space>

                    <Switch
                        loading={applicationLoading || flynetLoading}
                        checked={applicationEnabled}
                        onChange={handleApplicationEnabledChange}

                    />
                    <Paragraph>{formatMessage({ id: 'services.application.switch.applicationPanel' })}</Paragraph>

                </Space>

            </Col>

        </Row>

    </>
}

export default Index;