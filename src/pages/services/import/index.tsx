import React, { useContext, useEffect, useState } from 'react'
import { Typography, Banner, Upload, Input, Space, Button, Avatar, Breadcrumb, Row, Col, Tabs, TabPane } from '@douyinfe/semi-ui';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import SangforLogo from '@/assets/thirdparty/sangfor-logo.jpeg';
import { useLocale } from '@/locales';
const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss'
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const navigate = useNavigate();
    return <>
        <div className='settings-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/services`,
                        href: `${BASE_PATH}/services`,
                        name: formatMessage({ id: 'services.import.breadcrumb.services' })
                    },
                    {
                        path: `${BASE_PATH}/services/import`,
                        href: `${BASE_PATH}/services/import`,
                        name: formatMessage({ id: 'services.import.breadcrumb.dataImport' }),
                    }
                ]
            }>
            </Breadcrumb>
            <Title heading={3} className='mb10' >{formatMessage({ id: 'services.import.title.dataImport' })}</Title>
            <Paragraph type='tertiary' className='mb40'>
                {formatMessage({ id: 'services.import.description' })}
            </Paragraph>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Space style={{ padding: 20 }}>
                    <Button block size='large'
                        style={{ height: 80, width: 200 }}
                        onClick={() => navigate(`${BASE_PATH}/services/import/sangfor`)}><Avatar src={SangforLogo} style={{ marginRight: 20 }}></Avatar> <Text >{formatMessage({ id: 'services.import.sangforVpn' })}</Text></Button>
                </Space>
            </div>
        </div>
    </>
}

export default Index;
