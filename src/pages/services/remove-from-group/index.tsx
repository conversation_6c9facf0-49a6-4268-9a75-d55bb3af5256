import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification } from '@douyinfe/semi-ui';
import { Service, ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';
import { getSimpleServiceGroupName, getSimpleServiceName } from '@/utils/service';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    group: ServiceGroup,
    record: Service
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);

    // 获取服务显示名称
    const serviceName = getSimpleServiceName(props.record);
    // 获取服务组显示名称
    const serviceGroupName = getSimpleServiceGroupName(props.group);
    
    const handleSubmit = () => {
        setLoading(true);
        
        flylayerClient.removeServiceFromGroup({
            serviceId: props.record.id,
            groupId: props.group.id,
            flynetId: flynetGeneral.id
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'services.removeFromGroup.success.operationSuccess' }),
                content: formatMessage({ id: 'services.removeFromGroup.success.serviceRemovedFromGroup' }).replace('{serviceName}', serviceName).replace('{serviceGroupName}', serviceGroupName)
            });
            props.close();
            props.success && props.success();
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'services.removeFromGroup.error.operationFailed' }),
                content: formatMessage({ id: 'services.removeFromGroup.error.serviceRemoveFromGroupFailed' }).replace('{serviceName}', serviceName).replace('{serviceGroupName}', serviceGroupName)
            });
        }).finally(() => {
            setLoading(false);
        });
        
    }

    return <>
        <Modal
            title={`从服务组删除服务`}
            visible={true}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'> 确定把服务 <b>{serviceName}</b> 从服务组 <b>{serviceGroupName}</b> 中删除吗？</Paragraph>


            
        </Modal></>
}
export default Index;
