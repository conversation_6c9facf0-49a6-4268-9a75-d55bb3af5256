import React, { FC, useEffect, useState } from 'react'
import { Breadcrumb, Row, Col, Button, Typography, Tag, Notification, Dropdown, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List } from '@douyinfe/semi-ui';

import { BASE_PATH } from '@/constants/router';

import { Service, ServiceGroup, ServiceType, ServiceOrigin, ServiceProto, ServiceRouteMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import styles from './index.module.scss';
import { useLocale } from '@/locales';
const { Title, Paragraph, Text } = Typography;

const Index: FC = () => {
    const { formatMessage } = useLocale();
    // 详情加载标识，用于骨架屏
    const [detailLoading, setDetailLoading] = useState(false);

    // 详情数据
    const [service, setService] = useState<Service>();


    return (
        <><Skeleton placeholder={<div className='general-page'>
            <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>

            <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
            <Skeleton.Image style={{ height: 60 }} className='mb40' />
            <Skeleton.Image style={{ height: 200 }} />
        </div>} loading={detailLoading}>
            <div className='general-page'>
                <Breadcrumb routes={
                    [
                        {
                            path: `${BASE_PATH}/services`,
                            href: `${BASE_PATH}/services`,
                            name: formatMessage({ id: 'menu.services' })
                        },{
                            path: `${BASE_PATH}/services`,
                            href: `${BASE_PATH}/services`,
                            name: formatMessage({ id: 'services.group.title' })
                        },
                        {
                            name: service?.name,
                        }
                    ]
                }>
                </Breadcrumb>
                <Title  className={styles.heading} heading={3}>
                    <span>{service?.name}</span>
                    {/* {service?.status === 'running' ?
                            <Badge className={styles.headingBadge} dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> :
                            <Badge className={styles.headingBadge} dot type='tertiary' />} */}
                    </Title>

                <Divider style={{ marginBottom: 10 }} />
                <div className={styles.detailTable}>
                <Descriptions row={true}>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.name' })}>OA服务</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.type' })}>HTTP</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.source' })}>自动发现</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.status' })}>在线</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.network' })}>默认网络</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.site' })}>上海站</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.protocol' })}>TCP</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.port' })}>443</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.accessAddress' })}>**********</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.physicalAddress' })}>***************</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.createTime' })}>2020-11-11 23:30:20</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.discoveryTime' })}>2020-11-11 23:30:20</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.deviceGroup' })}>默认设备组</Descriptions.Item>
                    <Descriptions.Item key="remark" itemKey={formatMessage({ id: 'services.field.customTags' })}>标签</Descriptions.Item>
                </Descriptions>
                </div>
            </div>
        </Skeleton>
        </>
    )
}

export default Index;