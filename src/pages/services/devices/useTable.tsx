import { useEffect, useState, FC, useContext } from 'react';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import { useLocation, useNavigate } from 'react-router-dom';
import { Typography, Tag, List, Badge, Popover, Space, Notification, Button, Dropdown } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';
import { listMachines } from '@/services/device';

import { compare } from 'semver'
import { BASE_PATH } from '@/constants/router';
import { Timestamp } from "@bufbuild/protobuf";
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import { formatIPNVersion, formatDefaultTimestamp } from '@/utils/format';
const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss';
import { caseInsensitiveIncludes } from '@/utils/common';
import { useLocale } from '@/locales';
export type DeviceFilter = {
    query: string,
    os: Array<string>,
    connectStatus: 'online' | 'offline' | ''
}

const useTable = (initFilter: DeviceFilter) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const navigate = useNavigate();
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 设备列表
    const [devices, setDevices] = useState<Machine[]>([]);
    // 设备列表
    const [allDevices, setAllDevices] = useState<Machine[]>([]);


    const [originData, setOriginData] = useState<Array<Machine>>([]);
    // 当前页码
    const [page, setPage] = useState(1);

    const pageSize = 20;

    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);
    // 当前菜单选中设备
    const [curDevice, setCurDevice] = useState<Machine>();
    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filterParam, setFilterParam] = useState<DeviceFilter>(initFilter);

    const columns = [
        {
            with: 200,
            title: formatMessage({ id: 'services.devices.table.service' }),
            dataIndex: 'service',
            render: (field: string, record: Machine, index: number) => {
                return '服务' + index;
            }
        }, {
            width: 160,
            title: 'IP',
            dataIndex: 'ipV4',
            render: (field: string, record: Machine) => {
                return (
                    <div className={styles.ipCopyable} style={{ display: 'flex', alignItems: 'center' }}>

                        <Popover content={<List
                            bordered>
                            {/* <List.Item>
                            <Paragraph copyable>{record.hostname}</Paragraph>
                        </List.Item> */}
                            {[record.ipv4, record.ipv6].map((val, index) => {
                                return <List.Item key={index}>
                                    <Paragraph copyable>
                                        {val}</Paragraph>
                                </List.Item>
                            })}

                        </List>
                        }>
                            <Text className={styles.ipLine}>{record.ipv4}</Text>
                        </Popover>
                        <Copyable style={{ lineHeight: 1 }} content={record.ipv4}></Copyable>
                    </div>

                );
            },
        }, {
            with: 100,
            title: formatMessage({ id: 'services.devices.table.port' }),
            dataIndex: 'port',
            render: (field: string, record: Machine, index: number) => {
                return <>65536</>;
            }
        }, {
            with: 100,
            title: formatMessage({ id: 'services.devices.table.protocol' }),
            dataIndex: 'proto',
            render: (field: string, record: Machine, index: number) => {
                return <>{index % 2 == 0 ? 'TCP' : 'UDP'}</>;
            }
        },
        {
            width: 160,
            title: formatMessage({ id: 'services.devices.table.device' }),
            dataIndex: 'name',

            render: (field: string, record: Machine, index: number) => {
                // 设备名称
                const name = record.autoGeneratedName ? record.name : record.givenName
                return (
                    <a>{name}</a>
                );
            },
        },
        {
            width: 160,
            title: formatMessage({ id: 'services.devices.table.user' }),
            dataIndex: 'user',

            render: (field: string, record: Machine, index: number) => {
                // 设备名称
                const name = record.autoGeneratedName ? record.name : record.givenName
                return (
                    <div>

                        {record.tags && record.tags.length > 0 ? <div>
                            {record.tags.map((tag, index) => {
                                return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 5, marginBottom: 5 }}>{tag}</Tag>
                            })}
                        </div> : <Paragraph>{record.user?.loginName}</Paragraph>
                        }
                    </div>
                );
            },
        },


        {
            width: 100,
            title: formatMessage({ id: 'services.devices.table.os' }),
            dataIndex: 'os',

            render: (field: string, record: Machine) => {
                return (
                    <div className='layout-left-icon'>
                        <div>
                            <Paragraph>{field}</Paragraph>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 260,
            title: formatMessage({ id: 'services.devices.table.lastOnline' }),
            dataIndex: 'lastSeen',

            render: (field: Timestamp, record: Machine) => {
                return record.connected ?
                    <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {formatMessage({ id: 'services.devices.status.online' })}</span> :
                    <><span><Badge dot type='tertiary' /> {formatDefaultTimestamp(record.lastSeen)}</span></>

            }
        },
        {
            title: '',
            dataIndex: 'operate',
            render: (_: string, record: Machine) => {
                let isExpiry = false;
                if (!record.keyExpiryDisabled) {

                    if (record.expiresAt) {
                        if (record.createdAt && record.createdAt.toDate().getTime() > record.expiresAt.toDate().getTime()) {
                            // 创建时间大于过期时间，说明是旧数据，未过期
                        } else {
                            const expiry = record.expiresAt.toDate()
                            const now = new Date()
                            isExpiry = expiry < now;
                        }
                    }
                }
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => {
                                navigate(`${BASE_PATH}/logs/?target=${record.name}`)
                            }}>查看设备详情</Dropdown.Item>

                            {/* <Dropdown.Divider /> */}
                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];

    const rowSelection: any = {

        onSelect: (record: Machine, selected: boolean) => {
            console.log(`select row: ${selected}`, record);
        },
        onSelectAll: (selected: boolean, selectedRows: Array<Machine>) => {
            console.log(`select all rows: ${selected}`, selectedRows);
        },
        onChange: (selectedRowKeys: any, selectedRows: Array<Machine>) => {
            console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        },
    };

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;


        let sortedAllDate = [...allDevices];

        if (sortOrder == 'ascend') {
            setSortOrder('ascend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => (a && b && a.name > b.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return 1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return -1;
                            }
                        }
                    }
                    return -1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(a.clientVersion, b.clientVersion)
                    }
                    return -1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return 1;
                    }
                    if (!a.connected && b.connected) {
                        return -1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds > b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return 1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return -1;
                    }
                    return -1;
                });
            }
            setAllDevices(sortedAllDate);

            setDevices(doFilter(1, sortedAllDate, filterParam))
        } else if (sortOrder == 'descend') {
            setSortOrder('descend');



            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => (a && b && a.name < b.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return -1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return 1;
                            }
                        }
                    }
                    return 1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(b.clientVersion, a.clientVersion)
                    }
                    return 1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return -1;
                    }
                    if (!a.connected && b.connected) {
                        return 1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds < b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return -1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return 1;
                    }
                    return 1;
                });
            }

            setAllDevices(sortedAllDate);
            setDevices(doFilter(1, sortedAllDate, filterParam))
        } else {
            setSortOrder(undefined)
            setAllDevices(originData)
            setDevices(doFilter(1, originData, filterParam))

        }
        setSortField(dataIndex)



    }

    // 过滤结果
    const doFilter = (page: number, src: Array<Machine>, filter: DeviceFilter): Array<Machine> => {
        if (!src || src.length == 0) {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }

        if (filter.query == '' && filter.os.length == 0 && filter.connectStatus == '') {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }
        const filteredList = src.filter(record => {
            let { query, os, connectStatus } = filter;
            if (query) query = query.trim();
            let passQuery = true, passOs = true, passConnectStatus = true;
            if (query) {

                if (caseInsensitiveIncludes(record.givenName, query) ||
                    record.user && caseInsensitiveIncludes(record.user.displayName, query) ||
                    record.user && caseInsensitiveIncludes(record.user.loginName, query) ||
                    record.ipv4.indexOf(query) >= 0 ||
                    record.ipv6.indexOf(query) >= 0 ||
                    caseInsensitiveIncludes(record.clientVersion, query)
                ) {
                    passQuery = true;
                } else {
                    passQuery = false;
                }
            }
            if (os.length > 0) {
                passOs = os.indexOf(record.os) >= 0;
            }

            if (connectStatus == 'online') {
                passConnectStatus = record.connected;
            } else if (connectStatus == 'offline') {
                passConnectStatus = !record.connected;
            }

            return passQuery && passOs && passConnectStatus;
        })

        setTotal(filteredList.length)

        return filteredList.slice(0, page * pageSize)
    }

    const query = () => {

        setLoading(true)
        listMachines(flynet.id, 'limit=2&offset=0').then(res => {
            const machines = res.machines.filter((machine: Machine) => {
                if (machine.user?.disabled) {
                    return false;
                }
                if (!machine.authorized) {
                    return false;
                }
                if (!machine.keyExpiryDisabled) {
                    if (machine.expiresAt) {
                        if (machine.createdAt && machine.createdAt.toDate().getTime() > machine.expiresAt.toDate().getTime()) {
                            // 创建时间大于过期时间，说明是旧数据，未过期
                        } else {
                            const expiry = machine.expiresAt.toDate()
                            const now = new Date()
                            if (expiry < now) {
                                return false;
                            }
                        }
                    }
                }
                return true;
            });

            machines.sort((a?: Machine, b?: Machine) => {
                if (!a || !b) return -1;
                if (!a.authorized && b.authorized) {
                    return -1;
                }
                if (a.authorized && !b.authorized) {
                    return 1;
                }


                return 1;
            });

            setAllDevices(machines);
            let copyedData: any = []
            machines.forEach(user => {
                copyedData.push({ ...user })
            })
            setOriginData(copyedData);
            setDevices(doFilter(page, machines, filterParam))
        }).catch(e => {
            console.error(e)
            Notification.error({ content: formatMessage({ id: 'services.devices.error.loadDevicesFailed' }) })
        }).finally(() => setLoading(false))
    }
    const addPage = () => {
        setDevices(doFilter(page + 1, allDevices, filterParam));
        setPage(page + 1)

    }
    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setReloadFlag(false)
        setPage(1)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])
    useEffect(() => {
        setPage(1)
        setDevices(doFilter(1, allDevices, filterParam))

    }, [filterParam])
    return { columns, loading, allDevices, devices, curDevice, setCurDevice, reloadFlag, setReloadFlag, filterParam, setFilterParam, page, total, addPage, pageSize, handleSort, rowSelection }

}

export default useTable;
