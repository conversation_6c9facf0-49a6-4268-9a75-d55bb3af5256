import React, { useState, useEffect } from 'react'
import { Typography, Table, Row, Col, Button, Select, Space, Input, Layout, Tag, Spin, BackTop, Breadcrumb, Divider } from '@douyinfe/semi-ui';
import { IconSearch } from '@douyinfe/semi-icons';
import useTable, { DeviceFilter } from './useTable';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import InfiniteScroll from 'react-infinite-scroll-component';

import Edit from '../edit';
import qs from 'query-string';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import styles from './index.module.scss'
import { getQueryParam } from '@/utils/query';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;
const { Sider, Content } = Layout;
// 根据URL参数设置过滤参数
const getDeviceFilter = (location: Location): DeviceFilter => {
    const query: string = getQueryParam('query', location) as string;
    const osQuery = getQueryParam('os', location);
    const connectStatus: string = getQueryParam('connectStatus', location) as string;

    let os: string[] = [];
    if (osQuery && Array.isArray(osQuery)) {
        os = osQuery as string[];
    }
    if (osQuery && typeof osQuery == 'string') {
        os = [osQuery as string];
    }
    return {
        query: query || '',
        os: os,
        connectStatus: connectStatus == 'online' || connectStatus == 'offline' ? connectStatus : ''
    }
}
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const location = useLocation();
    const navigate = useNavigate();


    // 过滤参数
    const initFilter: DeviceFilter = getDeviceFilter(location);

    const [editVisible, setEditVisible] = useState(false);


    // 过滤参数改变时跳转路由
    const doNavigate = (param: DeviceFilter) => {

        let query = '';
        if (param.query || param.os.length > 0 || param.connectStatus) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/services/devices?${query}`)
        } else {
            navigate(`${BASE_PATH}/services/devices`)
        }
    }

    // 设备列表HOOKS
    const { columns, devices, allDevices, loading, curDevice, setCurDevice, setReloadFlag, filterParam, setFilterParam, page, total, addPage, pageSize, handleSort, rowSelection } = useTable(initFilter);


    const listConnectStatus = [
        { value: '', label: formatMessage({ id: 'services.devices.status.all' }) },
        { value: 'online', label: formatMessage({ id: 'services.devices.status.online' }) },
        { value: 'offline', label: formatMessage({ id: 'services.devices.status.offline' }) }
    ];
    const listOs = [
        { value: 'macOS', label: 'macOS' },
        { value: 'iOS', label: 'iOS' },
        { value: 'windows', label: 'Windows' },
        { value: 'linux', label: 'Linux' },
        { value: 'android', label: 'Android' },
    ];

    const handleConnectStatusChange = (value: any) => {
        setFilterParam({ ...filterParam, connectStatus: value })
        doNavigate({ ...filterParam, connectStatus: value });
    }
    const handleOsChange = (value: any) => {

        setFilterParam({ ...filterParam, os: value })
        doNavigate({ ...filterParam, os: value });
    }

    const handleQueryChange = (value: string) => {

        setFilterParam({ ...filterParam, query: value })
        doNavigate({ ...filterParam, query: value });
    }
    // 查询参数
    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        // 查询参数从有值变化为无值时，重新加载数据
        if (location.search == '' && search != '') {
            setFilterParam(initFilter);
        }
        setSearch(location.search);
    }, [location])


    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/services`,
                        href: `${BASE_PATH}/services`,
                        name: formatMessage({ id: 'services.group.breadcrumb.allServices' })
                    },
                    {
                        name: formatMessage({ id: 'services.devices.breadcrumb.autoDiscoveredServices' }),
                    }
                ]
            }>
            </Breadcrumb>
            <Row className='mb10'>
                <Col span={20}>
                    <Title heading={3}>自动发现服务列表</Title>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                    {/* <Button theme='solid'>部署</Button> */}
                </div></Col>
            </Row>
            {/* <Paragraph type='tertiary' className='mb40'>设备上自动发现的服务</Paragraph> */}

            <Divider className='mb20'></Divider>
            <Layout className='mb20 search-bar' >
                <Layout>
                    <Content className='pr10'>
                        <Input value={filterParam.query}
                            onChange={handleQueryChange}
                            style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={formatMessage({ id: 'services.devices.filter.searchPlaceholder' })}></Input>
                    </Content>
                    <Sider> <Space>
                        <Select style={{ width: 220 }}
                            optionList={listConnectStatus}
                            insetLabel={formatMessage({ id: 'services.devices.filter.status' })}
                            onChange={handleConnectStatusChange}
                            value={filterParam.connectStatus}></Select>

                        <Select multiple
                            maxTagCount={1}
                            style={{ width: 220 }}
                            optionList={listOs}
                            insetLabel="操作系统"
                            onChange={handleOsChange}
                            value={filterParam.os}></Select>
                    </Space></Sider>
                </Layout>

            </Layout>

            <Row className='mb10'>
                <Col span={20}><div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  服务总数 {total}</Tag>} </div>

                </Col>
                <Col span={4}><div className='btn-right-col'>
                    <Button theme='solid' onClick={() => setEditVisible(true)}>新建服务</Button>
                </div></Col>
            </Row>
            <InfiniteScroll
                dataLength={devices.length} //This is important field to render the next data
                next={addPage}
                hasMore={devices.length < total}
                loader={<div><Spin></Spin></div>}
                endMessage={
                    <div style={{ textAlign: 'center', paddingTop:16, paddingBottom: 16 }}>
                        {devices.length > pageSize && <Paragraph type='tertiary'>---- 到底了 ----</Paragraph>}
                    </div>
                }
            // below props only if you need pull down functionality
            // refreshFunction={()=>setReloadFlag(true)}
            // pullDownToRefresh
            // pullDownToRefreshThreshold={50}
            // pullDownToRefreshContent={
            //     <h3 style={{ textAlign: 'center' }}>&#8595; Pull down to refresh</h3>
            // }
            // releaseToRefreshContent={
            //     <h3 style={{ textAlign: 'center' }}>&#8593; Release to refresh</h3>
            // }
            >
                <Table
                    rowSelection={rowSelection}
                    rowKey={(record?: Machine) => record ? record.id + '' : ''}
                    onChange={handleSort}
                    empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={devices} pagination={false} />
            </InfiniteScroll>
            <BackTop style={{ right: 10 }} />
        </div>
        {/* {
                editVisible && curDevice  && <Edit record={curDevice} close={() => { setEditVisible(false);  }}
                success={(service) => {
                    setEditVisible(false);
                    setReloadFlag(true);
                    
                }}
                ></Edit>
            } */}
    </>
}

export default Index;
