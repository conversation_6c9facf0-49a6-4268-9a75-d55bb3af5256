import { useEffect, useState, useContext } from 'react';

import { Typography, Dropdown, Button, Popover } from '@douyinfe/semi-ui';
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { flylayerClient } from '@/services/core';

import DateFormat from '@/components/date-format';
import { useLocale } from '@/locales';
import { BASE_PATH } from '@/constants/router';
import { useNavigate } from 'react-router-dom';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { caseInsensitiveIncludes } from '@/utils/common';

export type ServicesFilter = {
    query?: string;
    source?: string;
    type?: string;
    lastSeen?: string;
}

const { Title, Paragraph, Text } = Typography;
const useTable = (filterParam: ServicesFilter) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 服务组列表
    const [groups, setGroups] = useState<ServiceGroup[]>([]);
    // 全部服务组列表
    const [allGroups, setAllGroups] = useState<ServiceGroup[]>([]);

    // 编辑弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 删除弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);

    // 当前菜单选中服务
    const [selectedGroup, setSelectedGroup] = useState<ServiceGroup>();

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    // 过滤参数
    const [filter, setFilter] = useState<ServicesFilter>(filterParam);


    const [servicesGroupTreeData, setServicesGroupTreeData] = useState<any[]>([]);

    const buildTreeData = (treeData: Array<ServiceGroup>,
        serviceGroups: ServiceGroup[],
        mapTreeData: Map<bigint, ServiceGroup>, isRoot: boolean, depth: number) => {
        const leftServiceGroups: ServiceGroup[] = [];
        serviceGroups.forEach((item) => {
            if (isRoot) {
                if (!item.parentId || item.parentId == BigInt(0)) {

                    treeData.push(item)
                    mapTreeData.set(item.id, item);
                } else {
                    leftServiceGroups.push(item);
                }
            } else {
                if (mapTreeData.has(item.parentId)) {
                    const parent = mapTreeData.get(item.parentId);

                    parent?.children.push(item);
                    mapTreeData.set(item.id, item);
                } else {
                    leftServiceGroups.push(item);
                }
            }


        })

        if (depth > 10) {

            return;
        }
        if (leftServiceGroups.length > 0) {
            buildTreeData(treeData, leftServiceGroups, mapTreeData, false, depth + 1);
        }

    }


    // 表格列
    const columns = [
        {
            title: formatMessage({ id: 'services.group.table.groupName' }),
            dataIndex: 'name',
            // sorter: (a?: ServiceGroup, b?: ServiceGroup) => (a && b && a.name > b.name ? 1 : -1),
            render: (field: string, record: ServiceGroup, index: number) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                        <div>
                            <Title heading={6}>
                                {record.alias} {record.description &&
                            <Popover content={<div className='p10'>{record.description}</div>}>
                                <IconArticle style={{
                                    fontSize: 14,
                                    marginLeft: '4px',
                                    color: '#999'
                                }} />
                            </Popover>}
                            </Title>
                            <Paragraph size='small'>{record.name}</Paragraph>
                        </div>
                    </div>
                </>
            },
        },
        {
            width: 160,
            title: formatMessage({ id: 'services.group.table.serviceCount' }),
            dataIndex: 'type',
            render: (field: string, record: ServiceGroup, index: number) => {
                return <>{record.serviceCount}</>

            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'services.group.table.createdTime' }),
            dataIndex: 'createdAt',
            render: (field: string, record: ServiceGroup, index: number) => {

                return <>
                    <DateFormat date={record.createdAt}></DateFormat>

                </>
            }
        },
        {
            width: 100,
            title: '',
            dataIndex: 'operate',
            render: (field: string, record: ServiceGroup) => {
                return <div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item onClick={() => {
                                navigate(`${BASE_PATH}/services?group=${record.name}`)
                            }}>查看服务</Dropdown.Item>
                            <Dropdown.Item
                                onClick={() => {
                                    setSelectedGroup(record)
                                    setEditVisible(true)
                                }}
                            >编辑服务组</Dropdown.Item>

                            <Dropdown.Divider />

                            <Dropdown.Item type="danger"
                                onClick={() => {
                                    setSelectedGroup(record)
                                    setDelVisible(true)
                                }}
                            >删除服务组</Dropdown.Item>
                        </Dropdown.Menu>
                    }
                >
                    <Button><IconMore className='align-v-center' /></Button>
                </Dropdown></div>;
            },
        },
    ];



    // 过滤数据
    const doFilter = (src: Array<ServiceGroup>, filter: ServicesFilter) => {
        if (!src || src.length == 0) {
            return src;
        }
        
        if (filter.query == '') {
            return src;
        }

        let dst: Array<ServiceGroup> = [];
        src.forEach((item) => {
            if (filter.query) {
                if (item.children) {
                    item.children = doFilter(item.children, filter);
                }
                if (caseInsensitiveIncludes(item.name, filter.query) || caseInsensitiveIncludes(item.description, filter.query) || item.children.length > 0) {
                    dst.push(item);
                }

            }
        })
        return dst;

    }

    // 加载数据
    const query = () => {
        setLoading(true);

        flylayerClient.listServiceGroups({
            flynetId: flynet.id
        }).then((res) => {
            const treeData = Array<ServiceGroup>();
            let mapTreeData = new Map();

            buildTreeData(treeData, res.serviceGroups, mapTreeData, true, 0);
            
            setGroups(treeData)
            setAllGroups(treeData)
        }, (err) => { }).finally(()=>{
            setLoading(false);
        })
    }

    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])
    useEffect(() => {
        const res = doFilter(allGroups, filterParam) 
        
        setGroups(res)

    }, [filterParam])

    return {
        columns,
        loading,
        allGroups,
        groups,
        selectedGroup,
        setSelectedGroup,
        editVisible, setEditVisible,
        delVisible, setDelVisible,
        reloadFlag,
        setReloadFlag,
        filter,
        setFilter
    }
}


export default useTable;