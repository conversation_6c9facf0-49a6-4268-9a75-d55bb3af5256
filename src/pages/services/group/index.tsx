import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Tag, Divider } from '@douyinfe/semi-ui';
import useTable, { ServicesFilter } from './useTable';
import { getQueryParam } from '@/utils/query';
import { ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { Location, useLocation, useNavigate } from 'react-router-dom';
import GroupAdd from '../group-add';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import SearchFilter, { FilterParam } from '@/components/search-filter';
import { useLocale } from '@/locales';

import GroupEdit from '../group-edit';
import GroupDel from '../group-del';

const { Title } = Typography;

// 根据URL参数设置过滤参数
const getServicesFilter = (location: Location): ServicesFilter => {
    const query: string = getQueryParam('query', location) as string;
    return {
        query: query || ''
    }
}

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const initFilter: ServicesFilter = getServicesFilter(useLocation());
    const { columns, loading, allGroups: allServices, groups: services, selectedGroup, setSelectedGroup,
        editVisible, setEditVisible,

        delVisible, setDelVisible,
        reloadFlag, setReloadFlag, filter, setFilter } = useTable(initFilter);

    const navigate = useNavigate();
    // 过滤参数改变时跳转路由
    const doNavigate = (param: ServicesFilter) => {
        const { query } = param;
        navigate(`${BASE_PATH}/services/group/?query=${query}`);
    }
    const handleQueryChange = (value: string) => {
        setFilter({ ...filter, query: value })
        doNavigate({ ...filter, query: value });
    }

    const [createVisible, setCreateVisible] = useState(false);
    const [groupVisible, setGroupVisible] = useState(false);

    const [filterParams, setFilterParams] = useState<FilterParam[]>([{
        name: 'query',
        placeholder: formatMessage({ id: 'services.group.filter.queryPlaceholder' }),
        label: formatMessage({ id: 'services.group.filter.query' }),
        value: initFilter.query || '',
    }
        // , {
        //     name: 'type',
        //     placeholder: '服务类型',
        //     label: '服务类型',
        //     value: initFilter.type || '',
        //     filterComponent: TypeSelector,
        //     funGetDisplayValue: getTypeDisplayValue,

        // }, {
        //     name: 'source',
        //     placeholder: '来源',
        //     label: '来源',
        //     value: initFilter.source || '',
        //     filterComponent: SourceSelector,
        //     funGetDisplayValue: getSourceDisplayValue
        // }, {
        //     name: 'lastSeen',
        //     placeholder: '最近在线时间',
        //     label: '最近在线时间',
        //     value: initFilter.lastSeen || '',
        //     filterComponent: LastSeenSelector,
        //     funGetDisplayValue: getLastSeenDisplayValue
        // }
    ]);

    return <><div className='general-page'><Breadcrumb routes={
        [
            {
                path: `${BASE_PATH}/services`,
                href: `${BASE_PATH}/services`,
                name: formatMessage({ id: 'services.group.breadcrumb.allServices' })
            },
            {
                name: formatMessage({ id: 'services.group.breadcrumb.serviceGroups' }),
            }
        ]
    }>
    </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>服务组列表</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>新建服务组</Button>
                </Space>
            </div></Col>
        </Row>
        <Divider className='mb20'></Divider>
        <SearchFilter onChange={(val: string, filterParam) => {
            setFilter({ ...filter, [filterParam.name]: val })
            doNavigate({ ...filter, [filterParam.name]: val });
            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);

        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
        <div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  服务组总数 {services.length}</Tag>} </div>
        <Table
            rowKey={(record?: ServiceGroup) => record ? record.id + '' : ''}
            expandRowByClick
            // expandAllRows={services.length < 10}
            empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={services} pagination={false} />
    </div>

        {createVisible && <GroupAdd
            close={() => { setCreateVisible(false); }}
            success={() => {
                setCreateVisible(false)
                setReloadFlag(true)
            }}
        ></GroupAdd>
        }

        {delVisible && selectedGroup && <GroupDel
            close={() => {
                setDelVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setDelVisible(false)
                setSelectedGroup(undefined)
                setReloadFlag(true)
            }}
            record={selectedGroup}
        ></GroupDel>}
        {editVisible && selectedGroup && <GroupEdit
            servicesGroupId={selectedGroup.id}
            close={() => {
                setEditVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setEditVisible(false)
                setReloadFlag(true)
            }}
        ></GroupEdit>}
    </>
}

export default Index;