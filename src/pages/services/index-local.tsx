import React, { useState, useContext } from 'react'
import { Typography, Table, Row, Col, Button, Space, Tag, BackTop, Tabs, TabPane, Popover } from '@douyinfe/semi-ui';
import useTable, { ServicesFilter } from './useTable-local';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import AddServiceGuide from './add-services-guide';
import { Service, ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import qs from 'query-string';

import InfiniteScroll from 'react-infinite-scroll-component';
import Edit from './edit';
import Del from './del';
import GroupAdd from './group-add';
import RemoveFromGroup from './remove-from-group';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import SearchFilter from '@/components/search-filter';
import { useLocale } from '@/locales';
const { Title, Paragraph } = Typography;

// 根据URL参数设置过滤参数
const getServicesFilter = (location: Location): ServicesFilter => {
    const query: string = getQueryParam('query', location) as string;
    const source: string = getQueryParam('source', location) as string;
    const type: string = getQueryParam('type', location) as string;
    const proto: string = getQueryParam('proto', location) as string;
    const group: string = getQueryParam('group', location) as string;
    return {
        query: query || '',
        source: source || '',
        type: type || '',
        proto: proto || '',
        group: group || ''
    }
}

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const initFilter: ServicesFilter = getServicesFilter(useLocation());
    const { columns, loading, services,
        delVisible, setDelVisible, editVisible, setEditVisible, selectedService, setSelectedService,
        reloadFlag, setReloadFlag, filter, setFilter,
        page, setPage, pageSize, addPage, total,
        handleSort, filterParams, setFilterParams, handleFilterChange, mapService,
        groups, curGroup, setCurGroup, handleGroupChange,
        removeFromGroupVisible, setRemoveFromGroupVisible
    } = useTable(initFilter);


    const navigate = useNavigate();
    // 过滤参数改变时跳转路由
    const doNavigate = (param: ServicesFilter) => {
        let query = '';
        if (param.query || param.source || param.type || param.proto || param.group) {
            query = qs.stringify(param, {
                skipEmptyString: true,
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/services?${query}`);
        }
        else {
            navigate(`${BASE_PATH}/services`);
        }
    }

    const [createVisible, setCreateVisible] = useState(false);

    const [groupVisible, setGroupVisible] = useState(false);


    return <><div className='general-page'>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>{formatMessage({ id: 'services.index.title' })}</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button
                        onClick={() => navigate(`${BASE_PATH}/services/import`)}
                    >{formatMessage({ id: 'services.index.button.dataImport' })}</Button>
                    {flynetGeneral.applicationEnabled && <Button
                        onClick={() => navigate(`${BASE_PATH}/services/application/`)}>{formatMessage({ id: 'services.index.button.application' })}</Button>}
                    <Button
                        onClick={() => navigate(`${BASE_PATH}/services/group/`)}>{formatMessage({ id: 'services.index.button.serviceGroup' })}</Button>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>{formatMessage({ id: 'services.index.button.newService' })}</Button>
                    {/* <Dropdown
                        position='bottomRight'
                        render={
                            <Dropdown.Menu>
                                <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/services/group/`)}>服务组</Dropdown.Item>
                                <Dropdown.Divider />
                                <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/services/devices`)}>添加自动发现服务</Dropdown.Item>
                            </Dropdown.Menu>
                        }
                    >
                        <Button theme='solid' onClick={() => { }} icon={<IconSetting />}></Button>
                    </Dropdown> */}
                </Space>
            </div></Col>
        </Row>
        <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'services.index.description' })}</Paragraph>
        <SearchFilter onChange={(val: string, filterParam) => {

            handleFilterChange({ ...filter, [filterParam.name]: val })
            doNavigate({ ...filter, [filterParam.name]: val });

            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);

        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
        <Tabs
            type="card"
            collapsible
            onChange={(activeKey: string) => {
                let activeGroup: ServiceGroup | undefined = undefined;
                if (activeKey) {
                    groups.forEach(group => {
                        if (group.name === activeKey) {
                            setCurGroup(group)
                            activeGroup = group;
                        }
                    })
                } else {
                    setCurGroup(undefined)
                }
                setFilter({ ...filter, group: activeKey });
                handleGroupChange(activeGroup);

                // setFilterParams({ ...filterParams, group: activeGroup })
                doNavigate({ ...filter, group: activeKey })
            }}
            activeKey={curGroup ? curGroup.name : ''}
        >
            <TabPane tab={formatMessage({ id: 'services.group.breadcrumb.allServices' })} itemKey=''></TabPane>
            {groups.map((group, index) => {
                return <TabPane key={index}
                    tab={group.alias ?
                        <Popover position='top' content={<div className='p10'>{group.description ? group.description : group.name}</div>}>{group.alias}</Popover>
                        : <span>{group.name}</span>}
                    itemKey={group.name}></TabPane>
            })}
        </Tabs>
        <div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  {formatMessage({ id: 'services.index.totalServices' })} {total}</Tag>} </div>
        <InfiniteScroll
            dataLength={services.length} //This is important field to render the next data
            next={addPage}

            hasMore={services.length < total}
            loader={<div><TableEmpty loading={true} /></div>}
            endMessage={
                <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                    {services.length > pageSize && <Paragraph type='tertiary'>{formatMessage({ id: 'services.index.endMessage' })}</Paragraph>}
                </div>
            }
        >
            <Table

                rowKey={(record?: Service) => record ? record.id + '' : ''}
                onChange={handleSort}
                empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={services} pagination={false} />
        </InfiniteScroll>

        <BackTop style={{ right: 10 }} />
    </div>
        {createVisible &&
            <AddServiceGuide
                mapService={mapService}
                close={() => { setCreateVisible(false); setSelectedService(undefined) }}
                success={(service) => {
                    setCreateVisible(false);
                    setReloadFlag(true);
                    if (service) {
                        setSelectedService(undefined);
                    }
                }}
            ></AddServiceGuide>}
        {
            delVisible && selectedService && <Del record={selectedService}
                close={() => {
                    setDelVisible(false);
                    setSelectedService(undefined)
                }}
                success={() => {
                    setDelVisible(false);
                    setReloadFlag(true);
                    setSelectedService(undefined);

                }}
            ></Del>
        }
        {
            editVisible && selectedService && <Edit
                mapService={mapService}
                recordId={selectedService.id}
                close={() => {

                    setEditVisible(false);
                    setSelectedService(undefined)
                }}
                success={() => {
                    setEditVisible(false);
                    setReloadFlag(true);
                    setSelectedService(undefined);

                }}
            ></Edit>
        }
        {groupVisible && <GroupAdd
            close={() => { setGroupVisible(false); }}
        ></GroupAdd>
        }
        {removeFromGroupVisible && selectedService && curGroup && <RemoveFromGroup
            close={() => {
                setRemoveFromGroupVisible(false);
                setSelectedService(undefined)
            }}
            success={() => {
                setRemoveFromGroupVisible(false);
                setReloadFlag(true);
                setSelectedService(undefined);

            }}
            group={curGroup}
            record={selectedService}
        ></RemoveFromGroup>    
    }

    </>
}

export default Index;
