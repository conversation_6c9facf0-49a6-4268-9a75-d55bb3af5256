import React, { FC, useState, useContext, useEffect } from 'react'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';
import { ServiceGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';


interface GroupTreeData {
    label: string,
    value: string,
    key: string
    parentId?: string,
    fullName: string
    children: Array<GroupTreeData>
}
let mapTreeData: Map<string, GroupTreeData> = new Map();
const useServiceGroup = () => {

    const flynet = useContext(FlynetGeneralContext);
    const [serviceGroupTreeData, setServiceGroupTreeData] = useState<any[]>([]);
    // const [mapTreeData, setMapTreeData] = useState<Map<string, GroupTreeData>>();
    const getMapTreeData = () => {return mapTreeData}
    const buildTreeData = (treeData: Array<GroupTreeData>,
        serviceGroups: ServiceGroup[],
        mapTreeData: Map<string, GroupTreeData>, isRoot: boolean, depth: number) => {
        const leftServiceGroups: ServiceGroup[] = [];
        serviceGroups.forEach((item) => {
            if (isRoot) {
                if (!item.parentId || item.parentId == BigInt(0)) {
                    const treeItem: GroupTreeData = {
                        label: `${item.alias}(${item.name})`,
                        fullName: item.fullName,
                        value: item.id + '',
                        key: item.id + '',
                        children: []
                    }
                    treeData.push(treeItem)
                    mapTreeData.set(item.id + '', treeItem);
                } else {
                    leftServiceGroups.push(item);
                }
            } else {
                if (mapTreeData.has(item.parentId+ '')) {
                    const parent = mapTreeData.get(item.parentId+ '');
                    const description = item.description ? `(${item.description})` : ''
                    const treeItem: GroupTreeData = {
                        parentId: item.parentId + '',
                        fullName: item.fullName,
                        label: `${item.alias}( ${item.name}))`,
                        value: item.id + '',
                        key: item.id + '',
                        children: []
                    }
                    parent?.children.push(treeItem);
                    mapTreeData.set(item.id + '', treeItem);
                } else {
                    leftServiceGroups.push(item);
                }
            }


        })

        if (depth > 10) {

            return;
        }
        if (leftServiceGroups.length > 0) {
            buildTreeData(treeData, leftServiceGroups, mapTreeData, false, depth + 1);
        }

    }


    useEffect(() => {
        flylayerClient.listServiceGroups({
            flynetId: flynet.id,
        }).then((res) => {
            const treeData: Array<GroupTreeData> = [];
            mapTreeData = new Map();
            buildTreeData(treeData, res.serviceGroups, mapTreeData, true, 0);
            
            //setMapTreeData(mapTreeData);
            setServiceGroupTreeData(treeData);
        })
    }, [])

    return {
        serviceGroupTreeData: serviceGroupTreeData,
        getMapTreeData
    }
};

export default useServiceGroup;

