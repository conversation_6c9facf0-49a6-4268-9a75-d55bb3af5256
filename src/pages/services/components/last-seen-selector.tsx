import { FC, useState } from 'react';
import { Select, Col, Input, Dropdown, Button, DatePicker, Divider } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

export const getLastSeenDisplayValue = (value: string) => {
    return value;
}

const LastSeenSelector: FC<{ value: string, onChange: (val: string) => void }> = (props) => {
    const { formatMessage } = useLocale();
    const { value, onChange } = props;
    const [val, setVal] = useState<string>(value);
    return <>
        <DatePicker zIndex={9999} className='mb10' value={val} onChange={(date, dateString) => setVal(dateString as string)} />
        <Divider className='mb10'></Divider>
        <Button block onClick={() => onChange(val)}>{formatMessage({ id: 'services.components.lastSeen.apply' })}</Button>
    </>
}

export default LastSeenSelector;