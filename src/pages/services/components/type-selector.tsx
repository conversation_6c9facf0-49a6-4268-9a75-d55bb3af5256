import { FC, useState } from 'react';
import { CheckboxGroup, Checkbox, Divider, Cascader, Button, Space } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

// 保留原函数以保持向后兼容
export const getTypeDisplayValue = (value: string) => {
    let values = value.split(',');
    let displayValues = [];
    if (values.indexOf('1') >= 0) {
        displayValues.push('远程桌面');
    }
    if (values.indexOf('2') >= 0) {
        displayValues.push('系统服务');
    }
    return displayValues.join(',');
}

// 新的国际化版本
export const getTypeDisplayValueI18n = (value: string, formatMessage: (descriptor: { id: string }) => string) => {
    let values = value.split(',');
    let displayValues = [];
    if (values.indexOf('1') >= 0) {
        displayValues.push(formatMessage({ id: 'services.components.type.remoteDesktop' }));
    }
    if (values.indexOf('2') >= 0) {
        displayValues.push(formatMessage({ id: 'services.components.type.systemService' }));
    }
    return displayValues.join(',');
}


const TypeSelector: FC<{ value: string, onChange: (val: string) => void }> = (props) => {
    const { formatMessage } = useLocale();
    const { value, onChange } = props;

    const [checkedList, setCheckedList] = useState<string[]>(value.split(','));

    return <><CheckboxGroup className='mb10' style={{ width: '100%' }} value={checkedList} onChange={value => {
        setCheckedList(value);
    }}>
        {/* <Checkbox checked={value.indexOf('APP') >= 0} value="APP">应用</Checkbox> */}
        {/* <Checkbox checked={value.indexOf('1') >= 0} value="1">远程桌面</Checkbox> */}
        <Checkbox checked={value.indexOf('2') >= 0} value="2">{formatMessage({ id: 'services.components.type.systemService' })}</Checkbox>
    </CheckboxGroup>
        <Divider className='mb10'></Divider>
        <Button block onClick={() => {
            let newCheckedList = checkedList.filter((item) => item != '');
            onChange(newCheckedList.join(','))
        }}>{formatMessage({ id: 'common.apply' })}</Button>
    </>
}

export default TypeSelector;