import { FC } from 'react';
import { Select, Col, Input, Dropdown, Button, Space } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

export const getSourceDisplayValue = (value: string) => {
    if(value == 'Config') {
        return '系统配置';
    } else if(value == 'Detect') {
        return '自动发现';
    }
    return value;
}

// 国际化版本
export const getSourceDisplayValueI18n = (value: string, formatMessage: (descriptor: { id: string }) => string) => {
    if(value == 'Config') {
        return formatMessage({ id: 'services.components.source.config' });
    } else if(value == 'Detect') {
        return formatMessage({ id: 'services.components.source.detect' });
    }
    return value;
}

const SourceSelector: FC<{ value: string, onChange: (val: string) => void }> = (props) => {
    const { formatMessage } = useLocale();
    const { value, onChange } = props;
    return <Select value={value}
    zIndex={9999}
        onChange={(value) => { onChange(value as string) }}
        style={{ width: '100%' }}>
        <Select.Option value='Config'>{formatMessage({ id: 'services.components.source.config' })}</Select.Option>
        <Select.Option value='Detect'>{formatMessage({ id: 'services.components.source.detect' })}</Select.Option>

    </Select>
}

export default SourceSelector;