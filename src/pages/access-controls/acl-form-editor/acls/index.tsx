import React, { useState, useEffect } from 'react'
import { Typography, Spin, Space, ArrayField, Form, Button, Row, Col, Popover } from '@douyinfe/semi-ui';
import { ACL, ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import Add from './add';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Title, Paragraph } = Typography;
import styles from '../index.module.scss'
import { IconPlusCircle, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons';
import { FormState } from '@douyinfe/semi-ui/lib/es/form';

const Index: React.FC<{
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    localVersion: number
}> = (props) => {
    const [formApi, setFormApi] = useState<FormApi>()
    const aclPolicy = props.aclPolicy;

    // 添加向导对话框是否显示
    const [addWizardVisible, setAddWizardVisible] = useState(false);

    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {
        let acls: {
            action: string,
            src: string[],
            dst: string[],
            priority: number
        }[] = [];
        policy.acls.forEach((acl: ACL) => {
            acls.push({
                action: acl.action,
                src: acl.src,
                dst: acl.dst,
                priority: acl.priority || 1
            })
        })

        return {
            acls: acls
        }
    }

    // 初始值
    const [initValues, setInitValues] = useState(calInitValues(aclPolicy));


    // 当本地版本号变化时，重新计算初始值
    useEffect(() => {
        if (props.localVersion > 0) {
            const initValues = calInitValues(aclPolicy);
            setInitValues(initValues);

            formApi?.setValues(initValues, { isOverride: true });
        }
    }, [props.localVersion])


    const handleChange = (values: {
        acls: {
            action: string,
            src: string[],
            dst: string[],
            priority: number
        }[]
    }) => {
        let acls: ACL[] = [];
        if (values.acls) {
            values.acls.forEach((acl) => {
                acls.push(new ACL({
                    action: acl.action,
                    src: acl.src,
                    dst: acl.dst,
                    priority: acl.priority
                }))
            })
        }

        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            acls: acls
        })
        props.onChange(policy);
    };

    return <><div className={styles.settingItem}>
        <Title heading={4}>规则列表</Title>
        <Paragraph type='tertiary' className='mb40'>网络访问规则列表，每个规则授予从一组源到一组目标的访问权限</Paragraph>

        <Form
            getFormApi={setFormApi}
            onValueChange={handleChange} initValues={initValues} allowEmpty>
            <ArrayField field='acls' >
                {({ addWithInitValue, arrayFields }) => (
                    <React.Fragment>
                        <Space>
                            <Button className='mb20' onClick={() => addWithInitValue({
                                action: 'accept',
                                src: [],
                                dst: []
                            })} icon={<IconPlusCircle />} >添加规则</Button>
                            <Button className='mb20' onClick={() => {
                                setAddWizardVisible(true)
                            }} style={{ display: 'none' }} icon={<IconPlusCircle />} >添加向导</Button>
                        </Space>
                        <Row className={styles.tableTitle} >
                            <Col span={3}>优先级 <Popover content={<div className='p10'>优先级范围为1-100，默认为1，即最高优先级。</div>}><IconHelpCircle style={{fontSize:14, verticalAlign: 'text-top'}}/></Popover> </Col>
                            <Col span={8}>源</Col>
                            <Col span={3}>策略 <Popover content={<div className='p10'>优先级相同的情况下，拒绝策略优于允许策略。</div>}><IconHelpCircle style={{fontSize:14, verticalAlign: 'text-top'}}/></Popover> </Col>
                            <Col span={8}>目标</Col>
                            <Col span={2}></Col>
                        </Row>
                        {
                            arrayFields.map(({ field, key, remove }, i) => (
                                <Row className={styles.tableBody} key={key} >
                                    
                                    <Col xs={24} sm={3}>
                                        <Form.InputNumber
                                            field={`${field}[priority]`}
                                            min={1} max={100} step={1}
                                            noLabel
                                        ></Form.InputNumber>
                                    </Col>
                                    <Col xs={24} sm={8}>
                                        <Form.TagInput
                                            separator={'                        '}
                                            field={`${field}[src]`}
                                            noLabel
                                            addOnBlur
                                            label={'源'}

                                        ></Form.TagInput>
                                    </Col>
                                    <Col xs={24} sm={3}> 
                                        <Form.Select field={`${field}[action]`} noLabel style={{ width: 80 }}>
                                            <Form.Select.Option value="accept">允许</Form.Select.Option>
                                            <Form.Select.Option value="reject">拒绝</Form.Select.Option>
                                        </Form.Select>
                                    </Col>
                                    <Col xs={24} sm={8}>

                                        <Form.TagInput
                                            separator={'                        '}
                                            field={`${field}[dst]`}
                                            noLabel
                                            addOnBlur
                                            label={'目标'}
                                        ></Form.TagInput>
                                    </Col>
                                    <Col xs={24} sm={2}>
                                        <Button
                                            type='danger'
                                            theme='borderless'
                                            icon={<IconMinusCircle />}
                                            onClick={remove}
                                            style={{ margin: 12 }}
                                        />
                                    </Col>

                                </Row>
                            ))
                        }
                    </React.Fragment>
                )}
            </ArrayField>

            {/* <div className={styles.submitDiv}><Space>
                    <Button type="primary" theme='solid' htmlType="submit" className="btn-margin-right">提交</Button>
                    <Button htmlType="reset">重置</Button></Space></div> */}
        </Form>
    </div>
        {addWizardVisible ? <Add close={() => { setAddWizardVisible(false) }} success={() => { setAddWizardVisible(false) }}></Add> : null}

    </>
}

export default Index