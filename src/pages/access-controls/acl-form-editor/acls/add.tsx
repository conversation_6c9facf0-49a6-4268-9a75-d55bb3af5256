import React, { useState, useEffect } from 'react'
import { Typography, Modal, Form, Row, Col, Notification } from '@douyinfe/semi-ui';

const Index: React.FC<{
    close: () => void,
    success: () => void
}> = (props) => {
    return <Modal
        width={600}
        title="添加规则"
        visible={true}
        onOk={() => {
            props.close();
        }}
        onCancel={() => {
            props.success()
        }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Form>

            <Row gutter={8}>
                <Col span={9}><Form.Select label='访问源' field='type' style={{ width: '100%' }} optionList={[
                    {
                        label: '全部',
                        value: '*'
                    }, {
                        label: 'IP地址',
                        value: 'group',
                    }, {
                        label: '主机名',
                        value: 'group',
                    },
                    {
                        label: 'autogroup:self',
                        value: 'autogroup:self',
                    }, {
                        label: '用户',
                        value: 'user',
                    }, {
                        label: '用户组',
                        value: 'group',
                    }, {
                        label: '标签',
                        value: 'tag'
                    }
                ]}></Form.Select>
                </Col>
                <Col span={9}><Form.Input field='src'></Form.Input></Col>
                <Col span={6}></Col>
            </Row>

                autogroup
            <Row gutter={8}>
                <Col span={9}><Form.Select label='访问目标' field='resources' style={{ width: '100%' }} optionList={[
                    {
                        label: '全部',
                        value: '*'
                    }, {
                        label: '用户',
                        value: 'user',
                    }, {
                        label: '用户组',
                        value: 'group',
                    }, {
                        label: 'IP地址',
                        value: 'group',
                    }, {
                        label: '主机名',
                        value: 'group',
                    },
                    {
                        label: 'autogroup:self',
                        value: 'autogroup:self',
                    }, {
                        label: '标签',
                        value: 'tag'
                    }
                ]}></Form.Select>
                </Col>
                <Col span={9}></Col>
            </Row>
            <Row gutter={8}>
                <Col span={9}><Form.Select label='端口' field='resources' style={{ width: '100%' }} optionList={[
                    {
                        label: '全部',
                        value: '*'
                    }, {
                        label: '自定义',
                        value: 'group',
                    }
                ]}></Form.Select>
                </Col>
                <Col span={9}></Col>

            </Row>


        </Form>
    </Modal>
}

export default Index;
