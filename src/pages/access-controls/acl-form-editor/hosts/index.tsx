import React, { useEffect, useState } from 'react'
import { Typo<PERSON>, Spin, Space, Row, Col } from '@douyinfe/semi-ui';
import { ArrayField, Form, Button } from '@douyinfe/semi-ui';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Title, Paragraph } = Typography;
import styles from '../index.module.scss'
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

const Index: React.FC<{
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    localVersion:number
}> = (props) => {

    const [formApi, setFormApi] = useState<FormApi>()

    
    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {
        let hosts: { ip: string, host: string }[] = [];
        Object.keys(policy.hosts).forEach(key => {
            hosts.push({ host: key, ip: policy.hosts[key] })
        })
        return { hosts };
    }
   
    // 初始值
    const initValues = calInitValues(props.aclPolicy); 
    

    // 当本地版本号变化时，重新计算初始值
    useEffect(() => {
        if(props.localVersion > 0) {
            formApi?.setValues(initValues, { isOverride: true });
        } 
    }, [props.localVersion])

    const handleChange = (values: { hosts: { ip: string, host: string }[] }) => {

        const aclPolicy = props.aclPolicy;
        if (!values || !values.hosts) {
            return;
        }
        const hosts: { [key: string]: string } = {};

        if (values.hosts) {
            values.hosts.forEach((item) => {

                hosts[item.host ? item.host: ''] = item.ip;
            })
        }
        const policy: any = {
            ...aclPolicy,
            hosts,
        }
        props.onChange(policy);
    };

    return <><div className={styles.settingItem}>
        <Title heading={4}>资源组</Title>
        {initValues ? <Form getFormApi={setFormApi} onValueChange={handleChange} initValues={initValues} labelPosition='left' labelWidth='100px' allowEmpty>
            <ArrayField field='hosts' >
                {({ add, arrayFields, addWithInitValue }) => (
                    <React.Fragment>
                        <Button onClick={add} icon={<IconPlusCircle />} className='mb20'>添加IP别名</Button>

                        <Row className={styles.tableTitle} >
                            
                            <Col span={4}>别名</Col>
                            <Col span={18}>IP或IP段或别名</Col>
                            <Col span={2}></Col>
                        </Row>
                        {
                            arrayFields.map(({ field, key, remove }, i) => (
                                <Row className={styles.tableBody} key={key} >
                                    <Col xs={24} sm={4}>
                                        <Form.Input
                                            field={`${field}[host]`}
                                            noLabel
                                            placeholder={'请输入别名，如：myhost'}
                                        />
                                    </Col>
                                    <Col xs={24} sm={18}>
                                        <Form.TextArea
                                            field={`${field}[ip]`}
                                            noLabel
                                            placeholder='请输入IP或IP段，如：**********或**********/24或**********-************或**********-100或myhost2,多个以逗号分隔'
                                        />
                                    </Col>
                                    <Col xs={24} sm={2}>
                                        <Button
                                            type='danger'
                                            theme='borderless'
                                            icon={<IconMinusCircle />}
                                            onClick={remove}
                                            style={{ margin: 12 }}
                                        />
                                    </Col>
                                </Row>
                            ))
                        }
                    </React.Fragment>
                )}
            </ArrayField>
            {/* <div className={styles.submitDiv}><Space>
                    <Button loading={aclPolicySaveLoading} type="primary" htmlType="submit" className="btn-margin-right" theme='solid'>提交</Button>
                    <Button htmlType="reset">重置</Button></Space></div> */}
        </Form> : ''}
    </div></>
}

export default Index