import React, {useState, useEffect} from 'react'
import { Typo<PERSON>, Spin, Space, Row, Col, Divider } from '@douyinfe/semi-ui';
import { ArrayField, Form, Button } from '@douyinfe/semi-ui';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Title, Paragraph } = Typography;
import styles from '../index.module.scss'
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { ListValue, Value } from '@bufbuild/protobuf';
// import {Value as GoogleValue} from '@bufbuild/protobuf/dist/types/google/protobuf/struct_pb';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

const Index: React.FC<{
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    localVersion:number
}> = (props) => {

    const [formApi, setFormApi] = useState<FormApi>()
    const aclPolicy = props.aclPolicy;

    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {

        let routes: { name: string, values: string[] }[] = [];
        let exitnode: string[] = []
        if (policy && policy.autoapprovers && policy.autoapprovers.routes) {
            Object.keys(policy.autoapprovers.routes).forEach(key => {
                if (policy && policy.autoapprovers && policy.autoapprovers.routes) {
                    routes.push({
                        name: key, values: policy.autoapprovers.routes[key].values.map((item) => {
                            if(item.kind && item.kind.value) {
                                return item.kind.value + ''
                            } else {
                                return ''
                            }
                        })
                    })
                }
            })
        }
        if (policy && policy.autoapprovers && policy.autoapprovers.exitnode) {
            exitnode = policy.autoapprovers.exitnode;
        }
        
        return { routes: routes, exitnode: exitnode }   
        
    }

    // 初始值
    const initValues = calInitValues(aclPolicy); 

    // 当本地版本号变化时，重新计算初始值
    useEffect(() => {
        if(props.localVersion > 0) {
            formApi?.setValues(calInitValues(props.aclPolicy), { isOverride: true });
        } 
    }, [props.localVersion])



    const handleChange = (values: {
        routes?:
        { name: string, values: string[] }[],
        exitnode?: string[]
    }) => {
        if (!values || !values.routes) {
            return;
        }

        let routes: { [key: string]: ListValue } = {};
        if (values.routes) {

            values.routes.forEach((item) => {
                if(item.name) {
                    if(item.values) {

                        routes[item.name] = new ListValue({
                            values:
                                item.values.map((item) => {
                                    let val = new Value({
                                        kind: { value: item, case: "stringValue" }
                                    });
                                    return val
                                })
                        });
                    } else {
                        routes[item.name] = new ListValue({
                            values:
                                []
                        });
                    }
                }
                
            })
        }
        let exitnode: string[] = [];
        if (values.exitnode) {
            exitnode = values.exitnode;
        }

        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            autoapprovers: {
                ...aclPolicy.autoapprovers,
                routes: routes,
                exitnode: exitnode
            }
        })
        props.onChange(policy);
    };

    return <><div  className={styles.settingItem}>
        <Title heading={4}>自动审批</Title>
        <Paragraph type='tertiary' className='mb40'>定义无需管理控制台批准即可执行某些操作的用户列表，其中包括将一组指定的路由宣告为子网路由或出口节点</Paragraph>
        { initValues ? <Form getFormApi={setFormApi} onValueChange={handleChange} initValues={initValues} labelPosition='left' labelWidth='100px' allowEmpty>
            <ArrayField field='routes' >
                {({ add, arrayFields, addWithInitValue }) => (
                    <React.Fragment>
                        <Button onClick={add} icon={<IconPlusCircle />} className='mb20'>添加路由</Button>
                        <Row className={styles.tableTitle} >
                            <Col span={4}>名称</Col>
                            <Col span={18}>值</Col>
                            <Col span={2}></Col>
                        </Row>
                        {
                            arrayFields.map(({ field, key, remove }, i) => (
                                <Row className={styles.tableBody} key={key} >
                                    <Col xs={24} sm={4}>
                                        <Form.Input
                                            field={`${field}[name]`}
                                            noLabel
                                        >
                                        </Form.Input>
                                    </Col>
                                    <Col xs={24} sm={18}>
                                        <Form.TagInput
                                            separator={'                        '}
                                            field={`${field}[values]`}
                                            noLabel
                                            addOnBlur
                                        ></Form.TagInput>
                                    </Col>
                                    <Col xs={24} sm={2}>


                                        <Button
                                            type='danger'
                                            theme='borderless'
                                            icon={<IconMinusCircle />}
                                            onClick={remove}
                                            style={{ margin: 12 }}
                                        />
                                    </Col>

                                </Row>
                            ))
                        }
                    </React.Fragment>
                )}
            </ArrayField>
            <Divider />
            <div>
                <Form.TagInput
                    separator={'                        '}
                    field={`exitnode`}
                    label={'出口节点'}
                    addOnBlur
                    style={{ width: 200, marginRight: 16 }}
                ></Form.TagInput>
            </div>
            {/* <div className={styles.submitDiv}><Space><Button loading={aclPolicySaveLoading} type="primary" htmlType="submit" theme='solid' className="btn-margin-right">提交</Button>
                    <Button htmlType="reset">重置</Button></Space></div> */}
        </Form> : ''}
    </div></>
}

export default Index