.sider {
    padding-top: 20px;
    width: 200px;
    border-left: 1px solid var(--semi-color-border);
    flex-shrink: 0;
}

.content {
    padding: 0 32px;
    height: var(--fyy-editor-height);
    overflow-y: auto;
    overflow-x: auto;
}

// 锚点样式
.anch {
    display: block;
    height: 16px;
}



// 编辑器样式
.submitDiv {
    min-width: 800px;
    padding-top: 20px;
    border-top: 1px solid var(--semi-color-border);
}
.settingItem {
    margin-bottom: 40px;
}
.tableTitle {
    min-width: 800px;
    // background-color: var(--semi-color-fill-1);
    
    border-bottom: 1px solid var(--semi-color-border);
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;
    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}
.tableBody {
    min-width: 800px;
    >div {
        padding-right: 8px;
    }
}


// 模式切换
// .modeSwitch {
//     display: flex;
//     align-items: center;
//     .switch {
//         margin-left: 8px;
//     }
//     .label {
//         margin-left: 8px;
//     }
// }
