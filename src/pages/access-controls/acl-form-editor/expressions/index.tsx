import React, { useState, useEffect } from 'react'
import { Typo<PERSON>, Row, Col } from '@douyinfe/semi-ui';
import { ArrayField, Form, Button } from '@douyinfe/semi-ui';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Title, Paragraph } = Typography;
import styles from '../index.module.scss'
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { ListValue, Value } from '@bufbuild/protobuf';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

const Index: React.FC<{
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    localVersion: number
}> = (props) => {

    const [formApi, setFormApi] = useState<FormApi>()
    const aclPolicy = props.aclPolicy;

    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {

        let expressions: { name: string, values: string }[] = [];
        Object.keys(policy.expressions).forEach(key => {
            const values = policy.expressions[key].values;
            expressions.push({
                name: key,
                values: values.length > 0 ? values[0].kind.value + '' : ''
            })
            // expressions.push({
            //     name: key, values: policy.expressions[key].values.map((item) => {
            //         return item.kind.value + ''
            //     })
            // })
        })
        return { expressions: expressions };
    }


    // 初始值
    const initValues = calInitValues(aclPolicy);

    // 当本地版本号变化时，重新计算初始值
    useEffect(() => {
        if (props.localVersion > 0) {
            formApi?.setValues(calInitValues(props.aclPolicy), { isOverride: true });
        }
    }, [props.localVersion])

    const handleChange = (values: {
        expressions?:
        { name: string, values: string }[]
    }) => {


        if (!values || !values.expressions) {
            return;
        }
        let expressions: { [key: string]: ListValue } = {};
        if (values.expressions) {

            values.expressions.forEach((item) => {
                if (item.name && item.name.length > 4 && item.name.indexOf('exp:') > -1) {
                    if (item.values) {
                        expressions[item.name] = new ListValue({
                            values:
                                [new Value({
                                    kind: { value: item.values, case: 'stringValue' }
                                })]
                        });
                    } else {
                        expressions[item.name] = new ListValue({
                            values:
                                []
                        });
                    }
                }
            })
        }
        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            expressions,
        })
        props.onChange(policy);
    };

    return <><div className={styles.settingItem}>
        <Title heading={4} >表达式</Title>
        <Paragraph type='tertiary' className='mb40'>
            表达式是一组由逻辑运算符连接的条件，用于描述请求上下文中的属性是否包含指定的值。表达式的值可以是字符串、数字、布尔值、列表或者字典。
        </Paragraph>
        {initValues ? <Form getFormApi={setFormApi} onValueChange={handleChange} initValues={initValues} labelPosition='left' labelWidth='100px' allowEmpty>
            <ArrayField field='expressions' >
                {({ add, arrayFields, addWithInitValue }) => (
                    <React.Fragment>
                        <Button onClick={add} icon={<IconPlusCircle />} className='mb20'>添加表达式</Button>
                        <Row className={styles.tableTitle} >
                            <Col span={4}>名称</Col>
                            <Col span={18}>值</Col>
                            <Col span={2}></Col>
                        </Row>
                        {
                            arrayFields.map(({ field, key, remove }, i) => (
                                <Row className={styles.tableBody} key={key} >
                                    <Col xs={24} sm={4}>
                                        <Form.Input
                                            field={`${field}[name]`}
                                            noLabel
                                            validate={value => {
                                                if (!value) {
                                                    return '名称不能为空';
                                                }
                                                if (value.indexOf('exp:') < 0) {
                                                    return '名称必须以exp:开头'
                                                }
                                                return '';
                                            }}
                                        >
                                        </Form.Input>
                                    </Col>
                                    <Col xs={24} sm={18}>
                                        <Form.Input

                                            field={`${field}[values]`}
                                            noLabel
                                        ></Form.Input>
                                    </Col>
                                    <Col xs={24} sm={2}>


                                        <Button
                                            type='danger'
                                            theme='borderless'
                                            icon={<IconMinusCircle />}
                                            onClick={remove}
                                            style={{ margin: 12 }}
                                        />
                                    </Col>

                                </Row>
                            ))
                        }
                    </React.Fragment>
                )}
            </ArrayField>
            {/* <div className={styles.submitDiv}><Space><Button loading={aclPolicySaveLoading} type="primary" htmlType="submit" theme='solid' className="btn-margin-right">提交</Button>
                <Button htmlType="reset">重置</Button></Space></div> */}
        </Form> : ''}
    </div></>
}

export default Index