import React, {useState, useEffect} from 'react'
import { Typography, Spin, Space, Row, Col } from '@douyinfe/semi-ui';
import { ArrayField, Form, Button } from '@douyinfe/semi-ui';
import { FormState } from '@douyinfe/semi-ui/lib/es/form';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Title, Paragraph } = Typography;
import styles from '../index.module.scss'
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { ACLPolicy, SSHRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

const Index: React.FC<{
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    localVersion:number
}> = (props) => {

    const [formApi, setFormApi] = useState<FormApi>()
    const aclPolicy = props.aclPolicy;
    
    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {
        let ssh: { action: string, src: string[], dst: string[], users: string[] }[] = [];
        policy.ssh.forEach((val) => {
            ssh.push({
                action: val.action,
                src: val.src,
                dst: val.dst,
                users: val.users

            })
        })
        return { ssh };
    }
    
    // 初始值
    const initValues = calInitValues(aclPolicy); 
    

    // 当本地版本号变化时，重新计算初始值
    useEffect(() => {
        if(props.localVersion > 0) {
            formApi?.setValues(calInitValues(props.aclPolicy), { isOverride: true });
        } 
    }, [props.localVersion])

    const hangleChagne = (values:  {
        ssh?:
        { action: string, src: string[], dst: string[], users: string[] }[]
    }) => {
        if (!values || !values.ssh) {
            return;
        }

        let ssh: SSHRule[] = [];
        if(values.ssh)
        {
            values.ssh.forEach((item) => {
                ssh.push(new SSHRule({
                    action: item.action,
                    src: item.src,
                    dst: item.dst,
                    users: item.users
                }))
            })    
        }
        
        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            ssh,
        })
        props.onChange(policy);
    };

    return <><div  className={styles.settingItem} style={{minHeight:450}}>
        <Title heading={4} >SSH</Title>
        <Paragraph type='tertiary' className='mb40'>定义可以使用SSH的用户和设备的列表</Paragraph>
        {initValues ? <Form getFormApi={setFormApi} onValueChange={hangleChagne} initValues={initValues} labelPosition='left' labelWidth='100px' allowEmpty>
                <ArrayField field='ssh' >
                    {({ add, arrayFields, addWithInitValue }) => (
                        <React.Fragment>
                            <Button onClick={add} icon={<IconPlusCircle />} className='mb20'>添加SSH</Button>

                            <Row className={styles.tableTitle} >
                                <Col span={5}>操作</Col>
                                <Col span={6}>源</Col>
                                <Col span={6}>目标</Col>
                                <Col span={5}>用户</Col>
                                <Col span={2}></Col>
                            </Row>
                            {
                                arrayFields.map(({ field, key, remove }, i) => (
                                    <Row className={styles.tableBody} key={key} >
                                        <Col xs={24} sm={5}>
                                            <Form.Input
                                                field={`${field}[action]`}
                                                noLabel
                                            >
                                            </Form.Input>
                                        </Col>
                                        <Col xs={24} sm={6}>
                                            <Form.TagInput
                                                separator={'                        '}
                                                field={`${field}[src]`}
                                                noLabel
                                                addOnBlur
                                            ></Form.TagInput>
                                        </Col>
                                        <Col xs={24} sm={6}>
                                            <Form.TagInput
                                                separator={'                        '}
                                                field={`${field}[dst]`}
                                                noLabel
                                                addOnBlur
                                            ></Form.TagInput>
                                        </Col>
                                        <Col xs={24} sm={5}>
                                            <Form.TagInput
                                                separator={'                        '}
                                                field={`${field}[users]`}
                                                noLabel
                                                addOnBlur
                                            ></Form.TagInput>

                                        </Col>
                                        <Col xs={24} sm={2}>

                                            <Button
                                                type='danger'
                                                theme='borderless'
                                                icon={<IconMinusCircle />}
                                                onClick={remove}
                                                style={{ margin: 12 }}
                                            />
                                        </Col>

                                    </Row>
                                ))
                            }
                        </React.Fragment>
                    )}
                </ArrayField>
                {/* <div className={styles.submitDiv}><Space><Button loading={aclPolicySaveLoading} type="primary" htmlType="submit" theme='solid' className="btn-margin-right">提交</Button>
                    <Button htmlType="reset">重置</Button></Space></div> */}
            </Form> : ''}
    </div></>
}

export default Index