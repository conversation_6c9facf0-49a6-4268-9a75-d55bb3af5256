import React, { useState, useEffect } from 'react'
import { Typography, Spin, Space, Row, Col } from '@douyinfe/semi-ui';
import { ArrayField, Form, Button } from '@douyinfe/semi-ui';
import { FormState } from '@douyinfe/semi-ui/lib/es/form';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
const { Title, Paragraph } = Typography;
import styles from '../index.module.scss'
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { ListValue, Value } from '@bufbuild/protobuf';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';


const Index: React.FC<{
    aclPolicy: ACLPolicy,
    onChange: (policy: ACLPolicy) => void,
    localVersion: number
}> = (props) => {

    const [formApi, setFormApi] = useState<FormApi>()
    const aclPolicy = props.aclPolicy;

    // 计算初始值
    const calInitValues = (policy: ACLPolicy) => {

        let tagowners: { name: string, values: string[] }[] = [];
        Object.keys(policy.tagowners).forEach(key => {
            tagowners.push({
                name: key, values: policy.tagowners[key].values.map((item) => {
                    return item.kind.value + ''
                })
            })
        })
        return { tagowners };
    }

    // 初始值
    const initValues = calInitValues(aclPolicy);


    // 当本地版本号变化时，重新计算初始值
    useEffect(() => {
        if (props.localVersion > 0) {
            formApi?.setValues(calInitValues(props.aclPolicy), { isOverride: true });
        }
    }, [props.localVersion])

    const handleChange = (values: {
        tagowners?:
        { name: string, values: string[] }[]
    }) => {

        if (!values || !values.tagowners) {
            return;
        }
        let tagowners: { [key: string]: ListValue } = {};
        if (values.tagowners) {
            values.tagowners.forEach((item) => {
                
                if(item.name && item.name.length > 4 && item.name.indexOf('tag:') > -1) {
                    if (item.values) {

                        tagowners[item.name] = new ListValue({
                            values:
                                item.values.map((item) => {
                                    let val = new Value({
                                        kind: { value: item, case: "stringValue" }
                                    });
                                    return val
                                })
                        });
                    } else {
                        tagowners[item.name] = new ListValue();
                    }
                }
                
            })
        }

        const policy: ACLPolicy = new ACLPolicy({
            ...aclPolicy,
            tagowners,
        })
        props.onChange(policy);
    };

    return <><div className={styles.settingItem}>
        <Title heading={4} >标签</Title>
        <Paragraph type='tertiary' className='mb40'>定义可以应用于设备的标签，以及允许分配每个标签的用户列表</Paragraph>
        {initValues ? <Form getFormApi={setFormApi} onValueChange={handleChange} initValues={initValues} labelPosition='left' labelWidth='100px' allowEmpty>
            <ArrayField field='tagowners' >
                {({ add, arrayFields, addWithInitValue }) => (
                    <React.Fragment>
                        <Button onClick={add} icon={<IconPlusCircle />} className='mb20'>添加标签</Button>

                        <Row className={styles.tableTitle} >
                            <Col span={4}>名称</Col>
                            <Col span={18}>值</Col>
                            <Col span={2}></Col>
                        </Row>
                        {
                            arrayFields.map(({ field, key, remove }, i) => (
                                <Row className={styles.tableBody} key={key} >
                                    <Col xs={24} sm={4}>
                                        <Form.Input
                                            field={`${field}[name]`}
                                            noLabel
                                            validate={value => {
                                                if (!value) {
                                                    return '名称不能为空';
                                                }
                                                if(value.indexOf('tag:') < 0) {
                                                    return '名称必须以tag:开头'
                                                }
                                                return '';
                                            }}
                                        >
                                        </Form.Input>
                                    </Col>
                                    <Col xs={24} sm={18}>
                                        <Form.TagInput
                                            separator={'                        '}
                                            field={`${field}[values]`}
                                            noLabel
                                            addOnBlur
                                        ></Form.TagInput>

                                    </Col>
                                    <Col xs={24} sm={2}>

                                        <Button
                                            type='danger'
                                            theme='borderless'
                                            icon={<IconMinusCircle />}
                                            onClick={remove}
                                            style={{ margin: 12 }}
                                        />
                                    </Col>

                                </Row>
                            ))
                        }
                    </React.Fragment>
                )}
            </ArrayField>
            {/* <div className={styles.submitDiv}><Space>
                    <Button loading={aclPolicySaveLoading} type="primary" htmlType="submit" className="btn-margin-right" theme='solid'>提交</Button>
                    <Button htmlType="reset">重置</Button></Space></div> */}
        </Form> : ''}
    </div></>
}

export default Index