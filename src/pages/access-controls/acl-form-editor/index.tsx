import React, { useState, useRef, useEffect } from 'react'
import { Layout, Anchor, Divider } from '@douyinfe/semi-ui';

import Acls from './acls';
import Hosts from './hosts';
import Groups from './groups';
import Tagowners from './tagowners';
import Autoapprovers from './autoapprovers';
import Expressions from './expressions';
import Ssh from './ssh';
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';


import styles from './index.module.scss'

const { Sider, Content } = Layout;

interface Props {
  aclPolicy: ACLPolicy,
  onChange: (policy: ACLPolicy) => void,
  localVersion:number
}

const Index: React.FC<Props> = (props) => {
  const {
    aclPolicy
  } = props


  const onChange = (policy: ACLPolicy) => {
    props.onChange(policy)
  }


  return <>
        <Layout className={styles.layout}>
          <Content className={styles.content}>
            {aclPolicy ? <>
              <a className={styles.anch} id="acl"></a>
              <Acls aclPolicy={aclPolicy} onChange={onChange} localVersion={props.localVersion} />
              <Divider/>
              <a className={styles.anch} id="hosts"></a>
              <Hosts aclPolicy={aclPolicy} onChange={onChange} localVersion={props.localVersion} />
              <Divider/>
              <a className={styles.anch} id="groups"></a>
              <Groups aclPolicy={aclPolicy} onChange={onChange} localVersion={props.localVersion} />
              <Divider/>
              <a className={styles.anch} id="expressions"></a>
              <Expressions aclPolicy={aclPolicy} onChange={onChange} localVersion={props.localVersion} />
              <Divider/>
              <a className={styles.anch} id="tagowners"></a>
              <Tagowners aclPolicy={aclPolicy} onChange={onChange} localVersion={props.localVersion} />
              <Divider/>
              <a className={styles.anch} id="autoapprovers"></a>
              <Autoapprovers aclPolicy={aclPolicy} onChange={onChange} localVersion={props.localVersion} />
              <Divider/>
              {/* <a className={styles.anch} id="ssh"></a>
              <Ssh aclPolicy={aclPolicy} onChange={onChange}  localVersion={props.localVersion}/> */}
            </> : ''}

          </Content>
          <Sider className={styles.sider}
          >
            <Anchor offsetTop={60}
            
            getContainer={() => {
              let ele = document.querySelector('#acl');
              if(ele && ele.parentElement) {
                return ele.parentElement;
              }
              return window;
          }}
            defaultAnchor='acl'
              className='right-anchor'>
              <Anchor.Link title="规则列表" href="#acl"></Anchor.Link>
              <Anchor.Link title="资源组" href="#hosts"></Anchor.Link>
              <Anchor.Link title="用户组" href="#groups"></Anchor.Link>
              <Anchor.Link title="表达式" href="#expressions"></Anchor.Link>
              <Anchor.Link title="标签" href="#tagowners"></Anchor.Link>
              <Anchor.Link title="自动审批" href="#autoapprovers"></Anchor.Link>
              {/* <Anchor.Link title="SSH" href="#ssh"></Anchor.Link> */}
            </Anchor>

          </Sider>
        </Layout>
</>
}

export default Index;