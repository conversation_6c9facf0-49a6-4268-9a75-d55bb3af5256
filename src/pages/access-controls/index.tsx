import React, { Suspense, useState, useRef, useCallback } from 'react';
import { Outlet, useLocation, useNavigate } from "react-router-dom";

import { Tabs, TabPane, Typography, Switch, Row, Col, Button, Select, Space, Input, Layout, Spin, Banner } from '@douyinfe/semi-ui';
import { IconEdit, IconEyeOpened } from '@douyinfe/semi-icons';

import AclEditor from './acl-editor';
import AclFormEditor from './acl-form-editor';
import AclJsonEditor from './acl-json-editor';
import CodeDiff from '@/components/code-diff';
import AclRulePreview from './acl-rule-preview';
import { BASE_PATH } from '@/constants/router';
import { ACLPolicy, ACL, SSHRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import useACLPolicy from './useACLPolicy';
const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss'
const Index: React.FC = () => {

    const navigate = useNavigate();
    const location = useLocation();
    let pathArr = location.pathname.split('/');


    const { localAcl, setLocalAcl, updateLocalAcl, computePolicyString, remoteAcl, setRemoteAcl,
        aclPolicyLoading, saveACLPolicy, aclPolicy, setACLPolicy, aclPolicySaveLoading, jsonEditorError, initAclPolicy } = useACLPolicy();

    const [hasUserActivity, setHasUserActivity] = useState(false)
    // 本地编辑版本号
    const [localVersion, setLocalVersion] = useState(0)

    // const [isPosting, setPosting] = useState(false)
    // const [locked, setLocked] = useState(false)

    // 表单编辑器里编辑策略回调
    const handleACLPolicyChange = (aclPolicy: ACLPolicy) => {
        setACLPolicy(aclPolicy);
        const policyString = computePolicyString(aclPolicy);
        setLocalAcl(policyString);
        setHasUserActivity(true)
    }


    const onLocalAclUpdate = (value: string) => {
        updateLocalAcl(value)
        setHasUserActivity(true)
    }
    // const save = (val: { localAcl: string, localRevision: string }) => {

    // }

    // 文本编辑模式
    const [editMode, setEditMode] = useState(true);

    const [activeTab, setActiveTab] = useState('edit');


    // 重置访问控制
    const handleACLPolicyReset = () => {
        setACLPolicy(initAclPolicy);
        if (initAclPolicy) {
            const policyString = computePolicyString(initAclPolicy);
            setLocalAcl(policyString)
            setRemoteAcl(policyString)
            
        }
        setHasUserActivity(false)
        setLocalVersion(localVersion + 1)
    }

    // 保存访问控制
    const handleACLPolicySave = () => {
        if (aclPolicy) saveACLPolicy(aclPolicy);
        setHasUserActivity(false)
        setLocalVersion(localVersion + 1)
    }


    return <>
        <div className='general-page'>
            <Row className='mb10'>
                <Col span={20}>
                    <Title heading={3}>策略</Title>
                </Col>

                <Col span={4}><div className='btn-right-col'>
                    <Button theme='solid' onClick={() => navigate(`${BASE_PATH}/policies`)}>普通模式</Button>

                </div></Col>
            </Row>


            <Paragraph type='tertiary' className='mb40'>定义允许设备和用户在网络中进行连接的策略</Paragraph>
            {aclPolicyLoading ? <div style={{ textAlign: 'center', paddingTop: 100 }}><Spin /></div> :
                <>
                    <Tabs
                        type="card"
                        className={styles.tabs}
                        tabBarClassName={styles.tabBar}
                        tabBarExtraContent={
                            <div style={{
                                alignItems: 'center',
                                height: 38,
                                display: activeTab === 'edit' ? 'flex' : 'none'
                            }}>
                                <Switch size='small'
                                    checked={!editMode}
                                    onChange={(checked) => {
                                        setEditMode(!checked)
                                    }}
                                />
                                <Text type='tertiary'>&nbsp;JSON编辑</Text>
                            </div>
                        }
                        onChange={(activeKey) => { setActiveTab(activeKey) }} contentStyle={{
                            paddingTop: 0
                        }}>
                        <TabPane className={styles.tabPane} tab={
                            <span><IconEdit />配置编辑</span>
                        } itemKey="edit">
                            {aclPolicy ? <>
                                {editMode ? <AclFormEditor
                                    localVersion={localVersion}
                                    onChange={handleACLPolicyChange}
                                    aclPolicy={aclPolicy}
                                /> : <AclJsonEditor
                                    onChange={onLocalAclUpdate}
                                    
                                    value={localAcl}
                                    
                                    mode={'edit'}

                                // style={{ display: editMode ? 'none' : 'block' }}
                                ></AclJsonEditor>}


                            </> : ''}

                        </TabPane>
                        <TabPane className='content-diff' tab={
                            <span>
                                <IconEyeOpened />
                                修改预览
                            </span>
                        } itemKey="diff">
                            {activeTab === 'diff' ? <CodeDiff height='460px' original={remoteAcl} modified={localAcl} /> : ''}
                        </TabPane>
                        <TabPane className={styles.tabPane} tab={
                            <span>
                                <IconEyeOpened />
                                规则预览
                            </span>
                        } itemKey="preview">
                            {aclPolicy ? <AclRulePreview aclPolicy={aclPolicy} /> : null}
                        </TabPane>
                    </Tabs>
                    {jsonEditorError ?
                        <Banner type='danger' className='mb20' closeIcon={null} bordered title='JSON编辑器格式错误' description={jsonEditorError.message}
                            fullMode={false}>
                        </Banner> : ''}
                    <Space>
                        <Button disabled={!hasUserActivity} type="primary" theme='solid' size='large' onClick={handleACLPolicySave} loading={aclPolicySaveLoading}>保存</Button>
                        <Button disabled={!hasUserActivity} size='large' onClick={() => handleACLPolicyReset()}>放弃更改</Button>
                    </Space>
                </>
            }
        </div>
    </>
}

export default Index;