.layout {
    min-height: calc(100vh - 60px);
}

// .sider {
//     min-height: calc(100vh - 60px);
//     width: 241px;
//     border-right: 1px solid var(--semi-color-border);

//     >div {
//         width: 241px;
//     }
// }

.content {}

.nav {
    border: none;
    position: fixed;
}

.tabPane {}

// 标签页样式
.tabs {
    margin-bottom: 20px;
    padding: 0;

    >div:last-of-type {
        padding: 0;
        border-left: 1px solid var(--semi-color-border);
        border-right: 1px solid var(--semi-color-border);
        border-bottom: 1px solid var(--semi-color-border);
        border-bottom-left-radius: var(--semi-border-radius-small);
        border-bottom-right-radius: var(--semi-border-radius-small);
    }
}
.tabBar {
    >div {

        border-bottom-width: 2px!important;
    }
}
