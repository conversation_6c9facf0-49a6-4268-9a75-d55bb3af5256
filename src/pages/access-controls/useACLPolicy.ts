import { useState, useEffect, useCallback, useContext } from 'react'
import { Notification } from '@douyinfe/semi-ui';
import { ListValue, Value } from '@bufbuild/protobuf';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ACLPolicy, ACL, SSHRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { flylayerClient } from '@/services/core';
import JSON5 from 'json5'

// 访问控制hooks
const useACLPolicy = () => {

    const flynet = useContext(FlynetGeneralContext);

    // 访问控制策略是否在加载中
    const [aclPolicyLoading, setACLPolicyLoading] = useState(false);

    // 访问控制策略
    const [aclPolicy, setACLPolicy] = useState<ACLPolicy>();

    // 访问控制策略
    const [initAclPolicy, setInitACLPolicy] = useState<ACLPolicy>();

    // 本地访问控制策略
    const [localAcl, setLocalAcl] = useState('');
    // 远程访问控制策略
    const [remoteAcl, setRemoteAcl] = useState('');

    // json编辑器错误
    const [jsonEditorError, setJsonEditorError] = useState<SyntaxError>();

    const parseJson = (aclStr: string): { obj?: any, error?: SyntaxError } => {

        const result: { obj?: any, error?: SyntaxError } = {
            obj: undefined,
            error: undefined
        }
        try {
            const obj = JSON5.parse(aclStr);
            if (obj) {
                result.obj = obj;
            }
        } catch (e) {
            if (e instanceof SyntaxError) {
                result.error = e;
            }

        }
        return result;
    }
    // 把json对象转换为aclPolicy
    const parseAclPolicyFromJson = (obj: any, p: ACLPolicy) => {

        const result: { obj?: ACLPolicy, errorMsg?: string } = {
            obj: undefined,
            errorMsg: undefined
        }
        const groups: { [key: string]: ListValue } = {};
        const expressions: { [key: string]: ListValue } = {};

        const hosts: { [key: string]: string } = {};
        const acls: ACL[] = [];
        const ssh: SSHRule[] = [];
        const tagowners: { [key: string]: ListValue } = {};

        const routes: { [key: string]: ListValue } = {};
        const exitnode: string[] = [];

        // 验证groups节点
        if (obj.groups) {
            // 遍历groups
            Object.keys(obj.groups).forEach(key => {
                let values = obj.groups[key]
                if (values instanceof Array) {
                    values.forEach(val => {
                        if (typeof val !== 'string') {
                            result.errorMsg = `groups.${key}的值必须是字符串数组`;
                            return result;
                        }
                    });
                    groups[key] = new ListValue({
                        values:
                            values.map((item) => {
                                let val = new Value({
                                    kind: { value: item, case: "stringValue" }
                                });
                                return val
                            })
                    });

                } else {
                    result.errorMsg = `groups.${key}的值必须是字符串数组`;
                    return result;
                }
            });
        }
        // 验证expressions节点
        if (obj.expressions) {
            // 遍历expressions
            Object.keys(obj.expressions).forEach(key => {
                let values = obj.expressions[key]
                if (values instanceof Array) {
                    values.forEach(val => {
                        if (typeof val !== 'string') {
                            result.errorMsg = `expressions.${key}的值必须是字符串数组`;
                            return result;
                        }
                    });
                    expressions[key] = new ListValue({
                        values:

                            values.map((item) => {
                                let val = new Value({
                                    kind: { value: item, case: "stringValue" }
                                });
                                return val
                            })
                    });

                } else {
                    result.errorMsg = `expressions.${key}的值必须是字符串数组`;
                    return result;
                }
            })
        }

        if (obj.hosts) {
            Object.keys(obj.hosts).forEach(key => {
                let val = obj.hosts[key];
                if (typeof val !== 'string') {
                    result.errorMsg = `hosts.${key}的值必须是字符串`;
                    return result;
                }
                hosts[key] = val;
            });
        }
        if (obj.acls) {
            obj.acls.forEach((item: any, i: number) => {
                if (!item.src || !item.dst || !item.action || !item.name || !item.description || !item.priority) {
                    result.errorMsg = `acls节点第${i}个元素的src、dst、action、name、description必须存在`;
                    return result;
                }
                acls.push(new ACL({
                    src: item.src,
                    dst: item.dst,
                    action: item.action,
                    priority: item.priority
                }));
            });
        }
        if (obj.ssh) {
            obj.ssh.forEach((item: any, i: number) => {
                if (!item.src || !item.dst || !item.action || !item.users) {
                    result.errorMsg = `ssh节点第${i}个元素的src、dst、action、users必须存在`;
                    return result;
                }
                // 验证action节点
                if (typeof item.action !== 'string') {
                    result.errorMsg = `ssh节点第${i}个元素的action必须是字符串`;
                    return result;
                }


                // 验证users节点
                if (!(item.users instanceof Array)) {
                    result.errorMsg = `ssh节点第${i}个元素的users必须是字符串数组`;
                    return result;
                } else {
                    item.users.forEach((user: any, j: number) => {
                        if (typeof user !== 'string') {
                            result.errorMsg = `ssh节点第${i}个元素的users第${j}个元素必须是字符串`;
                            return result;
                        }
                    });
                }


                // 验证src节点
                if (!(item.src instanceof Array)) {
                    result.errorMsg = `ssh节点第${i}个元素的src必须是字符串数组`;
                    return result;
                } else {
                    item.src.forEach((src: any, j: number) => {
                        if (typeof src !== 'string') {
                            result.errorMsg = `ssh节点第${i}个元素的src第${j}个元素必须是字符串`;
                            return result;
                        }
                    });
                }

                // 验证dst节点
                if (!(item.dst instanceof Array)) {
                    result.errorMsg = `ssh节点第${i}个元素的dst必须是字符串数组`;
                    return result;
                } else {
                    item.dst.forEach((dst: any, j: number) => {
                        if (typeof dst !== 'string') {
                            result.errorMsg = `ssh节点第${i}个元素的dst第${j}个元素必须是字符串`;
                            return result;
                        }
                    });
                }


                ssh.push(new SSHRule({
                    action: item.action,
                    src: item.src,
                    dst: item.dst,
                    users: item.users,
                }));
            });

        }
        if (obj.tagowners) {
            Object.keys(obj.tagowners).forEach(key => {
                let values = obj.tagowners[key]
                if (values instanceof Array) {
                    values.forEach(val => {
                        if (typeof val !== 'string') {
                            result.errorMsg = `tagowners.${key}的值必须是字符串数组`;
                            return result;
                        }
                    });
                    tagowners[key] = new ListValue({
                        values:
                            values.map((item) => {
                                let val = new Value({
                                    kind: { value: item, case: "stringValue" }
                                });
                                return val
                            })
                    });

                } else {
                    result.errorMsg = `tagowners.${key}的值必须是字符串数组`;
                    return result;
                }

            });
        }
        if (obj.autoapprovers) {
            if (obj.autoapprovers.routes) {
                Object.keys(obj.autoapprovers.routes).forEach(key => {
                    let values = obj.autoapprovers.routes[key]
                    if (values instanceof Array) {
                        values.forEach(val => {
                            if (typeof val !== 'string') {
                                result.errorMsg = `autoapprovers.routes.${key}的值必须是字符串数组`;
                                return result;
                            }
                        });
                        routes[key] = new ListValue({
                            values:
                                values.map((item) => {
                                    let val = new Value({
                                        kind: { value: item, case: "stringValue" }
                                    });
                                    return val
                                })
                        });

                    } else {
                        result.errorMsg = `autoapprovers.routes.${key}的值必须是字符串数组`;
                        return result;
                    }

                });
            }
            if (obj.autoapprovers.exitnode) {
                obj.autoapprovers.exitnode.forEach((item: any, i: number) => {
                    if (typeof item !== 'string') {
                        result.errorMsg = `autoapprovers.exitnode第${i}个元素必须是字符串`;
                        return result;
                    }
                    exitnode.push(item);
                });
            }
        }

        const policy: ACLPolicy = new ACLPolicy({
            ...p,
            groups: groups,
            hosts: hosts,
            acls: acls,
            ssh: ssh,
            tagowners: tagowners,
            autoapprovers: {
                ...p.autoapprovers,
                routes: routes,
                exitnode: exitnode
            }
        });
        result.obj = policy;
        return result;
    }


    // 本地访问控制策略改变
    const updateLocalAcl = (aclStr: string) => {

        const { obj, error } = parseJson(aclStr);
        if (error) {
            setJsonEditorError(error);
            return;
        }
        if (aclPolicy) {
            const { obj: newPolicy, errorMsg } = parseAclPolicyFromJson(obj, aclPolicy);
            if (errorMsg) {
                setJsonEditorError(new Error(errorMsg));
                return;
            }
            setACLPolicy(newPolicy);
            setJsonEditorError(undefined);
            setLocalAcl(aclStr);

        }
    }

    // 计算访问策略字符串
    const computePolicyString = useCallback((policy: ACLPolicy) => {

        const level1_prefix = '    ';
        const level2_prefix = '        ';
        const level3_prefix = '            ';

        let policyString = '';
        policyString += '{\n';  // 开始token

        // 组
        // 组注释
        policyString += `${level1_prefix}// 声明身份服务之外的静态用户组。\n`;
        // 组开始token
        policyString += `${level1_prefix}"groups": {\n`;
        Object.keys(policy.groups).forEach(key => {
            // key开始token
            policyString += `${level2_prefix}"${key}": [`;

            // values token
            const valuesToken: string[] = []
            policy.groups[key].values.forEach(val => {
                valuesToken.push(`"${val.kind.value}"`);
            })
            policyString += valuesToken.join(', ');

            // key结束token
            policyString += '],\n';

        })
        // 组结束token
        policyString += `${level1_prefix}},\n`;

        // 表达式
        // 表达式注释
        policyString += `${level1_prefix}// 表达式是一组由逻辑运算符连接的条件，用于描述请求上下文中的属性是否包含指定的值。\n`;
        // 表达式开始token
        policyString += `${level1_prefix}"expressions": {\n`;
        Object.keys(policy.expressions).forEach(key => {
            // key开始token
            policyString += `${level2_prefix}"${key}": [`;

            // values token
            const valuesToken: string[] = []
            policy.expressions[key].values.forEach(val => {
                valuesToken.push(`"${val.kind.value}"`);
            })
            policyString += valuesToken.join(', ');

            // key结束token
            policyString += '],\n';
        })
        // 表达式结束token
        policyString += `${level1_prefix}},\n`;

        // hosts
        // hosts注释
        policyString += `${level1_prefix}// 声明主机名别名以代替 IP 地址。\n`;
        // hosts开始token
        policyString += `${level1_prefix}"hosts": {\n`;
        Object.keys(policy.hosts).forEach(key => {
            // host 行
            policyString += `${level2_prefix}"${key}": "${policy.hosts[key]}", \n`;
        })
        // hosts结束token
        policyString += `${level1_prefix}},\n`;

        // acls
        // acls注释
        policyString += `${level1_prefix}// 访问控制列表。\n`;
        // acls开始token
        policyString += `${level1_prefix}"acls": [\n`;
        policy.acls.forEach(acl => {
            // acl开始token
            policyString += `${level2_prefix}{\n`;

            // acl 动作行
            policyString += `${level3_prefix}"action": "${acl.action}",\n`;

            // acl src行
            let srcToken: string[] = [];
            acl.src.forEach(src => {
                srcToken.push(`"${src}"`);
            })
            policyString += `${level3_prefix}"src": [${srcToken.join(',')}],\n`;
            // acl dst行
            let dstToken: string[] = [];
            acl.dst.forEach(dst => {
                dstToken.push(`"${dst}"`);
            })
            policyString += `${level3_prefix}"dst": [${dstToken.join(',')}],\n`;

            // priority
            policyString += `${level3_prefix}"priority": "${acl.priority||1}",\n`;
            // acl行结束token
            policyString += `${level2_prefix}},\n`;
        })
        // acls结束token
        policyString += `${level1_prefix}],\n`;


        // ssh
        // ssh注释
        policyString += `${level1_prefix}// SSH 服务配置。\n`;
        // ssh开始token
        policyString += `${level1_prefix}"ssh": [\n`;
        policy.ssh.forEach(sshItem => {
            // sshItem开始token
            policyString += `${level2_prefix}{\n`;

            // sshItem 动作行
            policyString += `${level3_prefix}"action": "${sshItem.action}",\n`;
            // sshItem src行
            let srcToken: string[] = [];
            sshItem.src.forEach(src => {
                srcToken.push(`"${src}"`);
            })
            policyString += `${level3_prefix}"src": [${srcToken.join(',')}],\n`;
            // sshItem dst行
            let dstToken: string[] = [];
            sshItem.dst.forEach(dst => {
                dstToken.push(`"${dst}"`);
            })
            policyString += `${level3_prefix}"dst": [${dstToken.join(',')}],\n`;

            // sshItem users行
            let usersToken: string[] = [];
            sshItem.users.forEach(user => {
                usersToken.push(`"${user}"`);
            })
            policyString += `${level3_prefix}"users": [${usersToken.join(',')}],\n`;

            // sshItem行结束token
            policyString += `${level2_prefix}},\n`;
        })
        // ssh结束token
        policyString += `${level1_prefix}],\n`;




        // tagowners
        // tagowners注释
        policyString += `${level1_prefix}// 标签所有者。\n`;
        // tagowners开始token
        policyString += `${level1_prefix}"tagowners": {\n`;
        Object.keys(policy.tagowners).forEach(key => {
            // key开始token
            policyString += `${level2_prefix}"${key}": [`;
            // values token
            const valuesToken: string[] = []
            policy.tagowners[key].values.forEach(val => {
                valuesToken.push(`"${val.kind.value}"`);
            })
            policyString += valuesToken.join(', ');
            // key结束token
            policyString += '],\n';
        })
        // tagowners结束token
        policyString += `${level1_prefix}},\n`;

        // autoapprovers
        // autoapprovers注释
        policyString += `${level1_prefix}// 自动批准设备。\n`;
        // autoapprovers开始token
        policyString += `${level1_prefix}"autoapprovers": {\n`;

        //  routes注释
        policyString += `${level2_prefix}// 路由。\n`;
        // routes开始token
        policyString += `${level2_prefix}"routes": {\n`;
        if (policy && policy.autoapprovers && policy.autoapprovers?.routes) {
            Object.keys(policy.autoapprovers.routes).forEach(key => {
                // key开始token
                policyString += `${level3_prefix}"${key}": [`;
                // values token
                const valuesToken: string[] = []
                policy.autoapprovers?.routes[key].values.forEach(val => {
                    valuesToken.push(`"${val.kind.value}"`);
                })
                policyString += valuesToken.join(', ');
                // key结束token
                policyString += '],\n';

            })
        }
        // routes结束token
        policyString += `${level2_prefix}},\n`;

        // autoapprovers exitnode注释
        policyString += `${level2_prefix}// 出口节点。\n`;
        // exitnode开始token
        policyString += `${level2_prefix}"exitnode": [`;
        if (policy && policy.autoapprovers && policy.autoapprovers?.exitnode) {

            // exitnode行
            let exitnodeToken: string[] = [];
            policy.autoapprovers?.exitnode.forEach(exitnode => {
                exitnodeToken.push(`"${exitnode}"`);
            })
            policyString += `${exitnodeToken.join(',')}`;

        }
        // exitnode结束token
        policyString += `],\n`;


        // autoapprovers 结束token
        policyString += `${level1_prefix}},\n`;

        policyString += '}\n';  // 结束token
        return policyString;
    }, [])

    // 获取访问控制策略
    const getACLPolicy = useCallback(() => {
        setACLPolicyLoading(true);
        flylayerClient.getACLPolicy({
            flynetId: flynet.id
        }).then(res => {
            if (res.policy) {
                setACLPolicy(res.policy);
                setInitACLPolicy(res.policy);
                const policyString = computePolicyString(res.policy);
                setLocalAcl(policyString)
                setRemoteAcl(policyString)
            }

        }).catch(err => {
            console.error(err);

            Notification.error({ content: '获取访问控制策略失败, 请稍后重试', position: "bottomRight" })
        }).finally(() => {
            setACLPolicyLoading(false);
        })
    }, [])

    useEffect(() => {
        getACLPolicy();
    }, [])

    // 更新访问控制策略是否正在加载
    const [aclPolicySaveLoading, setACLPolicySaveLoading] = useState(false);

    // 保存访问控制策略
    const saveACLPolicy = (policy: ACLPolicy) => {

        policy.acls.sort((a, b) => {
            
            // 先按照优先级排序，优先级高的排在前面，最后按照拒绝策略排在前面
            if (a.priority > b.priority) {
                return 1;
            } else if (a.priority < b.priority) {
                return -1;
            } else {
                if (a.action == 'reject' && b.action == 'accept') {
                    return -1;
                } else if (a.action == 'accept' && b.action == 'reject') {
                    return 1;
                } else {
                    return 0;
                }
            }
        })
        setACLPolicySaveLoading(true);

        flylayerClient.setACLPolicy({
            flynetId: flynet.id,
            policy: policy
        }).then(() => {
            setACLPolicy(policy);
            setInitACLPolicy(policy);
            const policyString = computePolicyString(policy);
            setLocalAcl(policyString)
            setRemoteAcl(policyString)
            Notification.success({ content: '保存成功', position: "bottomRight" })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: '保存失败, 请稍后重试', position: "bottomRight" })
        }).finally(() => {
            setACLPolicySaveLoading(false);
        })
    }




    return { localAcl, setLocalAcl, updateLocalAcl, initAclPolicy, computePolicyString, remoteAcl, setRemoteAcl, aclPolicy, setACLPolicy, aclPolicyLoading, saveACLPolicy, aclPolicySaveLoading, jsonEditorError }
}

export default useACLPolicy;