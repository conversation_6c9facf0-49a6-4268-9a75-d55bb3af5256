import React, { useState, useRef, useEffect } from 'react'
import { Typography, Modal, Collapse, List, Col, Divider } from '@douyinfe/semi-ui';

import styles from './index.module.scss'

interface Props {
  children?: React.ReactNode;
}
const Index: React.FC<Props> = (props) => {



  return <><div className={styles.editorLayout}>
    <div className={styles.editor}>
      {props.children}
    </div>
    <div className={styles.sidebar}><Collapse>
      <Collapse.Panel header="文档" itemKey="1">
        <p>了解如何配置策略文件</p>
        <ul className={styles.refList}>
          <li><a href='#'>语法配置</a></li>
          <li><a href='#'>标签参考</a></li>
          <li><a href='#'>SSH 参考</a></li>
          <li><a href='#'>漏斗参考</a></li>
          <li><a href='#'>测试参考</a></li>
          <li><a href='#'>例子</a></li>
        </ul>
      </Collapse.Panel>
      <Collapse.Panel header="过滤" itemKey="2">
        <p className='m10'>设置允许哪些节点可以接入互联网流量。</p>
        <a href='#'>了解更多 →</a>
      </Collapse.Panel>

    </Collapse></div>

  </div></>
}

export default Index;