import React, { useState, useEffect, useContext, useCallback } from 'react'
import { Typography, Select, Table, Notification, Card, Button, Divider, Avatar, Row, Col } from '@douyinfe/semi-ui';
import { IconPriceTag } from '@douyinfe/semi-icons';

import { flylayerClient } from '@/services/core';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { ACLPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'
import { debounce } from 'lodash';

interface Props {
    aclPolicy: ACLPolicy,
}

interface PreviewItem {
    key: string,
    src: string,
    dist: string,
}

const Index: React.FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);

    const initTags: Array<string> = [];

    if (props.aclPolicy && props.aclPolicy.tagowners) {
        Object.keys(props.aclPolicy.tagowners).forEach(key => {
            initTags.push(key)
            // props.aclPolicy.tagowners[key].values.map((item) => {
            //     const itemVal = item.kind.value + ''
            //     tags.push(key)
            // })
        })
    }
    const [tags, setTags] = useState<Array<string>>(initTags);
    const [allTags, setAllTags] = useState<Array<string>>(initTags);

    const renderSelectedItem = (optionNode: any,) => {

        const optionStyle = {
            display: 'flex',
            paddingLeft: 0,
            paddingTop: 9,
            paddingBottom: 9,
        };
        if (optionNode && optionNode.displayName) {
            const item: User = optionNode as User;

            return <div style={optionStyle}>
                <Avatar size="extra-small" src={item.avatarUrl}></Avatar>
                <div style={{ marginLeft: 8 }}>
                    <div style={{ fontSize: 14, lineHeight: '24px' }}>{item.displayName}({item.loginName})</div>
                </div>
            </div>
        } else {
            return <div style={optionStyle} >  <IconPriceTag className={styles.tagIcon} />{optionNode.value}</div>
        }
    };
    const renderCustomOption = (item: User, index: number) => {

        const optionStyle = {
            display: 'flex',
            paddingLeft: 12,
            paddingTop: 10,
            paddingBottom: 10,
        };
        return (
            <Select.Option value={item.loginName} style={optionStyle} showTick={true} key={index + 1} {...item}>
                <Avatar size="extra-small" src={item.avatarUrl} />
                <div style={{ marginLeft: 8 }}>
                    <div style={{ fontSize: 14 }}>{item.displayName}({item.loginName})</div>
                </div>
            </Select.Option>
        );
    };

    // 用户列表
    const [users, setUsers] = useState<User[]>();




    useEffect(() => {
        queryUser();
    }, []);

    const columns = [
        {
            title: '源',
            dataIndex: 'src',
        }, {
            title: '允许目标',
            dataIndex: 'dist',
        }

    ];


    const [selectDisplay, setSelectDisplay] = useState<string>('');

    const [previewItems, setPreviewItems] = useState<PreviewItem[]>([]);


    const handleTagUserChange = (value: any) => {
        const valueStr = value as string;
        const items: PreviewItem[] = [];

        if (valueStr.indexOf('tag:') >= 0) {
            if (props.aclPolicy && props.aclPolicy.acls) {
                props.aclPolicy.acls.forEach((acl, index) => {
                    if (acl.src && acl.src.indexOf(valueStr) >= 0) {
                        items.push({
                            key: index + '',
                            src: acl.src.join(','),
                            dist: acl.dst.join(','),
                        })
                    }
                })
                // Object.keys(props.aclPolicy.tagowners).forEach(key => {
                //     const tag = props.aclPolicy.tagowners[key];

                // })
            }    // 
        }
        // 用户
        else {
            if (props.aclPolicy && props.aclPolicy.acls) {
                props.aclPolicy.acls.forEach((acl, index) => {
                    // 用户
                    if (acl.src && (acl.src.indexOf(valueStr) >= 0 || acl.src.indexOf('*') >= 0)) {
                        items.push({
                            key: index + '',
                            src: acl.src.join(','),
                            dist: acl.dst.join(','),
                        })
                        return;
                    }
                    // 用户所属的tag
                    if (props.aclPolicy.tagowners) {
                        Object.keys(props.aclPolicy.tagowners).forEach(tagName => {
                            let userHasTag = false;
                            const tagValue = props.aclPolicy.tagowners[tagName];
                            if (tagValue.values) {
                                tagValue.values.forEach((user) => {
                                    if (user.kind.value + '' === valueStr) {
                                        userHasTag = true;
                                    }
                                })
                            }

                            if (userHasTag) {
                                if (acl.src && (acl.src.indexOf(tagName) >= 0 || acl.src.indexOf('*') >= 0)) {
                                    items.push({
                                        key: index + '',
                                        src: acl.src.join(','),
                                        dist: acl.dst.join(','),
                                    })
                                    return;
                                }
                            }
                        })
                    }
                    // 用户所属的组
                    if (props.aclPolicy.groups) {
                        Object.keys(props.aclPolicy.groups).forEach(groupName => {
                            const groupValue = props.aclPolicy.groups[groupName];
                            let userHasGroup = false;
                            if (groupValue.values) {
                                groupValue.values.forEach((user) => {

                                    if (user.kind.value + '' === valueStr) {
                                        userHasGroup = true;
                                    }
                                })
                            }
                            if (userHasGroup) {
                                if (acl.src && (acl.src.indexOf(groupName) >= 0 || acl.src.indexOf('*') >= 0)) {
                                    items.push({
                                        key: index + '',
                                        src: acl.src.join(','),
                                        dist: acl.dst.join(','),
                                    })
                                    return;
                                }
                            }
                        })
                    }
                    // 用户所属的主机名

                    // 用户所属的ip
                });
            }

        }


        setPreviewItems(items);
        setSelectDisplay(value);
    }

    const queryUser = async (val?: string) => {
        flylayerClient.listUsers({
            flynetId: flynet?.id,
            query : val ? `keywords=${encodeURIComponent(val)}&limit=20&offset=0` :"limit=20&offset=0"
        }).then((res) => {
            setUsers(res.users);
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: '获取用户列表失败, 请稍后重试' })

        })
    }

    const debounceQuery = useCallback(debounce((queryStr) => queryUser(queryStr), 500), []);

    return <><div className={styles.preview}>
        <Title heading={4} className='mb10' >规则预览</Title>
        <Paragraph className='mb20'>预览用户和标签能访问的资源</Paragraph>
        <Select
            filter
            onSearch={(value) => {
                if (value.indexOf('tag:') >= 0) {
                    setTags(tags.filter(tag => tag.indexOf(value) >= 0));
                } else {
                    setTags(allTags);
                }
                debounceQuery(value);
            }}
            className='mb20'
            placeholder="请选择用户或标签"
            style={{ width: 280, height: 40 }}
            onChange={handleTagUserChange}
            renderSelectedItem={renderSelectedItem}
            virtualize={{ itemSize: 44 }}
        >

            {tags.map((item, index) => {
                return <Select.Option key={index} value={item}><IconPriceTag className={styles.tagIcon} />{item}</Select.Option>
            })}

            {/* {list.map((item, index) => renderCustomOption(item, index))} */}
            {users?.map((user, index) => renderCustomOption(user, index))}
        </Select>
        {previewItems.length > 0 ?
            <Table columns={columns} dataSource={previewItems} pagination={false} /> :
            selectDisplay ? <Card >
                <Title heading={6} className='mb10'>没有匹配的访问规则</Title>
                {selectDisplay}不能访问网络上的任何资源</Card> : null}

    </div></>
}

export default Index;