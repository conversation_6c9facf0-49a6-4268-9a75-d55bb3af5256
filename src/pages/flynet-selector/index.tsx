import useFlynetGeneral from '@/hooks/useFlynetGeneral';

import { FC, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Typography, Button, Notification, Card } from '@douyinfe/semi-ui';

const { Title } = Typography;
import { DEFAULT_ROUTER } from '@/constants/router'
import styles from './index.module.scss'
import { FlynetGeneral } from '@/services/flynet';
import { UserRole } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb'
import { UserFlynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import Active from '@/components/active';
interface Props { }

// 切换网络
const FlynetSelector: FC<Props> = (props) => {

    const navigate = useNavigate();
    const { userFlynets, selectFlynetGeneral } = useFlynetGeneral()

    // 当前选择的网络
    const [flynetGeneral, setFlynetGeneral] = useState<FlynetGeneral>()

    // 邀请码对话框是否显示
    const [inviteCodeModalVisible, setInviteCodeModalVisible] = useState(false)

    const handleSelectFlynet = (fg: UserFlynet) => {
        if (!fg.flynet || !fg.user) {
            return;
        }

        if (fg.flynet.activated) {
            selectFlynetGeneral({
                id: fg.flynet ? fg.flynet?.id : BigInt(0),
                name: fg.flynet ? fg.flynet?.name + '' : '',
                alias: fg.flynet ? fg.flynet?.alias + '' : '',
                disabledExpiry: fg.flynet ? fg.flynet?.disabledExpiry : true,
                expiresAt: fg.flynet?.expiresAt,
                expired: fg.flynet.expired,
                userRole: fg.user ? fg.user.role.toString() : '',
                applicationEnabled: fg.flynet.applicationEnabled
            })

            navigate(DEFAULT_ROUTER)
            return;
        } else {
            if (fg.user.role != UserRole.FLYNET_ADMIN &&
                fg.user.role != UserRole.FLYNET_OWNER &&
                fg.user.role != UserRole.SUPER_ADMIN
            ) {
                Notification.error({ content: '请联系网络管理员激活网络', position: "bottomRight" })
                return;
            }
        }

        setFlynetGeneral({
            id: fg.flynet ? fg.flynet?.id : BigInt(0),
            name: fg.flynet ? fg.flynet?.name + '' : '',
            alias: fg.flynet ? fg.flynet?.alias + '' : '',
            disabledExpiry: fg.flynet ? fg.flynet?.disabledExpiry : true,
            expiresAt: fg.flynet?.expiresAt,
            expired: fg.flynet.expired,
            userRole: fg.user ? fg.user.role.toString() : '',
            applicationEnabled: fg.flynet.applicationEnabled
        })

        setInviteCodeModalVisible(true)

    }

    // useEffect(() => {
    //     if (userFlynets && userFlynets.length == 1) {
    //         const fg = userFlynets[0]
    //         if (fg?.flynet?.activated) {
    //             selectFlynetGeneral({
    //                 id: fg.flynet ? fg.flynet?.id : BigInt(0),
    //                 name: fg.flynet ? fg.flynet?.name + '' : '',
    //                 alias: fg.flynet ? fg.flynet?.alias + '' : '',
    //                 disabledExpiry: fg.flynet ? fg.flynet?.disabledExpiry : true,
    //                 expiresAt: fg.flynet?.expiresAt,
    //                 expired: fg.flynet.expired
    //             })

    //             navigate(DEFAULT_ROUTER)
    //             return;
    //         }

    //     }
    // }, [userFlynets])

    return <><div className={styles.flynetSelector}>
        {userFlynets.length === 0 ? <Card className='mb20' style={{ textAlign: 'center' }}>您还没有加入任何网络</Card> : <>
            <Title heading={1} style={{ marginBottom: 40, textAlign: 'center' }}>选择网络</Title>
            {userFlynets.map((fg, index: number) => {
                return <div key={index}>
                    <Button
                        className='mb20'
                        size='large'
                        block
                        onClick={() => handleSelectFlynet(fg)} >{fg.flynet?.alias}</Button>
                </div>
            })}</>}

    </div>
        {inviteCodeModalVisible && flynetGeneral
            &&
            <Active flynetGeneral={flynetGeneral} close={() => { setInviteCodeModalVisible(false) }} success={() => {

                selectFlynetGeneral(flynetGeneral)

                setInviteCodeModalVisible(false)
                window.location.pathname = DEFAULT_ROUTER;


            }} />}

    </>
}

export default FlynetSelector