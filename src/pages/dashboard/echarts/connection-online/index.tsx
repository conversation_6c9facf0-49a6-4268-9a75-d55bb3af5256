import { FC, useContext } from 'react';
import EChartsReact from 'echarts-for-react';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { getEchartsTheme } from '@/utils/common';
const Index: FC<{
    title: string;
}> = (props) => {
    const globalTheme = useContext(GlobalThemeContext);
    return <>
        <EChartsReact
            
            theme={getEchartsTheme(globalTheme)}
            option={{
                zIndex: 1,
                title: {
                    top: 10,
                    left: 10,
                    text: props.title || '连接及在线数'
                },
                grid: {
                    top: 100,
                    left: 20,
                    right: 20,
                    bottom: 20,
                    containLabel: true
                },

                tooltip: {},
                legend: {
                    top: 40,
                    data: ['连接数', 'PPP在线']
                },
                xAxis: {
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {},
                series: [
                    {
                      name: '连接数',
                      type: 'line',
                      stack: 'Total',
                      data: [10, 132, 101, 134, 90, 230, 210]
                    },
                    {
                      name: 'PPP在线',
                      type: 'line',
                      stack: 'Total',
                      data: [220, 30, 191, 234, 290, 330, 310]
                    },
                   
                  ]
            }}
        />
    </>
}

export default Index;