import { FC, useContext } from 'react';
import EChartsReact from 'echarts-for-react';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { getEchartsTheme } from '@/utils/common';
const Index: FC<{
  title: string;
}> = (props) => {
  const globalTheme = useContext(GlobalThemeContext);
  return <>
    <EChartsReact
      theme={getEchartsTheme(globalTheme)}
      option={{
        title: {
          top: 10,
          left: 10,
          text: props.title || '设备负载'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: 40,
          data: ['设备在线数', 'CPU平均负载', 'CPU最大负载', '内存平均负载', '内存最大负载']
        },
        grid: {
          top: 100,
          left: 20,
          right: 20,
          bottom: 20,
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '设备在线数',
            type: 'line',
            stack: 'Total',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: 'CPU平均负载',
            type: 'line',
            stack: 'Total',
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: 'CPU最大负载',
            type: 'line',
            stack: 'Total',
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: '内存平均负载',
            type: 'line',
            stack: 'Total',
            data: [320, 332, 301, 334, 390, 330, 320]
          },
          {
            name: '内存最大负载',
            type: 'line',
            stack: 'Total',
            data: [820, 932, 901, 934, 1290, 1330, 1320]
          }
        ]
      }}
    />
  </>
}

export default Index;
