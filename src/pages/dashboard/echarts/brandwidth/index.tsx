import { FC, useContext } from 'react';
import EChartsReact from 'echarts-for-react';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { getEchartsTheme } from '@/utils/common';
const Index: FC<{
    title: string;
}> = (props) => {
    const globalTheme = useContext(GlobalThemeContext);
    return <>
        <EChartsReact
            theme={getEchartsTheme(globalTheme)}
            option={{
                title: {
                    top: 10,
                    left: 10,
                    text: props.title || '主口流量'
                },
                grid: {
                    top: 100,
                    left: 20,
                    right: 20,
                    bottom: 20,
                    containLabel: true
                },
                tooltip: {},
                legend: {
                    top: 40,
                    data: ['上行带宽', '下行带宽']
                },
                xAxis: {
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {},
                series: [{
                    name: '上行带宽',
                    type: 'line',
                    stack: 'Total',
                    data: [888, 132, 101, 134, 90, 99, 210]
                  },
                  {
                    name: '下行带宽',
                    type: 'line',
                    stack: 'Total',
                    data: [220, 182, 191, 234, 290, 330, 310]
                  },]
            }}
        />
    </>
}

export default Index;
