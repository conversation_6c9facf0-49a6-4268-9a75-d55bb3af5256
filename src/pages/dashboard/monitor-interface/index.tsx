import { FC } from 'react';
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Tag, Divider } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';

import useTable from './useTable';
const { Title } = Typography;

const Index: FC = () => {
    const {
        columns,
        interfaces
    } = useTable();
    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/dashboard`,
                        href: `${BASE_PATH}/dashboard`,
                        name: '看板'
                    },
                    {
                        name: '物理网口',
                    }
                ]
            }>
            </Breadcrumb>
            <Row className='mb10'>
                <Col span={20}>
                    <Title heading={3}>网口列表</Title>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                    <Space>

                    </Space>
                </div></Col>
            </Row>
            <Divider className='mb20'></Divider>

            <Table

                columns={columns}
                dataSource={interfaces}
            />
        </div>
    </>
}

export default Index;