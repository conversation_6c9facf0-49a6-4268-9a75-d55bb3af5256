import { useEffect, useState, FC, useContext } from 'react';
import { Typography, Dropdown, Button, Tabs, TabPane, Space, Divider } from '@douyinfe/semi-ui';

import EchartsBrandwidth from '../echarts/brandwidth';
import EchartsDeviceLoad from '../echarts/device-load';
import EchartsConnectionOnline from '../echarts/connection-online';
import styles from './index.module.scss'
import { Interface } from '../device';
const { Title, Paragraph } = Typography;
const useTable = () => {
    // 接口列表
    const [interfaces, setInterfaces] = useState<Interface[]>([]);

    // 设备表格列
    const columns = [
        {
            title: '网口名称',
            key: 'action',
            width: 200,
            render: (text: any, record: Interface) => (
                <>
                    <Title heading={6}>{record.name}</Title>
                    <Paragraph>接口名：{record.name}</Paragraph>
                    <Paragraph>设备：{record.deviceName}</Paragraph>
                    <Paragraph>设备类型：{record.deviceType}</Paragraph>
                    <Paragraph>归属地：{record.location}</Paragraph>
                </>
            ),
        },
        {
            title: '网口流量',
            key: 'action',
            render: (text: any, record: Interface) => (
                <EchartsBrandwidth title='流量' />
            ),
        },

        {
            title: '数据概况',
            key: 'action',
            width: 200,
            render: (text: any, record: Interface) => (
                <>
                <Paragraph>上行带宽(采样:{record.downstreamPoints}点)</Paragraph>
                <Paragraph>最大：{record.upstreamMax}Mb/s</Paragraph>
                <Paragraph className='mb10'>平均：{record.upstreamAvg}Mb/s</Paragraph>
                <Divider className='mb10'/>

                <Paragraph>下行带宽(采样:{record.downstreamPoints}点)</Paragraph>
                <Paragraph>最大：{record.downstreamMax}Mb/s</Paragraph>
                <Paragraph>平均：{record.downstreamAvg}Mb/s</Paragraph>
                </>
            ),
        },
    ];

    useEffect(() => {
        // mock 设备列表
        const mockInterfaces: Interface[] = [
            {
                name: 'ether1',
                deviceName: 'MikroTik',
                deviceType: 'hEX S',
                location: '上海电信',
                upstreamPoints: 232,
                upstreamMax: 12,
                upstreamAvg: 20,
                downstreamPoints: 100,
                downstreamMax: 100,
                downstreamAvg: 60
            }, {
                name: 'ether2',
                deviceName: 'MikroTik',
                deviceType: 'hEX S',
                location: '上海电信',
                upstreamPoints: 232,
                upstreamMax: 12,
                upstreamAvg: 20,
                downstreamPoints: 100,
                downstreamMax: 100,
                downstreamAvg: 60
            }, {
                name: 'ether3',
                deviceName: 'MikroTik',
                deviceType: 'hEX S',
                location: '上海电信',
                upstreamPoints: 232,
                upstreamMax: 12,
                upstreamAvg: 20,
                downstreamPoints: 100,
                downstreamMax: 100,
                downstreamAvg: 60
            }, {
                name: 'ether4',
                deviceName: 'MikroTik',
                deviceType: 'hEX S',
                location: '上海电信',
                upstreamPoints: 232,
                upstreamMax: 12,
                upstreamAvg: 20,
                downstreamPoints: 100,
                downstreamMax: 100,
                downstreamAvg: 60
            }, 
        ];

        setInterfaces(mockInterfaces);
    }, [])


    return {
        columns,
        interfaces,
    }
}

export default useTable;
