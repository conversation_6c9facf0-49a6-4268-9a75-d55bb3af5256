export interface Device {
    // 名称
    name: string; 
    // 别名
    alias: string;
    // 归属地
    location: string;
    // 连接数
    connection: number;
    
}

//  网卡接口
export interface Interface {
    // 接口名
    name: string;
    // 设备名
    deviceName: string;
    // 设备类型
    deviceType: string;
    // 归属地
    location: string;
    // 上行带宽采样点数量
    upstreamPoints: number;
    // 上行带宽最大值
    upstreamMax: number;
    // 上行带宽平均值
    upstreamAvg: number;
    // 下行带宽采样点数量
    downstreamPoints: number;
    // 下行带宽最大值
    downstreamMax: number;
    // 下行带宽平均值
    downstreamAvg: number;
    

}