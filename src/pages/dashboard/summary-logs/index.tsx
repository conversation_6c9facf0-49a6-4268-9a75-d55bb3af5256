import React, {useContext, useRef} from 'react'
import { Typo<PERSON>, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { IconArrowDownRight } from '@douyinfe/semi-icons';
import EChartsReact from 'echarts-for-react';
import { getEchartsTheme } from '@/utils/common';
import styles from '../index.module.scss'
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const { Title } = Typography;

const Index: React.FC = () => {
  const globalTheme = useContext(GlobalThemeContext);
    const rootRef = useRef<EChartsReact>(null)
    return <>
        <div className={styles.chartPanel}>
            <Title heading={6} className={styles.panelTitle}>
                <IconArrowDownRight className={styles.panelIcon}></IconArrowDownRight>
                <a>实时日志速率</a>
            </Title>
            <EChartsReact
                theme={getEchartsTheme(globalTheme)}
                style={{ height: '400px' }}
                option={{
                    grid: {
                      top: 80
                    },
                    xAxis: {
                      type: 'category',
                      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    },
                    yAxis: {
                      type: 'value'
                    },
                    series: [
                      {
                        data: [820, 932, 901, 934, 1290, 1330, 1320],
                        type: 'line',
                        smooth: true
                      }
                    ]
                  }}
                ref={rootRef}
                onEvents={{
                    'click': (param: any) => { }
                }}
            />
        </div>
    </>
}

export default Index;
