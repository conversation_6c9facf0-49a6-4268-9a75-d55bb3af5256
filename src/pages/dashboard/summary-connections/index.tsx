import React, { useContext, useRef } from 'react'
import { Typo<PERSON>, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { IconArrowDownRight } from '@douyinfe/semi-icons';
import EChartsReact from 'echarts-for-react';

import { graphic } from 'echarts';
import styles from '../index.module.scss'
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { getEchartsTheme } from '@/utils/common';
const { Title } = Typography;

const Index: React.FC = () => {
    const globalTheme = useContext(GlobalThemeContext);
    const rootRef = useRef<EChartsReact>(null)
    return <>
        <div className={styles.chartPanel}>
            <Title heading={6} className={styles.panelTitle}>
                <IconArrowDownRight className={styles.panelIcon}></IconArrowDownRight>
                <a>连接数及在线信息汇总</a>
            </Title>
            <EChartsReact
        style={{ height: '400px' }}
                theme={getEchartsTheme(globalTheme)}
                option={{
                    title: {
                        text: '',
                        x: 'center',
                        y: 0,
                        textStyle: {
                            color: '#B4B4B4',
                            fontSize: 16,
                            fontWeight: 'normal'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',

                    },
                    legend: {
                        // 修改legend的高度宽度
                        itemHeight: 5,
                        itemWidth: 24,
                        data: [{
                            name: 'WIFI在线数',
                            icon: 'rect' // legend的icon
                        },
                        {
                            name: 'DHCP在线数',
                            icon: 'rect'
                        },
                        {
                            name: '设备连接数',
                            icon: 'rect'
                        }
                        ],
                        textStyle: {
                            color: '#B4B4B4'
                        },
                        top: 40,
                        // 选择关闭的legend
                        selected: {
                            'DHCP在线数': false
                        }
                    },
                    grid: {
                        top: 80,
                        x: '8%',
                        width: '82%',
                        y: '12%'
                    },
                    xAxis: [{
                        // type:'category',
                        data: ["1km", '2km', '3km', '4km', '5km', '6km'],
                        boundaryGap: true,
                        axisLine: {
                            lineStyle: {
                                color: '#B4B4B4'
                            }
                        },
                        axisTick: {
                            show: false
                        }
                    }],
                    yAxis: [{
                        name: 'DHCP在线数',
                        nameLocation: 'middle',
                        nameTextStyle: {
                            padding: [3, 4, 50, 6]
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: '#eee'
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333'
                            },
                            formatter: '{value} '
                        }
                    },
                    {
                        name: '设备连接数',
                        nameLocation: 'middle',
                        nameTextStyle: {
                            padding: [50, 4, 5, 6]
                        },
                        splitLine: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#333'
                            },
                            formatter: '{value} '
                        }
                    }
                    ],
                    series: [{
                        name: 'WIFI在线数',
                        type: 'line',
                        smooth: true,
                        showSymbol: true,
                        // 矢量画五角星
                        symbol: 'path://M150 0 L80 175 L250 75 L50 75 L220 175 Z',
                        symbolSize: 12,
                        yAxisIndex: 0,
                        areaStyle: {
                            normal: {
                                color: new graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(250,180,101,0.3)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(250,180,101,0)'
                                }
                                ],),
                                shadowColor: 'rgba(250,180,101,0.2)',
                                shadowBlur: 20
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#FF8000'
                            }
                        },
                        // data中可以使用对象，value代表相应的值，另外可加入自定义的属性
                        data: [{
                            value: 1,
                            stationName: "s1"
                        }, {
                            value: 3,
                            stationName: "s2"
                        }, {
                            value: 4,
                            stationName: "s3"
                        }, {
                            value: 9,
                            stationName: "s4"
                        }, {
                            value: 3,
                            stationName: "s5"
                        }, {
                            value: 2,
                            stationName: "s6"
                        }]
                    },
                    {
                        name: 'DHCP在线数',
                        type: 'line',
                        smooth: true,
                        showSymbol: true,
                        symbol: 'emptyCircle',
                        symbolSize: 12,
                        yAxisIndex: 0,
                        areaStyle: {
                            normal: {
                                color: new graphic.LinearGradient(0, 0, 0, 1,
                                    [{
                                        offset: 0,
                                        color: 'rgba(199, 237, 250,0.5)'
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(199, 237, 250,0.2)'
                                    }
                                    ],
                                    false
                                )
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#3bbc86'
                            }
                        },
                        data: [{
                            value: 31,
                            stationName: "s1"
                        }, {
                            value: 36,
                            stationName: "s2"
                        }, {
                            value: 54,
                            stationName: "s3"
                        }, {
                            value: 89,
                            stationName: "s4"
                        }, {
                            value: 73,
                            stationName: "s5"
                        }, {
                            value: 22,
                            stationName: "s6"
                        }]
                    },
                    {
                        name: '设备连接数',
                        type: 'bar',
                        barWidth: 30,
                        yAxisIndex: 1,
                        itemStyle: {
                            normal: {
                                color: new graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(108,80,243,0.3)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(108,80,243,0)'
                                }
                                ]),
                                //柱状图圆角
                                barBorderRadius: [30, 30, 0, 0],
                            }
                        },

                        data: [{
                            value: 11,
                            stationName: "s1"
                        }, {
                            value: 34,
                            stationName: "s2"
                        }, {
                            value: 54,
                            stationName: "s3"
                        }, {
                            value: 39,
                            stationName: "s4"
                        }, {
                            value: 63,
                            stationName: "s5"
                        }, {
                            value: 24,
                            stationName: "s6"
                        }]
                    }
                    ]
                }}
                ref={rootRef}
                onEvents={{
                    'click': (param: any) => { }
                }}
            />
        </div>
    </>
}

export default Index;
