import { FC, useContext } from 'react';
import EChartsReact from 'echarts-for-react';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Tag, Divider } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';

import iconHub from '@/assets/chart/icons8-hub-80.png';
import iconLaptop from '@/assets/chart/icons8-laptop-80.png';
import iconMacClient from '@/assets/chart/icons8-mac-client-80.png';
import iconRouter from '@/assets/chart/icons8-router-80.png';
import iconServer from '@/assets/chart/icons8-server-80.png';
import iconWindwosClient from '@/assets/chart/icons8-windows-client-80.png';
import { getEchartsTheme } from '@/utils/common';

const Index: FC = () => {
    const globalTheme = useContext(GlobalThemeContext);
    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/dashboard`,
                        href: `${BASE_PATH}/dashboard`,
                        name: '看板'
                    },
                    {
                        name: '网络拓扑',
                    }
                ]
            }>
            </Breadcrumb>

            <EChartsReact
                style={{ height: 800 }}
                theme={getEchartsTheme(globalTheme)}
                option={{
                    // title: {
                    //     text: 'Network Topology'
                    // },
                    tooltip: {},
                    animationDurationUpdate: 1500,
                    animationEasingUpdate: 'quinticInOut',
                    series: [
                        {
                            type: 'graph',
                            layout: 'none',
                            symbolSize: 50,
                            roam: true,
                            label: {
                                show: true
                            },
                            draggable: true,//节点是否可拖拽，只在使用力引导布局的时候有用。
                            focusNodeAdjacency: true,//是否在鼠标移到节点上的时候突出显示节点以及节点的边和邻接节点。

                            legendHoverLink: true,//是否启用图例 hover(悬停) 时的联动高亮。
                            hoverAnimation: true,//是否开启鼠标悬停节点的显示动画
                            coordinateSystem: null,//坐标系可选
                            edgeSymbol: ['circle', 'arrow'],
                            edgeSymbolSize: [4, 10],
                            data: [
                                {
                                    label: {
                                        position: 'right'
                                    },
                                    x: 300,
                                    y: 50,
                                    name: 'hub1', symbol: 'image:///mesh/chart/hub-80.png', symbolSize: 80, category: 0
                                },
                                {
                                    label: {
                                        position: 'right'
                                    },
                                    x: 300,
                                    y: 150,
                                    name: 'router1', symbol: 'image:///mesh/chart/router-80.png', symbolSize: 80, category: 0
                                },
                                {
                                    label: {
                                        position: 'right'
                                    },
                                    x: 100,
                                    y: 250,
                                    name: 'server1', symbol: 'image:///mesh/chart/server-80.png', symbolSize: 80, category: 0
                                },
                                {
                                    label: {
                                        position: 'right'
                                    },
                                    x: 300,
                                    y: 250,
                                    name: 'macClient1', symbol: 'image:///mesh/chart/mac-client-80.png', symbolSize: 80, category: 0
                                },
                                {
                                    label: {
                                        position: 'right'
                                    },
                                    x: 500,
                                    y: 250,
                                    name: 'laptop1', symbol: 'image:///mesh/chart/laptop-80.png', symbolSize: 80, category: 0
                                }
                            ],
                            links: [
                                { source: 'hub1', target: 'router1' },
                                { source: 'router1', target: 'server1' },
                                { source: 'router1', target: 'macClient1' },
                                { source: 'router1', target: 'laptop1' },
                            ],
                            lineStyle: {
                                opacity: 0.9,
                                width: 2,
                                curveness: 0
                            }
                        }
                    ]
                }}
            />
        </div>
    </>
}

export default Index;