import { useEffect, useState, FC, useContext } from 'react';
import { Typography, Dropdown, Button, Tabs, TabPane, Space } from '@douyinfe/semi-ui';

import EchartsBrandwidth from '../echarts/brandwidth';
import EchartsDeviceLoad from '../echarts/device-load';
import EchartsConnectionOnline from '../echarts/connection-online';
import styles from './index.module.scss'
import { Device } from '../device';
const { Title, Paragraph } = Typography;

const useTable = () => {
    // 设备列表
    const [devices, setDevices] = useState<Device[]>([]);

    // 设备表格列
    const columns = [
        {
            title: '名称',
            width: 200,
            dataIndex: 'name',
            key: 'name',
            render: (text: any, record: Device) => (
                <>
                <Title heading={6}>{record.name}</Title>
                <Paragraph>中国上海闵行电信</Paragraph>
                <Paragraph>CPU：1%</Paragraph>
                <Paragraph>内存：15%</Paragraph>
                </>
            ),
        },
        {
            title: '设备负载及温度',
            dataIndex: 'alias',
            key: 'alias',
            width: 400,
            render: (text: any, record: any) => (
                <><EchartsDeviceLoad title='设备负载' />
                </>
            ),
        },
        {
            title: '连接及在线数',
            dataIndex: 'alias',
            key: 'alias',
            width: 400,
            render: (text: any, record: any) => (
                <><EchartsConnectionOnline title='连接及在线数' />
                </>
            ),
        },
        {
            title: '主口流量',
            dataIndex: 'location',
            key: 'location',
            width: 400,
            render: (text: any, record: any) => (
                <><EchartsBrandwidth title='主口流量' />
                </>
            ),
        },
        {
            title: '全部接口流量和',
            dataIndex: 'connection',
            key: 'connection',
            width: 400,
            render: (text: any, record: any) => (
                <><EchartsBrandwidth title='全部接口流量和' />
                </>
            ),
        }
    ];

    useEffect(() => {
        // mock 设备列表
        const mockDevices: Device[] = [
            {
                name: '设备1',
                alias: '设备1别名',
                location: '北京',
                connection: 10
            },
            {
                name: '设备2',
                alias: '设备2别名',
                location: '上海',
                connection: 20
            },
            {
                name: '设备3',
                alias: '设备3别名',
                location: '广州',
                connection: 30
            }
        ];

        setDevices(mockDevices);
    }, [])


    return {
        columns,
        devices,
    }
}

export default useTable;
