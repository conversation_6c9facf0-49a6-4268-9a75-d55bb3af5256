import { FC } from 'react';
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Tag, Divider } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';

const { Title } = Typography;
import useTable from './useTable';
const Index: FC = () => {
    const {
        columns,
        devices
    } = useTable();
    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/dashboard`,
                        href: `${BASE_PATH}/dashboard`,
                        name: '看板'
                    },
                    {
                        name: '设备监控',
                    }
                ]
            }>
            </Breadcrumb><Row className='mb10'>
                <Col span={20}>
                    <Title heading={3}>设备监控</Title>
                </Col>
                {/* <Col span={4}><div className='btn-right-col'>
                    <Space>
                        <Button theme='solid'
                        >设备对接</Button>
                    </Space>
                </div></Col> */}
            </Row>
            <Divider className='mb20'></Divider>
            <Table
                columns={columns}
                dataSource={devices}
            />
        </div>
    </>
}

export default Index;