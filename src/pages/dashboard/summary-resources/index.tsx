import React, { useContext, useRef } from 'react'
import { Typo<PERSON>, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { IconArrowDownRight } from '@douyinfe/semi-icons';
import EChartsReact from 'echarts-for-react';
import { getEchartsTheme } from '@/utils/common';
import { graphic } from 'echarts';
import styles from '../index.module.scss'
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const { Title } = Typography;

const Index: React.FC = () => {
  const globalTheme = useContext(GlobalThemeContext);
  let data = [
    { name: '最高内存占用率', value: 97 },
    { name: '最高CPU负载', value: 68 },
    { name: '授权使用例', value: 50 },
    { name: '设备已备份比例', value: 36 },
    { name: '设备在线率', value: 13 }
  ]
  let xAxisData = data.map(item => item.name)
  let seriesData = data.map(item => item.value)
  let maxSeriesData = []
  const MAX = Math.max(...seriesData)
  for (let i = 0; i < seriesData.length; i++) {
    maxSeriesData.push(MAX)
  }
  let barLinearColors = [
    new graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: "#EB3B5A" },
      { offset: 1, color: "#FE9C5A" }
    ]),
    new graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: "#FA8231" },
      { offset: 1, color: "#FFD14C" }
    ]),
    new graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: "#F7B731" },
      { offset: 1, color: "#FFEE96" }
    ]),
    new graphic.LinearGradient(0, 1, 1, 1, [
      { offset: 0, color: "#395CFE" },
      { offset: 1, color: "#2EC7CF" }
    ])
  ]

  function rankBarColor(cData: any) {
    let tempData: any = []
    cData.forEach((item: any, index: any) => {
      tempData.push({
        value: item,
        itemStyle: {
          color: index > 3 ? barLinearColors[3] : barLinearColors[index]
        }
      })
    })
    return tempData
  }




  const rootRef = useRef<EChartsReact>(null)
  return <>
    <div className={styles.chartPanel}>
      <Title heading={6} className={styles.panelTitle}>
        <IconArrowDownRight className={styles.panelIcon}></IconArrowDownRight>
        <a>资源信息</a>
      </Title>
      <EChartsReact
        style={{ height: '400px' }}
        theme={getEchartsTheme(globalTheme)}
        option={{
          grid: {
            top: 80
          },
          tooltip: {
            backgroundColor: 'rgba(50,50,50,.3)',
            textStyle: {
              color: '#222'
            }
          },
          xAxis: {
            type: "value",
            splitLine: { show: false },
            axisLabel: { show: false },
            axisTick: { show: false },
            axisLine: { show: false }
          },
          yAxis: [
            {
              type: "category",
              inverse: true,
              axisLine: { show: false },
              axisTick: { show: false },
              data: xAxisData,
              axisLabel: {
                rich: {
                  nt1: {
                    color: "#fff",
                    backgroundColor: '#EB3B5A',
                    width: 16,
                    height: 16,
                    fontSize: 12,
                    align: "center",
                    borderRadius: 100,
                    padding: [0, 1, 2, 1]
                  },
                  nt2: {
                    color: "#fff",
                    backgroundColor: '#FA8231',
                    width: 16,
                    height: 16,
                    fontSize: 12,
                    align: "center",
                    borderRadius: 100,
                    padding: [0, 1, 2, 1]
                  },
                  nt3: {
                    color: "#fff",
                    backgroundColor: '#F7B731',
                    width: 16,
                    height: 16,
                    fontSize: 12,
                    align: "center",
                    borderRadius: 100,
                    padding: [0, 1, 2, 1]
                  },
                  nt: {
                    color: "#fff",
                    backgroundColor: '#00a9c8',
                    width: 16,
                    height: 16,
                    fontSize: 12,
                    align: "center",
                    borderRadius: 100,
                    padding: [0, 1, 2, 1]
                  }
                },
                formatter: function (value: any, index: any) {
                  let idx = index + 1
                  if (idx <= 3) {
                    return ["{nt" + idx + "|" + idx + "}"].join("\n");
                  } else {
                    return ["{nt|" + idx + "}"].join("\n");
                  }
                }
              }
            },
            {//名称
              type: 'category',
              offset: -10,
              position: "left",
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                color: '#333',
                align: "left",
                verticalAlign: "bottom",
                lineHeight: 32,
                fontSize: 12
              },
              data: xAxisData
            },
          ],
          series: [
            {
              zlevel: 1,
              type: "bar",
              barWidth: 16,
              data: rankBarColor(seriesData),
              itemStyle: {
                normal: {
                  barBorderRadius: 30
                }
              },
              label: {
                show: true,
                fontSize: 12,
                color: "#fff"
              }
            },
            {
              type: "bar",
              barWidth: 16,
              barGap: "-100%",
              itemStyle: {
                normal: {
                  barBorderRadius: 30,
                  color: 'rgba(0,0,0,0.04)'
                }
              },
              data: maxSeriesData
            }
          ]
        }}
        ref={rootRef}
        onEvents={{
          'click': (param: any) => { }
        }}
      />
    </div>
  </>
}

export default Index;
