import React, {useContext, useRef} from 'react'
import { Typo<PERSON>, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { IconArrowDownRight } from '@douyinfe/semi-icons';
import EChartsReact from 'echarts-for-react';
import { getEchartsTheme } from '@/utils/common';
import styles from '../index.module.scss'
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const { Title } = Typography;

const Index: React.FC = () => {
  const globalTheme = useContext(GlobalThemeContext);
    const rootRef = useRef<EChartsReact>(null)
    return <>
        <div className={styles.chartPanel}>
            <Title heading={6} className={styles.panelTitle}>
                <IconArrowDownRight className={styles.panelIcon}></IconArrowDownRight>
                <a>设备类型</a>
            </Title>
            <EChartsReact
                style={{ height: '400px' }}
                theme={getEchartsTheme(globalTheme)}
                option={{
                  tooltip: {
                    trigger: 'item'
                  },
                  legend: {
                    bottom: 20,
                    left: 'center'
                  },
                  grid: {
                    top: 80
                  },
                  series: [
                    {
                      name: '设备类型',
                      type: 'pie',
                      radius: ['40%', '70%'],
                      avoidLabelOverlap: false,
                      itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                      },
                      label: {
                        show: false,
                        position: 'center'
                      },
                      emphasis: {
                        label: {
                          show: true,
                          fontSize: 40,
                          fontWeight: 'bold'
                        }
                      },
                      labelLine: {
                        show: false
                      },
                      data: [
                        { value: 1048, name: '华为路由器' },
                        { value: 735, name: '小米路由' },
                        { value: 580, name: 'TP Link路由' },
                        { value: 484, name: '华三路由' },
                        { value: 300, name: '中兴路由' }
                      ]
                    }
                  ]
                }}
                ref={rootRef}
                onEvents={{
                    'click': (param: any) => { }
                }}
            />
        </div>
    </>
}

export default Index;
