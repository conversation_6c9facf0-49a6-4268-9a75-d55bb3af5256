import React, { useContext, useRef } from 'react'
import { Typo<PERSON>, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { IconArrowDownRight } from '@douyinfe/semi-icons';
import { useNavigate } from 'react-router-dom';
import { getEchartsTheme } from '@/utils/common';
import EChartsReact from 'echarts-for-react';
import styles from '../index.module.scss'
import { graphic } from 'echarts';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
const { Title } = Typography;

const Index: React.FC = () => {
  const navigate = useNavigate();
  const globalTheme = useContext(GlobalThemeContext);
  const rootRef = useRef<EChartsReact>(null)
  return <>
    <div className={styles.chartPanel}>
      <Title heading={6} className={styles.panelTitle}>
        <IconArrowDownRight className={styles.panelIcon}></IconArrowDownRight>
        <a >带宽汇总</a>
      </Title>

      <EChartsReact

        style={{ height: 400 }}
        theme={getEchartsTheme(globalTheme)}
        option={{
          // color: ['#80FFA5', '#00DDFF', '#37A2FF', '#FF0087', '#FFBF00'],
          // title: {
          //   text: '带宽汇总'
          // },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                // backgroundColor: '#6a7985'
              }
            }
          },
          legend: {
            top: 40,
            data: ['上行带宽', '下行带宽']
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          grid: {
            top: 80,
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '上行带宽',
              type: 'line',
              stack: 'Total',
              smooth: true,
              lineStyle: {
                width: 0
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(0, 221, 255)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(77, 119, 255)'
                  }
                ])
              },
              emphasis: {
                focus: 'series'
              },
              data: [120, 282, 111, 234, 220, 340, 310]
            },
            {
              name: '下行带宽',
              type: 'line',
              stack: 'Total',
              smooth: true,
              lineStyle: {
                width: 0
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(55, 162, 255)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(116, 21, 219)'
                  }
                ])
              },
              emphasis: {
                focus: 'series'
              },
              data: [320, 132, 201, 334, 190, 130, 220]
            },

          ]
        }}
        ref={rootRef}
        onEvents={{
          'click': (param: any) => { }
        }}
      />
    </div>
  </>
}

export default Index;
