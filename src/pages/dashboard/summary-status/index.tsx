import React, { useContext, useRef } from 'react'
import { Typo<PERSON>, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { IconArrowDownRight } from '@douyinfe/semi-icons';
import EChartsReact from 'echarts-for-react';
import { getEchartsTheme } from '@/utils/common';
import styles from '../index.module.scss'
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const { Title } = Typography;

const Index: React.FC = () => {
  const globalTheme = useContext(GlobalThemeContext);
  const rootRef = useRef<EChartsReact>(null)

  return <>
    <div className={styles.chartPanel}>
      <Title heading={6} className={styles.panelTitle}>
        <IconArrowDownRight className={styles.panelIcon}></IconArrowDownRight>
        <a>状态信息</a>
      </Title>
      <EChartsReact
      theme={getEchartsTheme(globalTheme)}
        style={{ height: '400px' }}
        option={{
          // title: {
          //   text: 'Stacked Line'
          // },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            top: 40,
            data: ['设备在线数', 'CPU平均负载', 'CPU最大负载', '内存平均负载', '内存最大负载']
          },
          grid: {
            top:80,
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '设备在线数',
              type: 'line',
              stack: 'Total',
              data: [120, 132, 101, 134, 90, 230, 210]
            },
            {
              name: 'CPU平均负载',
              type: 'line',
              stack: 'Total',
              data: [220, 182, 191, 234, 290, 330, 310]
            },
            {
              name: 'CPU最大负载',
              type: 'line',
              stack: 'Total',
              data: [150, 232, 201, 154, 190, 330, 410]
            },
            {
              name: '内存平均负载',
              type: 'line',
              stack: 'Total',
              data: [320, 332, 301, 334, 390, 330, 320]
            },
            {
              name: '内存最大负载',
              type: 'line',
              stack: 'Total',
              data: [820, 932, 901, 934, 1290, 1330, 1320]
            }
          ]
        }}
        ref={rootRef}
        onEvents={{
          'click': (param: any) => { }
        }}
      />
    </div>
  </>
}

export default Index;
