import { useEffect, useState } from 'react';
import { Tabs, TabPane, Space } from '@douyinfe/semi-ui';

import EchartsBrandwidth from '../echarts/brandwidth';
import EchartsDeviceLoad from '../echarts/device-load';
import EchartsConnectionOnline from '../echarts/connection-online';
import styles from './index.module.scss'
import { Device } from '../device';

const useTable = () => {
    // 设备列表
    const [devices, setDevices] = useState<Device[]>([]);

    // 设备表格列
    const columns = [
        {
            title: '设备名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '设备别名',
            dataIndex: 'alias',
            key: 'alias',
        },
        {
            title: '归属地',
            dataIndex: 'location',
            key: 'location',
        },
        {
            title: '连接数',
            dataIndex: 'connection',
            key: 'connection',
        },
        {
            title: '操作',
            key: 'action',
            render: () => (
                <Space>
                    <a>编辑</a>
                    <a>删除</a>
                </Space>
            ),
        },
    ];

    useEffect(() => {
        // mock 设备列表
        const mockDevices: Device[] = [
            {
                name: '设备1',
                alias: '设备1别名',
                location: '北京',
                connection: 10
            },
            {
                name: '设备2',
                alias: '设备2别名',
                location: '上海',
                connection: 20
            },
            {
                name: '设备3',
                alias: '设备3别名',
                location: '广州',
                connection: 30
            }
        ];

        setDevices(mockDevices);
    }, [])

    const expandRowRender = (record?: Device) => {
        if (!record) return;
        return (
            <Tabs size='small'>
                <TabPane tab="流量" itemKey='1'>
                    <div className={styles.chartRow}>
                        <EchartsDeviceLoad title='设备负载' />
                        <EchartsConnectionOnline title='连接及在线数' />
                        <EchartsBrandwidth title='主口流量' />
                        <EchartsBrandwidth title='全部接口流量和' />
                    </div>
                </TabPane>
                <TabPane tab="网口" itemKey='2'></TabPane>
                <TabPane tab="BackUp备份" itemKey='3'></TabPane>
                <TabPane tab="Rsc备份" itemKey='4'></TabPane>
                <TabPane tab="故障" itemKey='5'></TabPane>
                <TabPane tab="硬件" itemKey='6'></TabPane>
                <TabPane tab="IP" itemKey='7'></TabPane>
                <TabPane tab="ARP" itemKey='8'></TabPane>
                <TabPane tab="DNS" itemKey='9'></TabPane>
                <TabPane tab="DHCP" itemKey='10'></TabPane>
                <TabPane tab="路由" itemKey='11'></TabPane>
                <TabPane tab="限速" itemKey='12'></TabPane>
                <TabPane tab="防火墙" itemKey='13'></TabPane>
                <TabPane tab="Nat" itemKey='14'></TabPane>
                <TabPane tab="Radius" itemKey='15'></TabPane>
                <TabPane tab="PPP" itemKey='16'></TabPane>
                <TabPane tab="无线网卡" itemKey='17'></TabPane>
                <TabPane tab="无线客户端" itemKey='18'></TabPane>
                <TabPane tab="服务" itemKey='19'></TabPane>
                <TabPane tab="管理员" itemKey='20'></TabPane>
            </Tabs>
        );
    }

    return {
        columns,
        devices,
        expandRowRender
    }
}

export default useTable;
