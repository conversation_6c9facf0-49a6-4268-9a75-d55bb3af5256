import React, {useContext, useRef} from 'react'
import { Typo<PERSON>, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin } from '@douyinfe/semi-ui';
import { IconArrowDownRight } from '@douyinfe/semi-icons';
import EChartsReact from 'echarts-for-react';
import { getEchartsTheme } from '@/utils/common';
import styles from '../index.module.scss'
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const { Title } = Typography;

const Index: React.FC = () => {
  const globalTheme = useContext(GlobalThemeContext);
    const rootRef = useRef<EChartsReact>(null)
    return <>
        <div className={styles.chartPanel}>
            <Title heading={6} className={styles.panelTitle}>
                <IconArrowDownRight className={styles.panelIcon}></IconArrowDownRight>
                <a>接口终端数量Top5</a>
            </Title>
            <EChartsReact
                style={{ height: '400px' }}
                theme={getEchartsTheme(globalTheme)}
                option={{
                  tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                      type: 'shadow'
                    }
                  },
                  legend: {
                    top: 40,
                    data: ['ARP终端数量', 'Wifi终端数量', '全部终端数量']
                  },
                  grid: {
                    top: 80,
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                  },
                  xAxis: [
                    {
                      type: 'value'
                    }
                  ],
                  yAxis: [
                    {
                      type: 'category',
                      axisTick: {
                        show: false
                      },
                      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    }
                  ],
                  series: [
                    {
                      name: 'ARP终端数量',
                      type: 'bar',
                      label: {
                        show: true,
                        position: 'inside'
                      },
                      emphasis: {
                        focus: 'series'
                      },
                      data: [200, 170, 240, 244, 200, 220, 210]
                    },
                    {
                      name: 'Wifi终端数量',
                      type: 'bar',
                      stack: 'Total',
                      label: {
                        show: true
                      },
                      emphasis: {
                        focus: 'series'
                      },
                      data: [320, 302, 341, 374, 390, 450, 420]
                    },
                    {
                      name: '全部终端数量',
                      type: 'bar',
                      stack: 'Total',
                      label: {
                        show: true,
                        position: 'left'
                      },
                      emphasis: {
                        focus: 'series'
                      },
                      data: [-120, -132, -101, -134, -190, -230, -210]
                    }
                  ]
                }}
                ref={rootRef}
                onEvents={{
                    'click': (param: any) => { }
                }}
            />
        </div>
    </>
}

export default Index;
