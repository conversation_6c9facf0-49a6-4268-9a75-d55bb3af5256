import React, { useState, useEffect, useContext, useRef } from 'react'
import { Typography, Card, Row, Col, SplitButtonGroup, Button, Dropdown, Skeleton, Notification, Spin, Space } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useNavigate } from 'react-router-dom';

import { BASE_PATH } from '@/constants/router';
import { useLocale } from '@/locales';

import SummaryBrandwidth from './summary-bandwidth';
import SummaryConnections from './summary-connections';
import SummaryDeviceType from './summary-device-type';
import SummaryInterfaces from './summary-interfaces';
import SummaryLogs from './summary-logs';
import SummaryResources from './summary-resources';
import SummaryStatus from './summary-status';


import styles from './index.module.scss'
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const navigate = useNavigate();

    return <>
        <div className='general-page'>
            <Row className='mb10'>
                <Col span={24}>
                    <div className='btn-right-col'>
                        <Space>
                            <Button onClick={() => navigate(`${BASE_PATH}/dashboard/device-info`)}>{formatMessage({ id: 'dashboard.deviceManagement' })}</Button>
                            <Button onClick={() => navigate(`${BASE_PATH}/dashboard/device-network`)}>{formatMessage({ id: 'dashboard.networkTopology' })}</Button>
                            <Button onClick={() => navigate(`${BASE_PATH}/dashboard/monitor-interface`)}>{formatMessage({ id: 'dashboard.physicalPorts' })}</Button>
                            <Button onClick={() => navigate(`${BASE_PATH}/dashboard/monitor-device`)}>{formatMessage({ id: 'dashboard.deviceMonitoring' })}</Button>
                        </Space>
                    </div>
                </Col>
            </Row>
            <div className={styles.dashboard}>
                <Row gutter={16} className='mb20'>
                    <Col xs={24} sm={12} className={styles.chartCol}>
                        <Card bodyStyle={{ padding: 0 }}>
                            <SummaryBrandwidth />
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} className={styles.chartCol}>
                        <Card bodyStyle={{ padding: 0 }}>
                            <SummaryStatus />
                        </Card>
                    </Col>
                </Row>
                <Row gutter={16} className='mb20'>
                    <Col span={24} className={styles.chartCol}>
                        <Card bodyStyle={{ padding: 0 }}>
                            <SummaryConnections />
                        </Card>
                    </Col>
                </Row>
                <Row gutter={16} className='mb20'>
                    <Col xs={24} sm={12} className={styles.chartCol}>
                        <Card bodyStyle={{ padding: 0 }}>
                            <SummaryResources />
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} className={styles.chartCol}>
                        <Card bodyStyle={{ padding: 0 }}>
                            <SummaryDeviceType />
                        </Card>
                    </Col>
                </Row>
                <Row gutter={16} className='mb20'>
                    <Col span={24} className={styles.chartCol}>
                        <Card bodyStyle={{ padding: 0 }}>
                            <SummaryInterfaces />
                        </Card>
                    </Col>
                </Row>

                <Row gutter={16} className='mb20'>
                    <Col span={24} className={styles.chartCol}>
                        <Card bodyStyle={{ padding: 0 }}>
                            <SummaryLogs />
                        </Card>
                    </Col>
                </Row>
            </div>
        </div>
    </>
}

export default Index;