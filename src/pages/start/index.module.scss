
.card {
    // max-width: 890px;
    // padding-top: 40px;
    margin: 0 auto!important;
}
.cardMobile {
    padding-top: 80px;
    margin: 0 auto!important;
    box-shadow: none !important;
    border-radius: 0 !important;
    border-left: 0 !important;
    border-right: 0 !important;

}
.appContainer {
    padding: 0 32px;
}
.app {
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    cursor: pointer;
    .icon {
        width: 64px;
        height: 64px;
        font-size: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: var(--semi-border-radius-small);
        background-color: var(--semi-color-fill-0);
        box-shadow: var(--semi-shadow-2);
        margin-bottom: 10px;
        img {
            max-width: 32px;
            max-height: 32px;
        }
        [role="img"] {
            font-size: 32px!important;
            color: var(--semi-color-text-2)!important;
        }
    }
    .text {
        color: var(--semi-color-text-2);
    }
}
.app:hover {
    .icon {
        border-radius: var(--semi-border-radius-medium);

        box-shadow: var(--semi-shadow-elevated);
        
    }
    .text {
        color: var(--semi-color-text-1);
    }

}

.filter {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 20px;
    .filterTitle {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
    }
    .filterBar {
        max-width: 800px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.spin {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}