import React, { useEffect, useState, useContext } from 'react';
import { List, Layout, Typography, Divider, Input, Spin } from '@douyinfe/semi-ui';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { Application, ApplicationIconType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/application_pb";
import useIcons from '../services/application/useIcons';
import { IconSearch } from '@douyinfe/semi-icons';

import {
    isDesktop
} from 'react-device-detect';
import styles from './index.module.scss'
import { flylayerClient } from '@/services/core';
const { Text, Title, Paragraph } = Typography;
const { Header, Content } = Layout;
const Index: React.FC = () => {
    const flynetGeneral = useContext(FlynetGeneralContext);

    const { getIcon } = useIcons();

    const [allApplications, setAllApplications] = useState<Application[]>([]);

    const [applications, setApplications] = useState<Application[]>([]);

    const [filter, setFilter] = useState<string>('');

    const [loading, setLoading] = useState<boolean>(true)

    const [tags, setTags] = useState<{
        value: string,
        label: string
    }[]>([]);


    const [allTags, setAllTags] = useState<{
        value: string,
        label: string
    }[]>([]);

    const calTags = (applications: Application[], allTags: {
        value: string,
        label: string
    }[]) => {
        const tags = new Set<{
            value: string,
            label: string
        }>();
        allTags.forEach((item) => {
            let find = false;
            applications.forEach((app) => {
                if (app.group?.name === item.value) {
                    find = true;
                }
            });
            if (find) {
                tags.add(item);
            }

        })
        return Array.from(tags);


    };

    const doFilter = (filter: string, src: Application[]) => {
        if (filter === '') {
            setApplications(src);
            setTags(calTags(src, allTags));
            return;
        }

        const list = src.filter((item) => {
            return item.name.includes(filter) || item.url.includes(filter);
        });

        setApplications(list);
        setTags(calTags(list, allTags));


    }



    useEffect(() => {
        flylayerClient.listUserApplications({
            flynetId: flynetGeneral.id
        }).then((res) => {
            const apps = res.applications;
            setAllApplications(apps);
            setApplications(apps);
            flylayerClient.listApplicationGroups({
                flynetId: flynetGeneral.id
            }).then((res) => {
                const tags = res.applicationGroups.map((item) => {
                    return {
                        value: item.name,
                        label: item.alias
                    }
                });

                setAllTags(tags);
                if (apps && apps.length > 0) {

                    setTags(calTags(apps, tags));
                }
                setLoading(false)
            }).catch((err) => {
                setLoading(false)
                console.error(err);
            });

        }).catch((err) => {
            console.error(err);
            setLoading(false);
        });
    }, [])


    return <>
        <div>
            <div className={styles.filter}>
                <div className={styles.filterTitle}>
                    <Title heading={3}>应用面板</Title>
                </div>
                <div className={styles.filterBar}>
                    <Input placeholder="根据名称、URL搜索" value={filter} onChange={val => {
                        setFilter(val)
                        doFilter(val.trim(), allApplications)
                    }} style={{ width: 400 }} suffix={<IconSearch />} showClear />
                </div>
            </div>

        </div>
        <div style={{ cursor: 'default' }} className={isDesktop ? styles.card : styles.cardMobile}>
            {loading ? <div className={styles.spin}> <Spin size='large'></Spin></div> : tags.map((tag, tagIndex) => {
                return <div key={tagIndex}>
                    <Divider align='left' className='mb20'><Text size='small' type='tertiary'>{tag.label}</Text></Divider>
                    <div className={styles.appContainer}>
                        <List
                            grid={{
                                gutter: 32,
                            }}
                            dataSource={applications.filter((item) => item.group?.name === tag.value)}
                            renderItem={(item, index) => {


                                return <List.Item key={index} onClick={() => {
                                    let url = item.url;
                                    if (!item.url.startsWith('http') && !item.url.startsWith('https')) {
                                        url = `http://${url}`;
                                    }
                                    window.open(url, '_blank')
                                }}>
                                    <div className={styles.app}>
                                        <div className={styles.icon}>
                                            {item.iconType == ApplicationIconType.NAME ?
                                                getIcon(item.icon)
                                                : <img src={item.icon} style={{ width: 40, height: 40 }} />
                                            }
                                        </div>
                                        <div className={styles.text}>
                                            {item.name}
                                        </div>
                                    </div>
                                </List.Item>
                            }}
                        />
                    </div>
                </div>
            })}

        </div>
    </>
}

export default Index;