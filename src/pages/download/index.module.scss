.logo {
    cursor: pointer;
    width: 190px;
    margin-left: -10px;
    display: flex;
    align-items: center;
    img {
        width: 40x;
        height: 40px;
    }
    span {
        font-family: Mija,-apple-system,BlinkMacSystemFont,Robot<PERSON>,roboto slab,droid serif,segoe ui,system-ui,<PERSON><PERSON>,sans-serif;
        margin-left: 10px;
        color: var(--semi-color-text-1);
    }
}

.layout {
    height: 100vh;
}
.content {
    min-height: calc(100vh);
}

.title {
    text-align: center;
    padding-top: 40px;
    padding-bottom: 40px;
}
.card {
    max-width: 690px;
    margin: 0 auto!important;
}
.cardMobile {
    margin: 0 auto!important;
    box-shadow: none !important;
    border-radius: 0 !important;
    border-left: 0 !important;
    border-right: 0 !important;

}
.navLink {
    font-size: 14px;
    margin-right: 20px;
    color: var(--semi-color-text-2);
    min-width: 70px;
}
.wechatMask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    background: rgba(0, 0, 0, 0.6);
    text-align: end;
    overflow: hidden;
    padding: 1rem 5px 0 1rem;
    img {
        width: 93%;
        height: auto;
    }
}