import React, { useEffect, useState } from 'react';
import { Card, Layout, Nav, Typography, Tooltip } from '@douyinfe/semi-ui';
import Download from '@/components/download';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { IconDownload, IconHelpCircle, IconColorPalette, IconMoon, IconSun, IconBox } from '@douyinfe/semi-icons';
import logo from '@/assets/logo.png';
import wechatArrow from '@/assets/wechat-arrow.png';
import styles from './index.module.scss'
import UserIndicator from '@/components/user-indicator';
import useFlynetGeneral, { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { useGlobalTheme, GlobalThemeContext, GlobalTheme } from '@/hooks/useGlobalTheme';

import useGlobalConfig, { GlobalConfigContext } from '@/hooks/useGlobalConfig'
import {
    isDesktop
} from 'react-device-detect';
import useUserProfile, { UserProfileContext } from '@/hooks/useUserProfile';
import axios from 'axios';
const { Title, Paragraph } = Typography;
const { Header, Content } = Layout;
const Index: React.FC = () => {

    const { flynetGeneral } = useFlynetGeneral();
    const navigate = useNavigate()
    // 用户登录信息
    const { userProfile } = useUserProfile();
    const [showConsole, setShowConsole] = React.useState(false);
    const { colorMode, theme, setTheme, switchColorMode } = useGlobalTheme();

    useEffect(() => {
        axios.post(`/api${BASE_PATH}/flylayer.v1.FlylayerService/ListFlynets`, {}).then(res => {
            setShowConsole(true)
            
        }
        ).catch(err => {
            setShowConsole(false)
        })
    }, [])

    const ua = window.navigator.userAgent.toLowerCase();
    const matchRes = ua.match(/MicroMessenger/i);
    const isWechat = matchRes && matchRes[0] === 'micromessenger';

    // 全局配置信息
    const { isGlobalConfigQuery, globalConfig } = useGlobalConfig();
    // 主题切换对话框是否显示
    const [themeSwitchVisible, setThemeSwitchVisible] = useState(false)


    return <>{!isGlobalConfigQuery && <Layout className={styles.layout}>
        <Header>
            <div>
                <Nav mode="horizontal" selectedKeys={['download']}>
                    <Nav.Header>
                        <div className={styles.logo} onClick={() => navigate(`${BASE_PATH}`)}><img src={globalConfig.logo}></img><span>{globalConfig.name}</span></div>
                    </Nav.Header>
                    {flynetGeneral.applicationEnabled && <Nav.Item itemKey="/start" onClick={() => navigate(`${BASE_PATH}/start`)} text="应用面板" icon={<IconBox />} />}

                    <Nav.Item itemKey="download" onClick={() => navigate(`${BASE_PATH}/download`)} text="下载" icon={<IconDownload />} />

                    <Nav.Footer>
                        {showConsole && (flynetGeneral.userRole == 'USER_ROLE_SUPER_ADMIN' || flynetGeneral.userRole == 'USER_ROLE_FLYNET_ADMIN' || flynetGeneral.userRole == 'USER_ROLE_FLYNET_OWNER') ? <a href={`${BASE_PATH}`} className={styles.navLink}>管理控制台</a> : ''}
                        {!globalConfig.customized && <Tooltip content={'查看帮助文档'}><a href={`/docs`} title='文档' target='_blank' className={styles.navIcon}><IconHelpCircle size='large' title='文档' /></a></Tooltip>}
                        <Tooltip content="选择主题">
                            <a className={styles.navIcon} onClick={() => setThemeSwitchVisible(true)}>
                                <IconColorPalette size='large' />
                            </a>
                        </Tooltip>
                        <Tooltip content={colorMode == 'light' ? '切换到暗色模式' : '切换到亮色模式'}>
                            <a className={styles.navIcon} onClick={() => switchColorMode()}>
                                {colorMode == 'light' ? <IconMoon size='large' /> : <IconSun size='large' />}
                            </a>
                        </Tooltip>
                        <UserProfileContext.Provider value={userProfile}>

                            <UserIndicator></UserIndicator>
                        </UserProfileContext.Provider>
                    </Nav.Footer>
                </Nav>
            </div>
        </Header>
        <Content
            className={styles.content}
        >
            <div className={styles.title}>
                <Title heading={2}>下载{globalConfig.name}</Title>
                <Paragraph>安装应用并登录以开始使用{globalConfig.name}</Paragraph>
            </div>
            <Card shadows='always' style={{ cursor: 'default' }} className={isDesktop ? styles.card : styles.cardMobile}>
                <GlobalConfigContext.Provider value={globalConfig}>
                    <FlynetGeneralContext.Provider value={flynetGeneral}>
                        <Download navOnTab={true} hiddenTab={true} />
                    </FlynetGeneralContext.Provider>
                </GlobalConfigContext.Provider>
            </Card>
        </Content>

    </Layout>}
        {isWechat ? <div className={styles.wechatMask}>
            <img src={wechatArrow} />
        </div> : ''}

    </>

}
export default Index;
