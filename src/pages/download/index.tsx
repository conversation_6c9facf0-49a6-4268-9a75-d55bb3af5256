import React, { useEffect, useContext } from 'react';
import { Card, Typography } from '@douyinfe/semi-ui';
import Download from '@/components/download';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import logo from '@/assets/logo.png';
import wechatArrow from '@/assets/wechat-arrow.png';
import styles from './index.module.scss';
import useFlynetGeneral, { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'

import { GlobalConfigContext } from '@/hooks/useGlobalConfig'
import {
    isDesktop
} from 'react-device-detect';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { useLocale } from '@/locales';
import axios from 'axios';
const { Title, Paragraph } = Typography;
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const { flynetGeneral } = useFlynetGeneral();


    const ua = window.navigator.userAgent.toLowerCase();
    const matchRes = ua.match(/MicroMessenger/i);
    const isWechat = matchRes && matchRes[0] === 'micromessenger' && ua.indexOf('wxwork') < 0;

    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);

    return <><div className={styles.title}>
        <Title heading={2}>{formatMessage({ id: 'download.title' }).replace('{name}', globalConfig.name)}</Title>
        <Paragraph>{formatMessage({ id: 'download.description' }).replace('{name}', globalConfig.name)}</Paragraph>
    </div>
        <Card shadows='always' style={{ cursor: 'default' }} className={isDesktop ? styles.card : styles.cardMobile}>
            <GlobalConfigContext.Provider value={globalConfig}>
                <FlynetGeneralContext.Provider value={flynetGeneral}>

                    <Download navOnTab={true} hiddenTab={true} />
                </FlynetGeneralContext.Provider>
            </GlobalConfigContext.Provider>
        </Card>
        {isWechat ? <div className={styles.wechatMask}>
            <img src={wechatArrow} />
        </div> : ''}

    </>

}
export default Index;
