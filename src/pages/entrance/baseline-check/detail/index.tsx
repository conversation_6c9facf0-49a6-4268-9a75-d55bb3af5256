import React, { useState, useEffect } from "react";
import { useParams } from 'react-router-dom';
import { Notification } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { BaselineCheck } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

import BaselineCheckDetail from '@/pages/entrance/components/baseline-check-detail';

import styles from './index.module.scss'

interface Props {

}

const Index: React.FC<Props> = (props) => {

    const params = useParams<{ id: string }>()
    const id = params.id ? params.id : '';

    const [loading, setLoading] = useState(true);

    const [baselineCheck, setBaselineCheck] = useState<BaselineCheck | undefined>();

    const queryBaselineCheck = async (id: bigint) => {
        try {
            const res = await flylayerClient.getBaselineCheck({
                id: id,
            });
            if (res.baselineCheck) {
                setBaselineCheck(res.baselineCheck);
            }

        } catch (e) {
            Notification.error({
                title: '获取基线检测项配置失败',
                content: e,
            });
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        if (id) {
            queryBaselineCheck(BigInt(id));
        }
    }, [id])

    return <>
        <BaselineCheckDetail 
            loading={loading}
            baselineCheck={baselineCheck}
            useConfig={true}
            queryBaselineCheck={() => queryBaselineCheck(BigInt(id))}
        ></BaselineCheckDetail>
    </>


}

export default Index;