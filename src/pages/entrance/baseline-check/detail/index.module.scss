// 子网显示样式
.subnet {
    border-radius: var(--semi-border-radius-medium);
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-fill-0);
    padding: 20px;
}

.machineDetail {
    // box-shadow: var(--semi-shadow-elevated);
    background-color: var(--semi-color-bg-2);
    border-radius: 4px;
    padding: 10px;
    flex-grow: 1;
}

.heading {
    display: flex;
    align-items: flex-start;
}
.headingBadge {
    display: flex;
    width: 12px;
    height: 12px;
    margin-top: 10px;
    margin-left: 4px;
}
.generalInfo {
    margin-bottom: 40px;
    
    tr:last-of-type {
        padding-left: 10px;
        border-left: 1px solid var(--semi-color-border);
    }
}
.generalInfoNoStatus{
    margin-bottom: 40px;
    tr:last-of-type {
        padding-left: 11px;
    }
}


.tableTitle {
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 8px;
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
        line-height: 32px;
    }
}

.subTableTitle {
    >div {
        padding-right: 8px;
    }
}

.tableBody {
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 10px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.tableBody:last-of-type {
    border-bottom: none!important;
}

.tableBodyError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);

    padding-top: 5px;
    padding-bottom: 5px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.subTableBody {
    margin-bottom: 10px;
    >div {
        padding-right: 8px;
    }

}