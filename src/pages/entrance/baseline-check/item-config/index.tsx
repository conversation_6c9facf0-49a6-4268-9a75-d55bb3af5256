import React, { useContext, useState } from "react";
import { Modal, Notification } from "@douyinfe/semi-ui";
import { BaselineCheck, BaselineCheckItem, BaselineCheck_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import ItemEdit from "@/pages/entrance/baseline-check/item-edit";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from "@/services/core";
import styles from './index.module.scss'

interface Props {
    index: number;
    type: BaselineCheck_Type;
    item: BaselineCheckItem;
    baselineCheck: BaselineCheck;
    close: () => void,
    success?: () => void,
}


const Index: React.FC<Props> = (props) => {

    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const [item, setItem] = useState<BaselineCheckItem>(props.item);

    const [itemErr, setItemErr] = useState(false);
    const [validateFlag, setValidateFlag] = useState(false);

    const flynet = useContext(FlynetGeneralContext);

    const handleSubmit = async () => {
        setSaveLoading(true);
        let baselineCheck = props.baselineCheck;
        baselineCheck.items[props.index] = item;
        flylayerClient.updateBaselineCheck({
            baselineCheck: baselineCheck,
            flynetId: flynet.id
        }).then((res) => {
            Notification.success({
                title: '配置成功',
                content: '检测项已保存'
            });
            props.success && props.success();
            props.close();
        }).catch((err) => {
            console.trace(err);
            Notification.error({
                title: '配置失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        });
    }
    return <><Modal
        title="配置检测项"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={800}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    ><ItemEdit osReadonly={true}  item={item} conditions={item.conditions} type={props.type} onChange={(item, conditions) => {
        let newItem = new BaselineCheckItem({ ...item, conditions: conditions });
        setItem(newItem);
    }} 
    itemErr={itemErr} setItemErr={setItemErr} validateFlag={validateFlag} setValidateFlag={setValidateFlag}
    /></Modal></>
}

export default Index;
