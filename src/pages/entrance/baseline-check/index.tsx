import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Layout, Input, Select } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { BaselineCheck, BaselineCheck_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IconSearch } from '@douyinfe/semi-icons';
import BaselineCheckItemConfig from './item-config';
import BaselineCheckItemDel from './item-del';
import New from './new';
import Edit from './edit';
import RegNew from './reg-new';
import RegEdit from './reg-edit';
import Del from './del';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import TableEmpty from '@/components/table-empty';
import GroupEdit from './group-edit';

import qs from 'query-string';

import styles from './index.module.scss'
import { BaselineCheckFilter } from './useTable';
import useTable from './useTable';
const { Title } = Typography;

const { Sider, Content } = Layout;
// 根据URL参数设置过滤参数
const getInitFilter = (location: Location): BaselineCheckFilter => {
    const query: string = getQueryParam('query', location) as string;
    const osQuery = getQueryParam('os', location);
    const tagQuery = getQueryParam('tag', location);
    const typeQuery = getQueryParam('type', location);

    let os: number[] = [];
    if (osQuery && Array.isArray(osQuery)) {
        osQuery.forEach(item => {
            if (item && typeof item == 'string') {
                os.push(parseInt(item));
            }

        })
    }
    if (osQuery && typeof osQuery == 'string') {
        os = [parseInt(osQuery)];
    }

    let tag: string[] = [];
    if (tagQuery && Array.isArray(tagQuery)) {
        tagQuery.forEach(item => {
            if (item && typeof item == 'string') {
                tag.push(item);
            }
        })
    }
    if (tagQuery && typeof tagQuery == 'string') {
        tag = [tagQuery];
    }

    return {
        query: query || '',
        os: os,
        tag: tag,
        type: typeQuery as string || '',
    }
}

const Index: React.FC = () => {
    const navigate = useNavigate();
    const [createVisible, setCreateVisible] = useState(false);
    const [regCreateVisible, setRegCreateVisible] = useState(false);

    const initFilter: BaselineCheckFilter = getInitFilter(useLocation());

    const [groupEditVisible, setGroupEditVisible] = useState(false);

    // 过滤参数改变时跳转路由
    const doNavigate = (filter: BaselineCheckFilter) => {
        let query = '';
        if (filter.query || filter.os.length > 0 || filter.tag.length > 0 || filter.type) {
            query = qs.stringify(filter, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/policies/entrance/baseline-check?${query}`)
        } else {
            navigate(`${BASE_PATH}/policies/entrance/baseline-check`)
        }
    }

    const {
        columns,
        baselineChecks,
        baselineTags,
        loading,
        filterParam,
        setFilterParam,
        doFilter,
        expandedRowRender,
        selectedBaseline,
        setSelectedBaseline,
        editVisible,
        setEditVisible,
        regEditVisible,
        setRegEditVisible,
        delVisible,
        setDelVisible,
        reload,
        configVisible,
        setConfigVisible,
        selectedBaselineItem,
        setSelectedBaselineItem,
        selectedBaselineItemIndex,
        setSelectedBaselineItemIndex,
        itemDelVisible,
        setItemDelVisible,
    } = useTable(initFilter);

    const listOs = [
        { value: OS.MACOS, label: 'macOS' },
        { value: OS.IOS, label: 'iOS' },
        { value: OS.WINDOWS, label: 'Windows' },
        { value: OS.LINUX, label: 'Linux' },
        { value: OS.ANDROID, label: 'Android' },
    ];

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, query: value })
        doNavigate({ ...filterParam, query: value });
    }
    const handleOsChange = (value: any) => {
        setFilterParam({ ...filterParam, os: value })
        doNavigate({ ...filterParam, os: value });
    }
    const handleTagChange = (value: any) => {
        setFilterParam({ ...filterParam, tag: value })
        doNavigate({ ...filterParam, tag: value });
    }
    const handleTypeChange = (value: any) => {
        setFilterParam({ ...filterParam, type: value })
        doNavigate({ ...filterParam, type: value });
    }

    return <><div className='general-page'>
        <Breadcrumb routes={
            [
                {
                    path: `${BASE_PATH}/policies/entrance`,
                    href: `${BASE_PATH}/policies/entrance`,
                    name: '设备准入策略'
                },
                {
                    name: '基线检测项配置',
                }
            ]
        }>
        </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>基线检测项配置</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    {/* <Button onClick={() => setGroupEditVisible(true)}>分类管理</Button> */}
                    {/* <Button theme='solid'
                        onClick={() => setRegCreateVisible(true)}>新建注册表检测项</Button> */}
                    {/* <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>新建内置检测项</Button> */}
                </Space>
            </div></Col>
        </Row>
        <Layout className='mb20 search-bar'>
            <Layout>
                <Content className='pr10'>
                    <Input value={filterParam.query}
                        onChange={handleQueryChange}
                        style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={'根据名称、描述搜索'}></Input>
                </Content>
                <Sider> <Space>

                    <Select multiple
                        maxTagCount={1}
                        style={{ width: 200 }}
                        optionList={listOs}
                        insetLabel="操作系统"
                        onChange={handleOsChange}
                        value={filterParam.os}></Select>
                    {/* <Select multiple
                        maxTagCount={1}
                        style={{ width: 200 }}
                        optionList={baselineTags}
                        insetLabel="分类"
                        onChange={handleTagChange}
                        value={filterParam.tag}></Select>
                    <Select
                        style={{ width: 200 }}
                        insetLabel="类型"
                        onChange={handleTypeChange}
                        value={filterParam.type}>
                        <Select.Option value=''>全部</Select.Option>
                        <Select.Option value={BaselineCheck_Type[BaselineCheck_Type.BUILD_IN]}>内置检测项</Select.Option>
                        <Select.Option value={BaselineCheck_Type[BaselineCheck_Type.REGEX]}>注册表检测项</Select.Option>
                    </Select> */}
                </Space></Sider>
            </Layout>

        </Layout>

        <Table
            rowKey={(record?: BaselineCheck) => record ? record.id + '' : ''}
            loading={loading}
            columns={columns}
            dataSource={baselineChecks}
            pagination={false}
            // expandedRowRender={expandedRowRender}
            empty={<TableEmpty loading={loading}></TableEmpty>}
        ></Table>
    </div>
        {createVisible && <New
            close={() => setCreateVisible(false)}
            success={() => {
                setCreateVisible(false);
                reload();
            }}
        />}
        {regCreateVisible && <RegNew
            close={() => setRegCreateVisible(false)}
            success={() => {
                setRegCreateVisible(false);
                reload();
            }}
        />}
        {editVisible && selectedBaseline && <Edit
            id={selectedBaseline.id}
            close={() => {
                setEditVisible(false)
                setSelectedBaseline(undefined);
            }}
            success={() => {
                setEditVisible(false);
                setSelectedBaseline(undefined);
                reload();
            }}
        />}
        {regEditVisible && selectedBaseline && <RegEdit
            id={selectedBaseline.id}
            close={() => {
                setRegEditVisible(false)
                setSelectedBaseline(undefined);
            }}
            success={() => {
                setRegEditVisible(false);
                setSelectedBaseline(undefined);
                reload();
            }}
        />}
        {

        }
        {delVisible && selectedBaseline && <Del
            close={() => {
                setDelVisible(false);
                setSelectedBaseline(undefined);
            }}
            success={() => {
                setDelVisible(false);
                setSelectedBaseline(undefined);
                reload();
            }}
            record={selectedBaseline}
        />}
        {configVisible && selectedBaselineItem && selectedBaseline && <BaselineCheckItemConfig
            index={selectedBaselineItemIndex}
            type={selectedBaseline.type}
            item={selectedBaselineItem}
            baselineCheck={selectedBaseline}
            close={() => {
                setConfigVisible(false)
                setSelectedBaseline(undefined);
                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
            }}
            success={() => {
                setConfigVisible(false);
                setSelectedBaseline(undefined);
                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
                reload();
            }}
        />}
        {itemDelVisible && selectedBaselineItem && selectedBaseline && <BaselineCheckItemDel
            index={selectedBaselineItemIndex}
            close={() => {
                setItemDelVisible(false)
                setSelectedBaseline(undefined);
                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
            }}
            success={() => {
                setItemDelVisible(false);
                setSelectedBaseline(undefined);
                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
                reload();
            }}
            record={selectedBaselineItem}
            baselineCheck={selectedBaseline}
        />

        }
        {groupEditVisible && <GroupEdit
            close={() => setGroupEditVisible(false)}
            success={() => {
                setGroupEditVisible(false);
                reload();
            }}
        ></GroupEdit>}

    </>
}

export default Index;