import { useState, useContext, useEffect, useCallback } from 'react';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

import { Typography, Dropdown, Button, Tag, Popover, Row, Col, Notification, Space } from '@douyinfe/semi-ui';
import { BaselinePolicy, BaselineCheck, BaselineCheckItem, BaselineCheck_Type, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

import styles from './index.module.scss';

import { flylayerClient } from '@/services/core';
import { render } from 'react-dom';
import { caseInsensitiveIncludes } from '@/utils/common';

const { Title, Paragraph, Text } = Typography;

export type BaselineCheckFilter = {
    query: string;
    os: Array<number>,
    tag: Array<string>,
    type: string
}

const useTable = (initFilter: BaselineCheckFilter) => {
    const navigate = useNavigate();
    const flynet = useContext(FlynetGeneralContext);


    // 是否正在加载中
    const [loading, setLoading] = useState(false);
    const [filterParam, setFilterParam] = useState<BaselineCheckFilter>(initFilter)
    const [total, setTotal] = useState<number>(0);

    const [baselineChecks, setBaselineChecks] = useState<BaselineCheck[]>();
    const [allBaselineChecks, setAllBaselineChecks] = useState<BaselineCheck[]>();
    const [baselineTags, setBaselineTags] = useState<{
        value: string,
        label: string
    }[]>([]);


    const [editVisible, setEditVisible] = useState(false);
    const [regEditVisible, setRegEditVisible] = useState(false);
    const [configVisible, setConfigVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);
    const [itemDelVisible, setItemDelVisible] = useState(false);

    const [selectedBaseline, setSelectedBaseline] = useState<BaselineCheck>();
    const [selectedBaselineItem, setSelectedBaselineItem] = useState<BaselineCheckItem>();
    const [selectedBaselineItemIndex, setSelectedBaselineItemIndex] = useState<number>(-1);
    const query = () => {
        setLoading(true);

        flylayerClient.listBaselineCheck({
            flynetId: flynet.id
        }).then(res => {
            setAllBaselineChecks(res.baselineChecks);
            const list = doFilter(res.baselineChecks, filterParam);
            setBaselineChecks(list);
            const names: string[] = [];
            const tags = new Set<{
                value: string,
                label: string
            }>();
            res.baselineChecks.forEach(item => {
                if (!names.includes(item.group?.name || '')) {

                    tags.add({
                        value: item.group?.name || '',
                        label: item.group?.alias || ''
                    });
                    names.push(item.group?.name || '');
                }
            });
            setBaselineTags(Array.from(tags));
        }, err => {
            Notification.error({
                title: '获取基线检测项列表失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        })

    }

    // 重新加载数据
    const reload = () => {
        query();
    }
    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            render: (field: string, record: BaselineCheck, index: number) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                        <div>
                            <Title heading={6}>
                                {/* <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/policies/entrance/baseline-check/detail/${record.id}`), 10)}>
                                    {record.alias}
                                </a> */}
                                {record.alias}
                                {record.description &&
                                    <Popover content={<div className='p10'>{record.description}</div>}>
                                        <IconArticle style={{
                                            fontSize: 14,
                                            marginLeft: '4px',
                                            color: '#999'
                                        }} />
                                    </Popover>}
                            </Title>
                            <Paragraph size='small'>{record.name}</Paragraph>
                        </div>
                    </div>
                </>
            },
        },
        {
            title: '操作系统',
            dataIndex: 'os',
            key: 'os',
            render: (field: OS, record: BaselineCheck, index: number) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                                <Space>
                                {
                                    record.items.map((item, index) => {
                                        return <Tag key={index} >{OS[item.os]}</Tag>
                                    })
                                }
                            </Space>
                        
                    </div>
                </>
            }
        },
        // {
        //     title: '类型',
        //     dataIndex: 'type',
        //     render: (_: any, record: BaselineCheck) => {
        //         return <>{record.type == BaselineCheck_Type.REGEX ? '注册表检测项': '内置检测项'}</>
        //     }
        // },
        // {
        //     title: '分类',
        //     dataIndex: 'tag',
        //     render: (_: any, record: BaselineCheck) => {
        //         return <>{record.group?.alias}</>
        //     }
        // },
         {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            width: 100,
            render: (text: string, record: BaselineCheck) => {
                return <><div className='table-last-col' onClick={(e) => {
                    e.nativeEvent.preventDefault();
                    e.nativeEvent.stopPropagation();
                }}><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.preventDefault();
                                    e.nativeEvent.stopPropagation();

                                    if (record.type == BaselineCheck_Type.REGEX) {
                                        setRegEditVisible(true);
                                    } else {
                                        setEditVisible(true);
                                    }



                                    setSelectedBaseline(record);
                                }}
                            >编辑配置项</Dropdown.Item>
                            {/* <Divider />
                            <Dropdown.Item
                                onClick={(e) => {
                                    e.nativeEvent.stopPropagation();
                                    e.nativeEvent.stopPropagation();
                                    setDelVisible(true);
                                    setSelectedBaseline(record);
                                }}
                                type='danger'>删除策略</Dropdown.Item> */}

                        </Dropdown.Menu>}><Button onClick={(e) => { e.nativeEvent.stopPropagation() }}><IconMore className='align-v-center' /></Button>
                    </Dropdown>
                </div>
                </>;
            }
        }
    ];

    useEffect(() => {
        query();
    }, []);

    useEffect(() => {
        if (allBaselineChecks && allBaselineChecks.length > 0) {
            const list = doFilter(allBaselineChecks, filterParam);
            setBaselineChecks(list);
        }
    }, [filterParam]);

    // 过滤数据
    const doFilter = (src: Array<BaselineCheck>, baselineCheckFilter: BaselineCheckFilter) => {
        if (!src || src.length == 0) {
            setTotal(0);
            return src;
        }

        
        if (!baselineCheckFilter.query && baselineCheckFilter.os.length == 0 && baselineCheckFilter.tag.length == 0 && !baselineCheckFilter.type) {
            setTotal(src.length);
            return src;
        }

        const filteredList = src.filter(record => {
            let { query, os, tag } = baselineCheckFilter;
            if (query) query = query.trim();
            let passQuery = true;
            let passOs = true;
            let passTag = true;
            let passType = true;
            if (query) {
                passQuery = caseInsensitiveIncludes(record.name, query) || caseInsensitiveIncludes(record.alias, query) || caseInsensitiveIncludes(record.description, query);
            }

            if(baselineCheckFilter.type){
                passType = baselineCheckFilter.type == BaselineCheck_Type[record.type] ;
            }

            if (os.length > 0) {
                let hasOs = false;
                record.items?.forEach(item => {
                    if (os.includes(item.os)) {
                        hasOs = true;
                    }
                });
                passOs = hasOs;
            }
            if (tag && tag.length > 0) {
                passTag = tag.includes(record.group?.name || '');
            }

            return passQuery && passOs && passTag && passType;

        })

        setTotal(filteredList.length);
        return filteredList;
    }

    const expandedRowRender = (baselineCheck: BaselineCheck | undefined) => {
        if (!baselineCheck) {
            return null;
        }
        return <div className={styles.expandedRow}>{baselineCheck.items?.map((item, j) =>
            <Row key={j} className={styles.subBodyRow}>
                <Col span={10} className={styles.subBodyRowCell}>
                    <Text className={styles.bodyText}>
                        {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                    </Text>
                </Col>
                <Col span={10} className={styles.subBodyRowCell}>
                    <Text className={styles.bodyText}>
                        {item.repairMethod == RepairMethod.AUTO ? "自动修复" : "手动修复"}
                    </Text>
                </Col>
                <Col span={4} className={styles.subBodyRowCell}>
                    <Space>
                        <Button theme='solid' type='primary'
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setConfigVisible(true);
                                setSelectedBaseline(baselineCheck);
                                setSelectedBaselineItem(item);
                                setSelectedBaselineItemIndex(j);
                            }}
                        >配置</Button>
                        <Button theme='solid' type='danger'
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setItemDelVisible(true);
                                setSelectedBaseline(baselineCheck);
                                setSelectedBaselineItem(item);
                                setSelectedBaselineItemIndex(j);
                            }}
                        >删除</Button>
                    </Space>
                </Col>
            </Row>)}
        </div>
    }

    return {
        columns,
        baselineChecks,
        baselineTags,
        loading,
        filterParam,
        setFilterParam,
        doFilter,
        expandedRowRender,
        selectedBaseline,
        setSelectedBaseline,
        editVisible,
        setEditVisible,
        regEditVisible,
        setRegEditVisible,
        delVisible,
        setDelVisible,
        reload,
        selectedBaselineItem,
        setSelectedBaselineItem,
        selectedBaselineItemIndex,
        setSelectedBaselineItemIndex,
        configVisible,
        setConfigVisible,
        itemDelVisible,
        setItemDelVisible,
    }
}

export default useTable;