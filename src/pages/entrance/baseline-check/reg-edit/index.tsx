import React, { useState, useContext, useEffect } from "react";
import { Typography, Modal, Form, Row, Col, Button, Notification, Divider, Popover, Skeleton, Input, Space } from "@douyinfe/semi-ui";
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { sanitizeLabel } from '@/utils/common';
import { IconHelpCircle, IconPlus } from '@douyinfe/semi-icons';
import pinyin from 'tiny-pinyin';
import { BaselineCheck, BaselineCheckGroup, BaselineCheckItem, BaselineCheck_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import ItemRow from '../item-row';
import styles from '@/pages/entrance/index.module.scss'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from "@/services/core";

const { Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    id: bigint
}

// 检测项错误
interface ItemError {
    index: number,
    error: boolean,
}
const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        alias: string;
        description: string;
        tag: string;
        type: BaselineCheck_Type;
    }>>();
    // 对象loading
    const [loading, setLoading] = useState(false);
    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const [items, setItems] = useState<Array<BaselineCheckItem>>([]);

    // 检测项错误
    const [itemsErr, setItemsErr] = useState<ItemError[]>([{
        index: 0,
        error: false,
    }]);

    const validateItems = (items: BaselineCheckItem[]) => {
        let hasItemsError = false;
        let err = items.map((item, index) => {
            let err: ItemError = {
                index,
                error: false,
            }

            if (
                !item.repairSolution || !item.repairSolutionName || !item.repairSolutionDescription

            ) {
                hasItemsError = true;
                err.error = true;
            }
            return err;
        });
        setItemsErr(err);
        return hasItemsError;
    }
    const [validateFlag, setValidateFlag] = useState(false);


    const [baselineCheck, setBaselineCheck] = useState<BaselineCheck | null>(null);

    const query = async () => {
        setLoading(true);
        flylayerClient.getBaselineCheck({
            id: props.id
        }).then((res) => {
            const check = res.baselineCheck;
            if (!check) {
                Notification.error({
                    title: '获取失败',
                    content: '未找到检测策略'
                });
                return;
            }
            setBaselineCheck(check);
            validateItems(check.items);
            setItems(check.items);
        }).catch((err) => {
            console.trace(err);
            Notification.error({
                title: '获取失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        });
    }

    useEffect(() => {
        if (props.id) {
            query();
        }
    }, []);


    const [inputTag, setInputTag] = useState<string>('');

    const inputTagAddDisable = (tag: string, groups: BaselineCheckGroup[]) => {
        if (!inputTag || inputTag == '' || inputTag.trim() == '') {
            return true;
        }

        let exist = false;
        groups.forEach(group => {
            if (group.alias == tag.trim()) {
                exist = true;
                return;
            }
        })
        return exist;
    }

    const [groupSaveLoading, setGroupSaveLoading] = useState(false);

    const [groups, setGroups] = useState<BaselineCheckGroup[]>([]);
    const queryBaselineCheckGroup = () => {
        flylayerClient.listBaselineCheckGroups({
            flynetId: flynet.id
        }).then(res => {
            setGroups(res.baselineCheckGroups);
        }).catch((e) => {
            Notification.error({ title: '获取分类失败', content: e.message });
        })
    }

    useEffect(() => {
        queryBaselineCheckGroup();
    }, [])

    const handleSubmit = async () => {
        if (!formApi) {
            return
        }
        let hasItemsError = validateItems(items);
        await formApi.validate();
        if (hasItemsError) {
            setValidateFlag(true);
            return;
        }
        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';
        const tag = values.tag ? values.tag.trim() : '';
        const type = BaselineCheck_Type.REGEX;

        items.forEach((item) => {
            item.name = name;
        });

        const check = new BaselineCheck();
        check.id = baselineCheck?.id || BigInt(0);
        check.name = name;
        check.alias = alias;
        check.description = description;
        groups.forEach(val => {
            if (val.name == values.tag) {
                check.group = val;
            };
        })
        check.type = type;
        check.items = items;

        setSaveLoading(true);
        flylayerClient.updateBaselineCheck({
            baselineCheck: check,
            flynetId: flynet.id
        }).then(() => {
            Notification.success({
                title: '编辑成功',
                content: '编辑策略创建成功'
            });
            props.success && props.success();
            props.close();
        }).catch((err) => {
            console.trace(err);
            Notification.error({
                title: '编辑检测策略失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        });

    }

    return <><Modal
        title="编辑注册表检测项"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={960}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Skeleton loading={loading} placeholder={
            <>
                <Row gutter={20}>
                    <Col span={9}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />
                    </Col>
                    <Col span={9}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />
                    </Col>
                    <Col span={6}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />
                    </Col>
                </Row>
                <Row className="mb10">
                    <Col span={24}>
                        <Skeleton.Image style={{ height: 20, marginBottom: 8, marginTop: 12 }} />
                        <Skeleton.Image style={{ height: 32, marginBottom: 20 }} />
                    </Col>
                </Row>

                <Divider className="mb10" />
                <Row className="mb10">
                    <Col span={12}>
                        <Title heading={6} type="tertiary">检测项</Title>
                    </Col>
                </Row>
                <Skeleton.Image style={{ height: 105, marginBottom: 20 }} />

            </>
        }>{baselineCheck &&
            <Form
                getFormApi={setFormApi}
                initValues={{
                    name: baselineCheck.name,
                    alias: baselineCheck.alias,
                    description: baselineCheck.description,
                    tag: baselineCheck.group?.name || '',
                    type: baselineCheck.type
                }}
            >
                {({ values }) => (<>
                    <Row gutter={20}>
                        <Col span={9}>
                            <Form.Input field='alias' label='名称' trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '名称不能为空';
                                }
                                return '';
                            }} />
                        </Col>
                        <Col span={9}>
                            <Form.Input field='name'
                                readonly
                                label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                                trigger={'blur'} validate={value => {
                                    if (!value) {
                                        return '编码不能为空';
                                    }
                                    // 编码不能以-开头
                                    if (value.trim().startsWith('-')) {
                                        return '编码不能以-开头'
                                    }
                                    if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                        return "编码只能包含字母、数字和'-'";
                                    }
                                    return '';
                                }}
                                required />
                        </Col>
                        <Col span={6}>
                            <Form.Select
                                field='tag'
                                name='tag'
                                label='分类'
                                style={{ width: '100%' }}
                                rules={[{ required: true, message: '请选择分类' }]}
                                placeholder='请选择分类'
                                optionList={groups.map(group => {
                                    return { value: group.name, label: group.alias }
                                })}
                                outerBottomSlot={
                                    <Space className='p10'><Input
                                        value={inputTag}
                                        onChange={(e) => setInputTag(e)}
                                        placeholder={'输入分类'} /><Button
                                            onClick={() => {
                                                setGroupSaveLoading(true);
                                                flylayerClient.createBaselineCheckGroup({
                                                    flynetId: flynet.id,
                                                    baselineCheckGroup: {
                                                        name: sanitizeLabel(pinyin.convertToPinyin(inputTag, '', true)),
                                                        alias: inputTag
                                                    }
                                                }).then(() => {
                                                    queryBaselineCheckGroup();
                                                }).catch((e) => {
                                                    Notification.error({ title: '创建分类失败', content: e.message });
                                                }).finally(() => {
                                                    setGroupSaveLoading(false);
                                                })

                                                setInputTag('');
                                            }}
                                            disabled={inputTagAddDisable(inputTag, groups)}
                                        >添加</Button></Space>
                                }
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Form.TextArea field="description" autosize rows={1} label="策略描述" />
                        </Col>
                    </Row>
                    <Divider className="mb10" />
                    <Row className="mb10">
                        <Col span={12}>
                            <Title heading={6} type="tertiary">检测项</Title>
                        </Col>
                    </Row>
                    <Row className={styles.tableTitle} >
                        <Col span={2}>序号</Col>
                        <Col span={22} className={styles.bigRightColumn}>
                            <Row className={styles.subTableTitle}>
                                <Col span={4}>
                                    操作系统
                                </Col>
                                <Col span={18}>
                                    修复方式
                                </Col>
                                <Col span={2} className={styles.rightColumn}>
                                    <Button onClick={() => {
                                        let item = new BaselineCheckItem({
                                            os: OS.WINDOWS,
                                            conditions: [{
                                                name: '',
                                                op: '==',
                                                value: '',
                                                regMainKey: '',
                                                regPath: '',
                                            }]
                                        });
                                        let newItems = [...items, item];
                                        setItems(newItems);
                                        validateItems(newItems);
                                    }} icon={<IconPlus></IconPlus>}></Button>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    {items.map((checkItem, index) => {
                        return <ItemRow
                            removeable= {true}
                            osReadonly={false}
                            itemErr={itemsErr[index].error}
                            setItemErr={(val) => {
                                let copyedItemsErr = [...itemsErr];
                                copyedItemsErr[index].error = val;
                                setItemsErr(copyedItemsErr);
                            }}
                            validateFlag={validateFlag}
                            setValidateFlag={setValidateFlag}
                            type={values.type}
                            key={index} index={index} item={checkItem} onChange={(item) => {
                                let copyedItems = [...items];
                                copyedItems[index] = item;
                                setItems(copyedItems);
                            }} onRemove={() => {
                                // setItems(items.filter((item, i) => i != index));
                            }} />

                    })}
                </>)}
            </Form>}
        </Skeleton>
    </Modal></>
}

export default Index;
