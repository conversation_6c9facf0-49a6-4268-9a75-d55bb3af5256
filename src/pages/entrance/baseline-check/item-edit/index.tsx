import React, { useState, useEffect } from "react";
import { Typo<PERSON>, Row, Col, Button, Tag, Input, Select, TagInput } from "@douyinfe/semi-ui";
import { BaselineCheck, BaselineCheckItem, BaselineCheckItemCondition, BaselineCheck_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IconMinusCircle, IconPlus } from '@douyinfe/semi-icons';

import RepairMethodEditor from '@/pages/entrance/components/repair-method-editor';

import styles from '@/pages/entrance/index.module.scss'

const { Paragraph } = Typography;
interface Props {
    type: BaselineCheck_Type;
    item: BaselineCheckItem;
    conditions: BaselineCheckItemCondition[];
    onChange: (item: BaselineCheckItem, conditions: BaselineCheckItemCondition[]) => void;
    itemErr: boolean;
    setItemErr: (val: boolean) => void;
    validateFlag: boolean;
    setValidateFlag: (val: boolean) => void;
    osReadonly: boolean;
}

const Index: React.FC<Props> = (props) => {
    const { type } = props;

    const [repairMethodErr, setRepairMethodErr] = useState(false);

    const handleItemErr = (
        repairMethodErr: boolean,
    ) => {
        if (repairMethodErr) {
            props.setItemErr(true);
        } else {
            props.setItemErr(false);
        }
    }

    const [item, setItem] = useState<BaselineCheckItem>(props.item);
    const [conditions, setConditions] = useState<BaselineCheckItemCondition[]>(props.conditions);

    useEffect(() => {
        setItem(props.item);
    }, [props.item]);

    useEffect(() => {
        setConditions(props.conditions);
    }, [props.conditions]);

    useEffect(() => {
        if (props.validateFlag) {
            let repairMethodErr =
                item.repairSolution === '' || item.repairSolutionName === '' || item.repairSolutionDescription === '';
            setRepairMethodErr(repairMethodErr);

            props.setValidateFlag(false);
        }

    }, [props.validateFlag])


    return (
        <>
            <Row className={styles.subTableBody}>
                <Col span={4}>
                {props.osReadonly ? <>{OS[item.os]}</> : <Select  style={{ width: '100%' }} value={item.os} placeholder='请选择' onChange={val => {
                        item.os = val as OS;
                        props.onChange(item, conditions);
                    }}>
                        <Select.Option value={OS.WINDOWS}>Windows</Select.Option>
                        <Select.Option value={OS.MACOS}>Mac</Select.Option>
                        <Select.Option value={OS.IOS}>iOS</Select.Option>
                        <Select.Option value={OS.ANDROID}>Android</Select.Option>
                        <Select.Option value={OS.LINUX}>Linux</Select.Option>
                    </Select>}
                    
                </Col>
                <Col span={20} className={styles.rightColumn}>
                    <RepairMethodEditor
                        repairMethodErr={repairMethodErr}
                        repairMethod={item.repairMethod}
                        repairSolution={item.repairSolution}
                        repairSolutionName={item.repairSolutionName}
                        repairSolutionDescription={item.repairSolutionDescription}
                        onChange={(param) => {
                            let newItem = new BaselineCheckItem({ ...item });
                            newItem.repairMethod = param.repairMethod;
                            newItem.repairSolution = param.repairSolution;
                            newItem.repairSolutionName = param.repairSolutionName;
                            newItem.repairSolutionDescription = param.repairSolutionDescription;
                            props.onChange(newItem, conditions);
                            handleItemErr(false);
                        }}
                        onError={() => {
                            handleItemErr(true);
                            setRepairMethodErr(true);
                        }}
                    />
                </Col>

            </Row>
            {type === BaselineCheck_Type.REGEX && <Row><Col span={24}>
                <Row className={styles.tableTitle} >
                    <Col span={2}>序号</Col>
                    <Col span={22} className={styles.bigRightColumn}>
                        <Row className={styles.subTableTitle}>
                            <Col span={4}>
                                名称
                            </Col>
                            <Col span={4}>
                                操作符
                            </Col>

                            <Col span={4}>
                                值
                            </Col>
                            <Col span={5}>正则主键</Col>
                            <Col span={5}>正则路径</Col>
                            <Col span={2} className={styles.rightColumn}>
                                <Button onClick={() => {
                                    let condition = new BaselineCheckItemCondition({
                                        name: '',
                                        op: '==',
                                        value: '',
                                        regMainKey: '',
                                        regPath: '',
                                    });
                                    conditions.push(condition);

                                    props.onChange(item, conditions);
                                }} icon={<IconPlus></IconPlus>}></Button>
                            </Col>
                        </Row>

                    </Col>
                    <Col span={2}>
                    </Col>
                </Row>
                {conditions.map((condition, conditionIndex) => {
                    return <Row key={conditionIndex} className={styles.tableBody}>
                        <Col span={2}>
                            <Tag style={{ height: 32, width: 32 }} color={conditionIndex % 2 == 0 ? 'grey' : 'white'} >{conditionIndex + 1}</Tag>
                        </Col>
                        <Col span={22} className={styles.bigRightColumn}>
                            <Row className={styles.subTableBody}>
                                <Col span={4}>
                                    <Input
                                        value={condition.name}
                                        onChange={val => {

                                            conditions[conditionIndex].name = val;
                                            props.onChange(item, conditions);
                                        }}
                                    />
                                </Col>
                                <Col span={4}>
                                    <Select
                                        value={condition.op}
                                        onChange={val => {
                                            conditions[conditionIndex].op = val as string;
                                            props.onChange(item, conditions);
                                        }}
                                        style={{ width: '100%' }}>
                                        <Select.Option value="==">等于</Select.Option>
                                        <Select.Option value="!=">不等于</Select.Option>

                                    </Select>
                                </Col>
                                <Col span={4}>
                                    <Input
                                        value={condition.value}
                                        onChange={val => {
                                            conditions[conditionIndex].value = val;
                                            props.onChange(item, conditions);
                                        }}
                                    />
                                </Col>
                                <Col span={5}>
                                    <Input
                                        value={condition.regMainKey}
                                        onChange={val => {
                                            conditions[conditionIndex].regMainKey = val;
                                            props.onChange(item, conditions);
                                        }}
                                    />
                                </Col>
                                <Col span={5}>
                                    <Input
                                        value={condition.regPath}
                                        onChange={val => {
                                            conditions[conditionIndex].regPath = val;
                                            props.onChange(item, conditions);
                                        }}
                                    />
                                </Col>
                                <Col span={2} className={styles.rightColumn}>

                                    <Button type="danger" onClick={() => {
                                        conditions.splice(conditionIndex, 1);
                                        props.onChange(item, conditions);
                                    }} icon={<IconMinusCircle />} />
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                })}
            </Col></Row>}


        </>
    );
}

export default Index;
