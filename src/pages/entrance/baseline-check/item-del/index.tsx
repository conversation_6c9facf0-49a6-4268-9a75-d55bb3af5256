import { useState, useContext } from 'react'
import { Typography, Modal, Notification, Input, TabPane } from '@douyinfe/semi-ui';
import { BaselineCheck, BaselineCheckItem } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph } = Typography;

interface Props {
    index: number;
    close: () => void,
    success?: () => void,
    record: BaselineCheckItem,
    baselineCheck: BaselineCheck;
}
const Index: React.FC<Props> = (props) => {
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const flynet = useContext(FlynetGeneralContext);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={`删除基线检测项 ${props.record.name}`}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

        let baselineCheck = props.baselineCheck;
        baselineCheck.items.splice(props.index, 1);
        flylayerClient.updateBaselineCheck({
            baselineCheck: baselineCheck,
            flynetId: flynet.id
        }).then((res) => {
            Notification.success({
                title: '删除成功',
                content: '检测项已删除'
            });
            props.success && props.success();
            props.close();
        }).catch((err) => {
            console.trace(err);
            Notification.error({
                title: '删除失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        });
        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'> 基线检测项 <b>{props.record.name}</b> 将被删除，删除后将无法使用该基线检测项。
        </Paragraph>
        <Paragraph className='mb20'> 输入 <b>{props.record.name}</b> 以确认删除
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;