import React, { useState, useEffect } from "react";
import { Typo<PERSON>, Row, Col, Button, Tag, Input, Select } from "@douyinfe/semi-ui";
import { BaselineCheckItem, BaselineCheckItemCondition, BaselineCheck_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { IconMinusCircle, IconPlus } from '@douyinfe/semi-icons';
import ItemEdit from "@/pages/entrance/baseline-check/item-edit";

import styles from '@/pages/entrance/index.module.scss'

const { Title } = Typography;

interface Props {
    index: number;
    type: BaselineCheck_Type;
    item: BaselineCheckItem;
    onChange: (item: BaselineCheckItem) => void;
    onRemove: () => void;
    itemErr: boolean;
    setItemErr: (val: boolean) => void;
    validateFlag: boolean;
    setValidateFlag: (val: boolean) => void;
    removeable: boolean
    osReadonly: boolean;
}

const Index: React.FC<Props> = (props) => {
    const { index, item, type } = props;
    return <Row key={index} className={styles.tableBody}>
        <Col span={2}>
            <Tag style={{ height: 32, width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'} >{index + 1}</Tag>
        </Col>
        <Col span={20} className={styles.bigRightColumn}>
            <ItemEdit osReadonly={props.osReadonly} item={item} conditions={item.conditions} type={type} onChange={(item, conditions) => {
                let newItem = new BaselineCheckItem({ ...item, conditions: conditions });
                props.onChange(newItem);
            }}
                itemErr={props.itemErr} setItemErr={props.setItemErr} validateFlag={props.validateFlag} setValidateFlag={props.setValidateFlag}
            />
        </Col>
        <Col span={2} className={styles.rightColumn}>
            {props.removeable && <Button type="danger" onClick={() => {
                props.onRemove();
            }} icon={<IconMinusCircle />} />}

        </Col>


    </Row>
}

export default Index;
