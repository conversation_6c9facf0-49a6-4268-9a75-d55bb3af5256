import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input } from '@douyinfe/semi-ui';
import { EntrancePolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { flylayerClient } from '@/services/core';


import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: EntrancePolicy
}
const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={`删除准入策略 ${props.record.name}`}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

            flylayerClient.deleteEntrancePolicy({
                id: props.record.id
            }).then(() => {
                Notification.success({ content: "删除准入策略成功", position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                console.error(err);
                Notification.error({ content: "删除准入策略失败，请稍后重试", position: "bottomRight" })
            }).finally(() => setLoading(false))

        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'> 准入策略 <b>{props.record.alias}({props.record.name})</b> 将被删除，删除后将无法使用该准入策略。
        </Paragraph>
        <Paragraph className='mb20'> 输入 <b>{props.record.name}</b> 以确认删除
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;