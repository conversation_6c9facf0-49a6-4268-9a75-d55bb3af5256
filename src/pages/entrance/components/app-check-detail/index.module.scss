.titleRow {
    background-color: var(--semi-color-bg-1);
    color: var(--semi-color-text-2);
    font-size: 14px;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid var(--semi-color-border);
    padding: 8px 0;
    vertical-align: middle;
    overflow-wrap: break-word;
    position: relative;
}

.container {
    margin-bottom: 20px;
}

.tag {
    border-bottom: 1px solid var(--semi-color-border);
    border-left: 1px solid var(--semi-color-border);
    border-right: 1px solid var(--semi-color-border);
    .title {
        border-bottom: 1px solid var(--semi-color-border);
        padding: 5px 5px 5px 15px;
    }
}

.bodyRow {
    
    border-bottom: 1px solid var(--semi-color-border);
    
    box-sizing: border-box;
    
    vertical-align: middle;
    font-size: 14px;

    .bodyRowCell {
        
        overflow-wrap: break-word;
        border-left: none;
        border-right: none;
        padding: 16px 0 !important;
        box-sizing: border-box;
        
        vertical-align: middle;
        font-size: 14px;
    
    }
}

.subBodyRow {
    
    
    box-sizing: border-box;
    
    vertical-align: middle;
    font-size: 14px;

    .subBodyRowCell {
        border-left: 1px solid var(--semi-color-border) !important;
        border-bottom: 1px solid var(--semi-color-border);
        overflow-wrap: break-word;
        border-left: none;
        border-right: none;
        padding: 16px !important;
        box-sizing: border-box;
        
        vertical-align: middle;
        font-size: 14px;
        
    
    }
}

.bodyText {
    font-size: 14px;
    color: var(--semi-color-text-2);
    font-weight: 400;
    overflow-wrap: break-word;
    line-height: 24px!important;
}

.expandedRow {
    background-color: var(--semi-color-bg-1);
    color: var(--semi-color-text-2);
}
