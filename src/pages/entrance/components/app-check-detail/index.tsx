import React, { useEffect, useState } from "react";

import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Row, Col, Button, Typography, Popover, Space, Divider, Skeleton, Breadcrumb } from '@douyinfe/semi-ui';
import { AppCheck, AppCheckItem } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import RepairMethodDisplay from '@/pages/entrance/components/repair-method-display';

import ItemConfig from '@/pages/entrance/app-check/item-config';
import ItemDel from '@/pages/entrance/app-check/item-del';


const { Title, Paragraph, Text } = Typography;
import styles from '@/pages/entrance/index.module.scss'
import { use } from "echarts";
import { BASE_PATH } from "@/constants/router";

interface Props {
    loading: boolean,
    appCheck?: AppCheck,
    useConfig: boolean,
    queryAppCheck?: () => void,
}

const Index: React.FC<Props> = (props) => {
    const { queryAppCheck } = props;

    const [appCheck, setAppCheck] = useState<AppCheck | undefined>(props.appCheck);

    useEffect(() => {
        setAppCheck(props.appCheck);
    }, [props.appCheck]);

    const [loading, setLoading] = useState(props.loading);
    useEffect(() => {
        setLoading(props.loading);
    }, [props.loading]);

    const [configVisible, setConfigVisible] = useState(false);
    const [itemDelVisible, setItemDelVisible] = useState(false);


    const [selectedAppCheckItem, setSelectedAppCheckItem] = useState<AppCheckItem | undefined>();
    const [selectedAppCheckItemIndex, setSelectedAppCheckItemIndex] = useState<number>(-1);




    return <><Skeleton placeholder={<div className='general-page'>
        <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>
        <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
        <Skeleton.Image style={{ height: 60 }} className='mb40' />
        <Skeleton.Image style={{ height: 200 }} />
    </div>} loading={loading}>{appCheck && <div className='general-page'>
        <Breadcrumb routes={
            [
                {
                    path: `${BASE_PATH}/policies/entrance`,
                    href: `${BASE_PATH}/policies/entrance`,
                    name: '设备准入策略'
                },
                {
                    path: `${BASE_PATH}/policies/entrance/app-check`,
                    href: `${BASE_PATH}/policies/entrance/app-check`,
                    name: '应用检测项配置',
                },
                {
                    name: appCheck.name,
                }
            ]
        }>
        </Breadcrumb>
        <Row className="mb10">
            <Col span={20}>
                <Space>
                    <Title className={styles.heading} heading={3} style={{ flexShrink: 0 }}>
                        <span>{appCheck?.alias}&nbsp;</span>
                        <Popover content={<div className="p10" style={{ maxWidth: 450 }}>{appCheck.description}</div>}><Text type='tertiary' size='small' ellipsis={{
                            showTooltip: false
                        }} style={{ maxWidth: 450 }}>{appCheck?.description}</Text></Popover>
                    </Title>
                    <Paragraph type="tertiary"></Paragraph></Space>
            </Col>
            <Col span={4}><div className='btn-right-col'>
            </div></Col>
        </Row>

        <Divider className="mb40"></Divider>

        <Row className='mb2'>
            <Col span={20}>
                <div><Title heading={4}>检测项</Title></div></Col>
            <Col span={4}><div className='btn-right-col'>

            </div></Col>
        </Row>
        <Paragraph className="mb20" type="tertiary">监测员工终端设备中的风险应用，例如检测员工终端设备上是否安装了不符合企业规定的应用程序</Paragraph>
        {appCheck.items && appCheck.items.length > 0 && <div>

            <div className={styles.expandedRow}>
                <Row className={styles.titleRow}>
                    <Col span={8}>软件全称/Bundle ID</Col>
                    <Col span={2}>操作系统</Col>
                    <Col span={2}>检测版本</Col>
                    <Col span={3}>软件发布者</Col>
                    <Col span={6}>修复方案</Col>
                    <Col span={3}>
                        {/* <div className="btn-right-col">
                            操作
                        </div> */}
                    </Col>
                </Row>
                {appCheck.items?.map((item, i) => {
                    return  <Row key={i} className={styles.bodyRow}>
                            <Col span={8} className={styles.bodyRowCell}>
                                {item.fullName} {item.bundleId && <>/ <Text type='tertiary'>{item.bundleId}</Text></>} 
                            </Col>
                            <Col span={2} className={styles.bodyRowCell}>
                                <Text>
                                    {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                                </Text>
                            </Col>
                            <Col span={2} className={styles.bodyRowCell}>
                            <Text>{item.version}</Text>
                            </Col>
                            <Col span={3} className={styles.bodyRowCell}>
                            <Text>{item.publisher}</Text>
                            </Col>
                            <Col span={6} className={styles.bodyRowCell}>
                                <RepairMethodDisplay
                                    repairMethod={item.repairMethod}
                                    repairSolution={item.repairSolution}
                                    repairSolutionName={item.repairSolutionName}
                                    repairSolutionDescription={item.repairSolutionDescription}
                                />
                            </Col>
                            <Col span={3} className={styles.bodyRowCell}>
                                {props.useConfig && <div className="btn-right-col">
                                    <Space>
                                        <Button theme='solid' type='primary'
                                            onClick={() => {
                                                setSelectedAppCheckItem(item);
                                                setSelectedAppCheckItemIndex(i);
                                                setConfigVisible(true);
                                            }}
                                        >配置</Button>
                                        <Button theme='solid' type='danger'
                                            onClick={() => {
                                                setSelectedAppCheckItem(item);
                                                setSelectedAppCheckItemIndex(i);
                                                setItemDelVisible(true);
                                            }}
                                        >删除</Button>
                                    </Space>
                                </div>}

                            </Col>
                        </Row>
                })}
            </div>
        </div>}
    </div>}</Skeleton>
        {
            itemDelVisible && selectedAppCheckItem && appCheck && <ItemDel
                close={() => {
                    setItemDelVisible(false)
                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);

                }}
                success={() => {
                    setItemDelVisible(false);

                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);
                    if (props.queryAppCheck) {
                        props.queryAppCheck();
                    }
                }}
                record={selectedAppCheckItem}
                index={selectedAppCheckItemIndex}
                appCheck={appCheck}
            />
        }
        {
            configVisible && selectedAppCheckItem && appCheck && <ItemConfig
                close={() => {
                    setConfigVisible(false)
                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);

                }}
                success={() => {
                    setConfigVisible(false);
                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);
                    if (props.queryAppCheck) {
                        props.queryAppCheck();
                    }

                }}
                appCheck={appCheck}
                item={selectedAppCheckItem}
                index={selectedAppCheckItemIndex}
            />
        }
    </>
}

export default Index;