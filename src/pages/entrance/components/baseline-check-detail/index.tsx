import React, { useState, useContext, useEffect } from "react";
import { useParams } from 'react-router-dom';

import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Breadcrumb, Row, Col, Button, Typography, Tag, Layout, Notification, Descriptions, Popover, Space, Divider, Skeleton } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { flylayerClient } from '@/services/core';
import { BaselineCheck, BaselineCheckItem, BaselineCheck_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import RepairMethodDisplay from '@/pages/entrance/components/repair-method-display';

import BaselineCheckItemConfig from '@/pages/entrance/baseline-check/item-config';
import BaselineCheckItemDel from '@/pages/entrance/baseline-check/item-del';
const { Title, Paragraph, Text } = Typography;
const { Content, Sider } = Layout;
import styles from '@/pages/entrance/index.module.scss'

interface Props {
    loading: boolean,
    baselineCheck?: BaselineCheck,
    useConfig: boolean,
    queryBaselineCheck?: () => void,
}

const Index: React.FC<Props> = (props) => {

    const [configVisible, setConfigVisible] = useState(false);
    const [itemDelVisible, setItemDelVisible] = useState(false);
    const [selectedBaselineItem, setSelectedBaselineItem] = useState<BaselineCheckItem>();
    const [selectedBaselineItemIndex, setSelectedBaselineItemIndex] = useState<number>(-1);


    const [loading, setLoading] = useState(props.loading);
    const [baselineCheck, setBaselineCheck] = useState<BaselineCheck | undefined>(props.baselineCheck);

    useEffect(() => {
        setLoading(props.loading);
    }, [props.loading]);

    useEffect(() => {
        setBaselineCheck(props.baselineCheck);
    }, [props.baselineCheck]);



    return <><Skeleton placeholder={<div className='general-page'>
        <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>
        <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
        <Skeleton.Image style={{ height: 60 }} className='mb40' />
        <Skeleton.Image style={{ height: 200 }} />
    </div>} loading={loading}>
        {baselineCheck &&
            <div className='general-page'>
                <Breadcrumb routes={
                    [
                        {
                            path: `${BASE_PATH}/policies/entrance`,
                            href: `${BASE_PATH}/policies/entrance`,
                            name: '设备准入策略'
                        },
                        {
                            path: `${BASE_PATH}/policies/entrance/baseline-check`,
                            href: `${BASE_PATH}/policies/entrance/baseline-check`,
                            name: '基线检测项配置',
                        },
                        {
                            name: baselineCheck.name,
                        }
                    ]
                }>
                </Breadcrumb>
                <Row className="mb10">
                    <Col span={20}>
                        <Space>
                            <Title className={styles.heading} heading={3} style={{ flexShrink: 0 }}>
                                <span>{baselineCheck?.alias}&nbsp;</span>
                                <Popover content={<div className="p10" style={{ maxWidth: 450 }}>{baselineCheck.description}</div>}><Text type='tertiary' size='small' ellipsis={{
                                    showTooltip: false
                                }} style={{ maxWidth: 450 }}>{baselineCheck?.description}</Text></Popover>
                            </Title>
                            <Paragraph type="tertiary"></Paragraph></Space>
                    </Col>
                    <Col span={4}><div className='btn-right-col'>
                    </div></Col>
                </Row>
                <Divider></Divider>
                <Descriptions className={styles.generalInfo} data={[{
                    key: '分类',
                    value: <><Tag>{baselineCheck.group?.alias}</Tag></>
                }, {
                    key: '类型',
                    value: <Tag color="blue">{baselineCheck.type == BaselineCheck_Type.BUILD_IN ? '基线检测' : '注册表检测'}</Tag>
                }]} row />
                <Row className='mb2'>
                    <Col span={20}>
                        <div><Title heading={4}>检测项</Title></div></Col>
                    <Col span={4}><div className='btn-right-col'>

                    </div></Col>
                </Row>
                <Paragraph className='mb20' type='tertiary'>
                    基线检查项针对服务器操作系统、数据库、软件和容器的配置进行安全检测，可以帮助您加固系统安全，降低入侵风险并满足安全合规要求。
                </Paragraph>
                {baselineCheck.items && baselineCheck.items.length > 0 && <div>

                    {baselineCheck.items.map((item, index) => {
                        return <div key={index}>
                            <Layout>
                                <Sider style={{ width: 160 }}><Text type='tertiary'>操作系统</Text></Sider>
                                <Content>
                                    <Row>
                                        <Col span={20}><Text>
                                            {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}</Text>

                                        </Col>
                                        <Col span={4}>
                                            {props.useConfig && <div className='btn-right-col'>
                                                <Space>
                                                    <Button theme='solid' type='primary'
                                                        onClick={() => {
                                                            setConfigVisible(true);
                                                            setSelectedBaselineItem(item);
                                                            setSelectedBaselineItemIndex(index);
                                                        }}
                                                    >配置</Button>
                                                    <Button theme='solid' type='danger'
                                                        onClick={() => {
                                                            setItemDelVisible(true);
                                                            setSelectedBaselineItem(item);
                                                            setSelectedBaselineItemIndex(index);
                                                        }}
                                                    >删除</Button>
                                                </Space>
                                            </div>}

                                        </Col>

                                    </Row>

                                </Content>
                            </Layout>
                            <Layout className="mb10">
                                <Sider style={{ width: 160 }}><Text type='tertiary'>修复方式</Text></Sider>
                                <Content>
                                    <RepairMethodDisplay
                                        repairMethod={item.repairMethod}
                                        repairSolution={item.repairSolution}
                                        repairSolutionName={item.repairSolutionName}
                                        repairSolutionDescription={item.repairSolutionDescription}
                                    />
                                </Content>
                            </Layout>
                            <Layout>
                                <Sider style={{ width: 160 }}><Text type='tertiary'>检测条件</Text></Sider>
                                <Content>
                                    {baselineCheck.type === BaselineCheck_Type.BUILD_IN && item.conditions && item.conditions.length > 0 && <>
                                        <Row className={styles.tableTitle} >
                                            <Col span={2}>序号</Col>
                                            <Col span={22} className={styles.bigRightColumn}>
                                                <Row className={styles.subTableTitle}>
                                                    <Col span={12}>
                                                        名称
                                                    </Col>
                                                    <Col span={6}>
                                                        操作符
                                                    </Col>

                                                    <Col span={4}>
                                                        值
                                                    </Col>
                                                    <Col span={2} className={styles.rightColumn}>

                                                    </Col>
                                                </Row>

                                            </Col>
                                        </Row>
                                        {item.conditions.map((condition, conditionIndex) => {
                                            return <Row key={conditionIndex} className={styles.tableBody}>
                                                <Col span={2}>
                                                    <Tag style={{ height: 32, width: 32 }} color={conditionIndex % 2 == 0 ? 'grey' : 'white'} >{conditionIndex + 1}</Tag>
                                                </Col>
                                                <Col span={22} className={styles.bigRightColumn}>
                                                    <Row className={styles.subTableBody}>
                                                        <Col span={12}>
                                                            <Text>{condition.name}</Text>
                                                        </Col>
                                                        <Col span={6}>
                                                            <Text>
                                                                {condition.op == '==' ? '等于' : ''}
                                                                {condition.op == '!=' ? '不等于' : ''}
                                                                {condition.op == '>' ? '大于' : ''}
                                                                {condition.op == '<' ? '小于' : ''}
                                                                {condition.op == '>=' ? '大于等于' : ''}
                                                                {condition.op == '<=' ? '小于等于' : ''}
                                                                {condition.op == 'contains' ? '包含(模糊匹配)' : ''}
                                                                {condition.op == 'in' ? '包含(精确匹配)' : ''}
                                                            </Text>
                                                        </Col>
                                                        <Col span={4}>
                                                            <Text>{condition.value}</Text>
                                                        </Col>
                                                        <Col span={2} className={styles.rightColumn}>
                                                        </Col>
                                                    </Row>
                                                </Col>
                                            </Row>
                                        })}
                                    </>}
                                    {baselineCheck.type === BaselineCheck_Type.REGEX && item.conditions && item.conditions.length > 0 && <>
                                        <Row className={styles.tableTitle} >
                                            <Col span={2}>序号</Col>
                                            <Col span={22} className={styles.bigRightColumn}>
                                                <Row className={styles.subTableTitle}>
                                                    <Col span={4}>
                                                        名称
                                                    </Col>
                                                    <Col span={4}>
                                                        操作符
                                                    </Col>

                                                    <Col span={4}>
                                                        值
                                                    </Col>
                                                    <Col span={5}>正则主键</Col>
                                                    <Col span={5}>正则路径</Col>
                                                    <Col span={2} className={styles.rightColumn}>

                                                    </Col>
                                                </Row>

                                            </Col>
                                        </Row>
                                        {item.conditions.map((condition, conditionIndex) => {
                                            return <Row key={conditionIndex} className={styles.tableBody}>
                                                <Col span={2}>
                                                    <Tag style={{ height: 32, width: 32 }} color={conditionIndex % 2 == 0 ? 'grey' : 'white'} >{conditionIndex + 1}</Tag>
                                                </Col>
                                                <Col span={22} className={styles.bigRightColumn}>
                                                    <Row className={styles.subTableBody}>
                                                        <Col span={4}>
                                                            <Text>{condition.name}</Text>
                                                        </Col>
                                                        <Col span={4}>
                                                            <Text>
                                                                {condition.op == '==' ? '等于' : ''}
                                                                {condition.op == '!=' ? '不等于' : ''}
                                                            </Text>
                                                        </Col>
                                                        <Col span={4}>
                                                            <Text>{condition.value}</Text>
                                                        </Col>
                                                        <Col span={5}>
                                                            <Text>{condition.regMainKey}</Text>
                                                        </Col>
                                                        <Col span={5}>
                                                            <Text>{condition.regPath}</Text>
                                                        </Col>
                                                        <Col span={2} className={styles.rightColumn}>

                                                        </Col>
                                                    </Row>
                                                </Col>
                                            </Row>
                                        })}
                                    </>}

                                </Content>
                            </Layout>
                            <Divider className="mb40"></Divider>
                        </div>
                    })}
                </div>}

            </div>}
    </Skeleton>
        {configVisible && selectedBaselineItem && baselineCheck && <BaselineCheckItemConfig
            index={selectedBaselineItemIndex}
            type={baselineCheck.type}
            item={selectedBaselineItem}
            baselineCheck={baselineCheck}
            close={() => {
                setConfigVisible(false)

                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
            }}
            success={() => {
                setConfigVisible(false);
                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
                if (props.queryBaselineCheck) {
                    props.queryBaselineCheck();
                }
            }}
        />}
        {itemDelVisible && selectedBaselineItem && baselineCheck && <BaselineCheckItemDel
            index={selectedBaselineItemIndex}
            close={() => {
                setItemDelVisible(false)
                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
            }}
            success={() => {
                setItemDelVisible(false);
                setSelectedBaselineItem(undefined);
                setSelectedBaselineItemIndex(-1);
                if (props.queryBaselineCheck) {
                    props.queryBaselineCheck();
                }
            }}
            record={selectedBaselineItem}
            baselineCheck={baselineCheck}
        />

        }
    </>


}

export default Index;