import React, { useState, FC, useEffect } from 'react'
import { Typography, Button, Collapsible, Input, Select, Space, TextArea, Row, Col, Tag } from '@douyinfe/semi-ui';
import { RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { IconAlertCircle, IconChevronRight, IconChevronDown } from '@douyinfe/semi-icons';
import styles from './index.module.scss';
interface Props {
    repairMethod: RepairMethod;
    repairSolution: string;
    repairSolutionName: string;
    repairSolutionDescription: string;
    onChange: (param: {
        repairMethod: RepairMethod,
        repairSolution: string,
        repairSolutionName: string,
        repairSolutionDescription: string
    }) => void;
    repairMethodErr: boolean;
    onError: () => void;
}

const { Paragraph } = Typography;

const Index: FC<Props> = (props) => {

    const [repairMethodErr, setRepairMethodErr] = useState(props.repairMethodErr);
    useEffect(() => {
        setRepairMethodErr(props.repairMethodErr);
    }, [props.repairMethodErr]);

    const [repairMethod, setRepairMethod] = useState(props.repairMethod);
    const [repairSolution, setRepairSolution] = useState(props.repairSolution);
    const [repairSolutionName, setRepairSolutionName] = useState(props.repairSolutionName);
    const [repairSolutionDescription, setRepairSolutionDescription] = useState(props.repairSolutionDescription);

    useEffect(() => {
        setRepairMethod(props.repairMethod);
        setRepairSolution(props.repairSolution);
        setRepairSolutionName(props.repairSolutionName);
        setRepairSolutionDescription(props.repairSolutionDescription);

    }, [props.repairMethod, props.repairSolution, props.repairSolutionName, props.repairSolutionDescription]);

    const [toggle, setToggle] = useState(false);

    const handleResult = (
        repairMethod: RepairMethod,
        repairSolution: string,
        repairSolutionName: string,
        repairSolutionDescription: string
    ) => {
        if (!repairSolutionName || !repairSolution || !repairSolutionDescription) {
            setRepairMethodErr(true);
            if (props.onError) props.onError();
        } else {
            setRepairMethodErr(false);
            props.onChange({
                repairMethod: repairMethod,
                repairSolution: repairSolution,
                repairSolutionName: repairSolutionName,
                repairSolutionDescription: repairSolutionDescription
            })
        }
    }


    return <><div className={styles.container}>
        <Row>
            <Col span={7} style={{ paddingRight: 8 }}><Select
                value={repairMethod}
                style={{ width: '100%' }}
                onChange={(value) => {
                    setRepairMethod(value as any);
                    handleResult(value as any, repairSolution, repairSolutionName, repairSolutionDescription);
                }} optionList={[{
                    value: RepairMethod.AUTO,
                    label: '自动修复'
                }, {
                    value: RepairMethod.MANUAL,
                    label: '手动修复'
                }, {
                    value: RepairMethod.GUIDE,
                    label: '显示修复向导'
                }]} /> </Col>
            <Col span={13}><Input
                placeholder={'修复方案名称'}
                value={repairSolutionName}
                onChange={(e) => {
                    setRepairSolutionName(e);
                }}
                onBlur={() => {
                    handleResult(repairMethod, repairSolution, repairSolutionName, repairSolutionDescription);
                }}
            ></Input></Col>
            <Col span={4} className={styles.rightColumn}>
                <Button onClick={() => {
                    if (toggle) {
                        handleResult(repairMethod, repairSolution, repairSolutionName, repairSolutionDescription);
                    }
                    setToggle(!toggle);

                }}><Space>详情{toggle ? <IconChevronDown /> : <IconChevronRight />}</Space></Button>
            </Col>
        </Row>
        <Collapsible isOpen={toggle} className={styles.collapsible}>
            <Row>
                <Col span={24}><Input
                    placeholder={'修复方案描述'}
                    className='mb10'
                    style={{ width: '100%' }}
                    value={repairSolutionDescription}
                    onChange={(e) => {
                        setRepairSolutionDescription(e);
                    }}
                    onBlur={() => {
                        handleResult(repairMethod, repairSolution, repairSolutionName, repairSolutionDescription);
                    }}
                ></Input></Col>
            </Row>
            <Row className='mb10'>
                <Col span={24}>
                    <TextArea
                        placeholder={'修复方案详情'}
                        value={repairSolution}
                        onChange={(e) => {
                            setRepairSolution(e);
                        }}
                        onBlur={() => {
                            handleResult(repairMethod, repairSolution, repairSolutionName, repairSolutionDescription);
                        }}
                    ></TextArea>
                </Col>
            </Row>


        </Collapsible>
        {repairMethodErr &&
                <Paragraph type='danger' style={{display: 'flex', alignItems: 'center'}}><IconAlertCircle />&nbsp;修复方案填写不完整</Paragraph>
        }

    </div>
    </>
}
export default Index