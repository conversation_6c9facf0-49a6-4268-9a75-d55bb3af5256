import { FC } from 'react'
import { Typography, Popover, Tag, Space } from '@douyinfe/semi-ui';
import { RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import styles from './index.module.scss';
interface Props {
    repairMethod: RepairMethod;
    repairSolution: string;
    repairSolutionName: string;
    repairSolutionDescription: string;
}

const { Paragraph } = Typography;
const Index: FC<Props> = (props) => {
    return <>
        <Space>
            <Paragraph type='tertiary'>{
                props.repairMethod === RepairMethod.AUTO ? '自动修复' :
                    props.repairMethod === RepairMethod.MANUAL ? '手动修复' :
                        props.repairMethod === RepairMethod.GUIDE ? '显示修复向导' : '末知修复方式'
            }:</Paragraph>
            <Popover content={
                <div className={styles.container}>
                    <Paragraph>{props.repairSolutionDescription}</Paragraph>
                    <Paragraph type='tertiary'>{props.repairSolution}</Paragraph>
                </div>
            }>
                <Tag>{props.repairSolutionName}</Tag>
            </Popover>
        </Space>
    </>
}
export default Index;
