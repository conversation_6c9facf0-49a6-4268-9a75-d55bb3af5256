import React, { useState, useContext, useEffect } from 'react'
import { Typography, Row, Col, Button, Table, Input, Space, Dropdown, Layout, Select } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import TableEmpty from '@/components/table-empty';
import { IconSearch, IconSetting } from '@douyinfe/semi-icons';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

import qs from 'query-string';

import styles from '@/pages/entrance/index.module.scss'
import useTable, { EntranceFilter } from './useTable';
import { EntrancePolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

import New from './new';
import Edit from './edit';
import Del from './del';
const { Title, Paragraph, Text } = Typography;
const { Sider, Content } = Layout;

// 根据URL参数设置过滤参数
const getInitFilter = (location: Location): EntranceFilter => {
    const query: string = getQueryParam('query', location) as string;
    const disabled: string = getQueryParam('disabled', location) as string;
    return {
        query: query || '',
        disabled: disabled || ''
    }
}

const Index: React.FC = () => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();

    const initFilter: EntranceFilter = getInitFilter(useLocation());

    // 新建弹窗是否显示
    const [newVisible, setNewVisible] = useState(false);

    const {
        loading,
        entrances,
        allEntrances,
        filter,
        setFilter,
        reload,
        columns,
        doFilter,
        selectedEntrance,
        setSelectedEntrance,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        duplicateVisible, setDuplicateVisible,
        expandedRowRender
    } = useTable(initFilter);

    // 过滤参数改变时跳转路由
    const doNavigate = (param: EntranceFilter) => {
        let query = '';
        if (param.query || param.disabled) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }

        if (query) {
            navigate(`${BASE_PATH}/policies/entrance/?${query}`);
        } else {
            navigate(`${BASE_PATH}/policies/entrance`);
        }
    }

    return <>
        <div style={{ paddingTop: 10 }}>
            <Row>
                <Col span={20}>
                    <Layout className='mb20 search-bar'>
                        <Content className='pr10'>
                            <Input
                                placeholder='根据名称、描述搜索'
                                showClear
                                value={filter.query}
                                onChange={(e) => {
                                    setFilter({
                                        ...filter,
                                        query: e
                                    });
                                    doNavigate({
                                        ...filter,
                                        query: e
                                    });
                                }}
                                suffix={<IconSearch />}
                            />
                        </Content>
                        <Sider>
                            <Select
                                style={{ width: 120 }}
                                insetLabel="状态"
                                optionList={[
                                    { value: '', label: '全部' },
                                    { value: 'true', label: '禁用' },
                                    { value: 'false', label: '启用' }
                                ]}
                                value={filter.disabled}
                                onChange={(value) => {
                                    const val = value as string;
                                    setFilter({
                                        ...filter,
                                        disabled: val
                                    });
                                    doNavigate({
                                        ...filter,
                                        disabled: val
                                    });
                                }}
                            />
                        </Sider>
                    </Layout>
                </Col>
                <Col span={4} style={{ textAlign: 'right' }}>
                    <Space>
                        <Button
                            theme='solid'
                            type='primary'
                            onClick={() => setNewVisible(true)}
                        >添加策略</Button>

                        <Dropdown
                            position='bottomRight'
                            render={
                                <Dropdown.Menu>
                                    <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/policies/entrance/baseline-check`)}>基线检测项配置</Dropdown.Item>
                                    <Dropdown.Divider />
                                    <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/policies/entrance/app-check`)}>应用检测项配置</Dropdown.Item>
                                    <Dropdown.Divider />
                                    <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/policies/entrance/process-check`)}>进程检测项配置</Dropdown.Item>
                                </Dropdown.Menu>
                            }
                        >
                            <Button theme='solid' onClick={() => { }} icon={<IconSetting />}></Button>
                        </Dropdown>
                    </Space>
                </Col>
            </Row>

            <Table
                rowKey={(record?: EntrancePolicy) => record ? record.id + '' : ''}
                loading={loading}
                columns={columns}
                dataSource={entrances}
                pagination={false}
                // expandedRowRender={expandedRowRender}
                empty={<TableEmpty loading={loading}></TableEmpty>}
            ></Table>
        </div>
        {newVisible && <New
            close={() => {
                setNewVisible(false);
            }}
            success={() => {
                setNewVisible(false);
                reload();
            }}
        ></New>}
        {editVisible && selectedEntrance && <Edit
            id={selectedEntrance.id}
            close={() => setEditVisible(false)}
            success={() => {
                setEditVisible(false);
                setSelectedEntrance(undefined);
                reload();
            }}
        />}
        {delVisible && selectedEntrance && <Del
            close={() => setDelVisible(false)}
            success={() => {
                setDelVisible(false);
                reload();
                setSelectedEntrance(undefined);
            }}
            record={selectedEntrance}
        />}
        {duplicateVisible && selectedEntrance && <New
            close={() => setDuplicateVisible(false)}
            success={() => {
                setDuplicateVisible(false);
                reload();
            }}
            template={selectedEntrance}
        />
        }
        
    </>
}

export default Index;
