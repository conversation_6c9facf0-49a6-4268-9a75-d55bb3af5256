import React, { useState, useContext } from "react";
import { IconHelpCircle } from '@douyinfe/semi-icons';
import pinyin from 'tiny-pinyin';

import { EntrancePolicy, BaselinePolicy, AppPolicy, ProcessPolicy } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

import { sanitizeLabel } from '@/utils/common';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { Typography, Modal, Form, Row, Col, Button, Space, Notification, Divider, Popover, Badge } from "@douyinfe/semi-ui";
import { IconExit, IconGridView, IconListView } from '@douyinfe/semi-icons';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import EntranceScopes from '../entrance-scopes';

import styles from '@/pages/entrance/index.module.scss';
import { flylayerClient } from "@/services/core";

import BaselinePolicyEditor from '@/pages/entrance/baseline-policy-editor';
import AppPolicyEditor from '@/pages/entrance/app-policy-editor';
import ProcessPolicyEditor from '@/pages/entrance/process-policy-editor';

const { Input, RadioGroup, Radio, Slider, Checkbox, TextArea } = Form;
const { Title, Paragraph } = Typography;
interface Props {
    close: () => void,
    success?: () => void,
    template?: EntrancePolicy
}

const Index: React.FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        alias: string;
        description: string;
        priority: number;
        disabled: number;
        useBaselineCheck: boolean;
        useAppCheck: boolean;
        useProcessCheck: boolean;
    }>>();

    const [scopes, setScopes] = useState<string[]>([]);
    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const handleSubmit = async () => {
        if (!formApi) {
            return
        }
        await formApi.validate();
        const values = formApi?.getValues();
        if (!values) {
            return;
        }

        const alias = values.alias.trim();
        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const priority = values.priority;
        const disabled = values.disabled === 0 ? false : true;
        const useBaselineCheck = values.useBaselineCheck ? true : false;
        const useAppCheck = values.useAppCheck ? true : false;
        const useProcessCheck = values.useProcessCheck ? true : false;

        const policy = new EntrancePolicy();
        policy.alias = alias;
        policy.name = name;
        policy.description = description;
        policy.priority = priority;
        policy.disabled = disabled;
        policy.useBaselineCheck = useBaselineCheck;
        policy.useAppCheck = useAppCheck;
        policy.useProcessCheck = useProcessCheck;

        policy.scopes = scopes;
        policy.baselinePolicies = baselinePolicies;
        policy.forbiddenAppPolicies = forbiddenAppPolicies;
        policy.requiredAppPolicies = requiredAppPolicies;
        policy.forbiddenProcessPolicies = forbiddenProcessPolicies;
        policy.requiredProcessPolicies = requiredProcessPolicies;

        setSaveLoading(true);

        flylayerClient.createEntrancePolicy({
            entrancePolicy: policy,
            flynetId: flynet.id
        }).then(() => {
            Notification.success({
                title: '创建成功',
                content: '检测策略创建成功'
            });
            props.success && props.success();
            props.close();
        }).catch((e) => {
            Notification.error({
                title: '创建失败',
                content: e.message
            });
        }).finally(() => {
            setSaveLoading(false);
        });


    }

    // 基线检测策略对话框是否可见
    const [baselinePoliciesVisible, setBaselinePoliciesVisible] = useState(false);
    // 基线检测策略树数据
    const [baselinePolicies, setBaselinePolicies] = useState<BaselinePolicy[]>(props.template ? props.template.baselinePolicies : []);
    // 违规应用检测策略对话框是否可见
    const [forbiddenAppPoliciesVisible, setForbiddenAppPoliciesVisible] = useState(false);
    // 违规应用检测策略树数据
    const [forbiddenAppPolicies, setForbiddenAppPolicies] = useState<AppPolicy[]>(props.template ? props.template.forbiddenAppPolicies : []);
    // 必装应用检测策略对话框是否可见
    const [requiredAppPoliciesVisible, setRequiredAppPoliciesVisible] = useState(false);
    // 必装应用检测策略树数据
    const [requiredAppPolicies, setRequiredAppPolicies] = useState<AppPolicy[]>(props.template ? props.template.requiredAppPolicies : []);
    // 违规进程检测策略对话框是否可见
    const [forbiddenProcessPoliciesVisible, setForbiddenProcessPoliciesVisible] = useState(false);
    // 违规进程检测策略树数据
    const [forbiddenProcessPolicies, setForbiddenProcessPolicies] = useState<ProcessPolicy[]>(props.template ? props.template.forbiddenProcessPolicies : [
        new ProcessPolicy()
    ]);
    // 必装进程检测策略对话框是否可见
    const [requiredProcessPoliciesVisible, setRequiredProcessPoliciesVisible] = useState(false);
    // 必装进程检测策略树数据
    const [requiredProcessPolicies, setRequiredProcessPolicies] = useState<ProcessPolicy[]>(props.template ? props.template.requiredProcessPolicies : [
        new ProcessPolicy()
    ]);

    const getForbiddenProcessPoliciesCount = () => {
        let count = 0;
        forbiddenProcessPolicies.forEach((policy) => {
            count += policy.linuxProcesses.length;
            count += policy.windowsProcesses.length;
            count += policy.macProcesses.length;
        });
        return count;
    }

    const getRequiredProcessPoliciesCount = () => {
        let count = 0;
        requiredProcessPolicies.forEach((policy) => {
            count += policy.linuxProcesses.length;
            count += policy.windowsProcesses.length;
            count += policy.macProcesses.length;
        });
        return count;
    }



    return <><Modal
        title="添加检测策略"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={900}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Form
            getFormApi={setFormApi}
            initValues={
                props.template ? {
                    // alias: props.template.alias,
                    name: '',
                    description: props.template.description,
                    priority: props.template.priority,
                    disabled: props.template.disabled ? 1 : 0,
                    useBaselineCheck: props.template.useBaselineCheck,
                    useAppCheck: props.template.useAppCheck,
                    useProcessCheck: props.template.useProcessCheck
                } : {
                    disabled: 0,
                }}
            onValueChange={(values, changedValue) => {
                if (changedValue.hasOwnProperty('alias')) {
                    formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                }
            }}
        >
            {({ values }) => (<>
                <Row gutter={20}>
                    <Col span={9}>
                        <Input field='alias' label='名称' trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={9}>
                        <Input field='name'
                            label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '编码不能为空';
                                }
                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '编码不能以-开头'
                                }
                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "编码只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            required />
                    </Col>
                    <Col span={6}>
                        <RadioGroup field="disabled" label="状态">
                            <Radio checked value={0}>启用</Radio>
                            <Radio value={1}>禁用</Radio>
                        </RadioGroup>
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <TextArea field="description" label="策略描述" autosize rows={1}></TextArea>
                    </Col>
                </Row>
                <Divider />
                <Row className="mb20">
                    <Col span={24} style={{}}>
                        <Slider
                            field="priority"
                            label='优先级'
                            className={styles.slider}
                            railStyle={{
                                // background: 'linear-gradient(to right, var(--semi-color-fill-3) 0%, var(--semi-color-fill-0) 100%)'
                            }}

                            marks={{ 0: '高优', 25: '较高优', 50: '一般', 75: '较低优', 100: '低优' }}
                            initValue={10} min={0} max={100} />
                    </Col>
                </Row>
                <Row gutter={16} className="mb20">
                    <Col span={8}>
                        <Checkbox className={styles.check} extra={<div className={styles.checkCard}>
                            <Button icon={<IconExit />} className={styles.checkIcon} size="large"></Button>
                            <Title heading={6} className="mb5">终端基线检测</Title>
                            <Paragraph className='mb20' type='tertiary'>
                                终端入网检测项
                            </Paragraph>
                        </div>}
                            field="useBaselineCheck" noLabel></Checkbox>

                        <Button
                            disabled={!values.useBaselineCheck}
                            onClick={() => setBaselinePoliciesVisible(true)}>
                            <Space>            终端基线检测项
                                <Badge count={baselinePolicies.length} style={{ transform: 'translateY(-1px)' }} />
                            </Space>
                        </Button>


                    </Col>
                    <Col span={8}>
                        <Checkbox className={styles.check} extra={<div className={styles.checkCard}>
                            <Button icon={<IconGridView />} className={styles.checkIcon} size="large"></Button>
                            <Title heading={6} className="mb5">应用风险检测</Title>
                            <Paragraph className='mb20' type='tertiary'>
                                检测黑名单应用，必装应用
                            </Paragraph>
                        </div>
                        } field="useAppCheck" noLabel></Checkbox>
                        <Space>
                            <Button disabled={!values.useAppCheck} onClick={() => setForbiddenAppPoliciesVisible(true)}>
                                <Space>违规应用<Badge count={forbiddenAppPolicies.length} style={{ transform: 'translateY(-1px)' }} /></Space>
                            </Button>
                            <Button disabled={!values.useAppCheck} onClick={() => setRequiredAppPoliciesVisible(true)}>
                                <Space>必装应用<Badge count={requiredAppPolicies.length} style={{ transform: 'translateY(-1px)' }} /></Space></Button>
                        </Space>
                    </Col>
                    <Col span={8}>
                        <Checkbox className={styles.check} extra={<div className={styles.checkCard}>

                            <Button icon={<IconListView />} className={styles.checkIcon} size="large"></Button>
                            <Title heading={6} className="mb5">进程风险检测</Title>
                            <Paragraph className='mb20' type='tertiary'>
                                检测黑名单进程，必装进程
                            </Paragraph>
                        </div>
                        } field="useProcessCheck" noLabel></Checkbox>
                        <Space>
                            <Button disabled={!values.useProcessCheck} onClick={() => setForbiddenProcessPoliciesVisible(true)}>
                                <Space>黑名单进程<Badge count={getForbiddenProcessPoliciesCount()} style={{ transform: 'translateY(-1px)' }} /></Space></Button>
                            <Button disabled={!values.useProcessCheck} onClick={() => setRequiredProcessPoliciesVisible(true)}>
                                <Space>必装进程<Badge count={getRequiredProcessPoliciesCount()} style={{ transform: 'translateY(-1px)' }} /></Space></Button>
                        </Space>
                    </Col>
                </Row>
                <Divider className="mb10" />
                <EntranceScopes
                    value={scopes}
                    onChange={(value) => {
                        setScopes(value);
                    }}
                ></EntranceScopes></>)}


        </Form>
    </Modal>
        {
            baselinePoliciesVisible && <BaselinePolicyEditor
                close={() => setBaselinePoliciesVisible(false)}
                baselinePolicies={baselinePolicies}
                onChange={(value) => {
                    setBaselinePolicies(value);
                }}
            ></BaselinePolicyEditor>
        }
        {
            forbiddenAppPoliciesVisible && <AppPolicyEditor
                title="选择违规应用"
                close={() => setForbiddenAppPoliciesVisible(false)}
                success={(apppolicies => setForbiddenAppPolicies(apppolicies))}
                appPolicies={forbiddenAppPolicies}
                oppoAppPolicies={requiredAppPolicies}
                oppoLabel="必装应用"
            ></AppPolicyEditor>
        }
        {
            requiredAppPoliciesVisible && <AppPolicyEditor
                title="选择必装应用"
                close={() => setRequiredAppPoliciesVisible(false)}
                success={(apppolicies => setRequiredAppPolicies(apppolicies))}
                appPolicies={requiredAppPolicies}
                oppoAppPolicies={forbiddenAppPolicies}
                oppoLabel="违规应用"
            ></AppPolicyEditor>
        }
        {
            forbiddenProcessPoliciesVisible && <ProcessPolicyEditor
                title="选择违规进程"
                close={() => setForbiddenProcessPoliciesVisible(false)}
                success={(processPolicies => setForbiddenProcessPolicies([processPolicies]))}
                processPolicy={forbiddenProcessPolicies[0]}
            ></ProcessPolicyEditor>
        }
        {
            requiredProcessPoliciesVisible && <ProcessPolicyEditor
                title="选择必装进程"
                close={() => setRequiredProcessPoliciesVisible(false)}
                success={(processPolicies => setRequiredProcessPolicies([processPolicies]))}
                processPolicy={requiredProcessPolicies[0]}
            ></ProcessPolicyEditor>
        }
    </>
}

export default Index;

