import React, { useContext, useEffect, useState } from 'react'
import { Input, Checkbox, Select, Typography, Modal, Row, Col, Button, Space, Notification, Tag, Skeleton, Tooltip } from "@douyinfe/semi-ui";
import { AppPolicy, <PERSON><PERSON><PERSON><PERSON><PERSON>, RiskLevel } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { IconSearch, IconMinus, IconArrowUpRight } from '@douyinfe/semi-icons';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { flylayerClient } from "@/services/core";

import styles from '@/pages/entrance/index.module.scss'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { BASE_PATH } from '@/constants/router';

interface Props {
    title: string,
    close: () => void,
    success?: (appPolicies: AppPolicy[]) => void,
    appPolicies: AppPolicy[],
    oppoAppPolicies: AppPolicy[],
    oppoLabel: string
}

const { Text } = Typography;

const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    // 保存按钮loading
    const [loading, setLoading] = useState(false);

    const listOs = [
        { value: OS.MACOS, label: 'macOS' },
        { value: OS.IOS, label: 'iOS' },
        { value: OS.WINDOWS, label: 'Windows' },
        { value: OS.LINUX, label: 'Linux' },
        { value: OS.ANDROID, label: 'Android' },
    ];
    const [filterOs, setFilterOs] = useState<OS[]>();

    const handleOsChange = (val: any) => {
        let os = val as OS[];
        setFilterOs(os);
    }


    const [query, setQuery] = useState('');
    const handleQueryChange = (val: string) => {
        setQuery(val);
    }

    const [appPolicies, setAppPolicies] = useState<AppPolicy[]>(props.appPolicies);

    const [appChecks, setAppChecks] = useState<AppCheck[]>();

    useEffect(() => {
        setLoading(true);
        flylayerClient.listAppCheck({
            flynetId: flynet.id
        }).then(res => {
            setAppChecks(res.appChecks);
        }, err => {
            Notification.error({
                title: '获取应用检测项列表失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        });
    }, [])

    const handleSubmit = async () => {
        if (props.success) {
            props.success(appPolicies);
        }
        props.close();
    }

    const getChecked = (appCheck: AppCheck) => {
        let appPolicy = appPolicies.find((appPolicy) => {
            return appPolicy.appCheck?.id == appCheck.id;
        })
        return appPolicy ? true : false;
    }

    const getDisabled = (appCheck: AppCheck, oppoAppPolicies: AppPolicy[]) => {
        let appPolicy = oppoAppPolicies.find((appPolicy) => {
            return appPolicy.appCheck?.id == appCheck.id;
        })
        return appPolicy ? true : false;
    }

    return <><Modal
        title={<>{props.title}&nbsp;

            <a className='link-external' style={{
                fontSize: '12px',
                fontWeight: 'normal',
            }} target='_blank' href={`${BASE_PATH}/policies/entrance/app-check`} onClick={(e) => { e.stopPropagation() }}>
                应用检测项管理<IconArrowUpRight />
            </a>
        </>}
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={1200}
        closeOnEsc={true}
        maskClosable={false}
    >

        <Select multiple
            className='mb20'
            style={{ width: '100%' }}
            optionList={listOs}
            insetLabel="操作系统"
            onChange={handleOsChange}
            value={filterOs}></Select>
        <Row className={styles.splitRow}>
            <Col span={12} className={styles.splitLeftCol}>
                <Input value={query}
                    onChange={handleQueryChange}
                    style={{ width: '100%' }}
                    prefix={<IconSearch />}
                    className='mb20'
                    showClear></Input>
                <Skeleton loading={loading} active placeholder={<Skeleton.Image style={{ height: 150 }} />}>
                    {appChecks?.map((appCheck, index) => {
                        let findOs = false;

                        if (!filterOs || filterOs.length == 0) {
                            findOs = true;
                        }

                        filterOs?.forEach(os => {
                            appCheck.items.forEach(item => {
                                if (os == item.os) {
                                    findOs = true;
                                }
                            })
                        });

                        if (!findOs) {
                            return null
                        }
                        const disabled = getDisabled(appCheck, props.oppoAppPolicies);
                        return <div key={index} className={styles.appCheckRow}>
                            <Checkbox
                                disabled={disabled}
                                checked={getChecked(appCheck)} onChange={(e) => {
                                    const checked = e.target.checked;
                                    if (checked) {
                                        let appPolicy = new AppPolicy();
                                        appPolicy.appCheck = appCheck;
                                        setAppPolicies([...appPolicies, appPolicy]);
                                    } else {
                                        let newAppPolicies = appPolicies.filter((appPolicy) => {
                                            return appPolicy.appCheck?.id != appCheck.id;
                                        })
                                        setAppPolicies(newAppPolicies);
                                    }
                                }} extra={<>
                                    <Space>
                                        {disabled ? <Tooltip content={props.oppoLabel}>{appCheck.alias}</Tooltip> : appCheck.alias}
                                        <Space>

                                            {appCheck.items?.map((item, i) => <Tag key={i}>
                                                {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                                            </Tag>)}
                                        </Space>
                                    </Space>
                                </>}></Checkbox>
                        </div>
                    })}
                </Skeleton>
            </Col>
            <Col span={12} className={styles.splitRightCol}>
                <Row className={styles.appPolicyTitle}>
                    <Col span={16}>
                        <Text>已选择： {appPolicies.length}个</Text>
                    </Col>
                    <Col span={8} className={styles.rightColumn}>
                        <Button size='small'>清空</Button>
                    </Col>
                </Row>
                {appPolicies?.map((appPolicy, i) => {
                    return <Row key={i} className={styles.appPolicyRow}>
                        <Col span={18} className={styles.appPolicyCheck}>
                            <Space><Text>{appPolicy.appCheck?.alias}</Text><Space>

                                {appPolicy.appCheck?.items?.map((item, i) => <Tag key={i}>
                                    {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                                </Tag>)}
                            </Space></Space>
                        </Col>
                        <Col span={6} className={styles.appPolicyLevel}>
                            <Space>
                                <Select style={{ width: 80 }} size='small'
                                    value={appPolicy.riskLevel}
                                    onChange={val => {
                                        appPolicy.riskLevel = val as RiskLevel;
                                        setAppPolicies([...appPolicies]);

                                    }}
                                >
                                    <Select.Option value={RiskLevel.LOW}>低危</Select.Option>
                                    <Select.Option value={RiskLevel.MEDIUM}>中危</Select.Option>
                                    <Select.Option value={RiskLevel.HIGH}>高危</Select.Option>
                                </Select>
                                <Button size='small' icon={<IconMinus />} onClick={() => {
                                    let newAppPolicies = appPolicies.filter((policy) => {
                                        return policy.appCheck?.id != appPolicy.appCheck?.id;
                                    })
                                    setAppPolicies(newAppPolicies);
                                }}></Button>
                            </Space>
                        </Col>
                    </Row>
                })}
            </Col>
        </Row>
    </Modal>
    </>
}

export default Index;