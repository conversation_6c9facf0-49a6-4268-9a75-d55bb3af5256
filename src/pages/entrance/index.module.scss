.titleRow {
    background-color: var(--semi-color-bg-1);
    color: var(--semi-color-text-2);
    font-size: 14px;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid var(--semi-color-border);
    padding: 8px 16px;
    vertical-align: middle;
    overflow-wrap: break-word;
    position: relative;
}

.container {
    margin-bottom: 20px;
}

.inlineSwitch {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0 !important;

    >div {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}
.tag {
    border-top: 1px solid var(--semi-color-border);
    border-left: 1px solid var(--semi-color-border);
    border-right: 1px solid var(--semi-color-border);
    .title {
        border-bottom: 1px solid var(--semi-color-border);
        padding: 5px 5px 5px 15px;
    }
}
.rightColumn {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0 !important;
}

.bigRightColumn {
    padding-right: 0 !important;
}

.bodyRow {
    
    border-bottom: 1px solid var(--semi-color-border);
    
    box-sizing: border-box;
    
    vertical-align: middle;
    font-size: 14px;

    .bodyRowCell {
        
        overflow-wrap: break-word;
        border-left: none;
        border-right: none;
        padding: 16px !important;
        box-sizing: border-box;
        
        vertical-align: middle;
        font-size: 14px;
    
    }
}


.bodyRow:last-of-type {
    border-bottom: none;
}

.subBodyRow {
    
    
    box-sizing: border-box;
    
    vertical-align: middle;
    font-size: 14px;

    .subBodyRowCell {
        border-left: 1px solid var(--semi-color-border) !important;
        border-bottom: 1px solid var(--semi-color-border);
        overflow-wrap: break-word;
        border-left: none;
        border-right: none;
        padding: 16px !important;
        box-sizing: border-box;
        
        vertical-align: middle;
        font-size: 14px;
        
    
    }
}


.bodyText {
    font-size: 14px;
    color: var(--semi-color-text-2);
    font-weight: 400;
    overflow-wrap: break-word;
    line-height: 24px!important;
}

.expandedRow {
    background-color: var(--semi-color-bg-1);
    color: var(--semi-color-text-2);
}


.tableTitle {
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 8px;
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
        line-height: 32px;
    }
}

.subTableTitle {
    >div {
        padding-right: 8px;
    }
}


.tableBody {
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 10px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.tableBody:last-child {
    border-bottom: none;
}



.tableBodyError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);

    padding-top: 5px;
    padding-bottom: 5px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}



.subTableBody {
    margin-bottom: 10px;
    >div {
        padding-right: 8px;
    }

}

.templateEditor {
    >div {
        height: 450px !important;
    }
}

.check {
    border: 1px solid var(--semi-color-border);
    border-radius: var(--semi-border-radius-small);
    padding: 10px;
    .checkCard {
        padding-top: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .checkIcon {
            width: 40px;
            margin-bottom: 10px;
        }
    }
}


.slider {
    
    span[class="semi-slider-mark"] {
        word-break: keep-all;
    }
}

.appPolicyTitle {
    line-height: 32px;
    margin-bottom: 20px;
}

.appCheckRow {
    margin-bottom: 10px;
}


.appPolicyRow {
    margin-bottom: 10px;
    .appPolicyLevel {
        
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
}

.splitRow {
    border: 1px solid var(--semi-color-border);
    padding: 10px;
    .splitLeftCol {
        padding: 10px;
        border-right: 1px solid var(--semi-color-border);
    }
    .splitRightCol {
        padding: 10px;
    }
}

.container {
    max-height: 600px;
    padding-right: 2px;
    overflow-y: auto;
}
.tag {
    border-bottom: 1px solid var(--semi-color-border);
    border-left: 1px solid var(--semi-color-border);
    border-right: 1px solid var(--semi-color-border);
    .title {
        border-bottom: 1px solid var(--semi-color-border);
        padding: 5px 5px 5px 15px;
    }
}
.tag:last-of-type {
    border: 1px solid var(--semi-color-border);
}
.baselineCheckRow {

    // border: 1px solid var(--semi-color-border);
    .baseLineCheck {
        padding: 10px!important;
        background-color: var(--semi-color-fill-0);
    }
    .baselineItemRow {

        border-bottom: 1px solid var(--semi-color-border);
        border-left: 1px solid var(--semi-color-border);
        .baselineItemSelect {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .baselineItemCheck {
            padding: 10px!important;
        }
    }
}

// 子网显示样式
.subnet {
    border-radius: var(--semi-border-radius-medium);
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-fill-0);
    padding: 20px;
}

.machineDetail {
    // box-shadow: var(--semi-shadow-elevated);
    background-color: var(--semi-color-bg-2);
    border-radius: 4px;
    padding: 10px;
    flex-grow: 1;
}


.heading {
    display: flex;
    align-items: flex-start;
}
.headingBadge {
    display: flex;
    width: 12px;
    height: 12px;
    margin-top: 10px;
    margin-left: 4px;
}
.generalInfo {
    margin-bottom: 40px;
    
    tr:last-of-type {
        padding-left: 10px;
        border-left: 1px solid var(--semi-color-border);
    }
}
.generalInfoNoStatus{
    margin-bottom: 40px;
    tr:last-of-type {
        padding-left: 11px;
    }
}
