import React, { useContext, useEffect, useState } from 'react'
import { Typography, Modal, Row, Col, Skeleton, Checkbox, Select, Space, Button } from "@douyinfe/semi-ui";
import { BaselinePolicy, BaselineCheck, RiskLevel, BaselineCheckGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { flylayerClient } from '@/services/core';
import { IconEdit, IconArrowUpRight } from '@douyinfe/semi-icons';

import styles from '@/pages/entrance/index.module.scss'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

const { Title } = Typography;


const calMapBaselineCheck = (baselineChecks: BaselineCheck[], baselinePolicies: BaselinePolicy[]) => {
    let mapBaselineCheck = new Map<BigInt, boolean>();
    let mapBaselineIndeterminateCheck = new Map<BigInt, boolean>();
    let mapBaselineCheckItem = new Map<string, boolean>();
    console.log(baselineChecks, baselinePolicies);
    baselineChecks.forEach(baselineCheck => {
        let allChecked = true;
        let oneChecked = false;
        baselineCheck.items?.forEach(item => {
            let checked = false;
            baselinePolicies.forEach(policy => {
                if (policy.item?.name == item.name && policy.item?.os == item.os) {
                    checked = true;
                }
            })
            mapBaselineCheckItem.set(baselineCheck.id + '-' + item.os, checked);
            if (!checked) {
                allChecked = false;
            } else {
                oneChecked = true;
            }
        })
        mapBaselineCheck.set(baselineCheck.id, allChecked);
        mapBaselineIndeterminateCheck.set(baselineCheck.id, oneChecked && !allChecked);
    })

    return {
        mapBaselineCheck,
        mapBaselineIndeterminateCheck,
        mapBaselineCheckItem
    }
}

interface Props {
    close: () => void,
    success?: () => void,
    baselinePolicies: BaselinePolicy[],
    onChange: (baselinePolicies: BaselinePolicy[]) => void
}
const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    
    const listOs = [
        { value: OS.MACOS, label: 'macOS' },
        { value: OS.IOS, label: 'iOS' },
        { value: OS.WINDOWS, label: 'Windows' },
        { value: OS.LINUX, label: 'Linux' },
        { value: OS.ANDROID, label: 'Android' },
    ];

    // loading
    const [loading, setLoading] = useState(false);
    const handleSubmit = async () => {
        console.log(baselinePolicies);
        props.onChange(baselinePolicies);
        props.close();
    }

    const [filterOs, setFilterOs] = useState<OS[]>();

    const handleOsChange = (val: any) => {
        let os = val as OS[];
        setFilterOs(os);
    }


    // 基线分类
    const [baselineTags, setBaselineTags] = useState<{
        value: string,
        label: string
    }[]>([]);
    // 基线检测项
    const [baselineChecks, setBaselineChecks] = useState<BaselineCheck[]>();


    // 基线检测策略
    const [baselinePolicies, setBaselinePolicies] = useState<BaselinePolicy[]>(props.baselinePolicies);

    // 基线检测项选中状态
    const [mapBaselineCheck, setMapBaselineCheck] = useState<Map<BigInt, boolean>>(new Map());
    // 基线检测项半选中状态
    const [mapBaselineIndeterminateCheck, setMapBaselineIndeterminateCheck] = useState<Map<BigInt, boolean>>(new Map());
    // 基线检测项子项选中状态
    const [mapBaselineCheckItem, setMapBaselineCheckItem] = useState<Map<string, boolean>>(new Map());

    const getBaselineCheckItemLevel = (baselineCheck: BaselineCheck, os: OS) => {
        let level = RiskLevel.LOW;
        baselinePolicies.forEach(policy => {
            if (policy.item?.name == baselineCheck.name && policy.item?.os == os) {
                level = policy.riskLevel;
            }
        })
        return level;
    }

    const query = () => {
        setLoading(true);
        flylayerClient.listBaselineCheck({
            flynetId: flynet.id
        }).then(res => {
            const baselineChecks = res.baselineChecks.map(item => {
                if (item.group == null) {
                    item.group = new BaselineCheckGroup({
                        name: '',
                        alias: ''
                    }); 
                }
                return item;
            });
            setBaselineChecks(res.baselineChecks);
            const names: string[] = [];
            const tags = new Set<{
                value: string,
                label: string
            }>();
            res.baselineChecks.forEach(item => {
                if (!names.includes(item.group?.name || '')) {

                    tags.add({
                        value: item.group?.name || '',
                        label: item.group?.alias || ''
                    });
                    names.push(item.group?.name || '');
                }
            });
            setBaselineTags(Array.from(tags));

            const { mapBaselineCheck, mapBaselineIndeterminateCheck, mapBaselineCheckItem } = calMapBaselineCheck(res.baselineChecks, baselinePolicies);


            setMapBaselineCheck(mapBaselineCheck);
            setMapBaselineIndeterminateCheck(mapBaselineIndeterminateCheck);
            setMapBaselineCheckItem(mapBaselineCheckItem);

        }, err => {
            console.error(err)
        }).finally(() => {
            setLoading(false);
        });
    }

    useEffect(() => {
        query();
    }, [])




    return <><Modal
        title={<>配置检测项&nbsp;
            
            <a className='link-external' style={{
                fontSize: '12px',
                fontWeight: 'normal',
            }} target='_blank' href={`${BASE_PATH}/policies/entrance/baseline-check`} onClick={(e) => { e.stopPropagation() }}>
            基线检测项管理<IconArrowUpRight />
                    </a>
        </>}
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={600}
        closeOnEsc={true}
        maskClosable={false}
    >

        <Select multiple
            className='mb20'
            style={{ width: '100%' }}
            optionList={listOs}
            insetLabel="操作系统"
            onChange={handleOsChange}
            value={filterOs}></Select>
        <Skeleton loading={loading} placeholder={
            <Skeleton.Image style={{ width: '100%', height: '433px', marginBottom: 20 }}></Skeleton.Image>
        } active>

            <div className={styles.container}>
                {
                    baselineTags.map((tag, index) => {
                        let tagBaselineChecks = baselineChecks?.filter((baselineCheck) => {
                            if (baselineCheck.group?.name != tag.value) {
                                return false;
                            }

                            if (!filterOs || filterOs.length == 0) {
                                return true;
                            }

                            let findOs = false;
                            filterOs.forEach(os => {
                                baselineCheck.items.forEach(item => {
                                    if (os == item.os) {
                                        findOs = true;
                                    }
                                })
                            })

                            return findOs;
                        })

                        return <div className={styles.tag} key={index}>
                            {tag.label && <Title className={styles.title} heading={6}>{tag.label}:{tagBaselineChecks?.length}</Title>}
                            
                                
                                
                            {tagBaselineChecks?.map((baselineCheck, i) => {
                                return <Row key={i} className={styles.baselineCheckRow}>
                                    <Col span={10} className={styles.baseLineCheck}>
                                        <Checkbox
                                            checked={mapBaselineCheck.get(baselineCheck.id) ? true : false}
                                            indeterminate={mapBaselineIndeterminateCheck.get(baselineCheck.id) ? true : false}
                                            onChange={e => {
                                                const checked = e.target.checked ? true : false;
                                                mapBaselineCheck.set(baselineCheck.id, checked);
                                                mapBaselineIndeterminateCheck.set(baselineCheck.id, false);

                                                baselineCheck.items?.forEach(item => {
                                                    mapBaselineCheckItem.set(baselineCheck.id + '-' + item.os, checked);
                                                })

                                                setMapBaselineCheck(new Map(mapBaselineCheck));
                                                setMapBaselineCheckItem(new Map(mapBaselineCheckItem));
                                                setMapBaselineIndeterminateCheck(new Map(mapBaselineIndeterminateCheck));

                                                if (checked) {
                                                    let newBaselinePolicies: BaselinePolicy[] = [...baselinePolicies];
                                                    baselineCheck.items?.forEach(item => {
                                                        let exist = false;
                                                        baselinePolicies.forEach(policy => {
                                                            if (policy.item?.name == item.name) {
                                                                exist = true;
                                                            }
                                                        });
                                                        if (!exist) {
                                                            let newPolicy = new BaselinePolicy();
                                                            newPolicy.item = item;
                                                            newPolicy.riskLevel = RiskLevel.LOW;
                                                            newPolicy.type = baselineCheck.type;
                                                            newBaselinePolicies.push(newPolicy);
                                                        }
                                                    })
                                                    setBaselinePolicies(newBaselinePolicies);
                                                } else {
                                                    let newBaselinePolicies: BaselinePolicy[] = [];

                                                    baselinePolicies.forEach(policy => {
                                                        if (policy.item?.name != baselineCheck.name) {
                                                            newBaselinePolicies.push(policy);
                                                        }
                                                    });
                                                    setBaselinePolicies(newBaselinePolicies);
                                                }
                                            }}
                                        >{baselineCheck.alias}</Checkbox>
                                    </Col>
                                    <Col span={14}>
                                        {baselineCheck.items?.map((item, j) => <Row key={j} className={styles.baselineItemRow}>
                                            <Col span={12} className={styles.baselineItemCheck}>
                                                <Checkbox
                                                    checked={mapBaselineCheckItem.get(baselineCheck.id + '-' + item.os)}
                                                    onChange={e => {
                                                        const checked = e.target.checked ? true : false;
                                                        mapBaselineCheckItem.set(baselineCheck.id + '-' + item.os, checked);
                                                        if (checked) {
                                                            mapBaselineCheck.set(baselineCheck.id, checked);
                                                        }

                                                        let allChecked = true;
                                                        let oneChecked = false;
                                                        baselineCheck.items?.forEach(item => {
                                                            if (!mapBaselineCheckItem.get(baselineCheck.id + '-' + item.os)) {
                                                                allChecked = false;
                                                            } else {
                                                                oneChecked = true;
                                                            }
                                                        })
                                                        mapBaselineCheck.set(baselineCheck.id, allChecked);
                                                        mapBaselineIndeterminateCheck.set(baselineCheck.id, oneChecked && !allChecked);

                                                        setMapBaselineCheck(new Map(mapBaselineCheck));
                                                        setMapBaselineCheckItem(new Map(mapBaselineCheckItem));

                                                        if (checked) {
                                                            let newPolicy = new BaselinePolicy();
                                                            newPolicy.item = item;
                                                            newPolicy.riskLevel = RiskLevel.LOW;
                                                            newPolicy.type = baselineCheck.type;
                                                            setBaselinePolicies([...baselinePolicies, newPolicy]);
                                                        } else {
                                                            let newBaselinePolicies: BaselinePolicy[] = [];

                                                            baselinePolicies.forEach(policy => {
                                                                if (policy.item?.name != baselineCheck.name) {
                                                                    newBaselinePolicies.push(policy);
                                                                }
                                                            });
                                                            setBaselinePolicies(newBaselinePolicies);
                                                        }



                                                    }}
                                                >
                                                    {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                                                </Checkbox>
                                            </Col>
                                            <Col span={12} className={styles.baselineItemSelect}>
                                                <Select style={{ width: '100%' }}
                                                    value={getBaselineCheckItemLevel(baselineCheck, item.os)}
                                                    onChange={val => {
                                                        let level = val as RiskLevel;
                                                        let newBaselinePolicies: BaselinePolicy[] = [];
                                                        baselinePolicies.forEach(policy => {
                                                            if (policy.item?.name != item.name || policy.item?.os != item.os) {
                                                                newBaselinePolicies.push(policy);
                                                            }
                                                        });
                                                        let newPolicy = new BaselinePolicy();
                                                        newPolicy.item = item;
                                                        newPolicy.riskLevel = level;
                                                        newPolicy.type = baselineCheck.type;
                                                        newBaselinePolicies.push(newPolicy);
                                                        setBaselinePolicies(newBaselinePolicies);

                                                    }}
                                                >
                                                    <Select.Option value={RiskLevel.LOW}>低危</Select.Option>
                                                    <Select.Option value={RiskLevel.MEDIUM}>中危</Select.Option>
                                                    <Select.Option value={RiskLevel.HIGH}>高危</Select.Option>
                                                </Select>
                                            </Col>
                                        </Row>)}
                                    </Col>
                                </Row>
                            })
                            }

                        </div>;
                    })
                }
            </div>
        </Skeleton>
    </Modal>
    </>
}

export default Index;