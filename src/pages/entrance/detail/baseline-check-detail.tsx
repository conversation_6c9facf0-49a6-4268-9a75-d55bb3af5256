import { FC } from 'react'
import { Modal } from '@douyinfe/semi-ui';
import { BaselineCheck } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import BaselineCheckDetail from '@/pages/entrance/components/baseline-check-detail';

interface Props {
    close: () => void,
    record: BaselineCheck
}

const Index: FC<Props> = (props) => {
    return <><Modal
        width={500}
        title={`基线检测策略 ${props.record.name} 详情`}
        visible={true}
        onOk={() => {
            props.close()
        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    ></Modal>

        <BaselineCheckDetail
            loading={false}
            baselineCheck={props.record}
            useConfig={false}

        ></BaselineCheckDetail>
    </>
}

export default Index;
