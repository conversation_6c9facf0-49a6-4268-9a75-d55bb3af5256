import { FC } from 'react'
import { Modal } from '@douyinfe/semi-ui';
import { AppCheck } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import AppCheckDetail from '@/pages/entrance/components/app-check-detail';

interface Props {
    close: () => void,
    record: AppCheck
}

const Index: FC<Props> = (props) => {
    return <><Modal
        width={500}
        title={`应用检测策略 ${props.record.name} 详情`}
        visible={true}
        onOk={() => {
            props.close()
        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    ></Modal>

        <AppCheckDetail
            loading={false}
            appCheck={props.record}
            useConfig={false}
        ></AppCheckDetail>
    </>
}

export default Index;
