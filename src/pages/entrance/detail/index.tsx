import React, { useState, useContext, useEffect } from "react";
import { useNavigate, useParams } from 'react-router-dom';
import { AppPolicy, ProcessPolicy, ProcessPolicyItem, EntrancePolicy, RiskLevel, BaselineCheck, BaselineCheckItem, BaselineCheck_Type, AppCheck } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Breadcrumb, Row, Col, Button, Typography, Tag, Notification, Dropdown, Descriptions, Popover, Space, Divider, Skeleton, Layout } from '@douyinfe/semi-ui';
import { IconSetting } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { BASE_PATH } from '@/constants/router';
import { flylayerClient } from '@/services/core';
import RepairMethodDisplay from '@/pages/entrance/components/repair-method-display';

import New from '../new';
import Edit from '../edit';
import Del from '../del';

import styles from '@/pages/entrance/index.module.scss'

const { Title, Paragraph, Text } = Typography;
const { Sider, Content } = Layout;
interface Props { }

const RiskLevelTag: React.FC<{ level: RiskLevel }> = ({ level }) => {
    return level == RiskLevel.LOW ? <Tag color="green">低危</Tag> : level == RiskLevel.MEDIUM ? <Tag>中危</Tag> : level == RiskLevel.HIGH ? <Tag color="red">高危</Tag> : <></>
}

const AppPolicyDisplay: React.FC<{ appPolicies: AppPolicy[], title: string, appChecks: AppCheck[] }> = ({ appPolicies, title, appChecks }) => {

    const getAppCheck = (name: string, appChecks: AppCheck[]) => {
        return appChecks.find(item => item.name == name);
    }
    return <>{appPolicies.length > 0 && <>

        <Title heading={6}>{title}</Title>
        <Row className={styles.tableTitle} >
            <Col span={4}>检测项</Col>
            <Col span={14}>操作系统</Col>
            <Col span={3}>风险等级</Col>
            <Col span={3}>应用详情</Col>
        </Row>
        {appPolicies.map((item, index) => {
            let name = item.appCheck?.name || '';
            const appCheck = getAppCheck(name, appChecks);
            return <Row key={index} className={styles.tableBody}>
                <Col span={4}><Text>{item.appCheck?.alias}</Text></Col>
                <Col span={14}>
                    <Space>
                        {item.appCheck?.items.map((item, i) => <Tag key={i}>
                            {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                        </Tag>)}</Space>
                </Col>
                <Col span={3}><RiskLevelTag level={item.riskLevel} /></Col>
                <Col span={3}>
                    {appCheck && <Popover trigger="click" content={<div className="p10" style={{ width: 800 }}>
                        {appCheck.items && appCheck.items.length > 0 && <div>

                            <div className={styles.expandedRow}>
                                <Row className={styles.tableTitle}>
                                    <Col span={4}>操作系统</Col>
                                    <Col span={3}>检测版本</Col>
                                    <Col span={8}>软件全称/Bundle ID</Col>
                                    <Col span={3}>软件发布者</Col>
                                    <Col span={6}>修复方案</Col>
                                </Row>
                                {appCheck.items?.map((item, i) => {
                                    return <Row key={i} className={styles.tableBody}>
                                            <Col span={4} className={styles.bodyRowCell}>
                                                <Text className={styles.bodyText}>
                                                    {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                                                </Text>
                                            </Col>
                                            <Col span={3} className={styles.bodyRowCell}>
                                                {item.version}
                                            </Col>
                                            <Col span={8} className={styles.bodyRowCell}>
                                                {item.fullName}/{item.bundleId}
                                            </Col>
                                            <Col span={3} className={styles.bodyRowCell}>
                                                {item.publisher}
                                            </Col>
                                            <Col span={6} className={styles.bodyRowCell}>
                                                <RepairMethodDisplay
                                                    repairMethod={item.repairMethod}
                                                    repairSolution={item.repairSolution}
                                                    repairSolutionName={item.repairSolutionName}
                                                    repairSolutionDescription={item.repairSolutionDescription}
                                                />
                                            </Col>
                                        </Row>
                                })}
                            </div>
                        </div>}
                    </div>}><Button>查看</Button></Popover>}
                </Col>
            </Row>
        })}
    </>}</>
}

const ProcessPolicyItemDisplay: React.FC<{ policyItems: ProcessPolicyItem[], title: string }> = ({ policyItems, title }) => {
    return <>{policyItems.length > 0 && <>
        <Title heading={6} style={{ marginTop: 20 }}>{title}</Title>

        <Row className={styles.tableTitle} >
            <Col span={6}>检测项</Col>
            <Col span={12}>签名</Col>
            <Col span={6}>风险等级</Col>
        </Row>
        {policyItems.map((item, index) => {
            return <Row key={index} className="mb10">
                <Col span={6}><Text>{item.name}</Text></Col>
                <Col span={12}><Text>{item.signature}</Text></Col>
                <Col span={6}><RiskLevelTag level={item.riskLevel} /></Col>
            </Row>
        })}
    </>}</>
}

const ProcessPolicyDisplay: React.FC<{ processPolicies: ProcessPolicy[], title: string }> = ({ processPolicies, title }) => {


    return <>{processPolicies.length > 0 && <>
        <Title heading={5}>{title}</Title>

        {processPolicies.map((fItem, fIndex) => {
            return <div key={fIndex} className="mb40">
                <ProcessPolicyItemDisplay policyItems={fItem.windowsProcesses} title='Windows系统' />
                <ProcessPolicyItemDisplay policyItems={fItem.macProcesses} title='Mac系统' />
                <ProcessPolicyItemDisplay policyItems={fItem.linuxProcesses} title='Linux系统' />

            </div>
        })}</>}</>
}

const Index: React.FC<Props> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);

    const navigate = useNavigate();

    const params = useParams<{ id: string }>()
    const id = params.id ? params.id : '';

    const [loading, setLoading] = useState(true);
    const [entrancePolicy, setEntrancePolicy] = useState<EntrancePolicy>();

    const [editVisible, setEditVisible] = useState(false);

    const [delVisible, setDelVisible] = useState(false);

    const [duplicateVisible, setDuplicateVisible] = useState(false);

    // 基线检测项
    const [baselineChecks, setBaselineChecks] = useState<BaselineCheck[]>();
    // 应用检测项
    const [appChecks, setAppChecks] = useState<AppCheck[]>();

    const query = async (id: bigint) => {
        setLoading(true);
        try {
            const res = await flylayerClient.getEntrancePolicy({
                id: id
            });
            setEntrancePolicy(res.entrancePolicy);

            const resBaselineChecks = await flylayerClient.listBaselineCheck({
                flynetId: flynetGeneral.id
            });

            setBaselineChecks(resBaselineChecks.baselineChecks);

            const resAppChecks = await flylayerClient.listAppCheck({
                flynetId: flynetGeneral.id
            });

            setAppChecks(resAppChecks.appChecks);

        } catch (err) {
            Notification.error({
                title: '查询数据失败',
            })
        } finally {
            setLoading(false);
        }
    }


    const getBaselineCheckAndItem = (name: string, os: OS, baselineChecks: BaselineCheck[]) => {
        let baselineCheck = baselineChecks.find(item => item.name == name);
        let baselineCheckItem = undefined;
        if (baselineCheck) {
            baselineCheckItem = baselineCheck.items.find(item => item.os == os);
        }
        return { baselineCheck, baselineCheckItem };
    }


    useEffect(() => {
        if (id) {
            query(BigInt(id));
        }
    }, [id])

    return (<><Skeleton placeholder={<div className='general-page'>
        <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>
        <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
        <Skeleton.Image style={{ height: 60 }} className='mb40' />
        <Skeleton.Image style={{ height: 200 }} />
    </div>} loading={loading}>
        {entrancePolicy &&
            <div className='general-page'>
                <Breadcrumb routes={
                    [
                        {
                            path: `${BASE_PATH}/policies/entrance`,
                            href: `${BASE_PATH}/policies/entrance`,
                            name: '所有基线检测项配置'
                        },
                        {
                            name: entrancePolicy?.name,
                        }
                    ]
                }>
                </Breadcrumb>
                <Row className="mb10">
                    <Col span={20}>
                        <Space>
                            <Title className={styles.heading} heading={3} style={{ flexShrink: 0 }}>
                                <span>{entrancePolicy?.alias}&nbsp;</span>
                                <Popover content={<div className="p10" style={{ maxWidth: 450 }}>{entrancePolicy.description}</div>}><Text type='tertiary' size='small' ellipsis={{
                                    showTooltip: false
                                }} style={{ maxWidth: 450 }}>{entrancePolicy?.description}</Text></Popover>

                            </Title>
                            <Paragraph type="tertiary"></Paragraph></Space>
                    </Col>
                    <Col span={4}><div className='btn-right-col'>
                        <Space>
                            <Dropdown
                                position='bottomRight'
                                render={
                                    <Dropdown.Menu>  <Dropdown.Item
                                        onClick={() => {
                                            setDuplicateVisible(true);

                                        }}
                                    >复制策略</Dropdown.Item>
                                        <Divider />
                                        <Dropdown.Item
                                            onClick={() => {
                                                setEditVisible(true);

                                            }}
                                        >编辑策略</Dropdown.Item>
                                        <Divider />
                                        <Dropdown.Item
                                            onClick={() => {
                                                setDelVisible(true);

                                            }}
                                            type='danger'>删除策略</Dropdown.Item>

                                    </Dropdown.Menu>
                                }
                            >
                                <Button theme='solid' onClick={() => { }} icon={<IconSetting />}></Button>
                            </Dropdown>
                        </Space>
                    </div></Col>
                </Row>

                <Divider></Divider>

                <Descriptions className={styles.generalInfo} data={[{
                    key: '状态',
                    value: <><Tag>{entrancePolicy?.disabled ? '禁用' : '启用'}</Tag></>
                }, {
                    key: '优先级',
                    value: <Tag color="blue">{entrancePolicy?.priority}</Tag>
                }]} row />



                <Divider className="mb2"></Divider>
                {entrancePolicy.useBaselineCheck && <>

                    <Row className='mb2'>
                        <Col span={20}>
                            <div><Title heading={4}>终端基线检测</Title></div></Col>
                        <Col span={4}>
                        </Col>
                    </Row>
                    <Paragraph className='mb20' type='tertiary'>
                        基线检测项是指在特定领域或系统中，用于评估、监测或验证的一系列关键指标、参数或特征。
                    </Paragraph>
                    {entrancePolicy.baselinePolicies.length > 0 && <>
                        <Row className={styles.tableTitle} >
                            <Col span={5}>检测项</Col>
                            <Col span={5}>操作系统</Col>
                            <Col span={4}>修复方式</Col>
                            <Col span={5}>风险等级</Col>
                            <Col span={5}><div className='btn-right-col'>检测条件</div></Col>
                        </Row>
                        {entrancePolicy.baselinePolicies.map((item, index) => {
                            let name = '';
                            let baselineCheck: BaselineCheck | undefined = undefined;
                            let baselineCheckItem: BaselineCheckItem | undefined = undefined;
                            if (item.item && baselineChecks) {
                                const calRes = getBaselineCheckAndItem(item.item.name, item.item.os, baselineChecks);
                                baselineCheck = calRes.baselineCheck;
                                baselineCheckItem = calRes.baselineCheckItem;
                                if (baselineCheck) {
                                    name = baselineCheck.alias
                                }
                            }

                            return <Row key={index} className={styles.tableBody}>
                                <Col span={5}><Text>{name}</Text></Col>
                                <Col span={5}><Text>{item.item?.os == OS.WINDOWS ? "Windows" : item.item?.os == OS.MACOS ? "Mac" : item.item?.os == OS.IOS ? "iOS" : item.item?.os == OS.ANDROID ? "Android" : "Linux"}</Text></Col>
                                <Col span={4}>{baselineCheckItem && baselineCheck && <RepairMethodDisplay
                                    repairMethod={baselineCheckItem.repairMethod}
                                    repairSolution={baselineCheckItem.repairSolution}
                                    repairSolutionName={baselineCheckItem.repairSolutionName}
                                    repairSolutionDescription={baselineCheckItem.repairSolutionDescription}
                                />}</Col>
                                <Col span={5}><RiskLevelTag level={item.riskLevel} /></Col>
                                <Col span={5}>
                                    <div className='btn-right-col'>
                                        {baselineCheckItem && baselineCheck && baselineCheckItem.conditions.length > 0 && <>
                                            <Popover
                                                content={<div className="p10" style={{ width: 850 }}>
                                                    <Layout>
                                                        <Content>
                                                            <Row>
                                                                {baselineCheck.type === BaselineCheck_Type.BUILD_IN && baselineCheckItem.conditions && baselineCheckItem.conditions.map((condition, index) => {
                                                                    return <Col key={index} span={24}>
                                                                        <Row className={styles.tableTitle} >
                                                                            <Col span={2}>序号</Col>
                                                                            <Col span={22} className={styles.bigRightColumn}>
                                                                                <Row className={styles.subTableTitle}>
                                                                                    <Col span={12}>
                                                                                        名称
                                                                                    </Col>
                                                                                    <Col span={6}>
                                                                                        操作符
                                                                                    </Col>
                                                                                    <Col span={4}>
                                                                                        值
                                                                                    </Col>
                                                                                    <Col span={2} className={styles.rightColumn}>

                                                                                    </Col>
                                                                                </Row>
                                                                            </Col>
                                                                        </Row>
                                                                        {baselineCheckItem && baselineCheckItem.conditions.map((condition, conditionIndex) => {
                                                                            return <Row key={conditionIndex} className={styles.tableBody}>
                                                                                <Col span={2}>
                                                                                    <Tag style={{ height: 32, width: 32 }} color={conditionIndex % 2 == 0 ? 'grey' : 'white'} >{conditionIndex + 1}</Tag>
                                                                                </Col>
                                                                                <Col span={22} className={styles.bigRightColumn}>
                                                                                    <Row className={styles.subTableBody}>
                                                                                        <Col span={12}>
                                                                                            <Text>{condition.name}</Text>
                                                                                        </Col>
                                                                                        <Col span={6}>
                                                                                            <Text>
                                                                                                {condition.op == '==' ? '等于' : ''}
                                                                                                {condition.op == '!=' ? '不等于' : ''}
                                                                                                {condition.op == '>' ? '大于' : ''}
                                                                                                {condition.op == '<' ? '小于' : ''}
                                                                                                {condition.op == '>=' ? '大于等于' : ''}
                                                                                                {condition.op == '<=' ? '小于等于' : ''}
                                                                                                {condition.op == 'contains' ? '包含(模糊匹配)' : ''}
                                                                                                {condition.op == 'in' ? '包含(精确匹配)' : ''}
                                                                                            </Text>
                                                                                        </Col>
                                                                                        <Col span={4}>
                                                                                            <Text>{condition.value}</Text>
                                                                                        </Col>
                                                                                        <Col span={2} className={styles.rightColumn}>
                                                                                        </Col>
                                                                                    </Row>
                                                                                </Col>
                                                                            </Row>
                                                                        })}
                                                                    </Col>
                                                                })}
                                                                {baselineCheck.type === BaselineCheck_Type.REGEX && baselineCheckItem.conditions && baselineCheckItem.conditions.map((condition, index) => {
                                                                    return <Col key={index} span={24}>
                                                                        <Row className={styles.tableTitle} >
                                                                            <Col span={2}>序号</Col>
                                                                            <Col span={22} className={styles.bigRightColumn}>
                                                                                <Row className={styles.subTableTitle}>
                                                                                    <Col span={4}>
                                                                                        名称
                                                                                    </Col>
                                                                                    <Col span={4}>
                                                                                        操作符
                                                                                    </Col>

                                                                                    <Col span={4}>
                                                                                        值
                                                                                    </Col>
                                                                                    <Col span={5}>正则主键</Col>
                                                                                    <Col span={5}>正则路径</Col>
                                                                                    <Col span={2} className={styles.rightColumn}>

                                                                                    </Col>
                                                                                </Row>

                                                                            </Col>
                                                                        </Row>
                                                                        {baselineCheckItem && baselineCheckItem.conditions.map((condition, conditionIndex) => {
                                                                            return <Row key={conditionIndex} className={styles.tableBody}>
                                                                                <Col span={2}>
                                                                                    <Tag style={{ height: 32, width: 32 }} color={conditionIndex % 2 == 0 ? 'grey' : 'white'} >{conditionIndex + 1}</Tag>
                                                                                </Col>
                                                                                <Col span={22} className={styles.bigRightColumn}>
                                                                                    <Row className={styles.subTableBody}>
                                                                                        <Col span={4}>
                                                                                            <Text>{condition.name}</Text>
                                                                                        </Col>
                                                                                        <Col span={4}>
                                                                                            <Text>
                                                                                                {condition.op == '==' ? '等于' : ''}
                                                                                                {condition.op == '!=' ? '不等于' : ''}
                                                                                                {condition.op == '>' ? '大于' : ''}
                                                                                                {condition.op == '<' ? '小于' : ''}
                                                                                                {condition.op == '>=' ? '大于等于' : ''}
                                                                                                {condition.op == '<=' ? '小于等于' : ''}
                                                                                                {condition.op == 'contains' ? '包含(模糊匹配)' : ''}
                                                                                                {condition.op == 'in' ? '包含(精确匹配)' : ''}
                                                                                            </Text>
                                                                                        </Col>
                                                                                        <Col span={4}>
                                                                                            <Text>{condition.value}</Text>
                                                                                        </Col>
                                                                                        <Col span={5}>

                                                                                            <Text>{condition.regMainKey}</Text>

                                                                                        </Col>
                                                                                        <Col span={5}>
                                                                                            <Text>{condition.regPath}</Text>

                                                                                        </Col>
                                                                                        <Col span={2} className={styles.rightColumn}>


                                                                                        </Col>
                                                                                    </Row>
                                                                                </Col>
                                                                            </Row>
                                                                        })}
                                                                    </Col>
                                                                })}
                                                            </Row>
                                                        </Content>
                                                    </Layout>

                                                    <Divider className="mb40"></Divider>
                                                </div>}
                                            ><Button>查看</Button></Popover>
                                        </>}
                                    </div>
                                </Col>

                            </Row>
                            
                        })}
                    </>}
                </>}

                <div style={{ marginTop: 40, marginBottom: 40 }} />

                {entrancePolicy.useAppCheck && <>
                    <Title heading={4} className="mb2">应用风险检测</Title>
                    <Paragraph className='mb20' type='tertiary'>
                        应用检测项可以监测员工终端设备中的风险应用，例如检测员工终端设备上是否安装了不符合企业规定的应用程序。
                    </Paragraph>
                    {appChecks && <AppPolicyDisplay appPolicies={entrancePolicy.forbiddenAppPolicies} appChecks={appChecks} title='违规应用' />}
                    {appChecks && <AppPolicyDisplay appPolicies={entrancePolicy.requiredAppPolicies} appChecks={appChecks} title='必装应用' />}


                </>}

                <div style={{ marginTop: 40, marginBottom: 40 }} />

                {entrancePolicy.useProcessCheck && <>
                    <Title heading={4} className="mb2">进程风险检测</Title>
                    <Paragraph className='mb20' type='tertiary'>
                        进程检测项的监测，可以及时发现问题并进行处理，从而提高系统的稳定性和安全性。
                    </Paragraph>

                    <ProcessPolicyDisplay processPolicies={entrancePolicy.forbiddenProcessPolicies} title='黑名单进程' />
                    <ProcessPolicyDisplay processPolicies={entrancePolicy.requiredProcessPolicies} title='必装进程' />




                    <div style={{ marginBottom: 40 }}></div>
                </>}


            </div>}

    </Skeleton>
        {editVisible && entrancePolicy && <Edit
            id={entrancePolicy.id}
            close={() => setEditVisible(false)}
            success={() => {
                setEditVisible(false);
                query(BigInt(id));
            }}
        />}
        {delVisible && entrancePolicy && <Del
            close={() => setDelVisible(false)}
            success={() => {
                setDelVisible(false);
                navigate(`${BASE_PATH}/policies/entrance`);
            }}
            record={entrancePolicy}
        />}
        {duplicateVisible && entrancePolicy && <New
            close={() => setDuplicateVisible(false)}
            success={() => {
                setDuplicateVisible(false);
                navigate(`${BASE_PATH}/policies/entrance`);
            }}
            template={entrancePolicy}
        />
        }

    </>)
}

export default Index;