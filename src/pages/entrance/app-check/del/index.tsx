import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input, TabPane } from '@douyinfe/semi-ui';
import { AppCheck } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { flylayerClient } from '@/services/core';


import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: AppCheck
}
const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    // 确认值
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={`删除应用检测项 ${props.record.name}`}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

            flylayerClient.deleteAppCheck({
                id: props.record.id
            }).then(() => {
                Notification.success({ content: "删除应用检测项成功", position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                console.error(err);
                Notification.error({ content: "删除基线检测项失败，请稍后重试", position: "bottomRight" })
            }).finally(() => setLoading(false))

        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'> 应用检测项 <b>{props.record.alias}({props.record.name})</b> 将被删除，删除后将无法使用该基线检测项。
        </Paragraph>
        <Paragraph className='mb20'> 输入 <b>{props.record.name}</b> 以确认删除
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;