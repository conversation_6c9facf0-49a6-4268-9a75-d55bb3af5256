import React, { useState, useContext, useEffect } from 'react'
import { Typography, Input, Tabs, Table, Row, Col, Button, Space, Breadcrumb, Layout, Select } from '@douyinfe/semi-ui';
import { AppPolicy, AppCheck, AppCheckItem, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { IconSearch, IconSetting } from '@douyinfe/semi-icons';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import TableEmpty from '@/components/table-empty';

import qs from 'query-string';
import { BASE_PATH } from '@/constants/router';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import New from './new';
import Edit from './edit';
import Del from './del';
import ItemConfig from './item-config';
import ItemDel from './item-del';
import styles from '@/pages/entrance/index.module.scss'
import useTable, { AppCheckFilter } from './useTable';

const { Title, Text } = Typography;

const { Sider, Content } = Layout;
// 根据URL参数设置过滤参数
const getInitFilter = (location: Location): AppCheckFilter => {
    const query: string = getQueryParam('query', location) as string;
    const osQuery = getQueryParam('os', location);

    let os: number[] = [];
    if (osQuery && Array.isArray(osQuery)) {
        osQuery.forEach(item => {
            if (item && typeof item == 'string') {
                os.push(parseInt(item));
            }
        })
    }
    if (osQuery && typeof osQuery == 'string') {
        os = [parseInt(osQuery)];
    }

    return {
        query: query || '',
        os: os,
    }
}


const Index: React.FC = () => {
    const navigate = useNavigate();
    const [createVisible, setCreateVisible] = useState(false);
    const [tabKey, setTabKey] = useState('');

    // 过滤参数改变时跳转路由
    const doNavigate = (filter: AppCheckFilter) => {

        let query = '';
        if (filter.query || filter.os.length > 0) {
            query = qs.stringify(filter, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/policies/entrance/app-check?${query}`)
        } else {
            navigate(`${BASE_PATH}/policies/entrance/app-check`)
        }

    }

    const initFilter: AppCheckFilter = getInitFilter(useLocation());
    const {
        columns,
        appChecks,
        total,
        loading,
        filterParam,
        setFilterParam,
        query,
        expandedRowRender,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedAppCheck,
        reload,
        setConfigVisible,
        configVisible,
        itemDelVisible,
        setItemDelVisible,
        setSelectedAppCheck,
        selectedAppCheckItem,
        setSelectedAppCheckItem,
        selectedAppCheckItemIndex,
        setSelectedAppCheckItemIndex
    } = useTable(initFilter);

    const listOs = [
        { value: OS.MACOS, label: 'macOS' },
        { value: OS.IOS, label: 'iOS' },
        { value: OS.WINDOWS, label: 'Windows' },
        { value: OS.LINUX, label: 'Linux' },
        { value: OS.ANDROID, label: 'Android' },
    ];

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, query: value })
        doNavigate({ ...filterParam, query: value });
    }
    const handleOsChange = (value: any) => {
        setFilterParam({ ...filterParam, os: value })
        doNavigate({ ...filterParam, os: value });
    }
    return <><div className='general-page'>
        <Breadcrumb routes={
            [
                {
                    path: `${BASE_PATH}/policies/entrance`,
                    href: `${BASE_PATH}/policies/entrance`,
                    name: '设备准入策略'
                },
                {
                    name: '应用检测项配置',
                }
            ]
        }>
        </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>应用检测项配置</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>新建检测项</Button>
                </Space>
            </div></Col>
        </Row>
        <Layout className='mb20 search-bar' >
            <Layout>
                <Content className='pr10'>
                    <Input value={filterParam.query}
                        onChange={handleQueryChange}
                        style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={'根据名称、描述搜索'}></Input>
                </Content>
                <Sider> <Space>

                    <Select multiple
                        maxTagCount={1}
                        style={{ width: 200 }}
                        optionList={listOs}
                        insetLabel="操作系统"
                        onChange={handleOsChange}
                        value={filterParam.os}></Select>


                </Space></Sider>
            </Layout>

        </Layout>

        <Table
            rowKey={(record?: AppCheck) => record ? record.id + '' : ''}
            loading={loading}
            columns={columns}
            dataSource={appChecks}
            pagination={false}
            // expandRowByClick={true}
            // expandedRowRender={expandedRowRender}
            empty={<TableEmpty loading={loading}></TableEmpty>}
        ></Table>

    </div>
        {createVisible && <New
            close={() => setCreateVisible(false)}
            success={() => {
                setCreateVisible(false);
                reload();
            }}
        />}
        {editVisible && selectedAppCheck && <Edit
            id={selectedAppCheck.id}
            close={() => setEditVisible(false)}
            success={() => {
                setEditVisible(false);
                reload();
            }}
        />}
        {delVisible && selectedAppCheck && <Del
            close={() => setDelVisible(false)}
            success={() => {
                setDelVisible(false);
                reload();
            }}
            record={selectedAppCheck}
        />}
        {
            itemDelVisible && selectedAppCheckItem && selectedAppCheck && <ItemDel
                close={() => {
                    setItemDelVisible(false)
                    setSelectedAppCheck(undefined);
                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);
                    
                }}
                success={() => {
                    setItemDelVisible(false);
                    setSelectedAppCheck(undefined);
                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);
                    reload();
                }}
                record={selectedAppCheckItem}
                index={selectedAppCheckItemIndex}
                appCheck={selectedAppCheck}
            />
        }
        {
            configVisible && selectedAppCheckItem && selectedAppCheck && <ItemConfig
                close={() => {
                    setConfigVisible(false)
                    setSelectedAppCheck(undefined);
                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);

                }}
                success={() => {
                    setConfigVisible(false);
                    setSelectedAppCheck(undefined);
                    setSelectedAppCheckItem(undefined);
                    setSelectedAppCheckItemIndex(-1);
                    reload();
                }}
                appCheck={selectedAppCheck}
                item={selectedAppCheckItem}
                index={selectedAppCheckItemIndex}
            />
        }

    </>
}

export default Index;