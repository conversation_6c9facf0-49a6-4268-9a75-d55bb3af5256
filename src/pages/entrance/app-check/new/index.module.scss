.rightColumn {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0 !important;
}
.bigRightColumn{
    padding-right: 0 !important;
}
.inlineSwitch {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0 !important;
    >div {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}

.tableTitle {

    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 8px;
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
        line-height: 32px;
    }
}

.subTableTitle {
    >div {
        padding-right: 8px;
    }

}

.tableBody {
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 10px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.tableBody:last-of-type {
    border-bottom: none!important;
}


.tableBodyError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);

    padding-top: 5px;
    padding-bottom: 5px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}
.subTableBody {
    margin-bottom: 10px;
    >div {
        padding-right: 8px;
    }

}
.templateEditor {
    >div {
        height: 450px !important;
    }
}