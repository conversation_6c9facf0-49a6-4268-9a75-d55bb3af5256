import React, { useState, useContext, useEffect } from "react";
import { Typography, Modal, Form, Row, Col, Button, Select, Input, Tag, Space, Notification, Divider, Popover } from "@douyinfe/semi-ui";
import { sanitizeLabel } from '@/utils/common';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import RepairMethodEditor from '@/pages/entrance/components/repair-method-editor';

import { IconAlertCircle, IconHelpCircle, IconPlus, IconMinusCircle } from '@douyinfe/semi-icons';
import pinyin from 'tiny-pinyin';
import { AppCheck, AppCheckItem } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { flylayerClient } from "@/services/core";


import styles from '@/pages/entrance/index.module.scss'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

const { Title, Paragraph, Text } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
}

// 检测项错误
interface ItemError {
    index: number,
    os: boolean,
    repairMethod: boolean,
    version: boolean,
    bundleId: boolean,
    fullName: boolean,
    publisher: boolean,
}

const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        alias: string;
        description: string;

    }>>();

    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);


    const [items, setItems] = useState<AppCheckItem[]>([new AppCheckItem({ os: OS.WINDOWS })]);

    // 检测项错误
    const [itemsErr, setItemsErr] = useState<ItemError[]>([{
        index: 0,
        os: false,
        repairMethod: false,
        version: false,
        bundleId: false,
        fullName: false,
        publisher: false,
    }]);

    const validateItems = (items: AppCheckItem[]) => {
        let hasItemsError = false;
        let err = items.map((item, index) => {
            let err = {
                index,
                os: false,
                repairMethod: false,
                version: false,
                bundleId: false,
                fullName: false,
                publisher: false,
            }
            if (!item.os) {
                hasItemsError = true;
                err.os = true;
            }

            // if (!item.repairSolution || !item.repairSolutionName || !item.repairSolutionDescription) {
            //     hasItemsError = true;
            //     err.repairMethod = true;
            // }
            if (!item.version) {
                hasItemsError = true;
                err.version = true;
            }
            if (!item.bundleId) {
                hasItemsError = true;
                err.bundleId = true;
            }
            if (!item.fullName) {
                hasItemsError = true;
                err.fullName = true;
            }
            if (!item.publisher) {
                hasItemsError = true;
                err.publisher = true;
            }
            return err;
        });
        setItemsErr(err);
        return hasItemsError;
    }

    const handleSubmit = async () => {
        if (!formApi) {
            return
        }

        let hasItemsError = validateItems(items);

        await formApi.validate();
        if (hasItemsError) {
            return;
        }

        const values = formApi?.getValues();
        if (!values) {
            return;
        }
        setSaveLoading(true);

        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';

        const check = new AppCheck();
        check.name = name;
        check.alias = alias;
        check.description = description;
        check.items = items;

        flylayerClient.createAppCheck({
            flynetId: flynet.id,
            appCheck: check
        }).then(() => {
            Notification.success({
                title: '创建成功',
                content: '创建检测策略成功'
            });
            if (props.success) {
                props.success();
            }
        }, err => {
            Notification.error({
                title: '创建失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
            props.close();
        });
    }

    return <><Modal
        title="添加检测策略"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={1200}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Form
            getFormApi={setFormApi}
            initValues={{}}
            onValueChange={(values, changedValue) => {
                if (changedValue.hasOwnProperty('alias')) {
                    if (!changedValue.alias || changedValue.alias.trim() === '') {
                        formApi?.setValue('name', '');
                    } else {
                        formApi?.setValue('name', sanitizeLabel(pinyin.convertToPinyin(changedValue.alias, '', true)))
                    }
                    formApi?.validate();
                }
            }}
        >
            {({ values }) => (<>
                <Row gutter={20}>
                    <Col span={12}>
                        <Form.Input field='alias' label='名称' trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            return '';
                        }} />
                    </Col>
                    <Col span={12}>
                        <Form.Input field='name'
                            label={<>编码 <Popover content={<div className='p10'>新建完成后不可修改</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                            trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '编码不能为空';
                                }
                                // 编码不能以-开头
                                if (value.trim().startsWith('-')) {
                                    return '编码不能以-开头'
                                }
                                if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                    return "编码只能包含字母、数字和'-'";
                                }
                                return '';
                            }}
                            required />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Form.TextArea field="description" autosize rows={1} label="策略描述" />
                    </Col>
                </Row>
                <Divider className="mb10" />
                <Row className="mb10">
                    <Col span={12}>
                        <Title heading={6} type="tertiary">检测项</Title>
                    </Col>
                </Row>
                <Row className={styles.tableTitle} >
                    <Col span={2}>序号</Col>
                    <Col span={4}>操作系统</Col>
                    <Col span={4}>软件版本</Col>
                    <Col span={4}>软件全称</Col>
                    <Col span={4}>软件BundleID</Col>
                    <Col span={4}>软件发布者</Col>
                    <Col span={2} className={styles.rightColumn}>
                        <Button onClick={() => {
                            let item = new AppCheckItem({ os: OS.WINDOWS });
                            let newItems = [...items, item];
                            setItems(newItems);
                            validateItems(newItems);
                        }} icon={<IconPlus></IconPlus>}></Button>
                    </Col>
                </Row>
                {items.map((item, index) => {
                    return <Row key={index} className={styles.tableBody}>
                        <Col span={2}>
                            <Tag style={{ height: 32, width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'} >{index + 1}</Tag>
                        </Col>
                        <Col span={4}>
                            <Select value={item.os}
                                onChange={val => {
                                    item.os = val as OS;
                                    setItems([...items])
                                }}
                                style={{ width: '100%' }}>
                                <Select.Option value={OS.WINDOWS}>Windows</Select.Option>
                                <Select.Option value={OS.MACOS}>Mac</Select.Option>
                                <Select.Option value={OS.IOS}>iOS</Select.Option>
                                <Select.Option value={OS.ANDROID}>Android</Select.Option>
                                <Select.Option value={OS.LINUX}>Linux</Select.Option>

                            </Select>
                        </Col>
                        <Col span={4}>
                            <Input
                                className="mb10"
                                value={item.version}
                                onChange={val => {
                                    item.version = val;
                                    let newItems = [...items];
                                    setItems(newItems)
                                    validateItems(newItems);
                                }}
                            ></Input>
                            {itemsErr[index].version &&
                                <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件版本不能为空</Paragraph>
                            }
                        </Col>
                        <Col span={4}>
                            <Input
                                className="mb10"
                                value={item.fullName}
                                onChange={val => {
                                    item.fullName = val;
                                    let newItems = [...items];
                                    setItems(newItems)
                                    validateItems(newItems);
                                }}
                            ></Input>
                            {itemsErr[index].fullName && <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件全称不能为空</Paragraph>}
                        </Col>
                        <Col span={4}>
                            <Input
                                className="mb10"
                                value={item.bundleId}
                                onChange={val => {
                                    item.bundleId = val;
                                    let newItems = [...items];
                                    setItems(newItems)
                                    validateItems(newItems);
                                }}
                            ></Input>
                            {itemsErr[index].bundleId &&
                                <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件BundleID不能为空</Paragraph>}
                        </Col>
                        <Col span={4}>
                            <Input
                                className="mb10"
                                value={item.publisher}
                                onChange={val => {
                                    item.publisher = val;
                                    let newItems = [...items];
                                    setItems(newItems)
                                    validateItems(newItems);
                                }}
                            ></Input>

                            {itemsErr[index].publisher &&
                                <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件发布者不能为空</Paragraph>
                            }
                        </Col>
                        <Col span={2} className={styles.rightColumn}>

                            <Button type="danger" onClick={() => {
                                setItems(items.filter((item, i) => i != index));
                            }} icon={<IconMinusCircle />} />
                        </Col>
                    </Row>
                })}
                {/* <Row className={styles.tableTitle} >
                    <Col span={2}>序号</Col>
                    <Col span={22} className={styles.bigRightColumn}>
                        <Row className={styles.subTableTitle}>
                            <Col span={4}>
                                操作系统
                            </Col>
                            <Col span={18}>
                                修复方式
                            </Col>
                            <Col span={2} className={styles.rightColumn}>
                                <Button onClick={() => {
                                    let item = new AppCheckItem({ os: OS.WINDOWS });
                                    let newItems = [...items, item];
                                    setItems(newItems);
                                    validateItems(newItems);
                                }} icon={<IconPlus></IconPlus>}></Button>
                            </Col>
                        </Row>
                    </Col>
                </Row>
                {items.map((item, index) => {
                    return <Row key={index} className={styles.tableBody}>
                        <Col span={2}>
                            <Tag style={{ height: 32, width: 32 }} color={index % 2 == 0 ? 'grey' : 'white'} >{index + 1}</Tag>
                        </Col>
                        <Col span={22} className={styles.bigRightColumn}>
                            <Row className={styles.subTableBody}>
                                <Col span={4}>
                                    <Select value={item.os}
                                        onChange={val => {
                                            item.os = val as OS;
                                            setItems([...items])
                                        }}
                                        style={{ width: '100%' }}>
                                        <Select.Option value={OS.WINDOWS}>Windows</Select.Option>
                                        <Select.Option value={OS.MACOS}>Mac</Select.Option>
                                        <Select.Option value={OS.IOS}>iOS</Select.Option>
                                        <Select.Option value={OS.ANDROID}>Android</Select.Option>
                                        <Select.Option value={OS.LINUX}>Linux</Select.Option>

                                    </Select>
                                </Col>
                                <Col span={18}>
                                    <RepairMethodEditor
                                        repairMethodErr={itemsErr[index].repairMethod}
                                        repairMethod={item.repairMethod}
                                        repairSolution={item.repairSolution}
                                        repairSolutionName={item.repairSolutionName}
                                        repairSolutionDescription={item.repairSolutionDescription}
                                        onChange={(param) => {
                                            item.repairMethod = param.repairMethod;
                                            item.repairSolution = param.repairSolution;
                                            item.repairSolutionName = param.repairSolutionName;
                                            item.repairSolutionDescription = param.repairSolutionDescription;
                                            setItems([...items])

                                            let err = itemsErr;
                                            err[index].repairMethod = false;
                                            setItemsErr([...err]);
                                        }}
                                        onError={() => {
                                            let err = itemsErr;
                                            err[index].repairMethod = true;
                                            setItemsErr([...err]);
                                        }}
                                    />
                                </Col>
                                <Col span={2} className={styles.rightColumn}>

                                    <Button type="danger" onClick={() => {
                                        setItems(items.filter((item, i) => i != index));
                                    }} icon={<IconMinusCircle />} />
                                </Col>
                            </Row>
                            <Row gutter={16} className={styles.subTableBody}>
                                <Col span={4}>
                                    <Text >软件版本</Text>
                                </Col>
                                <Col span={8}>
                                    <Input
                                        className="mb10"
                                        value={item.version}
                                        onChange={val => {
                                            item.version = val;
                                            let newItems = [...items];
                                            setItems(newItems)
                                            validateItems(newItems);
                                        }}
                                    ></Input>
                                    {itemsErr[index].version &&
                                        <Paragraph type='danger' style={{display: 'flex', alignItems: 'center'}}><IconAlertCircle />&nbsp;软件版本不能为空</Paragraph>
                                    }
                                </Col>
                                <Col span={4}>
                                    <Text>软件BundleID</Text>
                                </Col>
                                <Col span={8}>
                                    <Input
                                        className="mb10"
                                        value={item.bundleId}
                                        onChange={val => {
                                            item.bundleId = val;
                                            let newItems = [...items];
                                            setItems(newItems)
                                            validateItems(newItems);
                                        }}
                                    ></Input>
                                    {itemsErr[index].bundleId &&
                                    <Paragraph type='danger' style={{display: 'flex', alignItems: 'center'}}><IconAlertCircle />&nbsp;软件BundleID不能为空</Paragraph>}
                                </Col>
                            </Row>
                            <Row gutter={16} className={styles.subTableBody}>
                                <Col span={4}>
                                    <Text>软件全称</Text>
                                </Col>
                                <Col span={8}>
                                    <Input
                                        className="mb10"
                                        value={item.fullName}
                                        onChange={val => {
                                            item.fullName = val;
                                            let newItems = [...items];
                                            setItems(newItems)
                                            validateItems(newItems);
                                        }}
                                    ></Input>
                                    {itemsErr[index].fullName && <Paragraph type='danger' style={{display: 'flex', alignItems: 'center'}}><IconAlertCircle />&nbsp;软件全称不能为空</Paragraph>}
                                </Col>
                                <Col span={4}>
                                    <Text>软件发布者</Text>
                                </Col>
                                <Col span={8}>
                                    <Input
                                        className="mb10"
                                        value={item.publisher}
                                        onChange={val => {
                                            item.publisher = val;
                                            let newItems = [...items];
                                            setItems(newItems)
                                            validateItems(newItems);
                                        }}
                                    ></Input>

                                    {itemsErr[index].publisher &&
                                    <Paragraph type='danger' style={{display: 'flex', alignItems: 'center'}}><IconAlertCircle />&nbsp;软件发布者不能为空</Paragraph>
                                    }
                                </Col>
                            </Row>

                        </Col>


                    </Row>
                })} */}
            </>)}
        </Form>
    </Modal></>
}

export default Index;
