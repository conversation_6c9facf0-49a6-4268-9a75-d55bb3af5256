import React, { useState, useContext, useEffect } from "react";
import { useNavigate, useParams } from 'react-router-dom';

import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Breadcrumb, Row, Col, Button, Typography, Tag, Tooltip, Notification, Dropdown, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List, Banner, Tabs, TabPane } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { BASE_PATH } from '@/constants/router';
import { flylayerClient } from '@/services/core';
import { AppCheck, AppCheckItem } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import AppCheckDetail from '@/pages/entrance/components/app-check-detail';

import ItemConfig from '../item-config';
import ItemDel from '../item-del';

const { Title, Paragraph, Text } = Typography;
import styles from '@/pages/entrance/index.module.scss'

interface Props {

}

const Index: React.FC<Props> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();


    const [appCheck, setAppCheck] = useState<AppCheck | undefined>();

    const params = useParams<{ id: string }>()
    const id = params.id ? params.id : '';

    const [loading, setLoading] = useState(true);

    const queryAppCheck = async (id: bigint) => {
        setLoading(true);
        try {
            const res = await flylayerClient.getAppCheck({
                id: id
            });
            if (res.appCheck) {
                setAppCheck(res.appCheck);
            }

        } catch (err) {
            Notification.error({
                title: '获取数据失败',
                content: err
            });
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        if (id) {
            queryAppCheck(BigInt(id));
        }
    }, [id]);



    return <>
        <AppCheckDetail
            loading={loading}
            appCheck={appCheck}
            useConfig={true}
            queryAppCheck={() => queryAppCheck(BigInt(id))}
        ></AppCheckDetail>
    </>
}

export default Index;