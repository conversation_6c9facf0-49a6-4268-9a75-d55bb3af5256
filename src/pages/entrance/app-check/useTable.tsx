import { useState, useContext, useEffect, useCallback } from 'react';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Typography, Notification, Dropdown, Button, Divider, Space, Tag, Popover, Row, Col } from '@douyinfe/semi-ui';

import { AppCheck, AppCheckItem, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';


import styles from '@/pages/entrance/index.module.scss'

import { flylayerClient } from '@/services/core';
import { caseInsensitiveIncludes } from '@/utils/common';

const { Title, Paragraph, Text } = Typography;


export type AppCheckFilter = {
    query?: string;
    os: Array<number>;
}

const useTable = (initFilter: AppCheckFilter) => {
    const navigate = useNavigate();

    const flynet = useContext(FlynetGeneralContext);
    // 是否正在加载中
    const [loading, setLoading] = useState(false);
    const [filterParam, setFilterParam] = useState<AppCheckFilter>(initFilter)
    const [total, setTotal] = useState<number>(0);

    const [appChecks, setAppChecks] = useState<AppCheck[]>();
    const [allAppChecks, setAllAppChecks] = useState<AppCheck[]>();


    const [editVisible, setEditVisible] = useState(false);

    const [delVisible, setDelVisible] = useState(false);

    const [selectedAppCheck, setSelectedAppCheck] = useState<AppCheck>();

    const [selectedAppCheckItem, setSelectedAppCheckItem] = useState<AppCheckItem>();
    const [selectedAppCheckItemIndex, setSelectedAppCheckItemIndex] = useState<number>(-1);
    const [configVisible, setConfigVisible] = useState(false);
    const [itemDelVisible, setItemDelVisible] = useState(false);

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            render: (field: string, record: AppCheck, index: number) => {
                return <>
                    <div style={{ display: 'inline-flex' }}>
                        <div>
                            <Title heading={6}>
                                {record.alias}
                                {/* <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/policies/entrance/app-check/detail/${record.id}`), 10)}>{record.alias}</a> */}
                                {record.description &&
                                    <Popover content={<div className='p10'>{record.description}</div>}>
                                        <IconArticle style={{
                                            fontSize: 14,
                                            marginLeft: '4px',
                                            color: '#999'
                                        }} />
                                    </Popover>}
                            </Title>
                            <Paragraph size='small'>{record.name}</Paragraph>
                        </div>
                    </div>
                </>
            },
        },
        {
            title: '',
            dataIndex: 'operation',
            key: 'operation',
            width: 100,
            render: (text: string, record: AppCheck) => {
                return <><div className='table-last-col'><Dropdown
                    position='bottomRight'
                    render={
                        <Dropdown.Menu>

                            <Dropdown.Item
                                onClick={() => {
                                    setEditVisible(true);
                                    setSelectedAppCheck(record);
                                }}
                            >编辑策略</Dropdown.Item>
                            <Divider />
                            <Dropdown.Item
                                onClick={() => {
                                    setDelVisible(true);
                                    setSelectedAppCheck(record);
                                }}
                                type='danger'>删除策略</Dropdown.Item>

                        </Dropdown.Menu>}><Button><IconMore className='align-v-center' /></Button>
                </Dropdown>
                </div>
                </>;
            }
        }
    ];
    const query = () => {
        setLoading(true);
        flylayerClient.listAppCheck({
            flynetId: flynet.id,
        }).then((res) => {
            setAppChecks(res.appChecks);
            const list = doFilter(res.appChecks, filterParam);
            setAllAppChecks(list);

        }).catch((err) => {
            Notification.error({
                title: '获取数据失败',
                content: err.message
            });

        }).finally(() => {
            setLoading(false);
        });
    }

    useEffect(() => {
        query();
    }, []);


    // 重新加载数据
    const reload = () => {
        query();
    }

    // 过滤数据
    const doFilter = (src: Array<AppCheck>, appcheckFilter: AppCheckFilter) => {
        if (!src || src.length == 0) {
            setTotal(0);
            return src;
        }

        if (!appcheckFilter.query && appcheckFilter.os.length == 0) {
            setTotal(src.length);
            return src;
        }

        const filteredList = src.filter(record => {
            if (appcheckFilter.query && !caseInsensitiveIncludes(record.name, appcheckFilter.query)) {
                return false;
            }

            if (appcheckFilter.os.length > 0) {
                let hasOs = false;
                record.items?.forEach(item => {
                    if (appcheckFilter.os.includes(item.os)) {
                        hasOs = true;
                    }
                });
                if (!hasOs) {
                    return false;
                }
            }


            return true;
        })

        setTotal(filteredList.length);
        return filteredList;
    }

    const expandedRowRender = (appCheck: AppCheck | undefined) => {
        if (!appCheck) return null;
        return (
            <div className={styles.expandedRow}>
                <Row className={styles.titleRow}>

                    <Col span={3}>操作系统</Col>
                    <Col span={3}>检测版本</Col>
                    <Col span={5}>软件全称/Bundle ID</Col>
                    <Col span={3}>软件发布者</Col>
                    <Col span={3}>类型</Col>
                    <Col span={3}>操作</Col>
                </Row>
                {appCheck.items?.map((item, i) => {
                    return <Row key={i} className={styles.bodyRow}>
                        <Col span={3} className={styles.bodyRowCell}>
                            <Text className={styles.bodyText}>
                                {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                            </Text>
                        </Col>
                        <Col span={3} className={styles.bodyRowCell}>
                            {item.version}
                        </Col>
                        <Col span={5} className={styles.bodyRowCell}>
                            {item.fullName}/{item.bundleId}
                        </Col>
                        <Col span={3} className={styles.bodyRowCell}>
                            {item.publisher}
                        </Col>
                        <Col span={3} className={styles.bodyRowCell}>
                            {item.repairMethod}
                        </Col>
                        <Col span={3} className={styles.bodyRowCell}>
                            <Space>
                                <Button theme='solid' type='primary'
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        setSelectedAppCheck(appCheck);
                                        setSelectedAppCheckItem(item);
                                        setSelectedAppCheckItemIndex(i);
                                        setConfigVisible(true);
                                    }}
                                >配置</Button>
                                <Button theme='solid' type='danger'
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        setSelectedAppCheck(appCheck);
                                        setSelectedAppCheckItem(item);
                                        setSelectedAppCheckItemIndex(i);
                                        setItemDelVisible(true);
                                    }}
                                >删除</Button>
                            </Space>
                        </Col>
                    </Row>
                })}
            </div>
        );
    }

    useEffect(() => {
        if (allAppChecks && allAppChecks.length > 0) {
            const list = doFilter(allAppChecks, filterParam);
            setAppChecks(list);
        }
    }, [filterParam])

    return {
        columns,
        appChecks,
        total,
        loading,
        filterParam,
        setFilterParam,
        query,
        expandedRowRender,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedAppCheck,
        reload,
        setConfigVisible,
        configVisible,
        itemDelVisible,
        setItemDelVisible,
        setSelectedAppCheck,
        selectedAppCheckItem,
        setSelectedAppCheckItem,
        selectedAppCheckItemIndex,
        setSelectedAppCheckItemIndex

    };

}

export default useTable;