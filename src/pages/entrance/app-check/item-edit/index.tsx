import React, { useState, useEffect } from "react";
import { Typography, Row, Col, Button, Tag, Input, Select } from "@douyinfe/semi-ui";
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import RepairMethodEditor from '@/pages/entrance/components/repair-method-editor';
import { IconAlertCircle } from '@douyinfe/semi-icons';

import { AppCheckItem } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

import styles from '@/pages/entrance/index.module.scss'

const { Title, Paragraph, Text } = Typography;

interface Props {
    item: AppCheckItem;
    onChange: (item: AppCheckItem) => void;
    itemErr: boolean;
    setItemErr: (val: boolean) => void;
    validateFlag: boolean;
    setValidateFlag: (val: boolean) => void;
}

const Index: React.FC<Props> = (props) => {
    const [item, setItem] = useState<AppCheckItem>(props.item);

    const [repairMethodErr, setRepairMethodErr] = useState(false);
    const [versionErr, setVersionErr] = useState(false);
    const [bundleIdErr, setBundleIdErr] = useState(false);
    const [fullNameErr, setFullNameErr] = useState(false);
    const [publisherErr, setPublisherErr] = useState(false);

    const handleItemErr = (
        repairMethodErr: boolean,
        versionErr: boolean,
        bundleIdErr: boolean,
        fullNameErr: boolean,
        publisherErr: boolean
    ) => {
        if (repairMethodErr || versionErr || bundleIdErr || fullNameErr || publisherErr) {
            props.setItemErr(true);
        } else {
            props.setItemErr(false);
        }
    }

    useEffect(() => {
        
        if(props.validateFlag) {
            let repairMethodErr = 
            item.repairSolution === '' || item.repairSolutionName === '' || item.repairSolutionDescription === '';
            let versionErr = item.version === '';
            let bundleIdErr = item.bundleId === '';
            let fullNameErr = item.fullName === '';
            let publisherErr = item.publisher === '';
    
            setRepairMethodErr(repairMethodErr);
            setVersionErr(versionErr);
            setBundleIdErr(bundleIdErr);
            setFullNameErr(fullNameErr);
            setPublisherErr(publisherErr);
            
            props.setValidateFlag(false);
        }
    }, [props.validateFlag]);

    useEffect(() => {
        setItem(props.item);
    }, [props.item]);

    return <><Row className={styles.subTableBody}>
        <Col span={4}>
            <Select value={item.os}
                onChange={val => {
                    let newItem = new AppCheckItem(item)
                    newItem.os = val as OS;
                    props.onChange(newItem);
                }}
                style={{ width: '100%' }}>
                <Select.Option value={OS.WINDOWS}>Windows</Select.Option>
                <Select.Option value={OS.MACOS}>Mac</Select.Option>
                <Select.Option value={OS.IOS}>iOS</Select.Option>
                <Select.Option value={OS.ANDROID}>Android</Select.Option>
                <Select.Option value={OS.LINUX}>Linux</Select.Option>

            </Select>
        </Col>
        <Col span={20} style={{ paddingRight: 0 }}>
            <RepairMethodEditor
                repairMethodErr={repairMethodErr}
                repairMethod={item.repairMethod}
                repairSolution={item.repairSolution}
                repairSolutionName={item.repairSolutionName}
                repairSolutionDescription={item.repairSolutionDescription}
                onChange={(param) => {
                    let newItem = new AppCheckItem(item)
                    newItem.repairMethod = param.repairMethod;
                    newItem.repairSolution = param.repairSolution;
                    newItem.repairSolutionName = param.repairSolutionName;
                    newItem.repairSolutionDescription = param.repairSolutionDescription;
                    props.onChange(newItem);
                    handleItemErr(false, versionErr, bundleIdErr, fullNameErr, publisherErr)
                }}
                onError={
                    () => {
                        handleItemErr(true, versionErr, bundleIdErr, fullNameErr, publisherErr)
                        setRepairMethodErr(true);
                    }
                }
            />
        </Col>
    </Row>

        <Row gutter={16} className={styles.subTableBody}>
            <Col span={5}>
                <Text >软件版本</Text>
            </Col>
            <Col span={7}>
                <Input
                    className="mb10"
                    value={item.version}
                    onChange={val => {
                        let newItem = new AppCheckItem(item)
                        newItem.version = val;
                        props.onChange(newItem);
                        let versionErr = val === '';
                        setVersionErr(versionErr);
                        handleItemErr(repairMethodErr, versionErr, bundleIdErr, fullNameErr, publisherErr)

                    }}
                ></Input>
                {versionErr && <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件版本不能为空</Paragraph>
                }
            </Col>
            <Col span={5}>
                <Text>软件BundleID</Text>
            </Col>
            <Col span={7}>
                <Input
                    className="mb10"
                    value={item.bundleId}
                    onChange={val => {
                        let newItem = new AppCheckItem(item)
                        newItem.bundleId = val;
                        props.onChange(newItem);
                        let bundleIdErr = val === '';
                        setBundleIdErr(bundleIdErr);
                        handleItemErr(repairMethodErr, versionErr, bundleIdErr, fullNameErr, publisherErr)
                    }}
                ></Input>{bundleIdErr &&
                    <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件BundleID不能为空</Paragraph>}

            </Col>
        </Row>
        <Row gutter={16} className={styles.subTableBody}>
            <Col span={5}>
                <Text>软件全称</Text>
            </Col>
            <Col span={7}>
                <Input
                    className="mb10"
                    value={item.fullName}
                    onChange={val => {
                        let newItem = new AppCheckItem(item)
                        newItem.fullName = val;
                        props.onChange(newItem);
                        let fullNameErr = val === '';
                        setFullNameErr(fullNameErr);
                        handleItemErr(repairMethodErr, versionErr, bundleIdErr, fullNameErr, publisherErr)
                    }}
                ></Input>
                {fullNameErr && <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件全称不能为空</Paragraph>}

            </Col>
            <Col span={5}>
                <Text>软件发布者</Text>
            </Col>
            <Col span={7}>
                <Input
                    className="mb10"
                    value={item.publisher}
                    onChange={val => {
                        let newItem = new AppCheckItem(item)
                        newItem.publisher = val;
                        props.onChange(newItem);
                        let publisherErr = val === '';
                        setPublisherErr(publisherErr);
                        handleItemErr(repairMethodErr, versionErr, bundleIdErr, fullNameErr, publisherErr)
                    }}
                ></Input>
                {publisherErr &&
                    <Paragraph type='danger' style={{ display: 'flex', alignItems: 'center' }}><IconAlertCircle />&nbsp;软件发布者不能为空</Paragraph>
                }
            </Col>
        </Row></>
}

export default Index;