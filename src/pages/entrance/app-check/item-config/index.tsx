import React, { useContext, useState, useEffect } from "react";
import { Modal, Typo<PERSON>, Row, Col, Button, Tag, Input, Select, Notification } from "@douyinfe/semi-ui";
import { AppCheck, AppCheckItem } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from "@/services/core";
import ItemEdit from '../item-edit';

interface Props {
    index: number;
    item: AppCheckItem;
    appCheck: AppCheck;
    close: () => void,
    success?: () => void,
}

const Index: React.FC<Props> = (props) => {
    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const [item, setItem] = useState<AppCheckItem>(props.item);


    const [validateFlag, setValidateFlag] = useState(false);
    const [itemErr, setItemErr] = useState(false);

    const flynet = useContext(FlynetGeneralContext);

    const handleSubmit = async () => {
        if (itemErr) {
            setValidateFlag(true);
            return;
        }
        setSaveLoading(true);
        let appCheck = props.appCheck;
        appCheck.items[props.index] = item;
        flylayerClient.updateAppCheck({
            appCheck: appCheck,
            flynetId: flynet.id
        }).then((res) => {
            Notification.success({
                title: '配置成功',
                content: '检测项已保存'
            });
            props.success && props.success();
            props.close();
        }).catch((err) => {
            console.trace(err);
            Notification.error({
                title: '配置失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        });
    }

    return <><Modal
        title="配置检测项"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={800}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <ItemEdit
            validateFlag={validateFlag}
            setValidateFlag={setValidateFlag}
            itemErr={itemErr}
            setItemErr={setItemErr}
            item={item}
            onChange={item => {
                setItem(item);
            }}
        ></ItemEdit>
    </Modal></>
}

export default Index;
