import React, { useState, useContext, useEffect } from "react";
import { Typography, Modal, Form, Row, Col, Button, Tag, TreeSelect, Select, Notification, Divider, Popover } from "@douyinfe/semi-ui";
import { User, UserGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { flylayerClient } from '@/services/core';

import UserSelector from "@/components/user-selector";

interface Props {
    value: string[];
    onChange: (value: string[]) => void;
}
const { Title, Paragraph } = Typography;

const Index: React.FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);

    const [users, setUsers] = useState<string[]>([]);
    const [groups, setGroups] = useState<string[]>([]);

    // 用户组是否正在加载中
    const [loadingGroups, setLoadingGroups] = useState(true);
    // 用户组列表
    const [userGroups, setUserGroups] = useState<UserGroup[]>([]);

    // 加载数据
    const query = () => {
        setLoadingGroups(true)

        flylayerClient.listUserGroups({
            flynetId: flynet.id
        }).then((res) => {
            setUserGroups(res.groups)
        }).finally(() => {
            setLoadingGroups(false);
        })
    }

    useEffect(() => {
        query();
    }, []);

    useEffect(() => {
        if (props.value) {
            let initUsers: string[] = [];
            let initGroups: string[] = [];
            props.value.forEach((item) => {
                if (item.startsWith("user:")) {
                    initUsers.push(item.replace("user:", ""));
                }
                if (item.startsWith("group:")) {
                    initGroups.push(item.replace("group:", ""));
                }
            });
            setUsers(initUsers);
            setGroups(initGroups);
        }
    }, [props.value]);

    return <>
        {/* <Divider className="mb10"/> */}
        <Title heading={6} type="tertiary" className="mb10">应用范围</Title>
        <Row className="mb10" gutter={12}>
            <Col span={12}>
                <Paragraph className="mb10">用户</Paragraph>
                <UserSelector
                    style={{ width: '100%' }}
                    maxTagCount={3}
                    value={users}
                    onChange={(value) => {
                        setUsers(value)
                        let newValue:string[] = [];
                        value.forEach((item) => {
                            newValue.push("user:" + item);
                        });
                        groups.forEach((item) => {
                            newValue.push("group:" + item);
                        });
                        props.onChange(newValue);
                    }
                    }
                    onLoadingChange={(loading) => { }}
                ></UserSelector>

            </Col>
            <Col span={12}>
                <Paragraph className="mb10">用户组</Paragraph>
                {userGroups && userGroups.length > 0 &&
                    <Select style={{ width: '100%' }}
                        multiple
                        filter
                        optionList={userGroups.map((item) => {
                            return {
                                label: `${item.alias}(${item.name})`,
                                value: item.id + ''
                            }
                        })}
                        value={groups}
                        onChange={(value) => {
                            setGroups(value as string[]);
                            let newValue:string[] = [];
                            users.forEach((item) => {
                                newValue.push("user:" + item);
                            });
                            (value as string[]).forEach((item) => {
                                newValue.push("group:" + item);
                            });

                            props.onChange(newValue);

                        }}
                        placeholder="请选择用户组"
                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    ></Select>
                }
            </Col>
        </Row>
    </>
}

export default Index;
