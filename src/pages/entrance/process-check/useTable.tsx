import { useState, useContext, useEffect, useCallback } from 'react';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Typography, Notification, Dropdown, Button, Divider, Space, Tag, Popover, Row, Col } from '@douyinfe/semi-ui';
import { ProcessCheck, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';

import RepairMethodDisplay from '@/pages/entrance/components/repair-method-display';

import { flylayerClient } from '@/services/core';
const { Title, Paragraph, Text } = Typography;

export type ProcessCheckFilter = {
    query?: string;
    os: Array<number>,
}

const useTable = (initFilter: ProcessCheckFilter) => {
    const navigate = useNavigate();

    const flynet = useContext(FlynetGeneralContext);
    // 是否正在加载中
    const [loading, setLoading] = useState(false);
    const [filterParam, setFilterParam] = useState<ProcessCheckFilter>(initFilter)
    const [total, setTotal] = useState<number>(0);

    const [processChecks, setProcessChecks] = useState<ProcessCheck[]>();
    const [allProcessChecks, setAllProcessChecks] = useState<ProcessCheck[]>();

    const [editVisible, setEditVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);
    const [selectedProcess, setSelectedProcess] = useState<ProcessCheck>();

    const columns = [{
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 400,
        render: (field: string, record: ProcessCheck, index: number) => {
            return <>
                <div style={{ display: 'inline-flex' }}>
                    <div>
                        <Title heading={6}>
                            {record.description}
                            {/* <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/policies/entrance/process-check/detail/${record.id}`), 10)}>{record.description}</a> */}
                        </Title>
                        <Paragraph size='small'>{record.name}</Paragraph>
                    </div>
                </div>
            </>
        },
    }, {
        title: '数字签名',
        dataIndex: 'signature',
        key: 'signature',
        render: (field: string, record: ProcessCheck, index: number) => {
            return <>
                <Paragraph type='quaternary' style={{ wordBreak: 'break-all', minWidth: '300px' }}>
                    {record.signature}
                </Paragraph>

            </>
        },
    }, {
        title: '操作系统',
        dataIndex: 'os',
        key: 'os',
        width: 100,
        render: (field: OS, processCheck: ProcessCheck, index: number) => {
            return <>
                <div style={{ display: 'inline-flex' }}>
                    <div>
                        <Text type='tertiary'>{processCheck.os == OS.WINDOWS ? "Windows" : processCheck.os == OS.MACOS ? "Mac" : processCheck.os == OS.IOS ? "iOS" : processCheck.os == OS.ANDROID ? "Android" : "Linux"}</Text>

                    </div>
                </div>
            </>
        },
    }, 
    // {
    //     title: '修复方案',
    //     dataIndex: 'repairSolution',
    //     key: 'repairSolution',
    //     width: 300,
    //     render: (field: string, processCheck: ProcessCheck, index: number) => {
    //         return <>
    //             <RepairMethodDisplay
    //                 repairMethod={processCheck.repairMethod}
    //                 repairSolution={processCheck.repairSolution}
    //                 repairSolutionName={processCheck.repairSolutionName}
    //                 repairSolutionDescription={processCheck.repairSolutionDescription}
    //             />
    //         </>
    //     },
    // }, 
    {
        title: '',
        dataIndex: 'operation',
        key: 'operation',
        width: 100,
        render: (text: string, record: ProcessCheck) => {
            return <><div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>

                        <Dropdown.Item
                            onClick={() => {
                                setEditVisible(true);
                                setSelectedProcess(record);
                            }}
                        >编辑策略</Dropdown.Item>
                        <Divider />
                        <Dropdown.Item
                            onClick={() => {
                                setDelVisible(true);
                                setSelectedProcess(record);
                            }}
                            type='danger'>删除策略</Dropdown.Item>

                    </Dropdown.Menu>}><Button><IconMore className='align-v-center' /></Button>
            </Dropdown>
            </div>
            </>;
        }
    }];

    const query = () => {
        setLoading(true);
        flylayerClient.listProcessCheck({ flynetId: flynet.id }).then((res) => {
            setAllProcessChecks(res.processChecks);
            const list = doFilter(res.processChecks, filterParam);
            setProcessChecks(list);
        }).catch((err) => {
            Notification.error({
                title: '查询失败',
                content: err.message,
            });
        }).finally(() => {
            setLoading(false);
        });
    }


    useEffect(() => {
        query();
    }, [filterParam]);


    // 重新加载数据
    const reload = () => {
        query();
    }

    // 过滤数据
    const doFilter = (src: Array<ProcessCheck>, processCheckFilter: ProcessCheckFilter) => {
        if (!src || src.length == 0) {
            setTotal(0);
            return src;
        }

        if (!processCheckFilter.query && processCheckFilter.os.length == 0) {
            setTotal(src.length);
            return src;
        }

        const filteredList = src.filter((reload) => {
            if (processCheckFilter.query) {
                if (!reload.name.includes(processCheckFilter.query) && !reload.description.includes(processCheckFilter.query)) {
                    return false;
                }
            }
            if (processCheckFilter.os.length > 0) {
                let hasOs = false;
                if (processCheckFilter.os.includes(reload.os)) {
                    hasOs = true;
                }
                if (!hasOs) {
                    return false;
                }
            }
            return true;
        });
        setTotal(filteredList.length);
        return filteredList;
    }

    return {
        loading,
        processChecks,
        columns,
        total,
        filterParam,
        setFilterParam,
        doFilter,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedProcess,
        setSelectedProcess,
        reload,
    }
}

export default useTable;
