import React, { useState, useContext, useEffect } from "react";
import { Typography, Modal, Form, Row, Col, Skeleton, Select, Input, Tag, Space, Notification, Divider, Popover } from "@douyinfe/semi-ui";
import { sanitizeLabel } from '@/utils/common';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import RepairMethodEditor from '@/pages/entrance/components/repair-method-editor';

import { IconHelpCircle, IconPlus, IconMinusCircle } from '@douyinfe/semi-icons';
import pinyin from 'tiny-pinyin';
import { ProcessCheck, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from "@/services/core";

const { Title, Paragraph, Text } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    id: bigint
}

const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        signature: string;
        description: string;
        os: OS;
    }>>();

    // 对象loading
    const [loading, setLoading] = useState(false);
    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const [processCheck, setProcessCheck] = useState<ProcessCheck>();

    const [repairMethodErr, setRepairMethodErr] = useState(false);

    const [repairMethod, setRepairMethod] = useState<RepairMethod>(RepairMethod.AUTO);
    const [repairSolution, setRepairSolution] = useState('');
    const [repairSolutionName, setRepairSolutionName] = useState('');
    const [repairSolutionDescription, setRepairSolutionDescription] = useState('');



    const query = async () => {

        setLoading(true);
        flylayerClient.getProcessCheck({
            id: props.id
        }).then((res) => {
            const check = res.processCheck;
            if (!check) {
                Notification.error({
                    title: '获取失败',
                    content: '未找到检测策略'
                });
                return;
            }

            setRepairMethod(check.repairMethod);
            setRepairSolution(check.repairSolution);
            setRepairSolutionName(check.repairSolutionName);
            setRepairSolutionDescription(check.repairSolutionDescription);

            setProcessCheck(check);
        }).catch((err) => {
            console.trace(err);
            Notification.error({
                title: '获取失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        });
    }

    useEffect(() => {
        if (props.id) {
            query();
        }
    }, []);
    const handleSubmit = async () => {
        if (!formApi) {
            return
        }

        // let hasRepairMethodErr = false;
        // if (!repairSolutionName || !repairSolution || !repairSolutionDescription) {
        //     setRepairMethodErr(true);
        //     hasRepairMethodErr = true;
        // }
        await formApi.validate();
        // if (hasRepairMethodErr) {
        //     return;
        // }
        const values = formApi?.getValues();
        if (!values) {
            return;
        }


        const name = values.name.trim();
        const signature = values.signature ? values.signature.trim() : '';
        const description = values.description ? values.description.trim() : '';
        const os = values.os;

        const check = new ProcessCheck();
        check.id = processCheck?.id || BigInt(0);
        check.name = name;
        check.signature = signature;
        check.description = description;
        check.os = os;

        check.repairMethod = repairMethod;
        check.repairSolution = repairSolution;
        check.repairSolutionName = repairSolutionName;
        check.repairSolutionDescription = repairSolutionDescription;

        setSaveLoading(true);

        flylayerClient.updateProcessCheck({
            flynetId: flynet.id,
            processCheck: check
        }).then(res => {
            Notification.success({
                title: '编辑成功',
                content: '编辑检测策略成功'
            });
            props.success && props.success();
        }, err => {
            Notification.error({
                title: '编辑失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        })

    }

    return <><Modal
        title="编辑检测策略"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={800}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >

        <Skeleton loading={loading} placeholder={
            <>
                <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title>
                <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />
            </>
        }>{processCheck &&
            <Form
                getFormApi={setFormApi}
                initValues={{
                    name: processCheck.name,
                    signature: processCheck.signature,
                    description: processCheck.description,
                    os: processCheck.os
                }}
            >
                {({ values }) => (<>
                    <Row gutter={20}>
                        <Col span={24}>
                            <Form.Input field='name' label='名称' trigger={'blur'} validate={value => {
                                if (!value) {
                                    return '名称不能为空';
                                }
                                return '';
                            }} />
                        </Col>

                    </Row>
                    <Row>
                        <Col span={24}>
                            <Form.Input field="signature" label="数字签名" />
                        </Col>
                    </Row>
                    <Row>
                        <Col span={24}>
                            <Form.TextArea field="description" autosize rows={1} label="策略描述" />
                        </Col>
                    </Row>
                    <Row className="mb10">
                        <Col span={24}>
                            <Form.Select field="os" label="操作系统"
                                validate={(value) => {
                                    if (!value) {
                                        return '请选择操作系统';
                                    }
                                    return '';
                                }}
                                style={{ width: '100%' }}>
                                <Form.Select.Option value={OS.WINDOWS}>Windows</Form.Select.Option>
                                <Form.Select.Option value={OS.MACOS}>Mac</Form.Select.Option>
                                <Form.Select.Option value={OS.IOS}>iOS</Form.Select.Option>
                                <Form.Select.Option value={OS.ANDROID}>Android</Form.Select.Option>
                                <Form.Select.Option value={OS.LINUX}>Linux</Form.Select.Option>
                            </Form.Select>
                        </Col>
                    </Row>
                    {/* <Paragraph type="tertiary" className="mb10">修复方式</Paragraph>
                    <Row>
                        <Col span={24}>
                            <RepairMethodEditor
                                repairMethodErr={repairMethodErr}
                                repairMethod={repairMethod}
                                repairSolution={repairSolution}
                                repairSolutionName={repairSolutionName}
                                repairSolutionDescription={repairSolutionDescription}
                                onChange={(param) => {
                                    setRepairMethod(param.repairMethod);
                                    setRepairSolution(param.repairSolution);
                                    setRepairSolutionName(param.repairSolutionName);
                                    setRepairSolutionDescription(param.repairSolutionDescription);
                                }}
                                onError={() => {
                                    setRepairMethodErr(true);
                                }}
                            />
                        </Col>
                    </Row> */}

                </>)}
            </Form>}</Skeleton>
    </Modal></>
}

export default Index;
