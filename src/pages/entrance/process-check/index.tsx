import React, { useState, useContext, useEffect } from 'react'
import { Typography, Input, Select, Table, Row, Col, Button, Space, Breadcrumb, Tag, Divider, Layout } from '@douyinfe/semi-ui';
import { IconSearch, IconSetting } from '@douyinfe/semi-icons';
import { ProcessCheck, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';

import qs from 'query-string';
import { BASE_PATH } from '@/constants/router';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import TableEmpty from '@/components/table-empty';
import New from './new';
import Edit from './edit';
import Del from './del';
import styles from '@/pages/entrance/index.module.scss'
import useTable, { ProcessCheckFilter } from './useTable';

const { Title, Text } = Typography;

const { Sider, Content } = Layout;
// 根据URL参数设置过滤参数
const getInitFilter = (location: Location): ProcessCheckFilter => {
    const query: string = getQueryParam('query', location) as string;const osQuery = getQueryParam('os', location);

    let os: number[] = [];
    if (osQuery && Array.isArray(osQuery)) {
        osQuery.forEach(item => {
            if (item && typeof item == 'string') {
                os.push(parseInt(item));
            }

        })
    }
    if (osQuery && typeof osQuery == 'string') {
        os = [parseInt(osQuery)];
    }

    return {
        query: query || '',
        os: os,
    }
}

const Index: React.FC = () => {
    const navigate = useNavigate();
    const [createVisible, setCreateVisible] = useState(false);
    const [tabKey, setTabKey] = useState('');

    const initFilter: ProcessCheckFilter = getInitFilter(useLocation());

    // 过滤参数改变时跳转路由
    const doNavigate = (filter: ProcessCheckFilter) => {

        let query = '';
        if (filter.query || filter.os.length > 0) {
            query = qs.stringify(filter, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/policies/entrance/process-check?${query}`)
        } else {
            navigate(`${BASE_PATH}/policies/entrance/process-check`)
        }

    }
    const {
        loading,
        columns,
        processChecks,
        total,
        filterParam,
        setFilterParam,
        doFilter,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        selectedProcess,
        setSelectedProcess, 
        reload,
    } = useTable(initFilter);


    const listOs = [
        { value: OS.MACOS, label: 'macOS' },
        { value: OS.IOS, label: 'iOS' },
        { value: OS.WINDOWS, label: 'Windows' },
        { value: OS.LINUX, label: 'Linux' },
        { value: OS.ANDROID, label: 'Android' },
    ];

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, query: value })
        doNavigate({ ...filterParam, query: value });
    }
    const handleOsChange = (value: any) => {
        setFilterParam({ ...filterParam, os: value })
        doNavigate({ ...filterParam, os: value });
    }


    return <><div className='general-page'>
        <Breadcrumb routes={
            [
                {
                    path: `${BASE_PATH}/policies/entrance`,
                    href: `${BASE_PATH}/policies/entrance`,
                    name: '设备准入策略'
                },
                {
                    name: '进程检测项配置',
                }
            ]
        }>
        </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>进程检测项配置</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>新建检测项</Button>
                </Space>
            </div></Col>
        </Row>

        <Layout className='mb20 search-bar' >
            <Layout>
                <Content className='pr10'>
                    <Input value={filterParam.query}
                        onChange={handleQueryChange}
                        style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={'根据名称、描述搜索'}></Input>
                </Content>
                <Sider> <Space>

                    <Select multiple
                        maxTagCount={1}
                        style={{ width: 200 }}
                        optionList={listOs}
                        insetLabel="操作系统"
                        onChange={handleOsChange}
                        value={filterParam.os}></Select>


                </Space></Sider>
            </Layout>

        </Layout>
        <Table
            rowKey={(record?: ProcessCheck) => record ? record.id + '' : ''}
            loading={loading}
            columns={columns}
            dataSource={processChecks}
            pagination={false}
            expandRowByClick={true}
            // expandedRowRender={expandedRowRender}
            empty={<TableEmpty loading={loading}></TableEmpty>}
        ></Table>
    </div>
        {createVisible && <New
            close={() => setCreateVisible(false)}
            success={() => {
                setCreateVisible(false);
                reload();
            }}
        />}
        {editVisible && selectedProcess && <Edit
            close={() => setEditVisible(false)}
            success={() => {
                setEditVisible(false);
                reload();
            }}
            id={selectedProcess?.id}
        />}
        {delVisible && selectedProcess && <Del
            close={() => setDelVisible(false)}
            success={() => {
                setDelVisible(false);
                reload();
            }}
            record={selectedProcess}
        />}
    </>
}

export default Index;