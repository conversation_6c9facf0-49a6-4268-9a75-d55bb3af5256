import React, { useState, useContext } from "react";
import { Typography, Modal, Form, Row, Col, Notification } from "@douyinfe/semi-ui";
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import RepairMethodEditor from '@/pages/entrance/components/repair-method-editor';
import { ProcessCheck, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import styles from '@/pages/entrance/index.module.scss'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from "@/services/core";

const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
}

const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi<{
        name: string;
        signature: string;
        description: string;
        os: OS;
    }>>();

    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false);

    const [repairMethodErr, setRepairMethodErr] = useState(false);

    const [repairMethod, setRepairMethod] = useState<RepairMethod>(RepairMethod.AUTO);
    const [repairSolution, setRepairSolution] = useState('');
    const [repairSolutionName, setRepairSolutionName] = useState('');
    const [repairSolutionDescription, setRepairSolutionDescription] = useState('');

    const handleSubmit = async () => {
        if (!formApi) {
            return
        }

        // let hasRepairMethodErr = false;
        // if (!repairSolutionName || !repairSolution || !repairSolutionDescription) {
        //     setRepairMethodErr(true);
        //     hasRepairMethodErr = true;
        // }
        await formApi.validate();
        // if (hasRepairMethodErr) {
        //     return;
        // }
        

        const values = formApi?.getValues();
        if (!values) {
            return;
        }



        const name = values.name.trim();
        const signature = values.signature ? values.signature.trim() : '';
        const description = values.description ? values.description.trim() : '';
        const os = values.os;

        const check = new ProcessCheck();
        check.name = name;
        check.signature = signature;
        check.description = description;
        check.os = os;

        check.repairMethod = repairMethod;
        check.repairSolution = repairSolution;
        check.repairSolutionName = repairSolutionName;
        check.repairSolutionDescription = repairSolutionDescription;

        setSaveLoading(true);

        flylayerClient.createProcessCheck({
            flynetId: flynet.id,
            processCheck: check
        }).then(res => {
            Notification.success({
                title: '添加成功',
                content: '添加检测策略成功'
            });
            props.success && props.success();
        }, err => {
            Notification.error({
                title: '添加失败',
                content: err.message
            });
        }).finally(() => {
            setSaveLoading(false);
        })

    }

    return <><Modal
        title="添加检测策略"
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={800}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Form
            getFormApi={setFormApi}
            initValues={{}}
        >
            {({ values }) => (<>
                <Row gutter={20}>
                    <Col span={24}>
                        <Form.Input field='name' label='名称' trigger={'blur'} validate={value => {
                            if (!value) {
                                return '名称不能为空';
                            }
                            return '';
                        }} />
                    </Col>

                </Row>
                <Row>
                    <Col span={24}>
                        <Form.Input field="signature" label="数字签名" />
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Form.TextArea field="description" autosize rows={1} label="策略描述" />
                    </Col>
                </Row>
                <Row className="mb10">
                    <Col span={24}>
                        <Form.Select field="os" label="操作系统" style={{ width: '100%' }}
                            validate={(value)=>{
                                if (!value) {
                                    return '请选择操作系统';
                                }
                                return '';
                            }}
                        >
                            <Form.Select.Option value={OS.WINDOWS}>Windows</Form.Select.Option>
                            <Form.Select.Option value={OS.MACOS}>Mac</Form.Select.Option>
                            <Form.Select.Option value={OS.IOS}>iOS</Form.Select.Option>
                            <Form.Select.Option value={OS.ANDROID}>Android</Form.Select.Option>
                            <Form.Select.Option value={OS.LINUX}>Linux</Form.Select.Option>
                        </Form.Select>
                    </Col>
                </Row>
                
                {/* <Paragraph type="tertiary" className="mb10">修复方式</Paragraph>
                <Row>
                    <Col span={24}>
                        <RepairMethodEditor
                            repairMethodErr={repairMethodErr}
                            repairMethod={repairMethod}
                            repairSolution={repairSolution}
                            repairSolutionName={repairSolutionName}
                            repairSolutionDescription={repairSolutionDescription}
                            onChange={(param) => {
                                setRepairMethod(param.repairMethod);
                                setRepairSolution(param.repairSolution);
                                setRepairSolutionName(param.repairSolutionName);
                                setRepairSolutionDescription(param.repairSolutionDescription);
                            }}
                            onError={() => {
                                setRepairMethodErr(true);
                            }}
                        />
                    </Col>
                </Row> */}

            </>)}
        </Form>
    </Modal></>
}

export default Index;
