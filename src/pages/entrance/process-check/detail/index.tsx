import React, { useState, useContext, useEffect } from "react";
import { useNavigate, useParams } from 'react-router-dom';

import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { Breadcrumb, Row, Col, Button, Typography, Tag, Tooltip, Notification, Dropdown, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List, Banner, Tabs, TabPane } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { BASE_PATH } from '@/constants/router';
import { flylayerClient } from '@/services/core';
import { ProcessCheck } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import RepairMethodDisplay from '@/pages/entrance/components/repair-method-display';

const { Title, Paragraph, Text } = Typography;
import styles from '@/pages/entrance/index.module.scss'
interface Props {

}

const Index: React.FC<Props> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();

    const [processCheck, setProcessCheck] = useState<ProcessCheck>();
    const [loading, setLoading] = useState(true);

    const params = useParams<{ id: string }>()
    const id = params.id ? params.id : '';

    const queryProcessCheck = async (id: bigint) => {
        setLoading(true);
        try {
            const res = await flylayerClient.getProcessCheck({
                id: id
            });
            if (res.processCheck) {
                setProcessCheck(res.processCheck);
            }

        } catch (err) {
            Notification.error({
                title: '获取数据失败',
                content: err
            });
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        if (id) {
            queryProcessCheck(BigInt(id));
        }
    }, [id]);

    return <><Skeleton placeholder={<div className='general-page'>
        <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>
        <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
        <Skeleton.Image style={{ height: 60 }} className='mb40' />
        <Skeleton.Image style={{ height: 200 }} />
    </div>} loading={loading}>
        {processCheck && <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/policies/entrance`,
                        href: `${BASE_PATH}/policies/entrance`,
                        name: '设备准入策略'
                    },
                    {
                        path: `${BASE_PATH}/policies/entrance/process-check`,
                        href: `${BASE_PATH}/policies/entrance/process-check`,
                        name: '进程检测项配置',
                    },
                    {
                        name: processCheck.name,
                    }
                ]
            }>
            </Breadcrumb>


            <Row className="mb10">
                <Col span={20}>
                    <Space>
                        <Title className={styles.heading} heading={3} style={{ flexShrink: 0 }}>
                            <span>{processCheck?.name}&nbsp;</span>
                            <Text type='tertiary' size='small' ellipsis={{
                                showTooltip: false
                            }} style={{ maxWidth: 450 }}>{processCheck?.description}</Text>
                        </Title>
                        <Paragraph type="tertiary"></Paragraph></Space>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                </div></Col>
            </Row>

            <Divider className="mb40"></Divider>

            <Descriptions>
                <Descriptions.Item itemKey={'数字签名'}>{processCheck.signature}</Descriptions.Item>
                <Descriptions.Item itemKey={'操作系统'}>
                    <Text>{processCheck.os == OS.WINDOWS ? "Windows" : processCheck.os == OS.MACOS ? "Mac" : processCheck.os == OS.IOS ? "iOS" : processCheck.os == OS.ANDROID ? "Android" : "Linux"}</Text>
                </Descriptions.Item>
                <Descriptions.Item itemKey={'修复方案'}>
                    <RepairMethodDisplay
                        repairMethod={processCheck.repairMethod}
                        repairSolution={processCheck.repairSolution}
                        repairSolutionName={processCheck.repairSolutionName}
                        repairSolutionDescription={processCheck.repairSolutionDescription}
                    /></Descriptions.Item>
            </Descriptions>


        </div>}
    </Skeleton>
    </>
}

export default Index;