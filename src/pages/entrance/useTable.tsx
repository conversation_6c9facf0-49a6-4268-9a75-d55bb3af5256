import { useState, useEffect, useContext } from 'react'
import { IconMore, IconArticle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { BASE_PATH } from '@/constants/router';

import { useNavigate } from 'react-router-dom';
import { EntrancePolicy, RiskLevel } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';


import { Typography, Notification, Dropdown, Button, Divider, Row, Col, Space, Tag, Popover } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { caseInsensitiveIncludes } from '@/utils/common';
import { time } from 'echarts';
const { Title, Paragraph, Text } = Typography;

export type EntranceFilter = {
    query?: string;
    disabled: string;
}

const useTable = (filterParam: EntranceFilter) => {
    const navigate = useNavigate();
    const flynet = useContext(FlynetGeneralContext);

    const [entrances, setEntrances] = useState<Array<EntrancePolicy>>([]);
    const [allEntrances, setAllEntrances] = useState<Array<EntrancePolicy>>([]);

    const [loading, setLoading] = useState(true);

    const [editVisible, setEditVisible] = useState(false);

    const [delVisible, setDelVisible] = useState(false);

    const [duplicateVisible, setDuplicateVisible] = useState(false);

    const [selectedEntrance, setSelectedEntrance] = useState<EntrancePolicy>();


    // 过滤参数
    const [filter, setFilter] = useState<EntranceFilter>(filterParam);

    const query = () => {
        setLoading(true);

        flylayerClient.listEntrancePolicy({
            flynetId: flynet.id
        }).then(res => {
            setAllEntrances(res.entrancePolicies);
            const list = doFilter(res.entrancePolicies, filterParam);
            setEntrances(list);
        }, err => {
            Notification.error({
                title: '获取入口策略列表失败',
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        })
    }


    useEffect(() => {
        query()
    }, []);

    // 重新加载数据
    const reload = () => {
        query();
    }

    const columns = [{
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        render: (field: string, record: EntrancePolicy, index: number) => {
            return <>
                <div style={{ display: 'inline-flex' }}>
                    <div>
                        <Space>
                            <Title heading={6}>
                                <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/policies/entrance/detail/${record.id}`), 10)}>
                                    {record.alias}
                                </a>

                            </Title>
                            {record.description &&
                                <Popover content={<div className='p10'>{record.description}</div>}>
                                    <IconArticle style={{
                                        fontSize: 14,
                                        color: '#999'
                                    }} />
                                </Popover>
                            }
                        </Space>

                        <Paragraph type="secondary">{record.name}</Paragraph>
                    </div>
                </div>
            </>
        }
    }, {
        title: '状态',
        width: 120,
        dataIndex: 'disabled',
        key: 'disabled',
        render: (field: boolean, record: EntrancePolicy, index: number) => {
            return <>
                <Tag color={record.disabled ? 'red' : 'green'}>{record.disabled ? '禁用' : '启用'}</Tag>
            </>
        }
    }, {
        title: '优先级',
        width: 120,
        dataIndex: 'priority',
        key: 'priority',
    }, {
        title: '',
        dataIndex: 'operation',
        key: 'operation',
        width: 100,
        render: (text: string, record: EntrancePolicy) => {
            return <><div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>

                        <Dropdown.Item
                            onClick={() => {
                                setDuplicateVisible(true);
                                setSelectedEntrance(record);
                            }}
                        >复制策略</Dropdown.Item>
                        <Divider />
                        <Dropdown.Item
                            onClick={() => {
                                setEditVisible(true);
                                setSelectedEntrance(record);
                            }}
                        >编辑策略</Dropdown.Item>
                        <Divider />
                        <Dropdown.Item
                            onClick={() => {
                                setDelVisible(true);
                                setSelectedEntrance(record);
                            }}
                            type='danger'>删除策略</Dropdown.Item>

                    </Dropdown.Menu>}><Button><IconMore className='align-v-center' /></Button>
            </Dropdown>
            </div>
            </>;
        }
    }]


    // 过滤数据
    const doFilter = (list: Array<EntrancePolicy>, filter: EntranceFilter) => {
        let result = list;

        if (filter.query) {
            let queryStr = filter.query.trim();
            result = result.filter(item => caseInsensitiveIncludes(item.name, queryStr) || caseInsensitiveIncludes(item.alias, queryStr));
        }

        if (filter.disabled) {
            result = result.filter(item => item.disabled === (filter.disabled === 'true'));
        }

        return result;
    }

    const expandedRowRender = (record: EntrancePolicy | undefined) => {
        if (!record) {
            return null;
        }
        return <div>
            {record.useBaselineCheck && <>
                <Title heading={6} >终端基线检测</Title>

                {record.baselinePolicies.map((item, index) => {
                    return <Row key={index}>
                        <Col span={12}><Text>{item.item?.name}</Text></Col>
                        <Col span={6}><Text>{item.item?.os == OS.WINDOWS ? "Windows" : item.item?.os == OS.MACOS ? "Mac" : item.item?.os == OS.IOS ? "iOS" : item.item?.os == OS.ANDROID ? "Android" : "Linux"}</Text></Col>
                        <Col span={6}><Text>{item.riskLevel == RiskLevel.LOW ? "低危" : item.riskLevel == RiskLevel.MEDIUM ? "中危" : item.riskLevel == RiskLevel.HIGH ? "高危" : ""}</Text></Col>
                    </Row>
                })}
            </>}
            {record.useAppCheck && <>
                <Title heading={6}>应用风险检测</Title>
                <Row>
                    <Col span={12}>
                        <Paragraph>违规应用</Paragraph>
                        {record.forbiddenAppPolicies.map((item, index) => {
                            return <Row key={index}>
                                <Col span={12}><Text>{item.appCheck?.name}</Text></Col>
                                <Col span={12}>{item.appCheck?.items.map((item, i) => <Tag key={i}>
                                    {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                                </Tag>)}</Col>
                                <Col span={6}><Text>{item.riskLevel == RiskLevel.LOW ? "低危" : item.riskLevel == RiskLevel.MEDIUM ? "中危" : item.riskLevel == RiskLevel.HIGH ? "高危" : ""}</Text></Col>
                            </Row>
                        })}
                    </Col>
                    <Col span={12}>
                        <Paragraph>必装应用</Paragraph>
                        {record.requiredAppPolicies.map((item, index) => {
                            return <Row key={index}>
                                <Col span={12}><Text>{item.appCheck?.name}</Text></Col>
                                <Col span={12}>{item.appCheck?.items.map((item, i) => <Tag key={i}>
                                    {item.os == OS.WINDOWS ? "Windows" : item.os == OS.MACOS ? "Mac" : item.os == OS.IOS ? "iOS" : item.os == OS.ANDROID ? "Android" : "Linux"}
                                </Tag>)}</Col>
                                <Col span={6}><Text>{item.riskLevel == RiskLevel.LOW ? "低危" : item.riskLevel == RiskLevel.MEDIUM ? "中危" : item.riskLevel == RiskLevel.HIGH ? "高危" : ""}</Text></Col>
                            </Row>
                        })}
                    </Col>
                </Row>
            </>}
            {record.useProcessCheck && <>
                <Title heading={6}>进程风险检测</Title>
                <Paragraph>黑名单进程</Paragraph>
                {record.forbiddenProcessPolicies.map((fItem, fIndex) => {
                    return <div key={fIndex}>
                        <Paragraph>Windows</Paragraph>
                        {fItem.windowsProcesses.map((item, index) => {
                            return <Row key={index}>
                                <Col span={12}><Text>{item.name}</Text></Col>
                                <Col span={6}><Text>{item.signature}</Text></Col>
                                <Col span={6}><Text>{item.riskLevel == RiskLevel.LOW ? "低危" : item.riskLevel == RiskLevel.MEDIUM ? "中危" : item.riskLevel == RiskLevel.HIGH ? "高危" : ""}</Text></Col>
                            </Row>
                        })}
                        <Paragraph>Mac</Paragraph>
                        {fItem.macProcesses.map((item, index) => {
                            return <Row key={index}>
                                <Col span={12}><Text>{item.name}</Text></Col>
                                <Col span={6}><Text>{item.signature}</Text></Col>
                                <Col span={6}><Text>{item.riskLevel == RiskLevel.LOW ? "低危" : item.riskLevel == RiskLevel.MEDIUM ? "中危" : item.riskLevel == RiskLevel.HIGH ? "高危" : ""}</Text></Col>
                            </Row>
                        })}
                        <Paragraph>Linux</Paragraph>{fItem.linuxProcesses.map((item, index) => {
                            return <Row key={index}>
                                <Col span={12}><Text>{item.name}</Text></Col>
                                <Col span={6}><Text>{item.signature}</Text></Col>
                                <Col span={6}><Text>{item.riskLevel == RiskLevel.LOW ? "低危" : item.riskLevel == RiskLevel.MEDIUM ? "中危" : item.riskLevel == RiskLevel.HIGH ? "高危" : ""}</Text></Col>
                            </Row>
                        })}
                    </div>
                })}
            </>}
        </div>
    }

    useEffect(() => {
        setEntrances(doFilter(allEntrances, filter));
    }, [filter]);

    return {
        loading,
        entrances,
        allEntrances,
        filter,
        setFilter,
        reload,
        columns,
        doFilter,
        duplicateVisible,
        setDuplicateVisible,
        selectedEntrance,
        setSelectedEntrance,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        expandedRowRender
    }

}

export default useTable;