import React, { useContext, useEffect, useState } from 'react'
import { Typography, Modal, Input, Select, Row, Col, Button, Space, Dropdown, Divider } from "@douyinfe/semi-ui";
import { ProcessCheck, ProcessPolicy, ProcessPolicyItem, RiskLevel, RepairMethod } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/entrance_pb';
import { OS } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { flylayerClient } from "@/services/core";

import { IconMinusCircle, IconPlus, IconArrowUpRight } from '@douyinfe/semi-icons';

import styles from '@/pages/entrance/index.module.scss'
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { BASE_PATH } from '@/constants/router';

const { Title, Text } = Typography;

interface Props {
    title: string,
    close: () => void,
    success?: (processPolicy: ProcessPolicy) => void,
    processPolicy: ProcessPolicy,
}
const Index: React.FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, setFormApi] = useState<FormApi>()
    // 保存按钮loading
    const [saveLoading, setSaveLoading] = useState(false); RepairMethod
    const handleSubmit = async () => {
        props.success && props.success(processPolicy);
        props.close();
    }


    const [processPolicy, setProcessPolicy] = useState<ProcessPolicy>(props.processPolicy);

    const [processChecks, setProcessChecks] = useState<ProcessCheck[]>([]);

    const query = () => {
        flylayerClient.listProcessCheck({
            flynetId: flynet.id,
        }).then(res => {
            setProcessChecks(res.processChecks);
        }).catch(err => {
            console.log(err);
        })
    }

    useEffect(() => {
        query();
    }, []);

    return <><Modal
        title={<>{props.title}&nbsp;

            <a className='link-external' style={{
                fontSize: '12px',
                fontWeight: 'normal',
            }} target='_blank' href={`${BASE_PATH}/policies/entrance/process-check`} onClick={(e) => { e.stopPropagation() }}>
                进程检测项管理<IconArrowUpRight />
            </a>
        </>}
        visible={true}
        onOk={handleSubmit}
        onCancel={props.close}
        width={800}
        okButtonProps={{ loading: saveLoading }}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Title heading={6} className='mb10'>Windows</Title>
        <Row className={styles.tableTitle} style={{ marginBottom: 12 }}>
            <Col span={12}>文件数字签名</Col>
            <Col span={8}>进程名称</Col>
            <Col span={4}>风险等级</Col>
        </Row>
        {
            processPolicy.windowsProcesses.map((item, index) => {
                return <Row key={index} className={styles.tableBody}>
                    <Col span={12}><Input
                        value={item.signature}
                        onChange={(val) => {
                            let newProcessPolicy = new ProcessPolicy({
                                windowsProcesses: processPolicy.windowsProcesses.map((item, i) => {
                                    if (i === index) {
                                        return new ProcessPolicyItem({
                                            os: OS.WINDOWS,
                                            riskLevel: item.riskLevel,
                                            name: item.name,
                                            signature: val,
                                        });
                                    }
                                    return item;
                                }),
                                macProcesses: processPolicy.macProcesses,
                                linuxProcesses: processPolicy.linuxProcesses,
                            });

                            setProcessPolicy(newProcessPolicy);
                        }}
                    /></Col>
                    <Col span={8}><Input
                        value={item.name}
                        onChange={(val) => {
                            let newProcessPolicy = new ProcessPolicy({
                                windowsProcesses: processPolicy.windowsProcesses.map((item, i) => {
                                    if (i === index) {
                                        return new ProcessPolicyItem({
                                            os: OS.WINDOWS,
                                            riskLevel: item.riskLevel,
                                            name: val,
                                            signature: item.signature,
                                        });
                                    }
                                    return item;
                                }),
                                macProcesses: processPolicy.macProcesses,
                                linuxProcesses: processPolicy.linuxProcesses,
                            });

                            setProcessPolicy(newProcessPolicy);
                        }}
                    /></Col>
                    <Col span={4}>
                        <Space>
                            <Select style={{ width: 80 }}
                                value={item.riskLevel}
                                onChange={(val) => {
                                    let newProcessPolicy = new ProcessPolicy({
                                        windowsProcesses: processPolicy.windowsProcesses.map((item, i) => {
                                            if (i === index) {
                                                return new ProcessPolicyItem({
                                                    os: OS.WINDOWS,
                                                    riskLevel: val as any,
                                                    name: item.name,
                                                    signature: item.signature,
                                                });
                                            }
                                            return item;
                                        }),
                                        macProcesses: processPolicy.macProcesses,
                                        linuxProcesses: processPolicy.linuxProcesses,
                                    });

                                    setProcessPolicy(newProcessPolicy);
                                }}
                            >
                                <Select.Option value={RiskLevel.LOW}>低危</Select.Option>
                                <Select.Option value={RiskLevel.MEDIUM}>中危</Select.Option>
                                <Select.Option value={RiskLevel.HIGH}>高危</Select.Option>

                            </Select>
                            <Button type='danger'
                                onClick={() => {
                                    // 删除一行

                                    let newProcessPolicy = new ProcessPolicy({
                                        windowsProcesses: processPolicy.windowsProcesses.filter((item, i) => i !== index),
                                        macProcesses: processPolicy.macProcesses,
                                        linuxProcesses: processPolicy.linuxProcesses,
                                    });

                                    setProcessPolicy(newProcessPolicy);
                                }}
                                icon={<IconMinusCircle />}></Button>
                        </Space>
                    </Col>
                </Row>
            })
        }
        <Dropdown content={
            <Dropdown.Menu>
                <Dropdown.Item onClick={() => {
                    // 添加一行
                    let newProcessPolicy = new ProcessPolicy({
                        windowsProcesses: processPolicy.windowsProcesses.concat(new ProcessPolicyItem({
                            os: OS.WINDOWS,
                            riskLevel: RiskLevel.HIGH,
                            name: '',
                            signature: '',

                        })),
                        macProcesses: processPolicy.macProcesses,
                        linuxProcesses: processPolicy.linuxProcesses,
                    });
                    setProcessPolicy(newProcessPolicy);
                }}>自定义</Dropdown.Item>
                {processChecks.map((item, index) => {
                    if (item.os !== OS.WINDOWS) {
                        return null;
                    }
                    return <Dropdown.Item key={index} onClick={() => {
                        // 添加一行
                        let newProcessPolicy = new ProcessPolicy({
                            windowsProcesses: processPolicy.windowsProcesses.concat(new ProcessPolicyItem({
                                os: OS.WINDOWS,
                                riskLevel: RiskLevel.HIGH,
                                name: item.name,
                                signature: item.signature,
                            })),
                            macProcesses: processPolicy.macProcesses,
                            linuxProcesses: processPolicy.linuxProcesses,
                        });

                        setProcessPolicy(newProcessPolicy);
                    }}>
                        <Space style={{ alignContent: 'center' }}>
                            <Text size='normal'>{item.name}</Text>
                            <Text size='small' type='tertiary'>{item.description}</Text>
                        </Space>
                    </Dropdown.Item>
                })}
            </Dropdown.Menu>
        }>
            <Button className='mb20' type='primary' icon={<IconPlus />}>添加</Button>
        </Dropdown>

        <Divider className='mb20' />
        <Title heading={6} className='mb10'>Mac</Title>
        <Row className={styles.tableTitle} style={{ marginBottom: 12 }}>
            <Col span={12}>文件数字签名</Col>
            <Col span={8}>进程名称</Col>
            <Col span={4}>风险等级</Col>
        </Row>

        {
            processPolicy.macProcesses.map((item, index) => {
                return <Row key={index} className={styles.tableBody}><Col span={12}><Input
                    value={item.signature}
                    onChange={(val) => {
                        let newProcessPolicy = new ProcessPolicy({
                            windowsProcesses: processPolicy.windowsProcesses,
                            macProcesses: processPolicy.macProcesses.map((item, i) => {
                                if (i === index) {
                                    return new ProcessPolicyItem({
                                        os: OS.MACOS,
                                        riskLevel: item.riskLevel,
                                        name: item.name,
                                        signature: val,
                                    });
                                }
                                return item;
                            }),
                            linuxProcesses: processPolicy.linuxProcesses,
                        });

                        setProcessPolicy(newProcessPolicy);
                    }}
                /></Col>
                    <Col span={8}><Input
                        value={item.name}
                        onChange={(val) => {
                            let newProcessPolicy = new ProcessPolicy({
                                windowsProcesses: processPolicy.windowsProcesses,
                                macProcesses: processPolicy.macProcesses.map((item, i) => {
                                    if (i === index) {
                                        return new ProcessPolicyItem({
                                            os: OS.MACOS,
                                            riskLevel: item.riskLevel,
                                            name: val,
                                            signature: item.signature,
                                        });
                                    }
                                    return item;
                                }),
                                linuxProcesses: processPolicy.linuxProcesses,
                            });

                            setProcessPolicy(newProcessPolicy);
                        }}
                    /></Col>
                    <Col span={4}>
                        <Space>
                            <Select style={{ width: 80 }}
                                value={item.riskLevel}
                                onChange={(val) => {
                                    let newProcessPolicy = new ProcessPolicy({
                                        windowsProcesses: processPolicy.windowsProcesses,
                                        macProcesses: processPolicy.macProcesses.map((item, i) => {
                                            if (i === index) {
                                                return new ProcessPolicyItem({
                                                    os: OS.MACOS,
                                                    riskLevel: val as any,
                                                    name: item.name,
                                                    signature: item.signature,
                                                });
                                            }
                                            return item;
                                        }),
                                        linuxProcesses: processPolicy.linuxProcesses,
                                    });
                                    setProcessPolicy(newProcessPolicy);
                                }}
                            >
                                <Select.Option value={RiskLevel.LOW}>低危</Select.Option>
                                <Select.Option value={RiskLevel.MEDIUM}>中危</Select.Option>
                                <Select.Option value={RiskLevel.HIGH}>高危</Select.Option>

                            </Select>
                            <Button type='danger'
                                onClick={() => {
                                    // 删除一行
                                    let newProcessPolicy = new ProcessPolicy({
                                        windowsProcesses: processPolicy.windowsProcesses,
                                        macProcesses: processPolicy.macProcesses.filter((item, i) => i !== index),
                                        linuxProcesses: processPolicy.linuxProcesses,
                                    });

                                    setProcessPolicy(newProcessPolicy);

                                }}
                                icon={<IconMinusCircle />}></Button>
                        </Space>
                    </Col>
                </Row>
            })
        }

        <Dropdown content={
            <Dropdown.Menu>
                <Dropdown.Item onClick={() => {
                    // 添加一行
                    let newProcessPolicy = new ProcessPolicy({
                        windowsProcesses: processPolicy.windowsProcesses,
                        macProcesses: processPolicy.macProcesses.concat(new ProcessPolicyItem({
                            os: OS.MACOS,
                            riskLevel: RiskLevel.HIGH,
                            name: '',
                            signature: '',

                        })),
                        linuxProcesses: processPolicy.linuxProcesses,
                    });

                    setProcessPolicy(newProcessPolicy);
                }}>自定义</Dropdown.Item>
                {processChecks.map((item, index) => {
                    if (item.os !== OS.MACOS) {
                        return null;
                    }
                    return <Dropdown.Item key={index} onClick={() => {
                        // 添加一行
                        let newProcessPolicy = new ProcessPolicy({
                            windowsProcesses: processPolicy.windowsProcesses,
                            macProcesses: processPolicy.macProcesses.concat(new ProcessPolicyItem({
                                os: OS.MACOS,
                                riskLevel: RiskLevel.HIGH,
                                name: item.name,
                                signature: item.signature,
                            })),
                            linuxProcesses: processPolicy.linuxProcesses,
                        });
                        setProcessPolicy(newProcessPolicy);
                    }}>

                        <Space style={{ alignContent: 'center' }}>
                            <Text size='normal'>{item.name}</Text>
                            <Text size='small' type='tertiary'>{item.description}</Text>
                        </Space>
                    </Dropdown.Item>
                })}
            </Dropdown.Menu>
        }>
            <Button className='mb20' type='primary' icon={<IconPlus />}>添加</Button>
        </Dropdown>

        <Divider className='mb20' />
        <Title heading={6} className='mb10'>Linux</Title>
        <Row className={styles.tableTitle} style={{ marginBottom: 12 }}>
            <Col span={12}>文件数字签名</Col>
            <Col span={8}>进程名称</Col>
            <Col span={4}>风险等级</Col>
        </Row>
        {
            processPolicy.linuxProcesses.map((item, index) => {
                return <Row key={index} className={styles.tableBody}><Col span={12}><Input
                    value={item.signature}
                    onChange={(val) => {
                        let newProcessPolicy = new ProcessPolicy({
                            windowsProcesses: processPolicy.windowsProcesses,
                            macProcesses: processPolicy.macProcesses,
                            linuxProcesses: processPolicy.linuxProcesses.map((item, i) => {
                                if (i === index) {
                                    return new ProcessPolicyItem({
                                        os: OS.LINUX,
                                        riskLevel: item.riskLevel,
                                        name: item.name,
                                        signature: val,
                                    });
                                }
                                return item;
                            }),
                        });
                        setProcessPolicy(newProcessPolicy);
                    }}
                /></Col>
                    <Col span={8}><Input
                        value={item.name}
                        onChange={(val) => {
                            let newProcessPolicy = new ProcessPolicy({
                                windowsProcesses: processPolicy.windowsProcesses,
                                macProcesses: processPolicy.macProcesses,
                                linuxProcesses: processPolicy.linuxProcesses.map((item, i) => {
                                    if (i === index) {
                                        return new ProcessPolicyItem({
                                            os: OS.LINUX,
                                            riskLevel: item.riskLevel,
                                            name: val,
                                            signature: item.signature,
                                        });
                                    }
                                    return item;
                                }),
                            });
                            setProcessPolicy(newProcessPolicy);
                        }}
                    /></Col>
                    <Col span={4}>
                        <Space>
                            <Select style={{ width: 80 }}
                                value={item.riskLevel}
                                onChange={(val) => {
                                    let newProcessPolicy = new ProcessPolicy({
                                        windowsProcesses: processPolicy.windowsProcesses,
                                        macProcesses: processPolicy.macProcesses,
                                        linuxProcesses: processPolicy.linuxProcesses.map((item, i) => {
                                            if (i === index) {
                                                return new ProcessPolicyItem({
                                                    os: OS.LINUX,
                                                    riskLevel: val as any,
                                                    name: item.name,
                                                    signature: item.signature,
                                                });
                                            }
                                            return item;
                                        }),
                                    });
                                    setProcessPolicy(newProcessPolicy);
                                }}
                            >
                                <Select.Option value={RiskLevel.LOW}>低危</Select.Option>
                                <Select.Option value={RiskLevel.MEDIUM}>中危</Select.Option>
                                <Select.Option value={RiskLevel.HIGH}>高危</Select.Option>

                            </Select>
                            <Button type='danger' onClick={() => {
                                // 删除一行
                                let newProcessPolicy = new ProcessPolicy({
                                    windowsProcesses: processPolicy.windowsProcesses,
                                    macProcesses: processPolicy.macProcesses,
                                    linuxProcesses: processPolicy.linuxProcesses.filter((item, i) => i !== index),
                                });

                                setProcessPolicy(newProcessPolicy);
                            }} icon={<IconMinusCircle />}></Button>
                        </Space>
                    </Col>
                </Row>
            })
        }

        <Dropdown content={
            <Dropdown.Menu>
                <Dropdown.Item onClick={() => {
                    // 添加一行
                    let newProcessPolicy = new ProcessPolicy({
                        windowsProcesses: processPolicy.windowsProcesses,
                        macProcesses: processPolicy.macProcesses,
                        linuxProcesses: processPolicy.linuxProcesses.concat(new ProcessPolicyItem({
                            os: OS.LINUX,
                            riskLevel: RiskLevel.HIGH,
                            name: '',
                            signature: '',

                        })),
                    });

                    setProcessPolicy(newProcessPolicy);
                }}>自定义</Dropdown.Item>
                {processChecks.map((item, index) => {
                    if (item.os !== OS.LINUX) {
                        return null;
                    }
                    return <Dropdown.Item key={index} onClick={() => {
                        // 添加一行
                        let newProcessPolicy = new ProcessPolicy({
                            windowsProcesses: processPolicy.windowsProcesses,
                            macProcesses: processPolicy.macProcesses,
                            linuxProcesses: processPolicy.linuxProcesses.concat(new ProcessPolicyItem({
                                os: OS.LINUX,
                                riskLevel: RiskLevel.HIGH,
                                name: item.name,
                                signature: item.signature,
                            })),
                        });

                        setProcessPolicy(newProcessPolicy);
                    }}>

                        <Space style={{ alignContent: 'center' }}>
                            <Text size='normal'>{item.name}</Text>
                            <Text size='small' type='tertiary'>{item.description}</Text>
                        </Space>
                    </Dropdown.Item>
                })}
            </Dropdown.Menu>
        }>
            <Button type='primary' icon={<IconPlus />}>添加</Button>
        </Dropdown>
    </Modal>
    </>
}

export default Index;