import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, Input, TabPane } from '@douyinfe/semi-ui';
import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: UserGroup
}
const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');
    return <><Modal
        width={500}
        title={formatMessage({ id: 'users.groupDel.title' })}
        visible={true}
        okButtonProps={{
            disabled: props.record.name !== confirmVal,
            loading,
            type: 'danger'
        }}
        onOk={() => {
            setLoading(true)

            flylayerClient.deleteUserGroup({
                groupId: props.record.id,
                flynetId: flynet?.id
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'users.groupDel.success' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch((err) => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'users.groupDel.failed' }), position: "bottomRight" })
            }).finally(() => setLoading(false))

        }}
        onCancel={() => props.close()}
        closeOnEsc={true}
        maskClosable={false}
    >
        <Paragraph className='mb20'> {formatMessage({ id: 'users.groupDel.content' }).replace('{name}', props.record.name)}
        </Paragraph>
        <Paragraph className='mb20'> {formatMessage({ id: 'users.groupDel.confirmText' }).replace('{name}', props.record.name)}
        </Paragraph>
        <Input value={confirmVal} onChange={(val) => setConfirmVal(val)}></Input>
    </Modal></>
}

export default Index;