import { FC, useState, useContext } from 'react'
import { Typography, Modal, List, Notification, TabPane, Input } from '@douyinfe/semi-ui';
import { User, UserGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
const { Paragraph, Title } = Typography;
import UserDevice from '@/components/user-device';

interface Props {
    close: () => void,
    success?: () => void,
    group: UserGroup,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');

    
    const handleSubmit = () => {
        setLoading(true);
        
        flylayerClient.removeUserFromGroup({
            userId: props.record.id,
            groupId: props.group.id,
            flynetId: flynetGeneral.id
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'users.removeFromGroup.success' }),
                content: formatMessage({ id: 'users.removeFromGroup.successMessage' })
                    .replace('{user}', `${props.record.displayName}(${props.record.loginName})`)
                    .replace('{group}', props.group.alias ? props.group.alias + `(${props.group.name})` : props.group.name)
            });
            props.close();
            props.success && props.success();
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'users.removeFromGroup.failed' }),
                content: formatMessage({ id: 'users.removeFromGroup.failedMessage' })
                    .replace('{user}', `${props.record.displayName}(${props.record.loginName})`)
                    .replace('{group}', props.group.alias ? props.group.alias + `(${props.group.name})` : props.group.name)
            });
        }).finally(() => {
            setLoading(false);
        });
        
    }

    return <>
        <Modal
            
            title={formatMessage({ id: 'users.removeFromGroup.title' })}
            visible={true}
            onOk={handleSubmit}
            
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'>
                {formatMessage({ id: 'users.removeFromGroup.confirm' })
                    .replace('{user}', `${props.record.displayName}(${props.record.loginName})`)
                    .replace('{group}', props.group.alias ? props.group.alias + `(${props.group.name})` : props.group.name)}
            </Paragraph>


            
        </Modal></>
}
export default Index;
