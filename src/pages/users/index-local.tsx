import React, { useState, useEffect, useContext } from 'react'
import { Typography, Table, Layout, Input, Space, Select, Tag, Spin, BackTop, Row, Col, Button, Tabs, TabPane, Popover, Dropdown } from '@douyinfe/semi-ui';
import { IconSearch, IconMore } from '@douyinfe/semi-icons';
import Del from './del';
import EditRdp from './edit-rdp';
import EditRole from './edit-role';
import Suspend from './suspend';
import AddUserToGroup from './add-user-to-group';
import Add from './add-direct';
import Password from './password';
import EditUserGroup from './edit-user-group';
import RemoveFromGroup from './remove-from-group';
import EditExpiresAt from './edit-expires-at';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import EditUser from './edit-user';
import { getQueryParam } from '@/utils/query';
import { User, UserGroup, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import InfiniteScroll from 'react-infinite-scroll-component';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { useLocale } from '@/locales';

import Unlock from './unlock';

import BatchAddGroup from './batch-add-group';
import BatchDel from './batch-del';
import BatchSuspend from './batch-suspend';
import BatchEditRole from './batch-edit-role';
import BatchEditRdp from './batch-edit-rdp';
import BatchEditExpiresAt from './batch-edit-expires-at';
import BatchEditMfa from './batch-edit-mfa';
import EditMfa from './edit-mfa';
import RemoveMFADevice from './remove-mfa-device';

import styles from './index.module.scss'
import useTable, { UserFilter } from './useTable-local';
import TableEmpty from '@/components/table-empty';
import qs from 'query-string';
import { BASE_PATH } from '@/constants/router';
import { LicenseContext } from '@/hooks/useLicense';
import { getRadioEntitlementVal } from '@/utils/common';
const { Title, Paragraph, Text } = Typography;
const { Sider, Content } = Layout;



// 根据URL参数设置过滤参数
const getUserFilter = (location: Location): UserFilter => {
    const keywords: string = getQueryParam('keywords', location) as string;
    const status: string = getQueryParam('status', location) as string;
    const roleQuery: string = getQueryParam('role', location) as string;
    const group: string = getQueryParam('group', location) as string;
    const temporary: string = getQueryParam('temporary', location) as string;

    let role: string[] = [];
    if (roleQuery && Array.isArray(roleQuery)) {
        role = roleQuery as string[];
    }
    if (roleQuery && typeof roleQuery == 'string') {
        role = [roleQuery as string];
    }
    return {
        keywords: keywords || '',
        status: status == 'enable' || status == 'disable' ? status : '',
        role: role || [],
        group: group || '',
        temporary: temporary == 'true' || temporary == 'false' ? temporary : ''
    }

}
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const userProfile = useContext(UserProfileContext);
    const location = useLocation();
    const navigate = useNavigate();

    const license = useContext(LicenseContext);
    const entitlementAllowImportExport = getRadioEntitlementVal('import_export', license.entitlements);
    // 过滤参数
    const initFilter: UserFilter = getUserFilter(location);
    // 过滤参数改变时跳转路由
    const doNavigate = (param: UserFilter) => {
        let query = '';
        if (param.temporary || param.keywords || param.status || param.role.length > 0 || param.group.length > 0) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/users?${query}`)
        } else {
            navigate(`${BASE_PATH}/users`)
        }
    }

    // 查询参数
    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        // 查询参数从有值变化为无值时，重新加载数据
        if (location.search == '' && search != '') {
            setFilterParam(initFilter);
        }
        setSearch(location.search);
    }, [location])

    const { columns, loading, data, allData, curUser, setCurUser,
        onRowChange, selectedRowKeys, setSelectedRowKeys, selectedUsers, setSelectedUsers, handleRowClick,
        editRDPVisible, setEditRDPVisible, addVisible, setAddVisible, delVisible, setDelVisible, suspendVisible, setSuspendVisible, editRoleVisible, setEditRoleVisible, setReloadFlag, filterParam, setFilterParam, page, setPage, pageSize, addPage, total, handleSort, addGroupVisible, setAddGroupVisible, flynet,
        password, setPassword,
        passwordVisible, setPasswordVisible,
        expires_at_visible, setExpiresAtVisible,
        editVisible, setEditVisible,
        groups,
        curGroup,
        setCurGroup,
        handleGroupChange,
        otherUsersCount,
        editGroupVisible,
        setEditGroupVisible,
        removeUserFromGroupVisible,
        setRemoveUserFromGroupVisible,
        mfaVisible,
        setMfaVisible,
        removeMFADeviceVisible,
        setRemoveMFADeviceVisible,
        handleExport,
        unlockVisible,
        setUnlockVisible,
    } = useTable(initFilter);

    const getBatchUserRole = (users: User[]): UserRole | undefined => {
        let role: UserRole | undefined;
        if (users.length > 0) {
            role = users[0].role
            for (let i = 1; i < users.length; i++) {
                if (role != users[i].role) {
                    return undefined;
                }
            }
        }
        return role;
    }

    const listStatus = [
        { value: '', label: formatMessage({ id: 'common.all' }) },
        { value: 'enable', label: formatMessage({ id: 'users.status.enabled' }) },
        { value: 'disable', label: formatMessage({ id: 'users.status.disabled' }) }
    ];
    const listTemporary = [
        { value: '', label: formatMessage({ id: 'common.all' }) },
        { value: 'true', label: formatMessage({ id: 'users.temporary.yes' }) },
        { value: 'false', label: formatMessage({ id: 'users.temporary.no' }) }
    ];
    const listRole = [
        { value: '8', label: formatMessage({ id: 'users.role.user' }) },
        { value: '3', label: formatMessage({ id: 'users.role.admin' }) }
    ];

    const handleStatusChange = (value: any) => {
        setFilterParam({ ...filterParam, status: value })
        doNavigate({ ...filterParam, status: value })
    }
    const handleRoleChange = (value: any) => {
        setFilterParam({ ...filterParam, role: value })
        doNavigate({ ...filterParam, role: value })
    }
    const handleTemporaryChange = (value: any) => {
        setFilterParam({ ...filterParam, temporary: value })
        doNavigate({ ...filterParam, temporary: value })
    }

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, keywords: value })
        doNavigate({ ...filterParam, keywords: value })
    }

    const [batchAddGroupVisible, setBatchAddGroupVisible] = useState(false);
    const [batchDelVisible, setBatchDelVisible] = useState(false);
    const [batchSuspendVisible, setBatchSuspendVisible] = useState(false);
    const [batchSuspendDisable, setBatchSuspendDisable] = useState(false);
    const [batchEditRoleVisible, setBatchEditRoleVisible] = useState(false);
    const [batchEditRDPVisible, setBatchEditRDPVisible] = useState(false);
    const [batchExpiresAtVisible, setBatchExpiresAtVisible] = useState(false);

    const [batchEnableUsers, setBatchEnableUsers] = useState<User[]>();
    const [batchDisableUsers, setBatchDisableUsers] = useState<User[]>();
    const [batchDelUsers, setBatchDelUsers] = useState<User[]>();

    const [batchMfaVisible, setBatchMfaVisible] = useState(false);


    useEffect(() => {
        if (selectedUsers && selectedUsers.length > 0) {
            let enableUsers: User[] = [];
            let disableUsers: User[] = [];
            let delUsers: User[] = [];
            selectedUsers.forEach(user => {
                // 是否是自己
                const isSelf = user.loginName == userProfile.identity?.traits?.email;
                if (!isSelf) {
                    if (user.disabled) {
                        enableUsers.push(user);
                    } else {
                        disableUsers.push(user);
                    }
                    delUsers.push(user);
                }
            })
            setBatchEnableUsers(enableUsers);
            setBatchDisableUsers(disableUsers);
            setBatchDelUsers(delUsers);
        } else {
            setBatchEnableUsers([]);
            setBatchDisableUsers([]);
            setBatchDelUsers([]);
        }
    }, [selectedUsers]);


    return <>
        <div className='general-page'>
            <Row className='mb10'>
                <Col span={20}>
                    <Title heading={3}>{formatMessage({ id: 'users.index.titleLocalPage' })}</Title>
                </Col>
                <Col span={4}>
                    <div className='btn-right-col'>
                        <Space>


                            {entitlementAllowImportExport && <><Button
                                onClick={() => navigate(`${BASE_PATH}/users/export/`)}
                            >{formatMessage({ id: 'users.dataExport.title' })}</Button>
                                <Button
                                    onClick={() => navigate(`${BASE_PATH}/users/import/`)}
                                >{formatMessage({ id: 'dataImport.title' })}</Button></>}

                            <Button
                                onClick={() => navigate(`${BASE_PATH}/users/group/`)}>{formatMessage({ id: 'users.breadcrumb.userGroups' })}</Button>
                            {flynet && flynet.accountManualCreate && <Button theme='solid'
                                onClick={() => setAddVisible(true)}>{formatMessage({ id: 'users.add.title' })}</Button>
                            }
                        </Space>
                    </div>
                </Col>
            </Row>
            <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'users.index.descriptionLocal' })}</Paragraph>

            <Layout className='mb20 search-bar'>

                <Layout>
                    <Content className='pr10'>
                        <Input value={filterParam.keywords}
                            onChange={handleQueryChange} style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={formatMessage({ id: 'users.search.placeholder' })}></Input>
                    </Content>
                    <Sider>
                        <Space>
                            <Select
                                value={filterParam.status}
                                onChange={handleStatusChange}
                                style={{ width: 220 }} optionList={listStatus} insetLabel={formatMessage({ id: 'users.filter.status' })} ></Select>
                            <Select value={filterParam.role} onChange={handleRoleChange} multiple maxTagCount={1} style={{ width: 220 }} optionList={listRole} insetLabel={formatMessage({ id: 'users.filter.role' })}></Select>

                            <Select
                                value={filterParam.temporary}
                                onChange={handleTemporaryChange}
                                style={{ width: 220 }} optionList={listTemporary} insetLabel={formatMessage({ id: 'users.filter.type' })} ></Select>
                        </Space></Sider>
                </Layout>

            </Layout>
            <Tabs
                type="card"
                collapsible
                onChange={(activeKey: string) => {
                    let activeGroup: UserGroup | undefined = undefined;
                    if (activeKey) {
                        groups.forEach(group => {
                            if (group.id + '' === activeKey) {
                                setCurGroup(group)
                                activeGroup = group;
                            }
                        })
                    } else {
                        setCurGroup(undefined)
                    }
                    handleGroupChange(activeGroup);

                    setFilterParam({ ...filterParam, group: activeKey })
                    doNavigate({ ...filterParam, group: activeKey })
                }}
                activeKey={curGroup ? curGroup.id + '' : ''}
            >
                <TabPane tab={formatMessage({ id: 'users.local.allUsers' })} itemKey=''></TabPane>
                {groups.map((group, index) => {
                    return <TabPane key={index}
                        tab={group.alias ?
                            <Popover position='top' content={<div className='p10'>{group.description ? group.description : group.name}</div>}>{group.alias}</Popover>
                            : <span>{group.name}</span>}
                        itemKey={group.id + ''}></TabPane>
                })}
            </Tabs>
            <div className='mb10'>
                <Row>
                    <Col span={12} style={{ height: 32, lineHeight: '32px' }}>
                        <Space>
                            {!loading && <Tag size='large'>{formatMessage({ id: 'users.local.totalUsers' }).replace('{count}', total.toString())}</Tag>}
                            {selectedUsers && selectedUsers.length > 0 && <Tag size='large'>{formatMessage({ id: 'users.local.selectedUsers' }).replace('{count}', selectedUsers.length.toString())}</Tag>}
                        </Space>
                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>
                                {otherUsersCount > 0 && <Text type='tertiary' size='small'>{formatMessage({ id: 'users.local.notJoinedUsers' }).replace('{count}', otherUsersCount.toString())}</Text>}
                                <Button onClick={() => setBatchAddGroupVisible(true)} disabled={!selectedUsers || selectedUsers.length == 0}>{formatMessage({ id: 'users.local.batchAddToGroup' })}</Button>
                                <Button onClick={() => setBatchDelVisible(true)} disabled={!batchDelUsers || batchDelUsers.length == 0}>{formatMessage({ id: 'users.local.batchDelete' })}</Button>
                                <Dropdown position='bottomRight'
                                    render={<Dropdown.Menu>
                                        <Dropdown.Item onClick={() => {
                                            setBatchSuspendVisible(true)
                                            setBatchSuspendDisable(true)
                                        }} disabled={!batchDisableUsers || batchDisableUsers.length == 0}>{formatMessage({ id: 'users.local.batchDisable' })}</Dropdown.Item>
                                        <Dropdown.Item onClick={() => {
                                            setBatchSuspendVisible(true)
                                            setBatchSuspendDisable(false)
                                        }} disabled={!batchEnableUsers || batchEnableUsers.length == 0}>{formatMessage({ id: 'users.local.batchEnable' })}</Dropdown.Item>
                                        <Dropdown.Divider />
                                        <Dropdown.Item onClick={() => setBatchEditRoleVisible(true)} disabled={!selectedUsers || selectedUsers.length == 0}>{formatMessage({ id: 'users.local.batchEditRole' })}</Dropdown.Item>
                                        <Dropdown.Divider />


                                        <Dropdown.Item onClick={() => setBatchExpiresAtVisible(true)} disabled={!selectedUsers || selectedUsers.length == 0}>{formatMessage({ id: 'users.local.batchSetExpiration' })}</Dropdown.Item>
                                    </Dropdown.Menu>}
                                ><Button><IconMore className='align-v-center' /></Button></Dropdown>


                            </Space>
                        </div>
                    </Col>
                </Row>


            </div>
            <InfiniteScroll
                dataLength={data.length} //This is important field to render the next data
                next={addPage}

                hasMore={data.length < total}
                loader={<div><Spin></Spin></div>}
                endMessage={
                    <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                        {data.length > pageSize && <Paragraph type='tertiary'>{formatMessage({ id: 'users.local.endOfList' })}</Paragraph>}
                    </div>
                }
            >
                <Table
                    rowKey={(record?: User) => record ? record.id + '' : ''}
                    rowSelection={{
                        onChange: onRowChange,
                        selectedRowKeys: selectedRowKeys,
                    }}
                    onRow={(record: any, index: any) => {
                        return {
                            onClick: () => {
                                if (record && index != undefined) {
                                    handleRowClick(record, index)
                                }

                            }
                        }
                    }}
                    onChange={handleSort}
                    empty={<TableEmpty loading={loading} />} columns={columns} loading={loading} dataSource={data} pagination={false} />
            </InfiniteScroll>
            <BackTop style={{ right: 10 }} />

        </div>
        {addVisible ?
            <Add success={(password: string) => {
                setAddVisible(false)
                setReloadFlag(true)
                setPasswordVisible(true)
                setPassword(password)
            }} close={() => { setAddVisible(false) }} /> : null}
        {passwordVisible && <Password keyValue={password} close={() => { setPasswordVisible(false), setPassword('') }} />}
        {delVisible && curUser ?
            <Del record={curUser} success={() => {
                setDelVisible(false)
                setCurUser(undefined)
                setReloadFlag(true)
            }} close={() => { setDelVisible(false); setCurUser(undefined) }} /> : null}
        {editRoleVisible && curUser ?
            <EditRole record={curUser} success={() => {
                setEditRoleVisible(false)
                setCurUser(undefined)
                setReloadFlag(true)
            }} close={() => {
                setEditRoleVisible(false)
                setCurUser(undefined)
            }} /> : null}
        {suspendVisible && curUser ?
            <Suspend record={curUser}
                close={() => { setSuspendVisible(false) }}
                success={() => {
                    setSuspendVisible(false)
                    setReloadFlag(true)
                }}
            /> : null}
        {addGroupVisible && curUser && <AddUserToGroup
            close={() => {
                setAddGroupVisible(false)
                setCurUser(undefined)
            }}
            record={curUser}></AddUserToGroup>}
        {editRDPVisible && curUser && <EditRdp
            close={() => {
                setEditRDPVisible(false)
                setCurUser(undefined)
            }}
            record={curUser}
            success={() => {
                setEditRDPVisible(false)
                setReloadFlag(true)
            }}
        ></EditRdp>}
        {curUser && editGroupVisible && <EditUserGroup
            close={() => {
                setCurUser(undefined)
                setEditGroupVisible(false)
            }}
            record={curUser}
            success={() => {
                setEditGroupVisible(false)
                setReloadFlag(true)
            }}
        ></EditUserGroup>
        }
        {curUser && curGroup && removeUserFromGroupVisible && <RemoveFromGroup

            close={() => {
                setCurUser(undefined)
                setRemoveUserFromGroupVisible(false)
            }}
            record={curUser}
            group={curGroup}
            success={() => {
                setRemoveUserFromGroupVisible(false)
                setReloadFlag(true)
            }}
        ></RemoveFromGroup>}
        {
            curUser && expires_at_visible && <EditExpiresAt
                close={() => {
                    setCurUser(undefined)
                    setExpiresAtVisible(false)
                }}
                record={curUser}
                success={() => {
                    setExpiresAtVisible(false)
                    setReloadFlag(true)
                }}
            ></EditExpiresAt>
        }
        {editVisible && curUser && <EditUser
            close={() => {
                setCurUser(undefined)
                setEditVisible(false)
            }}
            record={curUser}
            success={() => {
                setEditVisible(false)
                setReloadFlag(true)
            }}
        ></EditUser>}
        {batchAddGroupVisible && <BatchAddGroup
            close={() => {
                setBatchAddGroupVisible(false)
            }}
            success={() => {
                setBatchAddGroupVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }}
            records={selectedUsers}
        ></BatchAddGroup>}
        {batchDelVisible && <BatchDel
            close={() => {
                setBatchDelVisible(false)
            }}
            success={() => {
                setBatchDelVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setBatchDelUsers([])
                setSelectedRowKeys([])
            }}
            records={batchDelUsers || []}></BatchDel>}
        {batchSuspendVisible && <BatchSuspend
            close={() => {
                setBatchSuspendVisible(false)
            }
            } success={() => {
                setBatchSuspendVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }
            } records={batchSuspendDisable ? batchDisableUsers || [] : batchEnableUsers || []} disabled={batchSuspendDisable}></BatchSuspend>}
        {batchEditRoleVisible && <BatchEditRole
            close={() => {
                setBatchEditRoleVisible(false)
            }}
            success={() => {
                setBatchEditRoleVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }}
            role={getBatchUserRole(selectedUsers)}
            records={selectedUsers}></BatchEditRole>}
        {batchEditRDPVisible && <BatchEditRdp
            close={() => {
                setBatchEditRDPVisible(false)
            }}
            success={() => {
                setBatchEditRDPVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }}
            records={selectedUsers}></BatchEditRdp>}
        {batchExpiresAtVisible && <BatchEditExpiresAt
            close={() => {
                setBatchExpiresAtVisible(false)
            }}
            success={() => {
                setBatchExpiresAtVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])

            }}
            records={selectedUsers}></BatchEditExpiresAt>}
        {mfaVisible && curUser && <EditMfa
            close={() => {
                setCurUser(undefined)
                setMfaVisible(false)
            }
            } record={curUser}
            success={() => {
                setMfaVisible(false)
                setReloadFlag(true)
            }
            }></EditMfa>}
        {batchMfaVisible && <BatchEditMfa
            close={() => {
                setBatchMfaVisible(false)
            }
            } success={() => {
                setBatchMfaVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }
            } records={selectedUsers}></BatchEditMfa>}
        {removeMFADeviceVisible && curUser && <RemoveMFADevice
            close={() => {
                setCurUser(undefined)
                setRemoveMFADeviceVisible(false)
            }
            } record={curUser}
            success={() => {
                setRemoveMFADeviceVisible(false)
                setReloadFlag(true)
            }
            }></RemoveMFADevice>}
        {unlockVisible && curUser && <Unlock
            record={curUser}
            close={() => {
                setCurUser(undefined)
                setUnlockVisible(false)
            }}
            success={() => {
                setCurUser(undefined)
                setUnlockVisible(false)
                setReloadFlag(true)
            }}
        ></Unlock>
        }


    </>
}

export default Index;
