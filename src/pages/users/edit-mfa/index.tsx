import { FC, useState } from 'react'
import { Typography, Modal, Form, Space, Notification } from '@douyinfe/semi-ui';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [mfaLoading, setMfaLoading] = useState(false)
    const [mfaFormApi, SetMfaFormApi] = useState<FormApi<{
        mfaEnabled: boolean,
    }>>()

    let mfaEnabled = false;
    if (props.record.account && props.record.account.attrsJson) {
        const attrsJson = JSON.parse(props.record.account.attrsJson);
        if (attrsJson) {
            if (attrsJson['required_aal'] === 'aal2') {
                mfaEnabled = true
            }
        }
    }




    const handleMfaSubmit = () => {
        const values = mfaFormApi?.getValues();
        if (values) {
            setMfaLoading(true)
            flylayerClient.saveUserMFASettings({
                userId: props.record.id,
                mfaEnabled: values.mfaEnabled,
            }).then(() => {
                setMfaLoading(false)
                Notification.success({ content: formatMessage({ id: 'users.mfaEdit.success' }), position: "bottomRight" })
                props.close();
                if (props.success) {
                    props.success();
                }
            }).catch(err => {
                Notification.error({ content: formatMessage({ id: 'users.mfaEdit.failed' }), position: "bottomRight" })
                console.error(err);
                setMfaLoading(false)
            })
        }
    }

    return <>
        <Modal
            width={560}
            title={formatMessage({ id: 'users.mfaEdit.title' })}
            visible={true}
            okButtonProps={{ loading: mfaLoading }}
            onOk={handleMfaSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'users.editMfa.description' }).replace('{displayName}', props.record.displayName).replace('{loginName}', props.record.loginName)}</Paragraph>
            <Form getFormApi={SetMfaFormApi}
                initValues={{
                    mfaEnabled
                }}
            >{({ formState, values, formApi }) => (<>
                <Form.Switch style={{ marginRight: 40 }} labelWidth={200} labelPosition='left' label={formatMessage({ id: 'users.editMfa.forceMfaEnabled' })} field='mfaEnabled' />
            </>)}

            </Form>
        </Modal>
    </>
}

export default Index;