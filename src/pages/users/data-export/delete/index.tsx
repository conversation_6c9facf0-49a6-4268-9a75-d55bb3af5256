import { FC, useState } from 'react'
import { Typography, Modal, List, Notification, TabPane, Input } from '@douyinfe/semi-ui';
import { ExportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
const { Paragraph, Title } = Typography;
import UserDevice from '@/components/user-device';

interface Props {
    close: () => void,
    success?: () => void,
    record: ExportRecord
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');

    
    const handleSubmit = () => {
            flylayerClient.deleteExportRecord({
                id: props.record.id
            }).then(()=>{
                Notification.success({ content: formatMessage({ id: 'users.dataExport.deleteSuccess' }), position: "bottomRight" })
                if(props.success) {
                    props.success();
                }
            }).catch(err => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'users.dataExport.deleteFailed' }), position: "bottomRight" })
            })
        
    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'users.dataExport.deleteTitle' }).replace('{fileName}', props.record.fileName)}
            visible={true}
            onOk={handleSubmit}
            
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                disabled: props.record.fileName != confirmVal,
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >

            <Paragraph className='mb20'> {formatMessage({ id: 'users.dataExport.deleteConfirm' }).replace('{fileName}', props.record.fileName)}
            </Paragraph>
            <Input value={confirmVal} onChange={(val)=>setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
