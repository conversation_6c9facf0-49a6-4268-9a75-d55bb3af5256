import { useEffect, useState, useContext, useCallback } from 'react';
import { Typography, Notification } from '@douyinfe/semi-ui';
import { ExportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { flylayerClient } from '@/services/core';
import qs from 'query-string';
import { useNavigate } from 'react-router-dom';

import { BASE_PATH } from '@/constants/router';
import DateFormat from '@/components/date-format';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

import { debounce } from 'lodash';

const { Title, Text, Paragraph } = Typography;

export type RecordFilter = {
    keywords: string;
    actors: string[];
}

const useTable = (initFilter: RecordFilter) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();

    const [loading, setLoading] = useState(true);

    const [data, setData] = useState<ExportRecord[]>([]);

    const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');

    const [reloadFlag, setReloadFlag] = useState(false);

    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);

    const pageSize = 10;


    const [delVisible, setDelVisible] = useState(false);

    const [curRecord, setCurRecord] = useState<ExportRecord>();

    const [filter, setFilter] = useState<RecordFilter>(initFilter);

    const doNavigate = (param: RecordFilter) => {
        let query = '';
        if (param.keywords || (param.actors && param.actors.length > 0)) {
            const newParam = {
                keywords: param.keywords || '',
                actors: param.actors || []
            }
            query = qs.stringify(newParam, { skipEmptyString: true});
        }
        
        if (query) {
            navigate(`${BASE_PATH}/users/export?${query}`);
        } else {
            navigate(`${BASE_PATH}/users/export`);
        }
    }

    const columns = [
        {
            width: 400,
            title: formatMessage({ id: 'users.dataExport.fileName' }),
            dataIndex: 'file_name',
            sorter: true,
            render: (_: string, record: ExportRecord) => {
                

                return <>
                    <Title heading={6}>{record.fileName}</Title>
                    <a className='link-external' href={record.fileUrl} target='_blank'>{formatMessage({ id: 'components.common.download' })}</a>
                </>;
            }
        },

        {
            width: 200,
            title: formatMessage({ id: 'users.dataExport.operator' }),
            dataIndex: 'user_id',
            sorter: true,
            render: (_: any, record: ExportRecord) => {
                return record.user ? <>
                    <Paragraph>{record.user.displayName}</Paragraph>
                    <Text type='tertiary' style={{ fontSize: '12px' }}>
                        {record.user.loginName}</Text>
                </> : <Text type='tertiary'>{formatMessage({ id: 'common.unknown' })}</Text>;
            }
        },
        {
            width: 160,
            title: formatMessage({ id: 'users.dataExport.recordCount' }),
            dataIndex: 'total_record',
            sorter: true,
            render: (_: any, record: ExportRecord) => {
                return <Text type='tertiary'>{record.totalRecord}</Text>;
            }
        }, {

            width: 200,
            title: formatMessage({ id: 'users.dataExport.exportTime' }),
            dataIndex: 'created_at',
            sorter: true,
            render: (_: any, record: ExportRecord) => {
                return <DateFormat date={record.createdAt} />;
            }
        },
        {
            title: formatMessage({ id: 'users.dataExport.exportParams' }),
            dataIndex: 'filter_param',
            sorter: true,
            render: (_: any, record: ExportRecord) => {
                const filterParam = record.filterParam;
                if (!filterParam || Object.keys(filterParam).length === 0) {
                    return <Text type='tertiary'>{formatMessage({ id: 'components.common.none' })}</Text>;
                }
                return <Paragraph>
                    {record.filterParam}
                </Paragraph>;
            }
            
        },
    ];

    const queryRecord = (params?: {
        filter?: RecordFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
    }) => {
        let queryArray = [`record_name=User`];

        let limit = pageSize;
        let curPage = page;

        let filterKeywords = filter.keywords;
        let filterActors = filter.actors;
        let filterSortOrder = sortOrder;
        let filterSortField = sortField;

        if (params) {
            if (params.filter) {
                setFilter(params.filter);
                filterKeywords = params.filter.keywords;
                filterActors = params.filter.actors || [];
            }

            if (params.sortOrder && params.sortField) {
                setSortOrder(params.sortOrder as any);
                setSortField(params.sortField);

                filterSortOrder = params.sortOrder as any;
                filterSortField = params.sortField;
            } else {
                filterSortOrder = undefined;
                filterSortField = '';
                setSortOrder(undefined);
                setSortField('');
            }

            if (params.page) {
                curPage = params.page;
                setPage(params.page);
            }

            if (params.pageSize) {
                limit = params.pageSize;
            }
        }


        if (filterKeywords != "" && filterKeywords.trim() != "") {
            queryArray.push(`keywords=${encodeURIComponent(filterKeywords)}`);
        }
        if (filterActors && filterActors.length > 0) {
            queryArray.push(`user_ids=${encodeURIComponent(filterActors.join(','))}`);
        }


        if (filterSortOrder && filterSortField) {
            setSortOrder(filterSortOrder as any);
            setSortField(filterSortField);

            let order_by = encodeURIComponent(`${filterSortField} ${filterSortOrder}`);
            queryArray.push(`order_by=${order_by}`);
        }

        const offset = (curPage - 1) * limit;

        queryArray.push(`limit=${limit}`);
        queryArray.push(`offset=${offset}`);

        setLoading(true);
        flylayerClient.listExportRecord({
            flynetId: flynetGeneral.id,
            query: queryArray.join('&')
        }).then((res) => {

            setData(res.records);
            setTotal(Number(res.total));
        }).catch(err => {
            Notification.error({ content: formatMessage({ id: 'users.dataExport.fetchListFailed' }) })
            console.error(err)
        }).finally(() => {
            setLoading(false);
        })
    }

    const addPage = () => {
        queryRecord({
            page: page + 1,
            sortOrder: sortOrder,
            sortField: sortField,
        });
    }



    useEffect(() => {
        queryRecord();
    }, []);

    useEffect(() => {
        setReloadFlag(false);
        if (reloadFlag) {
            queryRecord({
                sortOrder: sortOrder,
                sortField: sortField,
                page: 1,
            });
        }
    }, [reloadFlag]);

    const debounceQuery = useCallback(debounce((filter) => queryRecord(filter), 500), []);
    const handleFilterChange = (value: RecordFilter) => {
        doNavigate(value);
        debounceQuery({
            filter: value,
            sortOrder: sortOrder,
            sortField: sortField,
            pageSize: pageSize,
            page: 1,
        });
    }

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;

        let sortOrder = '';
        if (sorter.sortOrder == 'ascend') {
            sortOrder = 'ASC';
        } else if (sorter.sortOrder == 'descend') {
            sortOrder = 'DESC';
        }
        queryRecord({
            page: 1,
            sortOrder: sortOrder,
            sortField: dataIndex,
        })

    }

    return {
        loading,
        data,
        total,
        page,
        setPage,
        pageSize,
        columns,
        sortOrder,
        setSortOrder,
        sortField,
        setSortField,
        queryRecord,
        addPage,
        delVisible,
        setDelVisible,
        curRecord,
        setCurRecord,
        filter,
        setFilter,
        doNavigate,
        handleFilterChange,
        handleSort,
        reloadFlag,
        setReloadFlag,
    }
}

export default useTable;

