import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, List } from '@douyinfe/semi-ui';

import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
import { Link } from 'react-router-dom';
const { Paragraph, Title } = Typography;
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import UserDevice from '@/components/user-device';

interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [loading, setLoading] = useState(false);
    const handleOK = () => {
//   headers:  [["X-FlYNET-ID", flynetGeneral.id.toString()]]
        flylayerClient.setUserState({
            userId: props.record.id,
            disabled: !props.record.disabled
        }).then(()=>{
            Notification.success({ content: props.record.disabled ? formatMessage({ id: 'users.suspend.enableSuccess' }) : formatMessage({ id: 'users.suspend.disableSuccess' }), position: "bottomRight" })


            if(props.success) {
                props.success();
            }

        }).catch(err => {
            if(err.rawMessage) {
                Notification.error({ content: `${props.record.disabled ? formatMessage({ id: 'users.suspend.enableFailed' }) : formatMessage({ id: 'users.suspend.disableFailed' })}：${err.rawMessage}`, position: "bottomRight" })
            } else {
                Notification.error({ content: props.record.disabled ? formatMessage({ id: 'users.suspend.enableFailed' }) : formatMessage({ id: 'users.suspend.disableFailed' }), position: "bottomRight" })
            }
            

        })
       
        
    }
    let optionName = '';
    if (props.record.disabled) {
        optionName = formatMessage({ id: 'users.suspend.enable' })
    } else {
        optionName = formatMessage({ id: 'users.suspend.disable' })
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'users.suspend.modalTitle' }).replace('{action}', optionName).replace('{displayName}', props.record.displayName).replace('{loginName}', props.record.loginName)}
            visible={true}
            okButtonProps={{ loading, type:'danger' }}
            onOk={handleOK}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>
                {formatMessage({ id: 'users.suspend.modalDescription' })
                    .replace('{action}', optionName)
                    .replace('{displayName}', props.record.displayName)
                    .replace('{loginName}', props.record.loginName)
                    .replace('{operation}', props.record.disabled ? formatMessage({ id: 'users.suspend.restore' }) : formatMessage({ id: 'users.suspend.suspend' }))}
            </Paragraph>
            <UserDevice record={props.record}></UserDevice>
        </Modal></>
}
export default Index;
