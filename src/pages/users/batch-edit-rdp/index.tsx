import { FC, useState, useContext } from 'react'
import { Typography, Modal, Form, Space, Notification } from '@douyinfe/semi-ui';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';
import UserList from '@/pages/users/components/user-list';
import { useLocale } from '@/locales';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    records: User[]
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [rdpLoading, setRdpLoading] = useState(false)
    const [rdpForm<PERSON>pi, SetRdpFormApi] = useState<FormApi<{
        rdpAutoGrant: boolean,
        rdpEnabled: boolean,
    }>>()

    const [users, setUsers] = useState<User[]>(props.records);

    const handleRdpSubmit = () => {
        const values = rdpFormApi?.getValues();
        if (values) {
            setRdpLoading(true)
            
            flylayerClient.batchSaveUserRdpSettings({
                flynetId: flynet.id,
                userIds: users.map((item) => {
                    return item.id;
                }),
                rdpEnabled: values.rdpEnabled,
                rdpAutoGrant: values.rdpAutoGrant
            }).then(() => {
                setRdpLoading(false)
                Notification.success({ content: formatMessage({ id: 'users.editRDP.success' }), position: "bottomRight" })
                props.close();
                if (props.success) {
                    props.success();
                }
            }).catch(err => {
                Notification.error({ title: formatMessage({ id: 'users.editRDP.failed' }),
                content: err.message
                , position: "bottomRight" })
                console.error(err);
                setRdpLoading(false)
            })
        }
    }

    return <>
        <Modal
            width={560}
            title={formatMessage({ id: 'users.index.batch.editRDP' })}
            visible={true}
            okButtonProps={{ loading: rdpLoading, disabled: users.length == 0}}
            onOk={handleRdpSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'users.editRDP.description' })}</Paragraph>
            <UserList className='mb20' users={users} onChange={(list)=>setUsers(list)} />
            <Form  getFormApi={SetRdpFormApi}
                initValues={{

                }}
            >{({ formState, values, formApi }) => (<>
                    <Form.Switch style={{marginRight:40}} labelWidth={100} labelPosition='left' label={formatMessage({ id: 'users.field.rdpEnabled' })} field='rdpEnabled' />
                    <Form.Switch labelPosition='left' label={formatMessage({ id: 'users.field.rdpAutoGrant' })} labelWidth={100}
                    extraTextPosition='middle' extraText={formatMessage({ id: 'users.field.rdpAutoGrant.description' })}
                    field='rdpAutoGrant' disabled={!values.rdpEnabled} />

            </>)}

            </Form>
        </Modal>
    </>
}

export default Index;