import { FC, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Banner, Row, Col, Button, Divider } from '@douyinfe/semi-ui';

import { formatDisplayTimestamp } from '@/utils/format';
import { useLocale } from '@/locales';

const { Paragraph } = Typography;


interface Props {
    keyValue: string
    close: () => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'users.passwordReset.title' })}
            visible={true}
            okButtonProps={{ loading }}
            onOk={() => props.close()}
            onCancel={() => props.close()}
            closeOnEsc={true}
            hasCancel={false}
            maskClosable={false}
        >
            <Paragraph type='danger' className='mb20'>{formatMessage({ id: 'users.password.copyInstruction' })} </Paragraph>
            <Paragraph className='mb20 copyable-code' copyable>{props.keyValue}</Paragraph>
             {/* <Banner
            type="info"
            closeIcon={null}
            description={formatMessage({ id: 'users.password.expiryWarning' })}
        />  */}
        </Modal>
    </>
}

export default Index;