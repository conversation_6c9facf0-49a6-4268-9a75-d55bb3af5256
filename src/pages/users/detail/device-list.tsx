import { FC, useEffect, useRef, useState, useContext } from 'react'
import { Typography, Tag, List, Badge, Popover, Table, Notification, Button, Dropdown } from '@douyinfe/semi-ui';
import { BASE_PATH } from '@/constants/router';
import { formatIPNVersion, formatDefaultTimestamp } from '@/utils/format';
import TableEmpty from '@/components/table-empty';

import { compare } from 'semver'
import { Timestamp } from "@bufbuild/protobuf";
import { useLocation, useNavigate } from 'react-router-dom';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
import DeviceTag from '@/components/device-tag';
import { setMachineKeyExpiry, listMachines, SaveMachineRdpSettings, listUserMachines } from '@/services/device';

import styles from './index.module.scss';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { IconArrowUpRight } from '@douyinfe/semi-icons';
const { Title, Paragraph, Text } = Typography;
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
const Index: FC<{
    loginName: string
    userId: bigint
}> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    const columns = [
        {
            width: 400,
            title: formatMessage({ id: 'users.detail.device' }),
            dataIndex: 'name',
            sorter: true,
            render: (field: string, record: Machine, index: number) => {
                // 设备名称
                const name = record.autoGeneratedName ? record.name : record.givenName
                return (
                    <div>
                        <Title heading={5}>
                            <a onClick={() => setTimeout(() => navigate(`${BASE_PATH}/devices/${record.ipv4}`), 10)}>{name}</a>&nbsp;
                            {record.connected ?
                                <span className='mobile-visible'><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /></span> :
                                <span className='mobile-visible'><Badge dot type='tertiary' /></span>}
                        </Title>
                        {record.tags && record.tags.length > 0 ? <div>
                            {record.tags.map((tag, index) => {
                                return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 5, marginBottom: 5 }}>{tag}</Tag>
                            })}
                        </div> : <Paragraph>{record.user?.displayName} <Text type="tertiary" className={styles.loginName} size='small' ellipsis={{ showTooltip: true }}>{record.user?.loginName}</Text></Paragraph>
                        }
                        <DeviceTag record={record}></DeviceTag>
                    </div>
                );
            },
        },
        {
            width: 230,
            title: 'IP',
            dataIndex: 'ipV4',
            sorter: true,
            render: (field: string, record: Machine) => {
                return (
                    <div className={styles.ipCopyable} style={{ display: 'flex', alignItems: 'center' }}>

                        <Popover content={<List
                            bordered>
                            {/* <List.Item>
                            <Paragraph copyable>{record.hostname}</Paragraph>
                        </List.Item> */}
                            {[record.ipv4, record.ipv6].map((val, index) => {
                                return <List.Item key={index}>
                                    <Paragraph copyable>
                                        {val}</Paragraph>
                                </List.Item>
                            })}

                        </List>
                        }>
                            <Text className={styles.ipLine}>{record.ipv4}</Text>
                        </Popover>
                        <Copyable style={{ lineHeight: 1 }} content={record.ipv4}></Copyable>
                    </div>

                );
            },
        },
        {
            width: 200,
            title: formatMessage({ id: 'users.detail.version' }),
            dataIndex: 'os',
            sorter: true,
            render: (field: string, record: Machine) => {

                return (
                    <div className='layout-left-icon'>
                        {/* <span>{false ? <Popover content={<div className='p10' style={{ width: 300 }}>
                            <Title heading={6}>{formatMessage({ id: 'users.detail.updateAvailable' })}</Title>
                            <Paragraph>{formatMessage({ id: 'users.detail.updateMessage' })}
                            </Paragraph>
                        </div>}><IconDownload style={{ marginTop: 2, color: 'var(--semi-color-text-2)' }} /></Popover> : ''}
                        </span> */}
                        <div>
                            <Paragraph>{formatIPNVersion(record.clientVersion)}</Paragraph>
                            <Paragraph>{field}</Paragraph>
                        </div>
                    </div>


                );
            },
        },
        {
            title: formatMessage({ id: 'users.detail.lastOnlineTime' }),
            dataIndex: 'lastSeen',
            sorter: true,
            render: (field: Timestamp, record: Machine) => {
                return record.connected ?
                    <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> {formatMessage({ id: 'users.detail.online' })}</span> :
                    <><span><Badge dot type='tertiary' /> {formatDefaultTimestamp(record.lastSeen)}</span></>

            }
        }
    ];

    const [originData, setOriginData] = useState<Array<Machine>>([]);
    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');
    // 设备列表
    const [devices, setDevices] = useState<Machine[]>([]);
    const query = () => {

        setLoading(true)
        listUserMachines(
            flynetGeneral.id, props.userId).then(res => {
                const list = res;
                list.sort((a?: Machine, b?: Machine) => {
                    if (!a || !b) return -1;
                    if (!a.authorized && b.authorized) {
                        return -1;
                    }
                    if (a.authorized && !b.authorized) {
                        return 1;
                    }
                    return 1;
                });


                let copyedData: any = []
                list.forEach(user => {
                    copyedData.push({ ...user })
                })
                setOriginData(copyedData);

                setDevices(list)
            }).catch(e => {
                console.error(e)
                Notification.error({ content: formatMessage({ id: 'users.detail.fetchDeviceListFailed' }) })
            }).finally(() => setLoading(false))
    }

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;

        let sortedAllDate = [...devices];

        if (sortOrder == 'ascend') {
            setSortOrder('ascend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => (a && b && a.name > b.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return 1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return -1;
                            }
                        }
                    }
                    return -1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(a.clientVersion, b.clientVersion)
                    }
                    return -1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return 1;
                    }
                    if (!a.connected && b.connected) {
                        return -1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds > b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return 1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return -1;
                    }
                    return -1;
                });
            }
            setDevices(sortedAllDate);

        } else if (sortOrder == 'descend') {
            setSortOrder('descend');



            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => (a && b && a.name < b.name ? 1 : -1))
            }
            if (dataIndex == 'ipV4') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.ipv4 && b.ipv4) {
                        const aArr = a.ipv4.split('.');
                        const bArr = b.ipv4.split('.');
                        const len = aArr.length > bArr.length ? bArr.length : aArr.length;
                        for (let i = 0; i < len; i++) {
                            if (parseInt(aArr[i]) > parseInt(bArr[i])) {
                                return -1;
                            } else if (parseInt(aArr[i]) < parseInt(bArr[i])) {
                                return 1;
                            }
                        }
                    }
                    return 1;
                });
            }
            if (dataIndex == 'os') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (a && b && a.clientVersion && b.clientVersion) {
                        return compare(b.clientVersion, a.clientVersion)
                    }
                    return 1;

                });
            }

            if (dataIndex == 'lastSeen') {
                sortedAllDate.sort((a?: Machine, b?: Machine) => {
                    if (!a || !b) return -1;
                    if (a.connected && b.connected) {
                        return 1;
                    }
                    if (a.connected && !b.connected) {
                        return -1;
                    }
                    if (!a.connected && b.connected) {
                        return 1;
                    }
                    if (b.lastSeen && a.lastSeen && a.lastSeen && b.lastSeen) {
                        return a.lastSeen.seconds < b.lastSeen.seconds ? 1 : -1;
                    }
                    if (a.lastSeen && !b.lastSeen) {
                        return -1;
                    }
                    if (!a.lastSeen && b.lastSeen) {
                        return 1;
                    }
                    return 1;
                });
            }

            setDevices(sortedAllDate);

        } else {
            setSortOrder(undefined)
            setDevices(originData)


        }
        setSortField(dataIndex)



    }

    useEffect(() => {
        query()
    }, []);
    return <> 
        <Paragraph className='mb10' type='tertiary'>{formatMessage({ id: 'users.detail.deviceListTitle' })}</Paragraph>

        <Table
            rowKey={(record?: Machine) => record ? record.id + '' : ''}
            onChange={handleSort}
            empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={devices} pagination={false} />
    </>
}

export default Index;