import { FC, useEffect, useState, useContext } from 'react'
import { Breadcrumb, Row, Col, Button, Typography, Modal, Notification, Dropdown, Descriptions, Popover, Badge, Avatar, Divider, Skeleton, Tag, Collapse, Card } from '@douyinfe/semi-ui';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { useNavigate, useParams } from 'react-router-dom';
import { flylayerClient } from '@/services/core';
import { BASE_PATH } from '@/constants/router';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { getFlynet } from '@/services/flynet';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { DNSConfig } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb";
import Password from '../password';
import avatarDefault from '@/assets/avatar_default.jpg';
import { getUserDisplayName } from '@/utils/user';
import Del from '../del';
import EditRdp from '../edit-rdp';
import EditRole from '../edit-role';
import EditUser from '../edit-user';
import Suspend from '../suspend';
import AddUserToGroup from '../add-user-to-group';
import DeviceList from './device-list';
import EditMfa from '../edit-mfa';
import Unlock from '../unlock';
import RemoveMFADevice from '../remove-mfa-device';

import EditExpiresAt from '../edit-expires-at';
import EditUserGroup from '../edit-user-group';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { UserProfileContext } from '@/hooks/useUserProfile';
import { IconSetting, IconArrowUpRight } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';

import styles from './index.module.scss'
import DateFormat from '@/components/date-format';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';

import DomainManager from '@/components/domain-manager';
import DnsManager from '@/components/dns-manager';
import RelayMapManager from '@/components/relay-map-manager';

const { Title, Paragraph } = Typography;

import CodeViewer from '@/components/code-viewer';
import { generatePassword } from '@/utils/common';


interface Props {

}
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const globalConfig = useContext(GlobalConfigContext);
    const flynetGeneral = useContext(FlynetGeneralContext);
    const userProfile = useContext(UserProfileContext);

    const templateUserListTitle = globalConfig.template?.userListTitle || ''

    const [flynet, setFlynet] = useState<Flynet>();
    const navigate = useNavigate();
    const params = useParams<{ name: string }>()
    const loginName = params.name ? params.name : ''



    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])
    const isSelf = (record: User) => {
        return record.loginName == userProfile.identity?.traits?.email;
    }
    const [user, setUser] = useState<User>();

    const [userLoading, setUserLoading] = useState(false);

    const [fieldValue, setFieldValue] = useState<Map<string, string>>(new Map());


    const [editVisible, setEditVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);
    const [suspendVisible, setSuspendVisible] = useState(false);
    const [editRoleVisible, setEditRoleVisible] = useState(false);
    const [addGroupVisible, setAddGroupVisible] = useState(false);
    const [editRDPVisible, setEditRDPVisible] = useState(false);
    const [mfaVisible, setMfaVisible] = useState(false);

    const [mfaEnabled, setMfaEnabled] = useState(false);

    const [removeMFADeviceVisible, setRemoveMFADeviceVisible] = useState(false);

    const [hasMFADevice, setHasMFADevice] = useState(false);

    const [formatedJson, setFormatedJson] = useState<string>('');

    const [unlockVisible, setUnlockVisible] = useState(false);

    const [password, setPassword] = useState('');
    const [passwordVisible, setPasswordVisible] = useState(false);

    const [editGroupVisible, setEditGroupVisible] = useState(false);

    const [expires_at_visible, setExpiresAtVisible] = useState(false);
    const query = () => {
        setUserLoading(true);

        flylayerClient.getUser({
            flynetId: flynetGeneral.id,
            identifier: {
                case: 'loginName',
                value: loginName
            }
        }).then(res => {
            if (res.user) {
                setUser(res.user);
                setRecords(res.user.dnsExtraRecords);
                let hasMFADevice = false;
                if (res.user.account && res.user.account.credentials) {
                    res.user.account.credentials.forEach(credential => {
                        if (credential.type == 'totp') {
                            hasMFADevice = true;
                        }
                    })
                }
                setHasMFADevice(hasMFADevice);
                
                if (res.user.account && res.user.account.attrsJson) {
                    try {
                        let attrJSON = JSON.parse(res.user.account.attrsJson);
                        let extraJSON = attrJSON['extra'];
                        try {
                            const formatedJson = JSON.stringify(extraJSON, null, 2)
                            setFormatedJson(formatedJson)
                        } catch (e) {
                            console.error(e)
                        }
                        // if (attrJSON && Object.hasOwnProperty.call(attrJSON, 'extra')) {

                        //     setExtra(JSON.stringify(attrJSON['extra']));
                        // }
                        if (attrJSON) {

                            setFieldValue(new Map(Object.entries(attrJSON)))
                            if (attrJSON['required_aal'] === 'aal2') {
                                setMfaEnabled(true)
                            } else {
                                setMfaEnabled(false)
                            }
                        }
                    } catch (e) {
                        console.error(e)
                    }
                }


                queryRelayMap(res.user.id);
                queryDNSConfig(res.user.id);


            }
        }).catch(err => {
            Notification.error({ content: formatMessage({ id: 'users.detail.getUserFailed' }), position: "bottomRight" })
        }).finally(() => {
            setUserLoading(false);
        });
    }

    const [records, setRecords] = useState<Array<Record>>([]);

    const [relayMapSaveLoading, setRelayMapSaveLoading] = useState(false);
    const [relayMapValue, setRelayMapValue] = useState<Uint8Array>();
    const queryRelayMap = (userId: bigint) => {
        flylayerClient.getUserRelayMap({
            flynetId: flynetGeneral.id,
            userId: userId
        }).then((res) => {
            setRelayMapValue(res.value)
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'users.detail.getRelayConfigFailed' }), position: "bottomRight" })
        });
    }

    const [dnsConfigSaveLoading, setDnsConfigSaveLoading] = useState(false);
    const [dnsConfig, setDnsConfig] = useState<DNSConfig>();
    const queryDNSConfig = (userId: bigint) => {
        flylayerClient.getUserDNSConfig({
            flynetId: flynetGeneral.id,
            userId: userId
        }).then((res) => {
            setDnsConfig(res.config)
            setRecords(res.config?.extraRecords || []);
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'users.detail.getDNSConfigFailed' }), position: "bottomRight" })
        });
    }

    useEffect(() => {
        query();
    }, [])

    return <><Skeleton placeholder={<div className='general-page'>
        <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>

        <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
        <Skeleton.Image style={{ height: 60 }} className='mb40' />
        <Skeleton.Image style={{ height: 200 }} />
    </div>} loading={userLoading}>

        <div className='general-page'>
            <Breadcrumb className='mb10' routes={
                [
                    {
                        path: `${BASE_PATH}/users`,
                        href: `${BASE_PATH}/users`,
                        name: formatMessage({ id: 'users.breadcrumb.users' })
                    },
                    {
                        name: user?.loginName ? user.loginName : '',
                    }
                ]
            }>
            </Breadcrumb>
            <Row className='mb20'>
                <Col span={20}>
                    <Title className={styles.heading} heading={3}>
                        <Avatar
                            size='small'
                            src={user?.avatarUrl ? user.avatarUrl : avatarDefault}
                            style={{ marginRight: 12 }}
                        ></Avatar>
                        {user ? getUserDisplayName(user, templateUserListTitle) : ''}
                        {user?.connected ?
                            <Badge className={styles.headingBadge} dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> :
                            <Badge className={styles.headingBadge} dot type='tertiary' />}
                    </Title>
                </Col>

                <Col span={4}><div className='btn-right-col'>
                    <Dropdown
                        position='bottomRight'
                        render={
                            <Dropdown.Menu>
                                <Dropdown.Item onClick={() => navigate(`${BASE_PATH}/devices?keywords=${user?.loginName}`)}>{formatMessage({ id: 'users.detail.viewDevices' })}</Dropdown.Item>
                                <Dropdown.Item onClick={() => {
                                    navigate(`${BASE_PATH}/logs?keywords=${user?.loginName}`)
                                }}>{formatMessage({ id: 'users.detail.viewRecentLogs' })}</Dropdown.Item>
                                <Dropdown.Divider />
                                <Dropdown.Item onClick={() => { setEditVisible(true); }}>{formatMessage({ id: 'users.detail.editUserInfo' })}</Dropdown.Item>
                                {flynet && flynet.accountManualCreate && <>
                                    <Dropdown.Divider />
                                    {user && <Dropdown.Item type='danger' disabled={isSelf(user)} onClick={() => {
                                        

                                        Modal.confirm({
                                            title: formatMessage({ id: 'users.passwordReset.title' }),
                                            content: formatMessage({ id: 'users.passwordReset.description' }),
                                            onOk: () => {
                                                let password = generatePassword(16);
                                                flylayerClient.resetUserPassword({
                                                    userId: user.id,
                                                    password: password
                                                }).then(() => {
                                                    Notification.success({
                                                        title: formatMessage({ id: 'users.passwordReset.success' }),
                                                        onClose: () => {
                                                        }
                                                    });
                                                    setPassword(password);
                                                    setPasswordVisible(true);
                                                }).catch(() => {

                                                    Notification.success({
                                                        title: formatMessage({ id: 'users.passwordReset.failed' })
                                                    });
                                                })
                                            }
                                        })

                                    }}>{formatMessage({ id: 'users.passwordReset.title' })}</Dropdown.Item>}
                                </>}
                             
                                <Dropdown.Divider />
                                <Dropdown.Item
                                    onClick={() => {
                                        setEditGroupVisible(true);
                                        query()
                                    }}
                                >
                                    {formatMessage({ id: 'users.detail.editUserGroup' })}
                                </Dropdown.Item>

                                <Dropdown.Item onClick={() => { setEditRoleVisible(true); }}>{formatMessage({ id: 'users.detail.editUserRole' })}</Dropdown.Item>




                                <Dropdown.Divider />

                                <Dropdown.Item onClick={() => {
                                    setUnlockVisible(true);

                                }}>{formatMessage({ id: 'users.detail.unlockAccount' })}</Dropdown.Item>
                                <Dropdown.Divider />
                                {user && user.disabled ?
                                    <Dropdown.Item type='danger' disabled={isSelf(user)} onClick={() => {
                                        if (isSelf(user)) { return }

                                        setSuspendVisible(true)
                                    }}>{formatMessage({ id: 'users.detail.enableUser' })}</Dropdown.Item>
                                    : ''}
                                {user && !user.disabled ?
                                    <Dropdown.Item type='danger' disabled={isSelf(user)} onClick={() => {
                                        if (isSelf(user)) { return }

                                        setSuspendVisible(true)
                                    }}>{formatMessage({ id: 'users.detail.disableUser' })}</Dropdown.Item>
                                    : ''}


                                {user && <Dropdown.Item type="danger" disabled={isSelf(user)} onClick={() => { if (isSelf(user)) { return }; setDelVisible(true); }}>{formatMessage({ id: 'users.detail.deleteUser' })}</Dropdown.Item>}
                                <Divider/>
                            {user && <Dropdown.Item disabled={isSelf(user)} type='danger' onClick={() => {
                            
                                setExpiresAtVisible(true)
                                query()
                            }}>
                                {formatMessage({ id: 'users.detail.setExpiry' })}
                            </Dropdown.Item>}
                                {
                                    hasMFADevice && <>
                                        
                                        <Dropdown.Item type='danger' onClick={() => {
                                            setRemoveMFADeviceVisible(true);
                                        }} >{formatMessage({ id: 'users.detail.removeMFADevice' })}</Dropdown.Item></>
                                }
                            </Dropdown.Menu>
                        }
                    >
                        <Button theme='solid' onClick={() => { }} icon={<IconSetting />}></Button>
                    </Dropdown>
                </div></Col>
            </Row>

            <Divider style={{ marginBottom: 10 }} />

            <Descriptions className='mb40'>
                <Descriptions.Item key="displayName" itemKey={formatMessage({ id: 'users.field.displayName' })}>{user?.displayName}</Descriptions.Item>
                <Descriptions.Item key="loginName" itemKey={formatMessage({ id: 'users.field.loginName' })}>{user?.loginName}<Copyable content={user?.loginName} /></Descriptions.Item>
                <Descriptions.Item key="id" itemKey="ID">{user?.id + ''}</Descriptions.Item>
                <Descriptions.Item key="createdAt" itemKey={formatMessage({ id: 'users.table.column.createdAtLocal' })}><DateFormat date={user?.createdAt}></DateFormat></Descriptions.Item>
                <Descriptions.Item key="role" itemKey={formatMessage({ id: 'users.table.column.roleLocal' })}>
                    {user?.role == UserRole.FLYNET_ADMIN ? formatMessage({ id: 'users.role.admin' }) :
                        user?.role == UserRole.FLYNET_USER ? formatMessage({ id: 'users.role.user' }) :
                            user?.role == UserRole.SUPER_ADMIN ? formatMessage({ id: 'users.role.superAdmin' }) : formatMessage({ id: 'users.role.unknown' })}
                </Descriptions.Item>
                <Descriptions.Item key="status" itemKey={formatMessage({ id: 'users.table.column.statusLocal' })}>{user?.disabled ? formatMessage({ id: 'users.status.disabled' }) : formatMessage({ id: 'users.status.enabled' })}</Descriptions.Item>
                <Descriptions.Item key="onlineStatus" itemKey={formatMessage({ id: 'users.detail.onlineStatus' })}>{user?.connected ? formatMessage({ id: 'users.detail.online' }) : formatMessage({ id: 'users.detail.offline' })}</Descriptions.Item>
                

                <Descriptions.Item key="uerGroup" itemKey={formatMessage({ id: 'users.table.column.groupLocal' })}>
                    {user?.userGroups.length == 0 ? formatMessage({ id: 'users.detail.unassigned' }) :
                        user?.userGroups.map((item, index) => {
                            return <Tag size='large' style={{ marginRight: 10 }} key={index}>{item.alias}({item.name})</Tag>
                        })
                    }
                </Descriptions.Item>
                <Descriptions.Item key="mfa" itemKey={formatMessage({ id: 'users.field.mfaEnabled' })}>{mfaEnabled ? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}</Descriptions.Item>
                {flynet?.accountAvailableFields.map((field, index) => {
                    return <Descriptions.Item key={index} itemKey={field.label} >{fieldValue.get(field.key) || formatMessage({ id: 'users.detail.notFilled' })}</Descriptions.Item>
                })}
            </Descriptions>

            <Title heading={4} className='mb2'>{formatMessage({ id: 'users.detail.accountDetails' })}</Title>
            <Paragraph className='mb10' type='tertiary'>{formatMessage({ id: 'users.detail.accountAttributes' })}</Paragraph>
            <CodeViewer value={formatedJson} height='280px'></CodeViewer>
            <div style={{ height: 40 }}></div>



            <Collapse defaultActiveKey={[]} expandIconPosition='left'>
                <Collapse.Panel header={<Title heading={4}>{formatMessage({ id: 'users.detail.deviceList' })}</Title>} itemKey="1">

                    {user && <DeviceList loginName={loginName} userId={user?.id}></DeviceList>}

                </Collapse.Panel>
                <Collapse.Panel header={<Title heading={4}>{formatMessage({ id: 'users.detail.networkSettings' })}</Title>} itemKey="2">
                    <Card>

                        {user && <DomainManager records={records} flynetId={flynetGeneral.id} onSave={(records) => {
                            let config = new DNSConfig({
                                ...dnsConfig,
                                extraRecords: records
                            });
                            setDnsConfig(config);
                            flylayerClient.setUserDNSConfig({
                                flynetId: flynetGeneral.id,
                                userId: user.id,
                                config: config
                            }).then(res => {
                                queryDNSConfig(user.id);
                                Notification.success({ content: formatMessage({ id: 'common.saveSuccess' }), position: "bottomRight" })
                            }).catch(err => {
                                Notification.error({ content: formatMessage({ id: 'common.saveFailed' }), position: "bottomRight" })
                            })
                        }}></DomainManager>}

                        {user && dnsConfig && <DnsManager flynetId={flynetGeneral.id}
                            dnsConfig={dnsConfig}
                            saveLoading={dnsConfigSaveLoading}
                            onSave={(config) => {
                                setDnsConfigSaveLoading(true);
                                flylayerClient.setUserDNSConfig({
                                    flynetId: flynetGeneral.id,
                                    userId: user.id,
                                    config: config
                                }).then(res => {
                                    queryDNSConfig(user.id);

                                    Notification.success({ content: formatMessage({ id: 'users.detail.saveDNSSuccess' }), position: "bottomRight" })
                                }).catch(err => {
                                    Notification.error({ content: formatMessage({ id: 'users.detail.saveDNSFailed' }), position: "bottomRight" })
                                }).finally(() => {
                                    setDnsConfigSaveLoading(false);
                                })
                            }
                            }></DnsManager>}

                        {user && relayMapValue && <RelayMapManager saveLoading={relayMapSaveLoading} className='mb40' flynetId={flynetGeneral.id} value={relayMapValue} onSave={(value) => {
                            setRelayMapSaveLoading(true);
                            flylayerClient.setUserRelayMap({
                                flynetId: flynetGeneral.id,
                                userId: user.id,
                                value: value
                            }).then(res => {
                                setRelayMapValue(value)
                                Notification.success({ content: formatMessage({ id: 'users.detail.saveRelaySuccess' }), position: "bottomRight" })
                            }).catch((err) => {
                                console.error(err)
                                Notification.error({
                                    title: formatMessage({ id: 'users.detail.saveRelayFailed' }),
                                    content: err.message,
                                    position: "bottomRight"
                                })
                            }).finally(() => {
                                setRelayMapSaveLoading(false);
                            })
                        }} />}
                    </Card>
                </Collapse.Panel>
            </Collapse>
            <div style={{ height: 100 }}></div>


        </div>
    </Skeleton>
        {delVisible && user ?
            <Del record={user} success={() => {
                navigate(`${BASE_PATH}/users`)
            }} close={() => { setDelVisible(false); }} /> : null}
        {editRoleVisible && user ?
            <EditRole record={user} success={() => {
                setEditRoleVisible(false)
                query()
            }} close={() => {
                setEditRoleVisible(false)
            }} /> : null}
        {suspendVisible && user ?
            <Suspend record={user}
                close={() => { setSuspendVisible(false) }}
                success={() => {
                    setSuspendVisible(false)
                    query()
                }}
            /> : null}
        {addGroupVisible && user && <AddUserToGroup
            close={() => {
                setAddGroupVisible(false)
            }}
            record={user}></AddUserToGroup>}
        {editRDPVisible && user && <EditRdp
            close={() => {
                setEditRDPVisible(false)
            }}
            record={user}
            success={() => {
                setEditRDPVisible(false)
                query()
            }}
        ></EditRdp>}
        {
            editVisible && user ? <EditUser
                record={user}
                close={() => {
                    setEditVisible(false)
                    query()
                }}
            ></EditUser> : null
        }
        {mfaVisible && user ? <EditMfa
            record={user}
            close={() => {
                setMfaVisible(false)
                query()
            }}
            success={() => {
                setMfaVisible(false)
                query()
            }}
        ></EditMfa> : null}
        {removeMFADeviceVisible && user && <RemoveMFADevice
            record={user}
            close={
                () => {
                    setRemoveMFADeviceVisible(false);
                    query()
                }
            }
            success={
                () => {
                    setRemoveMFADeviceVisible(false);
                    query()
                }
            }></RemoveMFADevice>
        }
        {unlockVisible && user && <Unlock
            record={user}
            close={() => {
                setUnlockVisible(false);
            }}
            success={
                () => {
                    setUnlockVisible(false)
                    query()
                }
            }
        ></Unlock>
        }
        {passwordVisible && <Password keyValue={password} close={() => { setPasswordVisible(false), setPassword('') }} />}
        {user && editGroupVisible && <EditUserGroup
            close={() => {
                
                setEditGroupVisible(false)
            }}
            record={user}
            success={() => {
                setEditGroupVisible(false)
                query()
            }}
        ></EditUserGroup>
        }
        {
            user && expires_at_visible && <EditExpiresAt
                close={() => {
                    
                    setExpiresAtVisible(false)
                }}
                record={user}
                success={() => {
                    setExpiresAtVisible(false)
                    query()
                }}
            ></EditExpiresAt>
        }
    </>
}

export default Index

