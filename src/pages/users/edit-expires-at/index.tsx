import React,{ FC, useState } from 'react'
import { Typography, Modal, Row, Col, Notification, Banner, Form } from '@douyinfe/semi-ui';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { useLocale } from '@/locales';

import { Timestamp } from "@bufbuild/protobuf";
interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const { Paragraph, Title } = Typography;
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    const [formApi, SetFormApi] = useState<FormApi<{
        temporary: boolean;
        expiredAt: Date;
    }>>()
    // const [value, setValue] = useState(props.record.expiredAt ? props.record.expiredAt.toDate() : undefined);
    const [loading, setLoading] = useState(false);
    // const onChange = (e: any) => {
    //     setValue(e.target.value);
    // };

    const handleSubmit = async () => {
        const res = await formApi?.validate();
        const values = formApi?.getValues()
        
        const temporary = values?.temporary;
        let expiredAt = values?.expiredAt ? Timestamp.fromDate(values.expiredAt) : undefined
        if(!temporary) {
            expiredAt = undefined;
        }
        setLoading(true);
        flylayerClient.editUserLifecycle({
            userId: props.record.id,
            temporary: temporary,
            expiredAt: expiredAt
        }).then(() => {
            Notification.success({
                title: formatMessage({ id: 'users.editExpiry.success' }),
                content: formatMessage({ id: 'users.editExpiry.successMessage' }).replace('{user}', `${props.record.displayName}(${props.record.loginName})`)
            });
            props.success && props.success();
            props.close();
        }).catch((e) => {
            Notification.error({
                title: formatMessage({ id: 'users.editExpiry.failed' }),
                content: e.message
            });
        }).finally(() => {
            setLoading(false);
        });
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'users.editExpiry.title' }).replace('{user}', `${props.record.displayName}(${props.record.loginName})`)}
            visible={true}
            okButtonProps={{ loading }}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Banner className='mb20' type='warning' description={formatMessage({ id: 'users.editExpiry.warning' })}>

            </Banner>
                <Form
                    getFormApi={SetFormApi}

                    initValues={{
                        temporary: props.record.temporary || false,
                        expiredAt: !props.record.temporary ? undefined : props.record.expiredAt ? props.record.expiredAt.toDate() : undefined
                    }}
                    validateFields={(values) => {
                        let { loginName, password, displayName } = values;
                        loginName = loginName ? loginName.trim() : loginName;
                        password = password ? password.trim() : password;
                        displayName = displayName ? displayName.trim() : displayName;

                        let errors: { [key: string]: string } = {};
                        
                        if (Object.keys(errors).length > 0) {

                            return errors;
                        }
                        return '';
                    }}
                >
                    {({ formState }) => (
                        <React.Fragment>
                           
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Switch field='temporary' label={formatMessage({ id: 'users.field.temporary' })}></Form.Switch>
                                </Col>
                                {formState.values.temporary && <Col span={12}>
                                    <Form.DatePicker field='expiredAt' label={formatMessage({ id: 'users.field.expiredAt' })} style={{ width: '100%' }} ></Form.DatePicker>
                                </Col>}
                            </Row>
                        
                        </React.Fragment>
                    )}



                </Form>

        </Modal>
    </>
}

export default Index
