import { FC, useState } from 'react';
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Input, Divider } from '@douyinfe/semi-ui';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';

import Import from './import/import';
import useTable, { RecordFilter } from './useTable';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation } from 'react-router-dom';
import UserSelector from '@/components/user-selector';
import { ImportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { useLocale } from '@/locales';

const { Title } = Typography;

const getRecordFilter = (loc: Location): RecordFilter => {
    const keywords: string = getQueryParam('keywords', loc) as string;
    const finish: 'true' | 'false' | '' = getQueryParam('finish', loc) as 'true' | 'false' | '';
    const actorsQuery = getQueryParam('actors', loc);

    let actors: string[] = [];
    if (actorsQuery && Array.isArray(actorsQuery)) {
        actors = actorsQuery as string[];
    }
    if (actorsQuery && typeof actorsQuery == 'string') {
        actors = [actorsQuery as string];
    }
    return {
        keywords: keywords || '',
        finish: finish || '',
        actors: actors || []
    };
}

const Index: FC = () => {
    const { formatMessage } = useLocale();
    const location = useLocation();

    const initFilter = getRecordFilter(location);

    const {
        loading,
        data,
        total,
        page,
        setPage,
        pageSize,
        columns,
        queryRecord,
        filter,
        setFilter,
        handleFilterChange,
        handleSort,
        setReloadFlag
    } = useTable(initFilter);

    const [createVisible, setCreateVisible] = useState(false);

    return <>
        <div className='general-page'>
            <Breadcrumb routes={
                [
                    {
                        path: `${BASE_PATH}/users`,
                        href: `${BASE_PATH}/users`,
                        name: formatMessage({ id: 'users.breadcrumb.users' })
                    },
                    {
                        name: formatMessage({ id: 'users.breadcrumb.dataImport' }),
                    }
                ]
            }>
            </Breadcrumb>
            <Row className='mb20'>
                <Col span={20}>
                    <Title heading={3}>{formatMessage({ id: 'dataImport.title' })}</Title>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                    <Space>
                        <Button theme='solid'
                            onClick={() => setCreateVisible(true)}>{formatMessage({ id: 'dataImport.title' })}</Button>
                    </Space>
                </div></Col>
            </Row>

            <Divider className='mb20'></Divider>
            <Row className='mb10'>
                <Col span={24}>
                    <Space>
                        <Input
                            placeholder={formatMessage({ id: 'dataImport.searchPlaceholder' })}
                            value={filter.keywords}
                            onChange={(value: string) => {
                                setFilter({ ...filter, keywords: value });
                                handleFilterChange({ ...filter, keywords: value });
                            }}

                            style={{ width: 400 }}
                            showClear
                            onClear={() => {
                                setFilter({ ...filter, keywords: '' });
                                handleFilterChange({ ...filter, keywords: '' });
                            }}
                        />
                        <UserSelector
                            style={{ width: 260 }}
                            value={filter.actors}
                            onChange={(value: string[]) => {
                                setFilter({ ...filter, actors: value });
                                handleFilterChange({ ...filter, actors: value });
                            }
                            }
                            onLoadingChange={(val) => { }}
                        />
                    </Space>
                </Col>
            </Row>
            <Table
                dataSource={data}
                loading={loading}
                rowKey={(record?: ImportRecord) => record ? record.id + '' : ''}
                columns={columns}
                onChange={handleSort}
                pagination={{
                    pageSize: pageSize,
                    currentPage: page,
                    total: total,
                    onChange: (page: number, pageSize: number) => {
                        console.log('page change', page, pageSize);
                        setPage(page);
                        queryRecord({ page, pageSize });
                    },
                    showTotal: true
                }}
                empty={<TableEmpty loading={loading} />}
            />

        </div>
        {createVisible && <Import close={() =>  {
            setCreateVisible(false)
            setReloadFlag(true)
        }} success={() => {
            setCreateVisible(false)
            setReloadFlag(true)
        }} />}
    </>
}
export default Index;