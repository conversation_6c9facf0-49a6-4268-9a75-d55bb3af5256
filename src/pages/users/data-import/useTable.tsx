import { useEffect, useState, useContext, useCallback } from 'react';
import { Space, Typography, Tag, Notification } from '@douyinfe/semi-ui';
import { ImportRecord } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/import_export_pb';
import { flylayerClient } from '@/services/core';
import qs from 'query-string';
import { useNavigate } from 'react-router-dom';

import { BASE_PATH } from '@/constants/router';
import DateFormat from '@/components/date-format';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { debounce } from 'lodash';

const { Title, Text } = Typography;

export type RecordFilter = {
    keywords: string;
    actors: string[];
    finish: 'true' | 'false' | '';
}

const useTable = (initFilter: RecordFilter) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const navigate = useNavigate();

    const [loading, setLoading] = useState(true);

    const [data, setData] = useState<ImportRecord[]>([]);

    const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');

    const [reloadFlag, setReloadFlag] = useState(false);

    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);

    const pageSize = 10;


    const [delVisible, setDelVisible] = useState(false);

    const [curRecord, setCurRecord] = useState<ImportRecord>();

    const [filter, setFilter] = useState<RecordFilter>(initFilter);

    const doNavigate = (param: RecordFilter) => {
        let query = '';
        if (param.keywords || (param.actors && param.actors.length > 0)) {
            const newParam = {
                keywords: param.keywords || '',
                actors: param.actors || []
            }
            query = qs.stringify(newParam, { skipEmptyString: true });
        }
        if (query) {
            navigate(`${BASE_PATH}/users/import?${query}`);
        } else {
            navigate(`${BASE_PATH}/users/import`);
        }
    }

    const columns = [
        {
            title: formatMessage({ id: 'dataImport.fileName' }),
            dataIndex: 'file_name',
            sorter: true,
            render: (_: string, record: ImportRecord) => {
                return <>
                    <Title heading={6}><a href={`${BASE_PATH}/users/import/${record.id}`}>{record.fileName}</a></Title>
                    <a className='link-external' href={record.fileUrl} target='_blank'>{formatMessage({ id: 'components.common.download' })}</a>
                </>;
            }
        },
        {
            width: 100,
            title: formatMessage({ id: 'dataImport.totalLines' }),
            dataIndex: 'totalLine',
        },
        {
            width: 100,
            title: formatMessage({ id: 'dataImport.errorLines' }),
            dataIndex: 'errorLine',
        },
        {
            width: 100,
            title: formatMessage({ id: 'dataImport.createdLines' }),
            dataIndex: 'createLine',
        },
        {
            width: 100,
            title: formatMessage({ id: 'dataImport.updatedLines' }),
            dataIndex: 'updateLine',
        },

        {
            width: 100,
            title: formatMessage({ id: 'dataImport.completed' }),
            dataIndex: 'finish',
            render: (value: boolean) => {
                return <Tag color={value ? 'green' : 'red'}>{value ? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}</Tag>;
            }
        },
        {
            width: 200,
            title: formatMessage({ id: 'dataImport.table.operator' }),
            sorter: true,
            dataIndex: 'user_id',
            render: (_: any, record: ImportRecord) => {
                return record.user ? <Space>
                    <Text>{record.user.displayName}({record.user.loginName})</Text>
                </Space> : <Text type='tertiary'>{formatMessage({ id: 'dataImport.table.unknown' })}</Text>;
            }
        }, {
            width: 200,
            title: formatMessage({ id: 'dataImport.table.importTime' }),
            sorter: true,
            dataIndex: 'created_at',
            render: (_: any, record: ImportRecord) => {
                return <DateFormat date={record.createdAt} />;
            }
        }
    ];

    const queryRecord = (params?: {
        filter?: RecordFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
    }) => {
        let queryArray = [`record_name=User`];

        let limit = pageSize;
        let curPage = page;

        let filterFinish = filter.finish;
        let filterKeywords = filter.keywords;
        let filterActors = filter.actors;

        let filterSortOrder = sortOrder;
        let filterSortField = sortField;

        if (params) {
            if (params.filter) {
                setFilter(params.filter);
                filterFinish = params.filter.finish;
                filterKeywords = params.filter.keywords;
                filterActors = params.filter.actors || [];
            }

            if (params.sortOrder && params.sortField) {
                setSortOrder(params.sortOrder as any);
                setSortField(params.sortField);

                filterSortOrder = params.sortOrder as any;
                filterSortField = params.sortField;
            } else {
                filterSortOrder = undefined;
                filterSortField = '';
                setSortOrder(undefined);
                setSortField('');
            }

            if (params.page) {
                curPage = params.page;
                setPage(params.page);
            }

            if (params.pageSize) {
                limit = params.pageSize;
            }
        }


        if (filterFinish == 'true') {
            queryArray.push(`finish=true`);
        } else if (filterFinish == 'false') {
            queryArray.push(`finish=false`);
        }
        if (filterKeywords != "" && filterKeywords.trim() != "") {
            queryArray.push(`keywords=${encodeURIComponent(filterKeywords)}`);
        }
        if (filterActors && filterActors.length > 0) {
            queryArray.push(`user_ids=${encodeURIComponent(filterActors.join(','))}`);
        }


        if (filterSortOrder && filterSortField) {
            setSortOrder(filterSortOrder as any);
            setSortField(filterSortField);

            let order_by = encodeURIComponent(`${filterSortField} ${filterSortOrder}`);
            queryArray.push(`order_by=${order_by}`);
        }

        const offset = (curPage - 1) * limit;

        queryArray.push(`limit=${limit}`);
        queryArray.push(`offset=${offset}`);

        setLoading(true);
        flylayerClient.listImportRecord({
            flynetId: flynetGeneral.id,
            query: queryArray.join('&')
        }).then((res) => {
            setData(res.records);
            setTotal(Number(res.total));
        }).catch(err => {
            Notification.error({ content: formatMessage({ id: 'dataImport.fetchListFailed' }) })
            console.error(err)
        }).finally(() => {
            setLoading(false);
        })
    }


    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;

        let sortOrder = '';
        if (sorter.sortOrder == 'ascend') {
            sortOrder = 'ASC';
        } else if (sorter.sortOrder == 'descend') {
            sortOrder = 'DESC';
        }
        queryRecord({
            page: 1,
            sortOrder: sortOrder,
            sortField: dataIndex,
        })

    }




    useEffect(() => {
        queryRecord();
    }, []);

    useEffect(() => {
        setReloadFlag(false);
        if (reloadFlag) {
            queryRecord({
                sortOrder: sortOrder,
                sortField: sortField,
                page: 1,
            });
        }
    }, [reloadFlag]);

    const debounceQuery = useCallback(debounce((filter) => queryRecord(filter), 500), []);
    const handleFilterChange = (value: RecordFilter) => {
        doNavigate(value);
        debounceQuery({
            filter: value,
            sortOrder: sortOrder,
            sortField: sortField,
            pageSize: pageSize,
            page: 1,
        });
    }


    return {
        loading,
        data,
        total,
        page,
        setPage,
        pageSize,
        columns,
        sortOrder,
        setSortOrder,
        sortField,
        setSortField,
        queryRecord,
        delVisible,
        setDelVisible,
        curRecord,
        setCurRecord,
        filter,
        setFilter,
        doNavigate,
        handleFilterChange,
        handleSort,
        setReloadFlag
    }
}

export default useTable;

