import { useEffect, useState, useContext, useCallback } from 'react';
import { getFlynet } from '@/services/flynet';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { set } from 'lodash';
import { useLocale } from '@/locales';

const useUserField = (hasPassword: boolean) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    let initFields = [{
        key: 'loginName',
        label: formatMessage({ id: 'users.field.loginName' })
    }, {
        key: 'displayName',
        label: formatMessage({ id: 'users.field.displayName' })
    }, {
        key: 'password',
        label: formatMessage({ id: 'users.field.password' })
    }, {
        key: 'temporary',
        label: formatMessage({ id: 'users.field.temporary' })
    }, {
        key: 'expiredAt',
        label: formatMessage({ id: 'users.field.expiredAt' })
    }, {
        key: 'mfaEnabled',
        label: formatMessage({ id: 'users.field.mfaEnabled' })
    }, {
        key: 'groups',
        label: formatMessage({ id: 'users.field.groups' })
    }, {
        key: 'role',
        label: formatMessage({ id: 'users.field.role' })
    }]
    if (!hasPassword) {
        initFields = initFields.filter((field) => field.key !== 'password');
    }
    const [fields, setFields] = useState<{ key: string, label: string }[]>(initFields);

    const [originalFields, setOriginalFields] = useState<{ key: string, label: string }[]>([]);


    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {
            // setFlynet(res.flynet)
            if (res.flynet && res.flynet.accountAvailableFields && res.flynet.accountAvailableFields.length > 0) {

                let newFields: { key: string, label: string }[] = [{
                    key: 'loginName',
                    label: formatMessage({ id: 'users.field.loginName' })
                }, {
                    key: 'displayName',
                    label: formatMessage({ id: 'users.field.displayName' })
                }];
                res.flynet.accountAvailableFields.forEach((field: any) => {
                    newFields.push({
                        key: field.key,
                        label: field.label
                    })
                });
                newFields = newFields.concat([{
                    key: 'temporary',
                    label: formatMessage({ id: 'users.field.temporary' })
                }, {
                    key: 'expiredAt',
                    label: formatMessage({ id: 'users.field.expiredAt' })
                }, {
                    key: 'mfaEnabled',
                    label: formatMessage({ id: 'users.field.mfaEnabled' })
                }, {
                    key: 'groups',
                    label: formatMessage({ id: 'users.field.groups' })
                }, {
                    key: 'role',
                    label: formatMessage({ id: 'users.field.role' })
                }, {
                    key: 'password',
                    label: formatMessage({ id: 'users.field.password' })
                }]);
                if (!hasPassword) {
                    newFields = newFields.filter((field) => field.key !== 'password');
                }
                setFields(newFields);
                setOriginalFields(newFields);
                setSelectedKeys(newFields.map((field: any) => field.key));
            } else {
                setOriginalFields(fields);
                setSelectedKeys(fields.map((field: any) => field.key));
            }
        }
    }

    useEffect(() => {
        queryFlynet()
    }, [])


    return {
        fields,
        setFields,
        originalFields,
        setOriginalFields,
        selectedKeys,
        setSelectedKeys,
    }
}

export default useUserField;