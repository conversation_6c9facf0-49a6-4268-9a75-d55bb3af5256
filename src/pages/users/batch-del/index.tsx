import { FC, useState, useContext } from 'react'
import { Typography, Modal, List, Notification, TabPane, Input, Banner } from '@douyinfe/semi-ui';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
const { Paragraph, Title } = Typography;
import UserDevice from '@/components/user-device';
import UserList from '@/pages/users/components/user-list';

interface Props {
    close: () => void,
    success?: () => void,
    records: User[]
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);


    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');
    const [users, setUsers] = useState<User[]>(props.records);
    
    const handleSubmit = () => {
        setLoading(true);
        flylayerClient.batchDeleteUsers({
            flynetId: flynet.id,
            userIds: users.map((item) => {
                return item.id;
            })
        }).then(() => {
            Notification.success({ content: formatMessage({ id: 'users.delete.success' }), position: "bottomRight" })
            if (props.success) {
                props.success();
            }
        }
        ).catch(err => {
            console.error(err);
            Notification.error({ 
                title: formatMessage({ id: 'users.delete.failed' }),
                content: err.message, position: "bottomRight" })
                
        }
        ).finally(() => {
            setLoading(false);
        }
        );
        
        
    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'users.batch.delete' })}
            visible={true}
            onOk={handleSubmit}
            
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
                type: 'danger',
                disabled: users.length == 0
            }}
            maskClosable={false}
        >
            <Banner type='danger' className='mb20' title={formatMessage({ id: 'users.batch.delete.warning' })}></Banner>
            <UserList className='mb20' users={users} onChange={(list)=>setUsers(list)} />

            
        </Modal></>
}
export default Index;
