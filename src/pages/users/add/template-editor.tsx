import React, { useState, useRef, useContext, useEffect } from 'react'
import { Modal, Notification } from "@douyinfe/semi-ui";
import styles from './index.module.scss'

import CodeEditor from '@/components/code-editor';
import { useLocale } from '@/locales';

const Index: React.FC<{
    close: () => void,
    template: string,
    success: (template: string) => void,
}> = (props) => {
    const { formatMessage } = useLocale();
    const [loading, setLoading] = useState(false);

    const [template, setTemplate] = useState<string>(props.template);
    const handleOk = () => {
        try {
            JSON.parse(template);

        } catch (e) {
            Notification.error({
                title: formatMessage({ id: 'users.add.templateEditor.invalidJson' }),
                content: (e as any).message,
                duration: 6000,
            });
            return;
        }
        setLoading(true);
        setTimeout(() => {
            props.success(template);
            Notification.success({
                content: formatMessage({ id: 'users.add.templateEditor.saveSuccess' }),
            });

            setLoading(false);
        }, 1000);

    }

    return <>
        <Modal
            title={formatMessage({ id: 'users.add.templateEditor.title' })}
            visible={true}
            onOk={handleOk}
            onCancel={props.close}
            width={1080}
            height={620}
            okText={formatMessage({ id: 'users.add.templateEditor.save' })}
            cancelText={formatMessage({ id: 'users.add.templateEditor.cancel' })}
            okButtonProps={{ loading }}
            confirmLoading={loading}
            closeOnEsc={true}
            maskClosable={false}
        >
            <CodeEditor value={template} onChange={setTemplate} />
        </Modal>
    </>
}

export default Index;