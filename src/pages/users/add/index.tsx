import React, { FC, useState, useContext, useEffect, ReactElement, JSXElementConstructor } from 'react'
import { Typography, Modal, Form, Notification, Button, Rating, Row, Col, Space, Skeleton } from '@douyinfe/semi-ui';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { IconEdit } from '@douyinfe/semi-icons';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import TemplateEditor from './template-editor';
import TableEmpty from '@/components/table-empty'
import { use } from 'echarts';
import { generatePassword, getPasswordRank } from '@/utils/common';
import { DynamicForm } from '@/interface/dynamic-form';
import { getAccountSchema } from '@/services/user';
const { Title } = Typography;
const { Switch, Input, RadioGroup, Radio } = Form
interface Props {
    close: () => void,
    success?: () => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const userProfile = useContext(UserProfileContext)


    const [loading, setLoading] = useState(false);

    const [userFormLoading, setUserFormLoading] = useState(true);

    const [formApi, SetFormApi] = useState<FormApi<DynamicForm>>();
    

    const [userForm, setUserForm] = useState<DynamicForm>();

    const [password, setPassword] = useState<string>('')
    const [passwordRank, setPasswordRank] = useState<number>(0)

    const handleSubmit = async () => {
        await formApi?.validate()
        console.log(formApi?.getValues())
        setLoading(true);
        setTimeout(() => {
            setLoading(false);
            Notification.success({
                title: formatMessage({ id: 'users.add.successTitle' }),
                content: formatMessage({ id: 'users.add.successMessage' })
            })

            props.success && props.success()
            props.close()
        }, 1000)
    }

    // const buildUserForm = (template: string) => {
    //     const userForm: DynamicForm = JSON.parse(template)
    //     setUserForm(userForm)
    //     formApi?.setValues(userForm)
    // };

    // useEffect(() => {
    //     buildUserForm(userTemplate)
    // }, [])

    // useEffect(() => {
    //     setUserFormLoading(true);
    //     getAccountSchema(userProfile.identity?.schema_url || '').then((userForm) => {
    //         setUserForm(userForm)
    //         formApi?.setValues(userForm)
    //     }).catch(err => {
    //         console.error(err)
    //         Notification.error({ content: '获取用户表单失败，请稍后重试', position: "bottomRight" })
    //     }).finally(() => {
    //         setUserFormLoading(false);
    //     });
    // }, [])

    const getFormElement = (userForm: DynamicForm) => {
        const properties = userForm.properties;
        const required_fields = userForm.required || []
        const res: Array<JSX.Element | undefined> = Object.keys(properties).map((key) => {

            const field = properties[key]
            if (field.type === 'object' && field.properties) {
                return <div className={styles.subForm} key={key}>
                    <Title heading={6} >{field.title}</Title>
                    {
                        getFormElement(field)
                    } </div>
            }

            let validate = (val: string) => {

                if (required_fields.indexOf(key) >= 0) {
                    if (!val || val.trim() == '') {
                        return field.title + formatMessage({ id: 'form.validation.required' })
                    }
                }

                if (field.minLength && val.length < field.minLength) {
                    return field.title + formatMessage({ id: 'form.validation.minLength' }).replace('{length}', field.minLength.toString())
                }
                if (field.maxLength && val.length > field.maxLength) {
                    return field.title + formatMessage({ id: 'form.validation.maxLength' }).replace('{length}', field.maxLength.toString())
                }
                if (field.pattern && !new RegExp(field.pattern).test(val)) {
                    return field.title + formatMessage({ id: 'form.validation.invalidFormat' })
                }
                if (field.format === 'email') {
                    if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(val)) {
                        return field.title + formatMessage({ id: 'form.validation.invalidEmail' })
                    }
                }
                if (field.format === 'ipv4') {
                    if (!/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(val)) {
                        return field.title + formatMessage({ id: 'form.validation.invalidIPv4' })
                    }
                }
                if (field.format === 'ipv6') {
                    if (!/^[0-9a-fA-F:]+$/.test(val)) {
                        return field.title + formatMessage({ id: 'form.validation.invalidIPv6' })
                    }
                }
                return ''
            }

            if (field.type === 'boolean') {
                return <Col key={key} span={12}>
                    <Form.Switch field={key} label={field.title} name={key}
                    ></Form.Switch>
                </Col>

            } else if (field.type === 'string') {

                if (field.format === 'password') {
                    return <Col key={key} span={12}><Row>
                        <Col span={24}>
                            <Form.Input
                                style={{ marginBottom: 0 }}
                                field={key}
                                label={field.title}
                                validate={validate}
                                trigger={'change'}
                                mode='password'
                                name={key}
                                onChange={(val) => {
                                    setPassword(val)
                                    setPasswordRank(getPasswordRank(val))
                                }}
                                required={required_fields.indexOf(key) >= 0}
                            />
                        </Col>
                        <Col span={12}>

                            <Rating disabled style={{ color: 'black' }} character='▇' size='small' value={passwordRank} count={3}></Rating>
                        </Col>
                        <Col span={12}>
                            <div className='btn-right-col'>
                                <Button size='small' type='primary' theme='solid'
                                    onClick={() => {

                                        let password = generatePassword(16);
                                        formApi?.setValue(key as any, password);
                                        setPassword(password);
                                        setPasswordRank(getPasswordRank(password))
                                        let scope = formApi?.getValue(key as any);
                                        formApi?.validate(scope)
                                    }}
                                >{formatMessage({ id: 'users.add.generate' })}</Button>
                            </div>
                        </Col>
                    </Row>
                    </Col>

                }
                if (field.format === 'date') {
                    return <Col key={key} span={12}>
                        <Form.DatePicker
                            style={{ width: '100%' }}
                            field={key}
                            label={field.title}
                            name={key}
                            initValue={field.type}
                            validate={validate}
                        ></Form.DatePicker>
                    </Col>
                }
                if(field.format === 'time') {
                    return <Col key={key} span={12}>
                        <Form.TimePicker
                            style={{ width: '100%' }}
                            field={key}
                            label={field.title}
                            name={key}
                            initValue={field.type}
                            validate={validate}
                        ></Form.TimePicker>
                    </Col>

                }
                if (field.format === 'date-time') {
                    return <Col key={key} span={12}>
                    <Form.DatePicker
                        type='dateTime'
                        style={{ width: '100%' }}
                        field={key}
                        label={field.title}
                        name={key}
                        initValue={field.type}
                        validate={validate}
                    ></Form.DatePicker>
                    </Col>
                }
                return <Col key={key} span={12}>
                    <Form.Input
                        field={key}
                        label={field.title}
                        validate={validate}
                        name={key}
                    />
                </Col>

            } else if (field.type === 'number') {
                return <Col key={key} span={12}>
                    <Form.InputNumber
                        style={{ width: '100%' }}
                        field={key}
                        label={field.title}
                        name={key}
                        initValue={field.type}
                        validate={validate}
                    ></Form.InputNumber>
                </Col>
            } else if (field.type === 'array') {
                if (field.items && field.items.length > 0 && field.items[0].enum) {
                    return <Col key={key} span={12}>
                        <Form.Select
                            style={{ width: '100%' }}
                            field={key}
                            label={field.title}
                            name={key}
                            initValue={field.type}
                            validate={validate}
                            optionList={field.items[0].enum.map((item) => {
                                return { value: item, label: item }
                            })}
                        ></Form.Select>
                    </Col>
                }
                return <Col key={key} span={12}>
                    <Form.TagInput
                        field={key}
                        label={field.title}
                        name={key}
                        addOnBlur
                        validate={validate}></Form.TagInput>
                </Col>
            }

        })
        let filteredRes = res.filter(e => e != undefined)
        const rowRount = filteredRes.length % 2 == 0 ? filteredRes.length / 2 : (filteredRes.length + 1) / 2;

        const resulet: Array<JSX.Element> = [];

        for (let i = 0; i < rowRount; i++) {
            resulet.push(<Row gutter={16}>{res[2 * i]}{(2 * i + 1) < filteredRes.length ? res[2 * i + 1] : ''}</Row>);
        }
        return resulet
    }

    return <>
        <Modal
            width={800}
            // title={<>添加用户&nbsp;<Space>
            //     <Button onClick={() => setTemplateEditVisible(true)} size='small' icon={<IconEdit />}>编辑属性定义</Button>
            // </Space>
            // </>}
            title={formatMessage({ id: 'users.add.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading }}
            className='semi-modal'
            maskClosable={false}
        >
            <div className={styles.addUser}>
                <Skeleton loading={userFormLoading} placeholder={<>
                    <Skeleton.Image style={{ height: 32, marginBottom: 50, marginTop: 36 }} />
                    <Skeleton.Image style={{ height: 32, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />

                    <Skeleton.Image style={{ height: 152 }} />
                </>}>
                    <Form
                        getFormApi={SetFormApi}
                        allowEmpty
                    >
                        {userForm && <>
                            {getFormElement(userForm)}
                        </>}
                    </Form>
                </Skeleton>


            </div>
        </Modal>
        {/* 
        {templateEditVisible && <TemplateEditor
            template={userTemplate}
            close={() => setTemplateEditVisible(false)}
            success={(template) => { setTemplateEditVisible(false); setUserTemplate(template);  }}
        ></TemplateEditor>} */}
    </>


}

export default Index;