import React, { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Form, Notification, Row, Col, Divider, Skeleton, Space } from '@douyinfe/semi-ui';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { Field } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb";

import { Timestamp } from "@bufbuild/protobuf";
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { getFieldValue, renderFormElement, validateFields } from '@/utils/user';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { getFlynet } from '@/services/flynet';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
const { Text } = Typography;
interface Props {
    close: () => void,
    success?: () => void,
    record: User
}


const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();

    const globalConfig = useContext(GlobalConfigContext);

    let loginNameOrg = globalConfig.code;
    if (props.record.loginName.indexOf('@') >= 0) {
        if (props.record.loginName.split('@').length > 1) {
            loginNameOrg = props.record.loginName.split('@')[1];
        }

    }


    let mfaEnabled = false;
    if (props.record.account && props.record.account.attrsJson) {
        const attrsJson = JSON.parse(props.record.account.attrsJson);
        if (attrsJson) {
            if (attrsJson['required_aal'] === 'aal2') {
                mfaEnabled = true
            }
        }
    }

    const formInitValues = {
        loginName: props.record.loginName.replace('@' + loginNameOrg, ''),
        displayName: props.record.displayName,
        role: props.record.role,
        temporary: props.record.temporary,
        expiredAt: !props.record.temporary ? undefined : props.record.expiredAt ? props.record.expiredAt.toDate() : undefined,
        rdpAutoGrant: props.record.rdpAutoGrant,
        rdpEnabled: props.record.rdpEnabled,
        mfaEnabled,
    }

    if (props.record.account && props.record.account.attrsJson) {
        const attrs = JSON.parse(props.record.account.attrsJson);
        Object.keys(attrs).forEach(key => {
            (formInitValues as any)[key] = attrs[key];
        })
    }

    const [flynetLoading, setFlynetLoading] = useState(true);
    const queryFlynet = async () => {
        setFlynetLoading(true);
        try {
            let res = await getFlynet(flynetGeneral.id);
            if (res) {
                setFlynet(res.flynet)

            }
        } catch (err) {
            Notification.error({
                title: formatMessage({ id: 'users.editUser.fetchNetworkInfoFailed' }),
                content: err
            })
        }
        setFlynetLoading(false);

    }
    useEffect(() => {
        queryFlynet()
    }, [])

    const [loading, setLoading] = useState(false);
    const [formApi, SetFormApi] = useState<FormApi<{
        loginName: string;
        displayName: string;
        role: UserRole;
        temporary: boolean;
        expiredAt?: Date;
        rdpAutoGrant: boolean;
        rdpEnabled: boolean;
        mfaEnabled: boolean;
    }>>(
    )


    const handleSubmit = async () => {

        const values = formApi?.getValues();
        await formApi?.validate();
        setLoading(true);

        const fields: { [key: string]: string } = {};

        flynet?.accountAvailableFields.forEach((field: Field, index: number) => {
            let val = '';
            if (values) {
                const value = (values as any)[field.key];

                if (value) {
                    val = getFieldValue(value, field);
                }
            }
            fields[field.key] = val;
        });



        const loginName = values?.loginName + '@' + loginNameOrg;


        flylayerClient.editUser({
            userId: props.record.id,
            loginName: loginName,
            displayName: values?.displayName,
            role: values?.role,
            temporary: values?.temporary,
            expiredAt: values?.expiredAt ? Timestamp.fromDate(values.expiredAt) : undefined,
            fields,
            mfaEnabled: values?.mfaEnabled,
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'users.edit.success' }),
                content: formatMessage({ id: 'users.edit.success' })
            })
            props.success && props.success()
            props.close()
        }).catch((err) => {
            Notification.error({
                title: formatMessage({ id: 'users.edit.failed' }),
                content: err.message
            })
        }).finally(() => {
            setLoading(false);
        })

    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'users.edit.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading }}
            maskClosable={false}
            className='semi-modal'
        ><Skeleton loading={flynetLoading} placeholder={
            <>
                <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title>
                <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />
            </>
        }>
                {flynet && <div className={styles.addUser}>
                    <Form
                        getFormApi={SetFormApi}
                        initValues={formInitValues}
                        validateFields={(values) => {
                            let { loginName, password, displayName } = values;
                            loginName = loginName ? loginName.trim() : loginName;
                            password = password ? password.trim() : password;
                            displayName = displayName ? displayName.trim() : displayName;

                            let errors: { [key: string]: string } = {};
                            validateFields(errors, values, flynet.accountAvailableFields);
                            if (!loginName) {
                                errors.loginName = formatMessage({ id: 'users.editUser.loginNameRequired' });
                            }

                            if (!displayName) {
                                errors.displayName = formatMessage({ id: 'users.editUser.displayNameRequired' });
                            }
                            if (Object.keys(errors).length > 0) {
                                return errors;
                            }
                            return '';
                        }}
                    >
                        {({ formState, values }) => (
                            <React.Fragment>
                                <Row gutter={16}>
                                    <Col span={12}>
                                        <Form.Input
                                            field='loginName'
                                            label={formatMessage({ id: 'users.editUser.loginName' })}
                                            name='loginName'
                                            suffix={`@${loginNameOrg}`}
                                            required></Form.Input>
                                    </Col>
                                    <Col span={12}>
                                        <Form.Input
                                            field='displayName'
                                            label={formatMessage({ id: 'users.editUser.displayName' })}
                                            name='displayName'
                                            required
                                        ></Form.Input>
                                    </Col>
                                </Row>

                                {flynet && <Row gutter={16}>
                                    {renderFormElement(flynet.accountAvailableFields)}
                                </Row>}
                                <Divider></Divider>
                                <Row gutter={16}>
                                    <Col span={5}>

                                        <Form.Switch field='temporary' label={formatMessage({ id: 'users.editUser.accountExpiry' })}></Form.Switch>

                                    </Col>

                                    <Col span={7}>
                                        {formState.values.temporary && <Form.DatePicker field='expiredAt' label={formatMessage({ id: 'users.editUser.expiryTime' })} style={{ width: 130 }}></Form.DatePicker>}
                                    </Col>
                                    <Col span={12}>
                                        <Form.Switch label={formatMessage({ id: 'users.editUser.forceMFA' })} field='mfaEnabled' />
                                    </Col>
                                </Row>

                                <Divider></Divider>
                                <Row>
                                    <Col span={24}>
                                        <Form.RadioGroup label={formatMessage({ id: 'users.editUser.role' })} field='role' direction="vertical"  >
                                            <Form.Radio value={UserRole.FLYNET_ADMIN}
                                                extra={formatMessage({ id: 'users.editUser.adminDescription' })} aria-label={formatMessage({ id: 'users.editUser.admin' })} name="role">
                                                {formatMessage({ id: 'users.editUser.admin' })}
                                            </Form.Radio>
                                            <Form.Radio value={UserRole.FLYNET_USER}
                                                checked
                                                extra={formatMessage({ id: 'users.editUser.userDescription' })} aria-label={formatMessage({ id: 'users.editUser.user' })} name="role">
                                                {formatMessage({ id: 'users.editUser.user' })}
                                            </Form.Radio>

                                        </Form.RadioGroup>

                                    </Col>
                                </Row>
                                <Divider></Divider>
                                {/* <Row>

                                    <Col span={24}><Form.Switch style={{ marginRight: 40 }} labelWidth={100} labelPosition='left' label={formatMessage({ id: 'users.editUser.enableRDP' })} field='rdpEnabled' />
                                    </Col>

                                </Row> */}

                                {/* <Row>


                                    <Col span={24}><Form.Switch labelPosition='left' label={formatMessage({ id: 'users.editUser.autoGrant' })} labelWidth={100}
                                        extraTextPosition='middle' extraText={formatMessage({ id: 'users.editUser.autoGrantDescription' })}
                                        field='rdpAutoGrant' disabled={!values.rdpEnabled} /></Col>
                                </Row> */}
                            </React.Fragment>
                        )}



                    </Form>
                </div>}
            </Skeleton>


        </Modal>


    </>


}

export default Index;