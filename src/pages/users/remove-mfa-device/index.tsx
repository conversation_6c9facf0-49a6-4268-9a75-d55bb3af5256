import { FC, useState, useContext } from 'react'
import { Typography, Modal, List, Notification, TabPane, Input } from '@douyinfe/semi-ui';
import { User, MFAToolType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
const { Paragraph, Title } = Typography;
import UserDevice from '@/components/user-device';

interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');

    
    const handleSubmit = () => {
        setLoading(true);
            flylayerClient.removeMFADevice({
                flynetId: flynet.id,
                userId: props.record.id,
                type: MFAToolType.TOTP
            }).then(()=>{
                Notification.success({ content: formatMessage({ id: 'users.removeMfaDevice.success' }), position: "bottomRight" })
                
                if(props.success) {
                    props.success();
                }
                setLoading(false);
            }).catch(err => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'users.removeMfaDevice.failed' }), position: "bottomRight" })
            }).finally(()=>{
                setLoading(false);
            })
        
    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'users.removeMfaDevice.title' })}
            visible={true}
            onOk={handleSubmit}
            
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                disabled: props.record.loginName != confirmVal,
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'> 
            {formatMessage({ id: 'users.removeMfaDevice.content' }).replace('{name}', props.record.displayName)}
            </Paragraph>

            <Paragraph className='mb20'> {formatMessage({ id: 'users.removeMfaDevice.confirmText' }).replace('{loginName}', props.record.loginName)}
            </Paragraph>
            <Input value={confirmVal} onChange={(val)=>setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
