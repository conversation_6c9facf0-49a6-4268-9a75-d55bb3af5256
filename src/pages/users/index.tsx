import React, { useState, useEffect, useContext } from 'react'
import { Typography, Table, Space, Tag, Spin, BackTop, Row, Col, Button, Dropdown } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';
import Del from './del';
import EditRdp from './edit-rdp';
import EditRole from './edit-role';
import Suspend from './suspend';
import AddUserToGroup from './add-user-to-group';
import Add from './add-direct';
import Password from './password';
import EditUserGroup from './edit-user-group';
import EditExpiresAt from './edit-expires-at';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import EditUser from './edit-user';
import { getQueryParam } from '@/utils/query';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import InfiniteScroll from 'react-infinite-scroll-component';
import { UserProfileContext } from '@/hooks/useUserProfile';
import Unlock from './unlock';

import BatchAddGroup from './batch-add-group';
import BatchDel from './batch-del';
import BatchSuspend from './batch-suspend';
import BatchEditRole from './batch-edit-role';
import BatchEditRdp from './batch-edit-rdp';
import BatchEditExpiresAt from './batch-edit-expires-at';
import BatchEditMfa from './batch-edit-mfa';
import EditMfa from './edit-mfa';
import RemoveMFADevice from './remove-mfa-device';
import SearchFilter from '@/components/search-filter-combo';

import ExportUser from './data-export/export';
import ImportUser from './data-import/import/import';

import useTable, { UserFilter } from './useTable';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import { useLocale } from '@/locales';
import { LicenseContext } from '@/hooks/useLicense';
import { getRadioEntitlementVal } from '@/utils/common';
const { Title, Paragraph, Text } = Typography;





const getUserFilter = (location: Location): UserFilter => {
    const keywords: string = getQueryParam('keywords', location) as string;
    const status: string = getQueryParam('status', location) as string;
    const roleQuery: string = getQueryParam('roles', location) as string;
    const groupQuery: string = getQueryParam('groups', location) as string;
    const temporary: string = getQueryParam('temporary', location) as string;


    return {
        keywords: keywords || '',
        status: status == 'enable' || status == 'disable' ? status : '',
        roles: roleQuery,
        groups: groupQuery,
        temporary: temporary == 'true' || temporary == 'false' ? temporary : ''
    }

}
const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const userProfile = useContext(UserProfileContext);
    const location = useLocation();
    const navigate = useNavigate();
    const license = useContext(LicenseContext);
    const entitlementAllowImportExport = getRadioEntitlementVal('import_export', license.entitlements);


    const initFilter: UserFilter = getUserFilter(location);


    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        if (location.search == '' && search != '') {
            setFilterParam(initFilter);
        }
        setSearch(location.search);
    }, [location])

    const { columns, loading, data, curUser, setCurUser,
        onRowChange, selectedRowKeys, setSelectedRowKeys, selectedUsers, setSelectedUsers, handleRowClick,
        editRDPVisible, setEditRDPVisible, addVisible, setAddVisible, delVisible, setDelVisible, suspendVisible, setSuspendVisible, editRoleVisible, setEditRoleVisible, setReloadFlag, filter: filterParam, setFilter: setFilterParam, page, setPage, pageSize, addPage, total, handleSort, addGroupVisible, setAddGroupVisible, flynet,
        password, setPassword,
        passwordVisible, setPasswordVisible,
        expires_at_visible, setExpiresAtVisible,
        editVisible, setEditVisible,
        editGroupVisible,
        setEditGroupVisible,
        mfaVisible,
        setMfaVisible,
        removeMFADeviceVisible,
        setRemoveMFADeviceVisible,
        unlockVisible,
        setUnlockVisible,
        filter,
        filterParams,
        setFilterParams,
        handleFilterChange,
        doNavigate
    } = useTable(initFilter);

    const getBatchUserRole = (users: User[]): UserRole | undefined => {
        let role: UserRole | undefined;
        if (users.length > 0) {
            role = users[0].role
            for (let i = 1; i < users.length; i++) {
                if (role != users[i].role) {
                    return undefined;
                }
            }
        }
        return role;
    }

    const [batchAddGroupVisible, setBatchAddGroupVisible] = useState(false);
    const [batchDelVisible, setBatchDelVisible] = useState(false);
    const [batchSuspendVisible, setBatchSuspendVisible] = useState(false);
    const [batchSuspendDisable, setBatchSuspendDisable] = useState(false);
    const [batchEditRoleVisible, setBatchEditRoleVisible] = useState(false);
    const [batchEditRDPVisible, setBatchEditRDPVisible] = useState(false);
    const [batchExpiresAtVisible, setBatchExpiresAtVisible] = useState(false);

    const [batchEnableUsers, setBatchEnableUsers] = useState<User[]>();
    const [batchDisableUsers, setBatchDisableUsers] = useState<User[]>();
    const [batchDelUsers, setBatchDelUsers] = useState<User[]>();

    const [batchMfaVisible, setBatchMfaVisible] = useState(false);

    const [importUserVisible, setImportUserVisible] = useState(false);
    const [exportUserVisible, setExportUserVisible] = useState(false);

    useEffect(() => {
        if (selectedUsers && selectedUsers.length > 0) {
            let enableUsers: User[] = [];
            let disableUsers: User[] = [];
            let delUsers: User[] = [];
            selectedUsers.forEach(user => {

                const isSelf = user.loginName == userProfile.identity?.traits?.email;
                if (!isSelf) {
                    if (user.disabled) {
                        enableUsers.push(user);
                    } else {
                        disableUsers.push(user);
                    }
                    delUsers.push(user);
                }
            })
            setBatchEnableUsers(enableUsers);
            setBatchDisableUsers(disableUsers);
            setBatchDelUsers(delUsers);
        } else {
            setBatchEnableUsers([]);
            setBatchDisableUsers([]);
            setBatchDelUsers([]);
        }
    }, [selectedUsers]);


    return <>
        <div className='general-page'>
            <Row>
                <Col sm={24} md={20}>
                    <Title heading={3} className='mb10'>{formatMessage({ id: 'users.index.title' })}</Title>
                </Col>
                <Col sm={24} md={4}>
                    <div className='btn-right-col mb10'>
                        <Space>
                            {entitlementAllowImportExport && <><Button
                                onClick={() => navigate(`${BASE_PATH}/users/export/`)}
                            >{formatMessage({ id: 'users.index.button.dataExport' })}</Button>
                                <Button
                                    onClick={() => navigate(`${BASE_PATH}/users/import/`)}
                                >{formatMessage({ id: 'users.index.button.dataImport' })}</Button></>}




                            <Button
                                onClick={() => navigate(`${BASE_PATH}/users/group/`)}>{formatMessage({ id: 'users.breadcrumb.userGroups' })}</Button>
                            {flynet && flynet.accountManualCreate && <Button theme='solid'
                                onClick={() => setAddVisible(true)}>{formatMessage({ id: 'users.index.button.addUser' })}</Button>
                            }
                        </Space>
                    </div>
                </Col>
            </Row>
            <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'users.index.description' })}</Paragraph>

            <SearchFilter onChange={(val: string, filterParam) => {
                handleFilterChange({ ...filter, [filterParam.name]: val })
                doNavigate({ ...filter, [filterParam.name]: val });


                const newFilterParams = filterParams.map((item) => {
                    if (item.name == filterParam.name) {
                        item.value = val;
                    }
                    return item;
                })
                setFilterParams(newFilterParams);
            }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>

            <div className='mb10'>
                <Row>
                    <Col span={12} style={{ height: 32, lineHeight: '32px' }}>
                        <Space>
                            <Tag size='large'>{formatMessage({ id: 'users.index.totalCount' })} {loading ? <Spin size='small' /> : total}</Tag>
                            {selectedUsers && selectedUsers.length > 0 && <Tag size='large'>{formatMessage({ id: 'users.index.selectedCount' })} {selectedUsers.length} {formatMessage({ id: 'users.index.users' })}</Tag>}
                        </Space>
                    </Col>
                    <Col span={12}>
                        <div className='btn-right-col'>
                            <Space>

                                <Button onClick={() => setBatchAddGroupVisible(true)} disabled={!selectedUsers || selectedUsers.length == 0}>{formatMessage({ id: 'users.batch.addToGroup' })}</Button>
                                <Button onClick={() => setBatchDelVisible(true)} disabled={!batchDelUsers || batchDelUsers.length == 0}>{formatMessage({ id: 'users.batch.delete' })}</Button>
                                <Dropdown position='bottomRight'
                                    render={<Dropdown.Menu>
                                        <Dropdown.Item onClick={() => {
                                            setBatchSuspendVisible(true)
                                            setBatchSuspendDisable(true)
                                        }} disabled={!batchDisableUsers || batchDisableUsers.length == 0}>{formatMessage({ id: 'users.batch.suspend' })}</Dropdown.Item>
                                        <Dropdown.Item onClick={() => {
                                            setBatchSuspendVisible(true)
                                            setBatchSuspendDisable(false)
                                        }} disabled={!batchEnableUsers || batchEnableUsers.length == 0}>{formatMessage({ id: 'users.batch.enable' })}</Dropdown.Item>
                                        <Dropdown.Divider />
                                        <Dropdown.Item onClick={() => setBatchEditRoleVisible(true)} disabled={!selectedUsers || selectedUsers.length == 0}>{formatMessage({ id: 'users.batch.editRole' })}</Dropdown.Item>
                                        <Dropdown.Divider />

                                        <Dropdown.Item onClick={() => setBatchMfaVisible(true)} disabled={!selectedUsers || selectedUsers.length == 0}>{formatMessage({ id: 'users.batch.editMFA' })}</Dropdown.Item>
                                        <Dropdown.Item onClick={() => setBatchExpiresAtVisible(true)} disabled={!selectedUsers || selectedUsers.length == 0}>{formatMessage({ id: 'users.batch.editExpiresAt' })}</Dropdown.Item>
                                    </Dropdown.Menu>}
                                ><Button><IconMore className='align-v-center' /></Button></Dropdown>
                            </Space>
                        </div>
                    </Col>
                </Row>


            </div>
            <InfiniteScroll
                dataLength={data.length} //This is important field to render the next data
                next={addPage}
                hasMore={data.length < total}
                loader={<div><Spin></Spin></div>}
                endMessage={
                    <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                        {data.length > pageSize && <Paragraph type='tertiary'>{formatMessage({ id: 'common.endOfList' })}</Paragraph>}
                    </div>
                }
            >
                <Table
                    rowKey={(record?: User) => record ? record.id + '' : ''}
                    rowSelection={{
                        onChange: onRowChange,
                        selectedRowKeys: selectedRowKeys,
                    }}
                    onRow={(record: any, index: any) => {
                        return {
                            onClick: () => {
                                if (record && index != undefined) {
                                    handleRowClick(record, index)
                                }

                            }
                        }
                    }}
                    onChange={handleSort}
                    empty={<TableEmpty loading={loading} />} columns={columns} loading={loading} dataSource={data} pagination={false} />
            </InfiniteScroll>
            <BackTop style={{ right: 10 }} />

        </div>
        {addVisible ?
            <Add success={(password: string) => {
                setAddVisible(false)
                setReloadFlag(true)
                setPasswordVisible(true)
                setPassword(password)
            }} close={() => { setAddVisible(false) }} /> : null}
        {passwordVisible && <Password keyValue={password} close={() => { setPasswordVisible(false), setPassword('') }} />}
        {delVisible && curUser ?
            <Del record={curUser} success={() => {
                setDelVisible(false)
                setCurUser(undefined)
                setReloadFlag(true)
            }} close={() => { setDelVisible(false); setCurUser(undefined) }} /> : null}
        {editRoleVisible && curUser ?
            <EditRole record={curUser} success={() => {
                setEditRoleVisible(false)
                setCurUser(undefined)
                setReloadFlag(true)
            }} close={() => {
                setEditRoleVisible(false)
                setCurUser(undefined)
            }} /> : null}
        {suspendVisible && curUser ?
            <Suspend record={curUser}
                close={() => { setSuspendVisible(false) }}
                success={() => {
                    setSuspendVisible(false)
                    setReloadFlag(true)
                }}
            /> : null}
        {addGroupVisible && curUser && <AddUserToGroup
            close={() => {
                setAddGroupVisible(false)
                setCurUser(undefined)
            }}
            record={curUser}></AddUserToGroup>}
        {editRDPVisible && curUser && <EditRdp
            close={() => {
                setEditRDPVisible(false)
                setCurUser(undefined)
            }}
            record={curUser}
            success={() => {
                setEditRDPVisible(false)
                setReloadFlag(true)
            }}
        ></EditRdp>}
        {curUser && editGroupVisible && <EditUserGroup
            close={() => {
                setCurUser(undefined)
                setEditGroupVisible(false)
            }}
            record={curUser}
            success={() => {
                setEditGroupVisible(false)
                setReloadFlag(true)
            }}
        ></EditUserGroup>
        }
        {/* {curUser && curGroup && removeUserFromGroupVisible && <RemoveFromGroup

            close={() => {
                setCurUser(undefined)
                setRemoveUserFromGroupVisible(false)
            }}
            record={curUser}
            group={curGroup}
            success={() => {
                setRemoveUserFromGroupVisible(false)
                setReloadFlag(true)
            }}
        ></RemoveFromGroup>} */}
        {
            curUser && expires_at_visible && <EditExpiresAt
                close={() => {
                    setCurUser(undefined)
                    setExpiresAtVisible(false)
                }}
                record={curUser}
                success={() => {
                    setExpiresAtVisible(false)
                    setReloadFlag(true)
                }}
            ></EditExpiresAt>
        }
        {editVisible && curUser && <EditUser
            close={() => {
                setCurUser(undefined)
                setEditVisible(false)
            }}
            record={curUser}
            success={() => {
                setEditVisible(false)
                setReloadFlag(true)
            }}
        ></EditUser>}
        {batchAddGroupVisible && <BatchAddGroup
            close={() => {
                setBatchAddGroupVisible(false)
            }}
            success={() => {
                setBatchAddGroupVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }}
            records={selectedUsers}
        ></BatchAddGroup>}
        {batchDelVisible && <BatchDel
            close={() => {
                setBatchDelVisible(false)
            }}
            success={() => {
                setBatchDelVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setBatchDelUsers([])
                setSelectedRowKeys([])
            }}
            records={batchDelUsers || []}></BatchDel>}
        {batchSuspendVisible && <BatchSuspend
            close={() => {
                setBatchSuspendVisible(false)
            }
            } success={() => {
                setBatchSuspendVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }
            } records={batchSuspendDisable ? batchDisableUsers || [] : batchEnableUsers || []} disabled={batchSuspendDisable}></BatchSuspend>}
        {batchEditRoleVisible && <BatchEditRole
            close={() => {
                setBatchEditRoleVisible(false)
            }}
            success={() => {
                setBatchEditRoleVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }}
            role={getBatchUserRole(selectedUsers)}
            records={selectedUsers}></BatchEditRole>}
        {batchEditRDPVisible && <BatchEditRdp
            close={() => {
                setBatchEditRDPVisible(false)
            }}
            success={() => {
                setBatchEditRDPVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }}
            records={selectedUsers}></BatchEditRdp>}
        {batchExpiresAtVisible && <BatchEditExpiresAt
            close={() => {
                setBatchExpiresAtVisible(false)
            }}
            success={() => {
                setBatchExpiresAtVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])

            }}
            records={selectedUsers}></BatchEditExpiresAt>}
        {mfaVisible && curUser && <EditMfa
            close={() => {
                setCurUser(undefined)
                setMfaVisible(false)
            }
            } record={curUser}
            success={() => {
                setMfaVisible(false)
                setReloadFlag(true)
            }
            }></EditMfa>}
        {batchMfaVisible && <BatchEditMfa
            close={() => {
                setBatchMfaVisible(false)
            }
            } success={() => {
                setBatchMfaVisible(false)
                setReloadFlag(true)
                setSelectedUsers([])
                setSelectedRowKeys([])
            }
            } records={selectedUsers}></BatchEditMfa>}
        {removeMFADeviceVisible && curUser && <RemoveMFADevice
            close={() => {
                setCurUser(undefined)
                setRemoveMFADeviceVisible(false)
            }
            } record={curUser}
            success={() => {
                setRemoveMFADeviceVisible(false)
                setReloadFlag(true)
            }
            }></RemoveMFADevice>}
        {unlockVisible && curUser && <Unlock
            record={curUser}
            close={() => {
                setCurUser(undefined)
                setUnlockVisible(false)
            }}
            success={() => {
                setCurUser(undefined)
                setUnlockVisible(false)
                setReloadFlag(true)
            }}
        ></Unlock>
        }
        {importUserVisible && <ImportUser
            close={() => {
                setImportUserVisible(false)
            }}
            success={() => {
                setImportUserVisible(false)
                setReloadFlag(true)
            }}></ImportUser>}
        {exportUserVisible && <ExportUser
            close={() => {
                setExportUserVisible(false)
            }}></ExportUser>}

    </>
}

export default Index;
