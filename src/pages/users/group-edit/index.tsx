import React, { FC, useEffect, useState, useContext } from 'react'
import { Typography, Modal, Form, Notification, Skeleton, Popover, Button, Row, Col, Divider, Card } from '@douyinfe/semi-ui';
import { User, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { GroupType, DynamicGroupMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { getFlynet } from '@/services/flynet';

import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import TableEmpty from '@/components/table-empty'
import { IconPlus, IconMinusCircle, IconHelpCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import Expressions from '@/components/expressions';
import CodeEditor from '@/components/code-editor';

import UserModalSelector from '@/components/user-modal-selector';
import { BASE_PATH } from '@/constants/router';
import { AttributeTemplate } from '@/interface/attribute-template';
import { validateParamCombo } from '@/utils/common';
import { useLocale } from '@/locales';
const { Paragraph, Text } = Typography;

const { Input, RadioGroup, Radio, InputNumber, Switch } = Form


interface Props {
    close: () => void,
    success?: (userGroup?: UserGroup) => void
    userGroupId: bigint
}



const Index: React.FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [formApi, SetFormApi] = useState<FormApi<{
        name: string,
        description: string,
        alias: string,
        // otherUsers: Array<string>,
        expressionsCombo: string,
        priority: number,
    }>>()

    const [userGroup, setUserGroup] = useState<UserGroup>();

    const [users, setUsers] = useState<User[]>([]);
    const [userSelectorVisible, setUserSelectorVisible] = useState(false);
    const [type, setType] = useState<GroupType>(GroupType.GROUP_STATIC);


    const [expressions, setExpressions] = useState<Array<Expression>>([]);
    const [advancedDynamicMode, setAdvancedDynamicMode] = useState(false);
    const [expressionAdvanced, setExpressionAdvanced] = useState('');

    const [expressionsError, setExpressionsError] = useState(false);

    const [groupLoading, setGroupLoading] = useState(false);


    const [loading, setLoading] = useState(false);

    const [attributeTemplate, setAttributeTemplate] = useState<AttributeTemplate>();

    const queryFlynet = async () => {
        let res = await getFlynet(flynet.id);
        if (res && res.flynet && res.flynet.attributeTemplate) {
                        const json = JSON.parse(res.flynet.attributeTemplate);
                        const userProperties = json.properties.input.properties.User;
        
                        let attributeTemplate: AttributeTemplate = {
                            type: 'object',
                            title: 'properties',
                            description: 'properties',
                            properties: {
                                'input': {
                                    type: 'object',
                                    description: formatMessage({ id: 'users.groupAdd.input' }),
                                    title: formatMessage({ id: 'users.groupAdd.input' }),
                                    properties: {
                                        User: userProperties
                                    }
                                }
                            }
        
                        }

            setAttributeTemplate(attributeTemplate);
        }
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    const handleSubmit = async () => {
        await formApi?.validate();

        const values = formApi?.getValues();
        if (!values) {
            return;
        }


        const name = values.name.trim();
        const description = values.description ? values.description.trim() : '';
        const alias = values.alias ? values.alias.trim() : '';
        // const type = values.type;
        // const otherUsers = values.otherUsers;
        const expressionsCombo = values.expressionsCombo;
        const priority = values.priority;

        if (type == GroupType.GROUP_DYNAMIC) {
            if (advancedDynamicMode) {
                if (expressionAdvanced.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            } else {
                if (expressionsError || expressions.length == 0) {
                    setExpressionsError(true)
                    return;
                }
            }
        }

        setLoading(true);

        flylayerClient.updateUserGroup({
            flynetId: flynet.id,
            groupId: props.userGroupId,
            name: name,
            description: description,
            alias: alias,
            // type: type,
            users: users,
            priority: priority,
            attrs: advancedDynamicMode ? {
                // otherUsers: otherUsers,
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_ADVANCED,
                expressionAdvanced: expressionAdvanced
            } : {
                // otherUsers: otherUsers,
                dynamicGroupMode: DynamicGroupMode.DYNAMIC_GROUP_STANDARD,
                expressions: expressions,
                expressionsCombo: expressionsCombo
            }
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'users.group.edit.success' })
            });
            props.success && props.success();
            props.close();
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'users.group.edit.failed' }),
                content: err.message
            });
        }).finally(() => {
            setLoading(false);
        })
    }

    useEffect(() => {
        setGroupLoading(true);
        flylayerClient.getUserGroup({
            groupId: props.userGroupId
        }).then(res => {
            setUserGroup(res.group);
            if (res.group && res.group.users) {
                setUsers(res.group?.users)
                setType(res.group.type);

                setExpressions(res.group.attrs?.expressions ? res.group.attrs.expressions : []);
                setAdvancedDynamicMode(res.group.attrs?.dynamicGroupMode == DynamicGroupMode.DYNAMIC_GROUP_ADVANCED ? true : false);
                setExpressionAdvanced(res.group.attrs?.expressionAdvanced ? res.group.attrs.expressionAdvanced : '')

            }

            setGroupLoading(false);
        }).catch(err => {
            Notification.error({
                title: formatMessage({ id: 'users.group.edit.getFailed' }),
                content: err.message
            });
            setGroupLoading(false);
        })
    }, [])

    return <>
        <Modal
            width={800}
            title={formatMessage({ id: 'users.group.edit.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading: loading }}
            className='semi-modal'
            maskClosable={false}
        >

            <Skeleton loading={groupLoading} placeholder={
                <>
                    <Skeleton.Title style={{ marginBottom: 60, height: 30 }}></Skeleton.Title>
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 42, marginBottom: 50 }} />
                    <Skeleton.Image style={{ height: 230, marginBottom: 20 }} />

                </>
            }>
                {userGroup && <div className={styles.addService}>
                    <Form getFormApi={SetFormApi}
                        allowEmpty
                        initValues={
                            {
                                name: userGroup.name,
                                description: userGroup.description,
                                alias: userGroup.alias,
                                type: userGroup.type,
                                // otherUsers: userGroup.attrs && userGroup.attrs.otherUsers ? userGroup.attrs.otherUsers : [],
                                advancedDynamicMode: userGroup.attrs?.dynamicGroupMode == DynamicGroupMode.DYNAMIC_GROUP_ADVANCED ? true : false,
                                expressionsCombo: userGroup.attrs && userGroup.attrs.expressionsCombo ? userGroup.attrs.expressionsCombo : '',
                                priority: userGroup.priority,
                            }
                        }
                    >
                        <Row gutter={12}>
                            <Col span={12}>
                                <Input field='alias' label={formatMessage({ id: 'users.group.field.name' })} validate={value => {
                                    if (!value) {
                                        return formatMessage({ id: 'users.group.validation.nameRequired' });
                                    }
                                    return '';
                                }} />
                            </Col>
                            <Col span={12}>
                                <Input field='name'
                                    label={<>{formatMessage({ id: 'users.group.field.code' })} <Popover content={<div className='p10'>{formatMessage({ id: 'users.group.field.code.help' })}</div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></>}
                                    trigger={'blur'} readonly validate={value => {
                                        if (!value) {
                                            return formatMessage({ id: 'users.group.validation.codeRequired' });
                                        }

                                        if (value.trim().startsWith('-')) {
                                            return formatMessage({ id: 'users.group.validation.codeInvalidStart' })
                                        }
                                        if (!/^[\dA-Za-z-]+$/.test(value.trim())) {
                                            return formatMessage({ id: 'users.group.validation.codeInvalidFormat' });
                                        }
                                        return '';
                                    }}
                                    required />
                            </Col>

                        </Row>
                        <Row>
                            <Col span={24}>
                                <Input field='description' label={formatMessage({ id: 'users.group.field.description' })} />
                            </Col>
                        </Row>
                        <Divider className='mb10'></Divider>
                        <Row>
                            <Col span={16}>

                                <RadioGroup fieldStyle={{paddingTop:10, paddingBottom:10}} field='type' label={formatMessage({ id: 'users.group.field.type' })}>
                                    <Radio disabled={type == GroupType.GROUP_DYNAMIC} checked={type == GroupType.GROUP_STATIC} value={GroupType.GROUP_STATIC}>{formatMessage({ id: 'users.group.type.static' })}</Radio>
                                    <Radio disabled={type == GroupType.GROUP_STATIC} checked={type == GroupType.GROUP_DYNAMIC} value={GroupType.GROUP_DYNAMIC}>{formatMessage({ id: 'users.group.type.dynamic' })}</Radio>
                                </RadioGroup>
                            </Col>

                            {/* <Col span={8} className={styles.rightColumn} style={{ paddingTop: 24, display: type == GroupType.GROUP_DYNAMIC ? '' : 'none' }}>
                                {type == GroupType.GROUP_DYNAMIC &&
                                    <Switch field='advancedDynamicMode' onChange={(checked) => {
                                        setAdvancedDynamicMode(checked)
                                    }} label={formatMessage({ id: 'users.groupAdd.expertMode' })} labelPosition='left' ></Switch>
                                }
                            </Col> */}
                        </Row>
                        {/* <Divider className='mb10'></Divider> */}
                        {type == GroupType.GROUP_STATIC && <>
                            <Row className="mb20">
                                <Col span={20}>
                                    <Text type='tertiary'>{formatMessage({ id: 'users.group.builtinUsers' })}</Text>
                                </Col>
                                <Col span={4} className={styles.rightColumn}>
                                    <Button
                                        onClick={() => {
                                            setUserSelectorVisible(true);
                                        }}
                                        icon={<IconPlus></IconPlus>}></Button>
                                </Col>
                            </Row>
                            {
                                users.length == 0 ? <TableEmpty loading={false}></TableEmpty> :

                                    <>
                                        {users.map((item, index) => {

                                            return <Row className="mb10" key={index}>
                                                <Col span={20}>
                                                    {item.displayName}({item.loginName})
                                                </Col>
                                                <Col span={4} className={styles.rightColumn}>
                                                    <Button
                                                        type='danger'
                                                        onClick={() => {
                                                            let newUsers = users.filter((item, i) => i != index);
                                                            setUsers(newUsers);
                                                        }}
                                                        icon={<IconMinusCircle></IconMinusCircle>}></Button>

                                                </Col>
                                            </Row>
                                        })}
                                    </>
                            }

                            {/* <Divider></Divider> */}
                            {/* <Row className='mb20'>
                                <Col span={24}>
                                    <TagInput label={formatMessage({ id: 'users.groupAdd.otherUsers' })} maxTagCount={5} field='otherUsers' extraText={formatMessage({ id: 'users.groupAdd.otherUsersHint' })} addOnBlur></TagInput>
                                </Col>
                            </Row> */}
                        </>}
                        {type == GroupType.GROUP_DYNAMIC && attributeTemplate && <>
                            {
                                advancedDynamicMode ? <>
                                    <CodeEditor value={expressionAdvanced} onChange={(value) => setExpressionAdvanced(value || '')} height='280px' language='systemverilog'></CodeEditor>
                                    {expressionsError && <Paragraph type='danger'>{formatMessage({ id: 'users.group.validation.expressionRequired' })}</Paragraph>}
                                </> : <>
                                    <Expressions
                                        expressions={expressions}
                                        onChange={(expressions: Array<Expression>) => {
                                            setExpressions(expressions);
                                            setExpressionsError(false);
                                        }}
                                        onError={() => {
                                            setExpressionsError(true);
                                        }}
                                        attributeTemplate={attributeTemplate}
                                    ></Expressions>
                                    {expressionsError && <Paragraph type='danger'>{formatMessage({ id: 'users.group.validation.parameterError' })}</Paragraph>}
                                    <Row>
                                        <Col span={24}>
                                            <Input
                                                validate={ value => (validateParamCombo(value, expressions.length))}
                                                extraText={formatMessage({ id: 'users.group.field.expressionsCombo.help' })}
                                                field='expressionsCombo' label={formatMessage({ id: 'users.group.field.expressionsCombo' })} />
                                        </Col>
                                    </Row>
                                </>
                            }
                        </>}
                        {type == GroupType.GROUP_DYNAMIC && !attributeTemplate && <Card>
                            <Paragraph style={{ textAlign: 'center' }}>{formatMessage({ id: 'users.group.dynamic.emptyAttributes' })}
                                <a className='link-external' target='_blank' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>
                                    {formatMessage({ id: 'users.group.dynamic.goToSettings' })}<IconArrowUpRight />
                                </a>

                            </Paragraph>

                        </Card>}

                    </Form>
                </div>}
            </Skeleton>

        </Modal>
        {
            userSelectorVisible && <UserModalSelector
                multi={true}
                value={users}
                onChange={(value: User | User[]) => {
                    setUserSelectorVisible(false)

                    let newUsers = users.filter((item) => true);
                    if (value instanceof Array) {
                        value.forEach((item) => {
                            if (!newUsers.some(u => u.id == item.id)) {
                                newUsers.push(item);
                            }
                        })
                    } else {
                        if (!newUsers.some(u => u.id == value.id)) {
                            newUsers.push(value);
                        }
                    }
                    setUsers(newUsers);
                }}
                close={() => setUserSelectorVisible(false)}
            ></UserModalSelector>
        }
    </>
}

export default Index;
