import { FC, useState, useContext } from 'react'
import { Typography, Modal, Notification, List } from '@douyinfe/semi-ui';

import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';

import styles from './index.module.scss';
import { Link } from 'react-router-dom';
const { Paragraph, Title } = Typography;
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import UserDevice from '@/components/user-device';
import UserList from '@/pages/users/components/user-list';
import { useLocale } from '@/locales';

interface Props {
    close: () => void,
    success?: () => void,
    records: User[],
    disabled: boolean,
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [loading, setLoading] = useState(false);
    const [users, setUsers] = useState<User[]>(props.records);
    const handleOK = () => {

        setLoading(true);
        flylayerClient.batchSetUserStates({
            flynetId: flynetGeneral.id,
            userIds: users.map((item) => {
                return item.id;
            }),
            disabled: props.disabled
        }).then(() => {
            Notification.success({ content: props.disabled ? formatMessage({ id: 'users.suspend.enableSuccess' }) : formatMessage({ id: 'users.suspend.disableSuccess' }), position: "bottomRight" })
            if (props.success) {
                props.success();
            }
        }).catch(err => {
            console.error(err);
            Notification.error({ 
                
                title: props.disabled ? formatMessage({ id: 'users.suspend.enableFailed' }) : formatMessage({ id: 'users.suspend.disableFailed' }),
                content: err.message, position: "bottomRight" })
        }).finally(() => {
            setLoading(false);
        });

       
        
    }
    let optionName = '';
    if (!props.disabled) {
        optionName = formatMessage({ id: 'users.suspend.enable' })
    } else {
        optionName = formatMessage({ id: 'users.suspend.disable' })
    }

    return <>
        <Modal
            width={500}
            title={optionName + formatMessage({ id: 'users.index.users' })}
            visible={true}
            okButtonProps={{ loading, type:'danger', disabled: users.length == 0}}
            onOk={handleOK}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>
                {formatMessage({ id: 'users.batch.suspend.confirm' }).replace('{action}', optionName)}
                {formatMessage({ id: 'users.batch.suspend.description' }).replace('{action}', props.disabled ? formatMessage({ id: 'users.batch.suspend.resume' }) : formatMessage({ id: 'users.batch.suspend.pause' }))}
            </Paragraph>
            <UserList className='mb20' users={users} onChange={(list)=>setUsers(list)} />

            
        </Modal></>
}
export default Index;
