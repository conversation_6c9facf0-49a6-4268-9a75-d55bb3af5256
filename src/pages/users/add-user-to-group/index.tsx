import { FC, useState, useContext, useEffect, useCallback } from 'react'
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { Modal, Typography, Notification, CheckboxGroup, Checkbox } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { ACLPolicy, ACL, SSHRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

import { ListValue, Value } from '@bufbuild/protobuf';
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [groups, setGroups] = useState<{ name: string, values: string[] }[]>([]);
    const [opts, setOpts] = useState<string[]>();
    const [values, setValues] = useState<string[]>();


    const calInitValues = (policy: ACLPolicy) => {
        let groups: { name: string, values: string[] }[] = [];
        let opts:string[] = [];
        let vals:string[] = [];
        Object.keys(policy.groups).forEach(key => {
            const values = policy.groups[key].values.map((item) => {
                return item.kind.value + ''
            })
            if (values.length == 1 && values[0] == '*') {

            } else {
                opts.push(key);
                if(values.indexOf(props.record.loginName)>=0)  {
                    vals.push(key)
                }
                groups.push({
                    name: key, values: values
                })
            }
        })

        setOpts(opts)
        setValues(vals)


        return groups;
    }


    const [aclPolicy, setACLPolicy] = useState<ACLPolicy>();
    const [aclPolicyLoading, setACLPolicyLoading] = useState(false);
    const flynet = useContext(FlynetGeneralContext);
    const [loading, setLoading] = useState(false);

    const handleSubmit = () => {
        let new_groups: { [key: string]: ListValue } = {};
        if(aclPolicy) {
            Object.keys(aclPolicy?.groups).forEach(key => {
                
            })
        }
    }



    const getACLPolicy = useCallback(() => {
        setACLPolicyLoading(true);
        flylayerClient.getACLPolicy({
            flynetId: flynet.id
        }).then(res => {
            if (res.policy) {
                setACLPolicy(res.policy);
                setGroups(calInitValues(res.policy))
            }
        }).catch(err => {
            console.error(err);

            Notification.error({ content: formatMessage({ id: 'users.addToGroup.getACLFailed' }), position: "bottomRight" })
        }).finally(() => {
            setACLPolicyLoading(false);
        })
    }, [])

    useEffect(() => {
        getACLPolicy();
    }, [])

    return <>
        <Modal
            visible={true}
            width={500}
            title={formatMessage({ id: 'users.addToGroup.title' })}
            okButtonProps={{ loading }}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'users.addToGroup.description' }).replace('{user}', `${props.record.displayName}(${props.record.loginName})`)}</Paragraph>
            <CheckboxGroup options={opts} value={values} onChange={val=>setValues(val)} direction='vertical'>
                {/* {
                    groups.map((value, index) => {

                        return <Checkbox
                            checked={value.values.indexOf(props.record.loginName) >= 0}
                            value={value.name} key={index}>
                            {value.name}
                        </Checkbox>
                    })
                } */}

            </CheckboxGroup>
        </Modal>
    </>
}

export default Index;