import React, { useState, useContext, useEffect } from 'react'
import { Modal, Divider, Notification } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { DNSConfig } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb";
import { useLocale } from '@/locales';

import DomainManager from '@/components/domain-manager';
import DnsManager from '@/components/dns-manager';
import RelayMapManager from '@/components/relay-map-manager';

interface Props {
    groupId: bigint;
    groupName: string;
    close: () => void;
    success: () => void;
}

const Index: React.FC<Props> = ({ groupId, groupName, close, success }) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const [records, setRecords] = useState<Array<Record>>([]);
    const [dnsConfig, setDnsConfig] = useState<DNSConfig>();
    const [relayMapValue, setRelayMapValue] = useState<Uint8Array>();

    const [recordsSaveLoading, setRecordsSaveLoading] = useState(false);
    const [dnsConfigSaveLoading, setDnsConfigSaveLoading] = useState(false);
    const [relayMapSaveLoading, setRelayMapSaveLoading] = useState(false);

    const queryRelayMap = (groupId: bigint) => {
        flylayerClient.getUserGroupRelayMap({
            groupId: groupId,
            flynetId: flynet.id
        }).then(res => {
            setRelayMapValue(res.value);
        }).catch(err => {
            Notification.error({
                content: formatMessage({ id: 'users.groupDNS.getRelayFailed' })
            });
        });
    }

    const queryDNSConfig = (groupId: bigint) => {
        flylayerClient.getUserGroupDNSConfig({
            groupId: groupId,
            flynetId: flynet.id
        }).then(res => {
            setDnsConfig(res.config);
            setRecords(res.config?.extraRecords || []);
        }).catch(err => {
            Notification.error({
                content: formatMessage({ id: 'users.groupDNS.getDNSFailed' })
            });
        });
    };

    useEffect(() => {
        queryRelayMap(groupId);
        queryDNSConfig(groupId);
    }, [groupId]);


    return <><Modal
        width={800}
        title={formatMessage({ id: 'users.groupDNS.title' }).replace('{group}', groupName)}
        visible={true}
        onCancel={close}
        footer={null}
        className='semi-modal'
        maskClosable={false}
    >
        <DomainManager
            records={records}
            flynetId={flynet.id}

            onSave={(records) => {
                setRecordsSaveLoading(true);
                setRecords(records);
                const config = new DNSConfig(
                    {
                        ...dnsConfig,
                        extraRecords: records
                    });
                setDnsConfig(config);

                flylayerClient.setUserGroupDNSConfig({
                    flynetId: flynet.id,
                    groupId: groupId,
                    config: config
                }).then(() => {
                    Notification.success({
                        content: formatMessage({ id: 'common.saveSuccess' })
                    });
                }
                ).catch(err => {
                    Notification.error({
                        content: formatMessage({ id: 'common.saveFailed' })
                    });
                }).finally(() => {
                    setRecordsSaveLoading(false);
                });


            }} />
        <Divider className='mb20' />
        {dnsConfig && <DnsManager
            dnsConfig={dnsConfig}
            saveLoading={dnsConfigSaveLoading}
            flynetId={flynet.id}
            onSave={(config) => {
                setDnsConfig(config);
                setDnsConfigSaveLoading(true);

                flylayerClient.setUserGroupDNSConfig({
                    groupId: groupId,
                    flynetId: flynet.id,
                    config: config
                }).then(() => {
                    Notification.success({
                        content: formatMessage({ id: 'common.saveSuccess' })
                    });
                }).catch((err) => {
                    console.error(err)
                    Notification.error({
                        title: formatMessage({ id: 'users.groupDNS.saveRelayFailed' }),
                        content: err.message,
                        position: "bottomRight"
                    })
                }).finally(() => {
                    setDnsConfigSaveLoading(false);
                });
            }} />}
        <Divider className='mb20' />
        {relayMapValue && <RelayMapManager
            value={relayMapValue}
            saveLoading={relayMapSaveLoading}
            flynetId={flynet.id}
            onSave={(value) => {
                setRelayMapValue(value);
                setRelayMapSaveLoading(true);

                flylayerClient.setUserGroupRelayMap({
                    groupId: groupId,
                    flynetId: flynet.id,
                    value: value
                }).then(() => {
                    Notification.success({
                        content: formatMessage({ id: 'common.saveSuccess' })
                    });
                }).catch(err => {
                    Notification.error({
                        content: formatMessage({ id: 'common.saveFailed' })
                    });
                }).finally(() => {
                    setRelayMapSaveLoading(false);
                });
            }} />}
            <div style={{height: 20}}></div>
    </Modal>
    </>
}

export default Index
