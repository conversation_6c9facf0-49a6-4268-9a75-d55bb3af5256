import { FC, useEffect, useState, useContext } from 'react'
import { Typography, Modal, Notification, Banner, Row, Col, Button, Divider, Skeleton } from '@douyinfe/semi-ui';
import { User, UserAttemptLockedList, UserAttemptLocked } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import DateFormat from '@/components/date-format';
import styles from './index.module.scss';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const [loading, setLoading] = useState(true);

    const handleSubmit = () => { };

    const [passwordLockedList, setPasswordLockedList] = useState<UserAttemptLocked[]>([]);
    const [totpLockedList, setTotpLockedList] = useState<UserAttemptLocked[]>([]);

    const [unlockType, setUnlockType] = useState<'password' | 'totp' | ''>('')
    const [unlockIndex, setUnlockIndex] = useState<number>(-1);

    const query = () => {
        setTotpLockedList([]);
        setPasswordLockedList([]);
        setLoading(true);
        flylayerClient.listUserAttemptLocked({
            userId: props.record.id,
            flynetId: flynet.id
        }).then((res) => {

            if (res.attempts['password']) {
                setPasswordLockedList(res.attempts['password'].items)
            }
            if (res.attempts['totp']) {
                setTotpLockedList(res.attempts['totp'].items)
            }

        }).catch((err) => {
            console.log(err)
        }).finally(() => {
            setLoading(false)
        })
    }

    useEffect(() => {
        query();
    }, [])



    return <><Modal
        width={600}
        title={formatMessage({ id: 'users.unlock.title' })}
        visible={true}
        onOk={handleSubmit}
        footer={null}
        onCancel={() => props.close()}
        closeOnEsc={true}
        okButtonProps={{
            loading
        }}
        maskClosable={false}
    >
        <Skeleton loading={loading} placeholder={
            <Skeleton.Image style={{ width: '100%', height: 200 }} />
        }>
            {
                (!passwordLockedList || passwordLockedList.length == 0) && (!totpLockedList || totpLockedList.length == 0) ?
                    <div style={{ paddingBottom: 40, paddingTop: 40 }}><Banner
                        type="info"
                        closeIcon={null}
                        description={formatMessage({ id: 'users.unlock.notLocked' })}
                    /></div>
                    :
                    <>
                        {passwordLockedList && passwordLockedList.length > 0 && <><Paragraph type='tertiary' className='mb10'>{formatMessage({ id: 'users.unlock.passwordLocked' })}</Paragraph>
                            <Row className='tableTitle'>
                                <Col span={8}>{formatMessage({ id: 'users.unlock.device' })}</Col>
                                <Col span={8}>{formatMessage({ id: 'users.unlock.unlockTime' })}</Col>
                                <Col span={8} className='btn-right-col'>
                                    <Button type='danger' loading={unlockType == 'password' && unlockIndex == -1} onClick={() => {
                                        setUnlockIndex(-1);
                                        setUnlockType('password');

                                        flylayerClient.userAttemptUnlock({
                                            flynetId: flynet.id,
                                            userId: props.record.id,
                                            credentialsType: 'password',
                                            ip: passwordLockedList.map((item) => item.ip)
                                        }).then(() => {
                                            setUnlockType('')
                                            query()
                                        }).catch((e) => { })
                                    }}>{formatMessage({ id: 'users.unlock.unlockAll' })}</Button>
                                </Col>
                            </Row>
                            {passwordLockedList.map((item, index) => {
                                return <Row className='tableTitle' key={index}>
                                    <Col span={8}>{item.ip}</Col>
                                    <Col span={8}><DateFormat date={item.lockoutTime}></DateFormat></Col>
                                    <Col span={8} className='btn-right-col'>
                                        <Button type='danger'
                                            loading={unlockType == 'password' && unlockIndex == index}
                                            onClick={() => {
                                                setUnlockIndex(index)
                                                setUnlockType('password');
                                                flylayerClient.userAttemptUnlock({
                                                    flynetId: flynet.id,
                                                    userId: props.record.id,
                                                    credentialsType: 'password',
                                                    ip: [item.ip]
                                                }).then(() => {
                                                    setUnlockType('')
                                                    query()
                                                }).catch((e) => { })
                                            }}>{formatMessage({ id: 'users.unlock.unlockDevice' })}</Button>
                                    </Col>
                                </Row>
                            })}
                            <div className='mb40'>
                            </div>
                        </>}

                        {totpLockedList && totpLockedList.length > 0 && <>
                            <Paragraph type='tertiary' className='mb20'>{formatMessage({ id: 'users.unlock.mfaLocked' })}</Paragraph>
                            <Row className='tableTitle'>
                                <Col span={8}>{formatMessage({ id: 'users.unlock.device' })}</Col>
                                <Col span={8}>{formatMessage({ id: 'users.unlock.unlockTime' })}</Col>
                                <Col span={8} className='btn-right-col'>
                                    <Button type='danger'
                                        loading={unlockType == 'totp' && unlockIndex == -1}

                                        onClick={() => {
                                            setUnlockType('totp')
                                            setUnlockIndex(-1)
                                            flylayerClient.userAttemptUnlock({
                                                flynetId: flynet.id,
                                                userId: props.record.id,
                                                credentialsType: 'totp',
                                                ip: totpLockedList.map((item) => item.ip)
                                            }).then(() => {
                                                setUnlockType('')
                                                query()
                                            }).catch((e) => { })
                                        }}>{formatMessage({ id: 'users.unlock.unlockAll' })}</Button></Col>
                            </Row>
                            {totpLockedList.map((item, index) => {
                                return <Row className='tableTitle' key={index}>
                                    <Col span={8}>{item.ip}</Col>
                                    <Col span={8}><DateFormat date={item.lockoutTime}></DateFormat></Col>
                                    <Col span={8} className='btn-right-col'>
                                        <Button type='danger'
                                            loading={unlockType == 'totp' && unlockIndex == index}
                                            onClick={() => {
                                                setUnlockType('totp')
                                                setUnlockIndex(index)
                                                flylayerClient.userAttemptUnlock({
                                                    flynetId: flynet.id,
                                                    userId: props.record.id,
                                                    credentialsType: 'totp',
                                                    ip: [item.ip]
                                                }).then(() => {
                                                    setUnlockType('')
                                                    query()
                                                }).catch((e) => { })
                                            }}>{formatMessage({ id: 'users.unlock.unlockDevice' })}</Button>
                                    </Col>
                                </Row>
                            })}

                            <div className='mb40'>
                            </div>
                        </>}

                    </>
            }
        </Skeleton>

    </Modal></>
}

export default Index;