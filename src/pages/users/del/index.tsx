import { FC, useState } from 'react'
import { Typography, Modal, List, Notification, TabPane, Input } from '@douyinfe/semi-ui';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
const { Paragraph, Title, Text } = Typography;
import UserDevice from '@/components/user-device';

interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);
    const [confirmVal, setConfirmVal] = useState('');

    
    const handleSubmit = () => {
        if(loading) {
            return;
        }
        setLoading(true);
        
    
            flylayerClient.deleteUser({
                userId: props.record.id
            }).then(()=>{
                Notification.success({ content: formatMessage({ id: 'users.delete.success' }), position: "bottomRight" })
                if(props.success) {
                    props.success();
                }
            }).catch(err => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'users.delete.failed' }), position: "bottomRight" })
            }).finally(() => {
                setLoading(false);
                
            }
            );
        
    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'users.delete.title' })}
            visible={true}
            onOk={handleSubmit}
            
            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                disabled: props.record.loginName != confirmVal,
                loading,
                type: 'danger'
            }}
            maskClosable={false}
        >
            <Paragraph className='mb20'>
                {formatMessage({ id: 'users.del.confirmMessagePrefix' })} <Text strong>{props.record.displayName}({props.record.loginName})</Text> {formatMessage({ id: 'users.del.confirmMessageSuffix' })}
            </Paragraph>

            <UserDevice record={props.record}></UserDevice>
            <Paragraph className='mb20'>
                {formatMessage({ id: 'users.del.confirmTextPrefix' })} <Text strong>{props.record.loginName}</Text> {formatMessage({ id: 'users.del.confirmTextSuffix' })}
            </Paragraph>
            <Input value={confirmVal} onChange={(val)=>setConfirmVal(val)}></Input>
        </Modal></>
}
export default Index;
