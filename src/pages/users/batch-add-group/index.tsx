import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Space, Notification, Tooltip, Select } from '@douyinfe/semi-ui';
import { User, UserGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { IconList, IconCheckList } from '@douyinfe/semi-icons';

import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
const { Paragraph, Title } = Typography;
import UserList from '@/pages/users/components/user-list';

interface Props {
    close: () => void,
    success?: () => void,
    records: User[]
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [loading, setLoading] = useState(false);
    const [loadingGroups, setLoadingGroups] = useState(true);
    const [groups, setGroups] = useState<UserGroup[]>([]);

    const [groupIds, setGroupIds] = useState<string[]>();

    const [users, setUsers] = useState<User[]>(props.records);


    const query = () => {
        setLoadingGroups(true)

        flylayerClient.listUserGroups({
            flynetId: flynet.id
        }).then((res) => {
            setGroups(res.groups)
        }).finally(() => {
            setLoadingGroups(false);
        })
    }

    useEffect(() => {
        query();
    }, []);

    const handleSubmit = () => {
        setLoading(true);

        

        flylayerClient.batchAddUserGroups({
            flynetId: flynet.id,
            userIds: users.map((item) => {
                return item.id;
            }),
            groupIds: groupIds ? groupIds.map((item) => {
                return BigInt(item);
            }): []
        }).then((res) => {
            Notification.success({
                title: formatMessage({ id: 'users.batch.addToGroup.success' })
            })
            props.success && props.success();
        }).catch((err) => {
            Notification.error({ 
                title: formatMessage({ id: 'users.batch.addToGroup.failed' }),
                content: err.message,
                 position: "bottomRight" })

        }).finally(() => {
            setLoading(false);
        })
        


    }

    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'users.batch.addToGroup.title' })}
            visible={true}
            onOk={handleSubmit}

            onCancel={() => props.close()}
            closeOnEsc={true}
            okButtonProps={{
                loading,
                disabled: !groupIds || groupIds.length == 0 || users.length == 0
            }}
            maskClosable={false}
        >
            <Paragraph type='tertiary' className='mb10'>{formatMessage({ id: 'users.batch.addToGroup.description' })}</Paragraph>
            <UserList className='mb20' users={users} onChange={(list)=>setUsers(list)} />
            <div style={{ minHeight: 200 }}>
                {groups && groups.length > 0 &&
                    <Select style={{ width: '100%' }}
                        multiple
                        filter
                        value={groupIds}
                        onChange={(value) => {
                            setGroupIds(value as any);
                        }}
                        placeholder={formatMessage({ id: 'users.batch.addToGroup.selectPlaceholder' })}
                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                    >
                        {groups.map((item) => {
                            return <Select.Option key={item.id + ''} disabled={item.type == GroupType.GROUP_DYNAMIC} value={item.id + ''}>
                                <Space>
                                    {item.type == GroupType.GROUP_DYNAMIC ? <Tooltip content={formatMessage({ id: 'users.group.type.dynamic' })}><IconCheckList title={formatMessage({ id: 'users.group.type.dynamic' })}/></Tooltip> : <Tooltip content={formatMessage({ id: 'users.group.type.static' })}><IconList title={formatMessage({ id: 'users.group.type.static' })} /></Tooltip>}
                                    {`${item.alias}(${item.name})`}
                                </Space>
                            </Select.Option>
                        })}
                    </Select>
                }
            </div>
        </Modal></>
}
export default Index;
