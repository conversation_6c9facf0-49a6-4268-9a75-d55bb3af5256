import { FC } from 'react'
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";

import { Tag, Modal, Form, Notification, Skeleton, Popover, Button, Row, Col, Space, Divider } from '@douyinfe/semi-ui';
import { getUserDisplayName } from '@/utils/user';

interface Props {
    users: User[],
    className?: string,
    onChange: (users: User[]) => void
}

const Index: FC<Props> = (props) => {
    return <Space className={props.className} style={{flexWrap: 'wrap'}}>
        {props.users.map((user, index) => {
            return <Tag size='large' key={user.id + ''} closable onClose={()=>{
                let newUsers = [...props.users];
                newUsers.splice(index, 1);
                props.onChange(newUsers);
            }}>{user.displayName}({user.loginName})</Tag>
        })}
    </Space>
}

export default Index;
