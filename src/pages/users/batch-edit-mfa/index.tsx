import { FC, useState, useContext } from 'react'
import { Typography, Modal, Form, Space, Notification } from '@douyinfe/semi-ui';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';
import UserList from '@/pages/users/components/user-list';
import { useLocale } from '@/locales';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    records: User[]
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [mfaLoading, setMfaLoading] = useState(false)
    const [mfaForm<PERSON>pi, SetMfaFormApi] = useState<FormApi<{
        mfaEnabled: boolean,
    }>>()

    const [users, setUsers] = useState<User[]>(props.records);

    const handleMfaSubmit = () => {
        const values = mfaFormApi?.getValues();
        if (values) {
            setMfaLoading(true)

            flylayerClient.batchSaveUserMFASettings({
                flynetId: flynet.id,
                userIds: users.map((item) => {
                    return item.id;
                }),
                mfaEnabled: values.mfaEnabled,

            }).then(() => {
                setMfaLoading(false)
                Notification.success({ content: formatMessage({ id: 'users.editMFA.success' }), position: "bottomRight" })
                props.close();
                if (props.success) {
                    props.success();
                }
            }).catch(err => {
                Notification.success({
                    title: formatMessage({ id: 'users.editMFA.failed' }),
                    content: err.message
                    , position: "bottomRight"
                })
                console.error(err);
                setMfaLoading(false)
            })
        }
    }

    return <>
        <Modal
            width={560}
            title={formatMessage({ id: 'users.index.batch.editMFA' })}
            visible={true}
            okButtonProps={{ loading: mfaLoading, disabled: users.length == 0 }}
            onOk={handleMfaSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'users.editMFA.description' })}</Paragraph>
            <UserList className='mb20' users={users} onChange={(list) => setUsers(list)} />
            <Form getFormApi={SetMfaFormApi}
                initValues={{

                }}
            >{({ formState, values, formApi }) => (<>
                <Form.Switch style={{ marginRight: 40 }} labelWidth={200} labelPosition='left' label={formatMessage({ id: 'users.field.mfaEnabled' })} field='mfaEnabled' />


            </>)}

            </Form>
        </Modal>
    </>
}

export default Index;