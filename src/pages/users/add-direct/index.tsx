import React, { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Form, Notification, Button, Rating, Row, Col, Divider, Space, Tooltip } from '@douyinfe/semi-ui';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";
import { Field, GroupType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb";
import { Timestamp } from "@bufbuild/protobuf";
import styles from './index.module.scss';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { UserRole, UserGroup } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { generatePassword, getPasswordRank } from '@/utils/common';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { getFlynet } from '@/services/flynet';
import { IconList, IconCheckList } from '@douyinfe/semi-icons';

import { getFieldValue, renderFormElement, validateFields } from '@/utils/user';
import { useLocale } from '@/locales';
const { Text } = Typography;
interface Props {
    close: () => void,
    success?: (password: string) => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const [flynet, setFlynet] = useState<Flynet>();

    const queryFlynet = async () => {
        let res = await getFlynet(flynetGeneral.id);
        if (res) {
            setFlynet(res.flynet)
        }
    }

    useEffect(() => {
        queryFlynet()
    }, [])

    const [groups, setGroups] = useState<UserGroup[]>([]);

    const [loadingGroups, setLoadingGroups] = useState(true);
    const query = () => {
        setLoadingGroups(true)

        flylayerClient.listUserGroups({
            flynetId: flynetGeneral.id
        }).then((res) => {
            setGroups(res.groups)
        }).finally(() => {
            setLoadingGroups(false);
        })
    }

    useEffect(() => {
        query();
    }, []);


    const globalConfig = useContext(GlobalConfigContext);

    const [loading, setLoading] = useState(false);
    const [formApi, SetFormApi] = useState<FormApi<{
        loginName: string;
        password: string;
        displayName: string;
        role: UserRole;
        temporary: boolean;
        expiredAt: Date;
        mfaEnabled: boolean;
        groupIds: string[];
    }>>()


    // const [password, setPassword] = useState<string>('')
    const [passwordRank, setPasswordRank] = useState<number>(0)

    const handleSubmit = async () => {

        const values = formApi?.getValues();
        await formApi?.validate();
        setLoading(true);


        const pass = values?.password ? values.password : ''

        const fields: { [key: string]: string } = {};

        flynet?.accountAvailableFields.forEach((field: Field, index: number) => {
            if (values) {
                const value = (values as any)[field.key];

                if (value) {
                    fields[field.key] = getFieldValue(value, field);
                }
            }
        });



        flylayerClient.addUser({
            loginName: values?.loginName + '@' + globalConfig?.code,
            password: pass,
            displayName: values?.displayName,
            role: values?.role,
            temporary: values?.temporary,
            expiredAt: values?.expiredAt ? Timestamp.fromDate(values.expiredAt) : undefined,
            mfaEnabled: values?.mfaEnabled,
            groupIds: values?.groupIds ? values?.groupIds.map((item) => {
                return BigInt(item);
            }) : [],
            fields
        }).then(res => {
            Notification.success({
                title: formatMessage({ id: 'users.add.success' }),
                content: formatMessage({ id: 'users.add.success' })
            })
            props.success && props.success(pass)
            props.close()
        }).catch((err) => {
            Notification.error({
                title: formatMessage({ id: 'users.add.failed' }),
                content: err.message
            })
        }).finally(() => {
            setLoading(false);
        })

    }



    return <>
        <Modal
            width={600}
            title={formatMessage({ id: 'users.add.title' })}
            visible={true}
            onCancel={props.close}
            onOk={handleSubmit}
            okButtonProps={{ loading }}
            maskClosable={false}
            className='semi-modal'
        >
            <div className={styles.addUser}>
                <Form
                    getFormApi={SetFormApi}

                    initValues={{
                        role: UserRole.FLYNET_USER,
                        temporary: false
                    }}
                    validateFields={(values) => {
                        let { loginName, password, displayName } = values;
                        loginName = loginName ? loginName.trim() : loginName;
                        password = password ? password.trim() : password;
                        displayName = displayName ? displayName.trim() : displayName;

                        let errors: { [key: string]: string } = {};

                        if (flynet?.accountAvailableFields) {
                            validateFields(errors, values, flynet?.accountAvailableFields);

                        }

                        if (!loginName) {
                            errors.loginName = formatMessage({ id: 'users.addDirect.loginNameRequired' });
                        } else if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*$/.test(loginName + '@' + globalConfig?.code)) {
                            errors.loginName = formatMessage({ id: 'users.addDirect.loginNameFormat' });
                        }
                        if (!password) {
                            errors.password = formatMessage({ id: 'users.addDirect.passwordRequired' });

                        } else if (password.length < 8) {
                            errors.password = formatMessage({ id: 'users.addDirect.passwordLength' });
                        }
                        else if (getPasswordRank(password.trim()) < 3) {
                            errors.password = formatMessage({ id: 'users.addDirect.passwordStrength' });
                        }
                        if (!displayName) {
                            errors.displayName = formatMessage({ id: 'users.addDirect.displayNameRequired' });
                        }
                        if (Object.keys(errors).length > 0) {

                            return errors;
                        }
                        return '';
                    }}
                >
                    {({ formState }) => (
                        <React.Fragment>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Input
                                        field='loginName'
                                        label={formatMessage({ id: 'users.addDirect.loginName' })}
                                        name='loginName'
                                        suffix={`@${globalConfig?.code}`}
                                        required
                                        validate={value => {
                                            if (!value.trim()) {
                                                return formatMessage({ id: 'users.addDirect.loginNameRequired' });
                                            }
                                            if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*$/.test(value.trim() + '@' + globalConfig?.code)) {
                                                return formatMessage({ id: 'users.addDirect.loginNameFormat' });
                                            }
                                            return '';
                                        }}></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input
                                        field='displayName'
                                        label={formatMessage({ id: 'users.addDirect.displayName' })}
                                        name='displayName'
                                        required
                                        validate={value => {
                                            if (!value.trim()) {
                                                return formatMessage({ id: 'users.addDirect.displayNameRequired' });
                                            }

                                            return '';
                                        }}></Form.Input>
                                </Col>
                            </Row>

                            <Row className='mb20'>
                                <Col span={24}>
                                    <Form.Input
                                        style={{ marginBottom: 0 }}
                                        field='password'
                                        label={formatMessage({ id: 'users.addDirect.password' })}
                                        required
                                        validate={(value) => {
                                            if (!value.trim()) {
                                                return formatMessage({ id: 'users.addDirect.passwordRequired' });
                                            }
                                            if (value.length < 8) {
                                                return formatMessage({ id: 'users.addDirect.passwordLength' });
                                            }
                                            if (getPasswordRank(value.trim()) < 3) {
                                                return formatMessage({ id: 'users.addDirect.passwordStrength' });
                                            }
                                            return ''
                                        }}
                                        mode='password'
                                        name='password'
                                        onChange={(val) => {
                                            // setPassword(val)
                                            setPasswordRank(getPasswordRank(val))
                                        }}

                                    />

                                </Col>
                                <Col span={12} style={{ display: 'flex', alignItems: 'center' }}>
                                    <Text type='tertiary'>{formatMessage({ id: 'users.addDirect.passwordStrengthLabel' })}：</Text>
                                    <Rating disabled style={{ color: 'black' }} character='▇' size='small' value={passwordRank} count={3}></Rating>
                                </Col>
                                <Col span={12}>
                                    <div className='btn-right-col'>
                                        <Button size='small' type='primary' theme='solid'
                                            onClick={() => {

                                                let password = generatePassword(16);
                                                formApi?.setValue('password', password);
                                                // setPassword(password);
                                                setPasswordRank(getPasswordRank(password))
                                                formApi?.validate(['password'])
                                            }}
                                        >{formatMessage({ id: 'users.addDirect.generatePassword' })}</Button>
                                    </div>
                                </Col>
                            </Row>
                            {flynet && <Row gutter={16}>
                                {renderFormElement(flynet.accountAvailableFields)}
                            </Row>}
                            <Divider></Divider>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Switch field='temporary' label={formatMessage({ id: 'users.addDirect.accountExpiry' })}></Form.Switch>
                                </Col>
                                {formState.values.temporary && <Col span={12}>
                                    <Form.DatePicker field='expiredAt' label={formatMessage({ id: 'users.addDirect.expiryTime' })} style={{ width: '100%' }} ></Form.DatePicker>
                                </Col>}
                            </Row>
                            <Divider></Divider>
                            <Row>
                                <Col span={24}>
                                    <Form.Switch label={formatMessage({ id: 'users.addDirect.forceMFA' })} field='mfaEnabled' />
                                </Col>
                            </Row>
                            <Divider></Divider>
                            <Row>
                                <Col span={24}>
                                    <Form.Select
                                        field='groupIds'
                                        label={formatMessage({ id: 'users.addDirect.userGroups' })}
                                        multiple
                                        showClear
                                        style={{ width: '100%' }}
                                        placeholder={formatMessage({ id: 'users.addDirect.selectUserGroups' })}
                                        loading={loadingGroups}
                                        dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                                    >
                                        {groups.map((item) => {
                                            return <Form.Select.Option key={item.id + ''} disabled={item.type == GroupType.GROUP_DYNAMIC} value={item.id + ''}>
                                                <Space>
                                                    {item.type == GroupType.GROUP_DYNAMIC ? <Tooltip content={formatMessage({ id: 'users.group.type.dynamic' })}><IconCheckList title={formatMessage({ id: 'users.group.type.dynamic' })} /></Tooltip> : <Tooltip content={formatMessage({ id: 'users.group.type.static' })}><IconList title={formatMessage({ id: 'users.group.type.static' })} /></Tooltip>}
                                                    {`${item.alias}(${item.name})`}
                                                </Space>
                                            </Form.Select.Option>
                                        })}
                                    </Form.Select>
                                </Col>
                            </Row>
                            <Row>
                                <Col span={24}>
                                    <Form.RadioGroup label={formatMessage({ id: 'users.addDirect.role' })} field='role' direction="vertical"  >
                                        <Form.Radio value={UserRole.FLYNET_ADMIN}
                                            extra={formatMessage({ id: 'users.addDirect.adminDescription' })} aria-label={formatMessage({ id: 'users.addDirect.admin' })} name="role">
                                            {formatMessage({ id: 'users.addDirect.admin' })}
                                        </Form.Radio>
                                        <Form.Radio value={UserRole.FLYNET_USER}
                                            checked
                                            extra={formatMessage({ id: 'users.addDirect.userDescription' })} aria-label={formatMessage({ id: 'users.addDirect.user' })} name="role">
                                            {formatMessage({ id: 'users.addDirect.user' })}
                                        </Form.Radio>

                                    </Form.RadioGroup>

                                </Col>
                            </Row>
                        </React.Fragment>
                    )}
                </Form>
            </div>
        </Modal>
    </>
}

export default Index;