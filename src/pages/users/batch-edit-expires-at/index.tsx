import React,{ FC, useState, useContext } from 'react'
import { Typo<PERSON>, Modal, Row, Col, Notification, Banner, Form } from '@douyinfe/semi-ui';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

import UserList from '@/pages/users/components/user-list';
import { Timestamp } from "@bufbuild/protobuf";
interface Props {
    close: () => void,
    success?: () => void,
    records: User[]
}

const { Paragraph, Title } = Typography;
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);

    const [formApi, SetFormApi] = useState<FormApi<{
        temporary: boolean;
        expiredAt: Date;
    }>>()

    const [users, setUsers] = useState<User[]>(props.records);

    // const [value, setValue] = useState(props.record.expiredAt ? props.record.expiredAt.toDate() : undefined);
    const [loading, setLoading] = useState(false);
    // const onChange = (e: any) => {
    //     setValue(e.target.value);
    // };

    const handleSubmit = async () => {
        const res = await formApi?.validate();
        const values = formApi?.getValues()
        
        const temporary = values?.temporary;
        let expiredAt = values?.expiredAt ? Timestamp.fromDate(values.expiredAt) : undefined
        if(!temporary) {
            expiredAt = undefined;
        }

        setLoading(true);
        
        flylayerClient.batchEditUserLifecycles({
            flynetId: flynet.id,
            userIds: users.map((item) => {
                return item.id;
            }),
            temporary: temporary,
            expiredAt: expiredAt
        }).then(() => {
            Notification.success({
                title: formatMessage({ id: 'users.batch.editExpiry.success' }),
                content: formatMessage({ id: 'users.batch.editExpiry.successMessage' })
            });
            props.success && props.success();
            props.close();
        }).catch((e) => {
            Notification.error({
                title: formatMessage({ id: 'users.batch.editExpiry.failed' }),
                content: e.message
            });
        }).finally(() => {
            setLoading(false);
        });
        
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'users.batch.editExpiry.title' })}
            visible={true}
            okButtonProps={{ loading, disabled: users.length == 0}}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Banner className='mb20' type='warning' description={formatMessage({ id: 'users.editExpiry.warning' })}>

            </Banner>
            <Paragraph className='mb20'>
                {formatMessage({ id: 'users.batch.editExpiry.description' })}
                </Paragraph>
            <UserList className='mb20' users={users} onChange={(list)=>setUsers(list)} />
                <Form
                    getFormApi={SetFormApi}

                    validateFields={(values) => {
                        let { loginName, password, displayName } = values;
                        loginName = loginName ? loginName.trim() : loginName;
                        password = password ? password.trim() : password;
                        displayName = displayName ? displayName.trim() : displayName;

                        let errors: { [key: string]: string } = {};
                        
                        if (Object.keys(errors).length > 0) {

                            return errors;
                        }
                        return '';
                    }}
                >
                    {({ formState }) => (
                        <React.Fragment>
                           
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Switch field='temporary' label={formatMessage({ id: 'users.field.temporary' })}></Form.Switch>
                                </Col>
                                {formState.values.temporary && <Col span={12}>
                                    <Form.DatePicker field='expiredAt' label={formatMessage({ id: 'users.field.expiredAt' })} style={{ width: '100%' }} ></Form.DatePicker>
                                </Col>}
                            </Row>
                        
                        </React.Fragment>
                    )}



                </Form>

        </Modal>
    </>
}

export default Index
