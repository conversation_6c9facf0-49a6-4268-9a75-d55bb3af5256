import { FC, useState, useContext } from 'react'
import { Typography, Modal, Radio, RadioGroup, Notification } from '@douyinfe/semi-ui';

import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import styles from './index.module.scss';
import { RadioChangeEvent } from '@douyinfe/semi-ui/lib/es/radio';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [value, setValue] = useState(props.record.role.toString());

    const [loading, setLoading] = useState(false);
    const onChange = (e: RadioChangeEvent) => {
        setValue(e.target.value);
    };

    const handleSubmit = () => {
        let role: UserRole | undefined;
        if (value == UserRole.FLYNET_ADMIN.toString()) {
            role = UserRole.FLYNET_ADMIN
        } else if (value == UserRole.FLYNET_USER.toString()) {
            role = UserRole.FLYNET_USER
        }
        if (role) {
            let param:any = {
                flynetId: flynetGeneral.id,
                role: role,
            }
            if(props.record.avatarUrl == 'avatarUrl') {
                param.loginName = props.record.loginName;
            } else {
                param.userId = props.record.id;
            }
            
            flylayerClient.setUserRole(param).then(() => {
                Notification.success({ content: formatMessage({ id: 'users.editRole.singleSuccess' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch(err => {
                console.error(err);
                Notification.error({ content: formatMessage({ id: 'users.editRole.singleFailed' }), position: "bottomRight" })
            })
        }
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'users.editRole.singleTitle' }).replace('{user}', `${props.record.displayName}(${props.record.loginName})`)}
            visible={true}
            okButtonProps={{ loading }}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'users.editRole.singleDescription' }).replace('{user}', `${props.record.displayName}(${props.record.loginName})`)}</Paragraph>
            <RadioGroup direction="vertical" onChange={onChange} value={value} >
                <Radio value={UserRole.FLYNET_ADMIN.toString()}
                    checked={props.record.role === UserRole.FLYNET_ADMIN}
                    extra={formatMessage({ id: 'users.role.admin.description' })} aria-label={formatMessage({ id: 'users.role.admin' })} name="role">
                    {formatMessage({ id: 'users.role.admin' })}
                </Radio>
                <Radio value={UserRole.FLYNET_USER.toString()}
                    checked={props.record.role === UserRole.FLYNET_USER}
                    extra={formatMessage({ id: 'users.role.user.description' })} aria-label={formatMessage({ id: 'users.role.user' })} name="role">
                    {formatMessage({ id: 'users.role.user' })}
                </Radio>

            </RadioGroup>
        </Modal></>
}
export default Index;
