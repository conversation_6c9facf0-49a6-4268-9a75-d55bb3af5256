import { FC, useState } from 'react'
import { Typography, Modal, Form, Space, Notification } from '@douyinfe/semi-ui';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';
import { useLocale } from '@/locales';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    record: User
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [rdpLoading, setRdpLoading] = useState(false)
    const [rdpFormApi, SetRdpFormApi] = useState<FormApi<{
        rdpAutoGrant: boolean,
        rdpEnabled: boolean,
    }>>()

    const handleRdpSubmit = () => {
        const values = rdpFormApi?.getValues();
        if (values) {
            setRdpLoading(true)
            flylayerClient.saveUserRdpSettings({
                userId: props.record.id,
                rdpEnabled: values.rdpEnabled,
                rdpAutoGrant: values.rdpAutoGrant
            }).then(() => {
                setRdpLoading(false)
                Notification.success({ content: formatMessage({ id: 'users.rdpEdit.success' }), position: "bottomRight" })
                props.close();
                if (props.success) {
                    props.success();
                }
            }).catch(err => {
                Notification.error({ content: formatMessage({ id: 'users.rdpEdit.failed' }), position: "bottomRight" })
                console.error(err);
                setRdpLoading(false)
            })
        }
    }

    return <>
        <Modal
            width={560}
            title={formatMessage({ id: 'users.rdpEdit.title' })}
            visible={true}
            okButtonProps={{ loading: rdpLoading }}
            onOk={handleRdpSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb20'>{formatMessage({ id: 'users.editRdp.description' }).replace('{displayName}', props.record.displayName).replace('{loginName}', props.record.loginName)}</Paragraph>
            <Form  getFormApi={SetRdpFormApi}
                initValues={{
                    rdpAutoGrant: props.record?.rdpAutoGrant,
                    rdpEnabled: props.record?.rdpEnabled
                }}
            >{({ formState, values, formApi }) => (<>
                    <Form.Switch style={{marginRight:40}} labelWidth={100} labelPosition='left' label={formatMessage({ id: 'users.editRdp.enableRdp' })} field='rdpEnabled' />
                    <Form.Switch labelPosition='left' label={formatMessage({ id: 'users.editRdp.autoGrant' })} labelWidth={100}
                    extraTextPosition='middle' extraText={formatMessage({ id: 'users.editRdp.autoGrantHint' })}
                    field='rdpAutoGrant' disabled={!values.rdpEnabled} />

            </>)}

            </Form>
        </Modal>
    </>
}

export default Index;