import { FC, useState, useContext } from 'react'
import { Typography, Modal, Radio, RadioGroup, Notification } from '@douyinfe/semi-ui';

import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import styles from './index.module.scss';
import { RadioChangeEvent } from '@douyinfe/semi-ui/lib/es/radio';
import { flylayerClient } from '@/services/core';
import UserList from '@/pages/users/components/user-list';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';

const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    records: User[]
    role?: UserRole
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const [value, setValue] = useState(props.role?.toString());

    const [loading, setLoading] = useState(false);
    const onChange = (e: RadioChangeEvent) => {
        setValue(e.target.value);
    };
    
    const [role, setRole] = useState<UserRole | undefined>(props.role);
    const [users, setUsers] = useState<User[]>(props.records);
    const handleSubmit = () => {
        let role: UserRole | undefined;
        if (value == UserRole.FLYNET_ADMIN.toString()) {
            role = UserRole.FLYNET_ADMIN
        } else if (value == UserRole.FLYNET_USER.toString()) {
            role = UserRole.FLYNET_USER
        }
        if (role) {

            setLoading(true);
            flylayerClient.batchSetUserRoles({
                flynetId: flynet.id,
                userIds: users.map((item) => {
                    return item.id;
                }),
                role: role
            }).then(() => {
                Notification.success({ content: formatMessage({ id: 'users.editRole.success' }), position: "bottomRight" })
                if (props.success) {
                    props.success();
                }
            }).catch(err => {
                console.error(err);
                Notification.error({ 
                    title: formatMessage({ id: 'users.editRole.failed' }),
                    content: err.message, position: "bottomRight" })
            }).finally(() => {
                setLoading(false);
            });
        }
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'users.index.batch.editRole' })}
            visible={true}
            okButtonProps={{ loading, disabled: !value || users.length == 0}}
            onOk={handleSubmit}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Paragraph className='mb10'>{formatMessage({ id: 'users.editRole.description' })}</Paragraph>
            <UserList className='mb20' users={users} onChange={(list)=>setUsers(list)} />
            <RadioGroup direction="vertical" onChange={onChange} value={value} >
                <Radio value={UserRole.FLYNET_ADMIN.toString()}
                    checked={role === UserRole.FLYNET_ADMIN}
                    extra={formatMessage({ id: 'users.role.admin.description' })} aria-label={formatMessage({ id: 'users.role.admin' })} name="role">
                    {formatMessage({ id: 'users.role.admin' })}
                </Radio>
                <Radio value={UserRole.FLYNET_USER.toString()}
                    checked={role === UserRole.FLYNET_USER}
                    extra={formatMessage({ id: 'users.role.user.description' })} aria-label={formatMessage({ id: 'users.role.user' })} name="role">
                    {formatMessage({ id: 'users.role.user' })}
                </Radio>

            </RadioGroup>
        </Modal></>
}
export default Index;
