import { useEffect, useState, FC, useContext } from 'react';

import { Typography, Dropdown, Button, Popover, Space } from '@douyinfe/semi-ui';
import { IconMore, IconArticle, IconList, IconCheckList, IconArrowUpRight, IconHelpCircle } from '@douyinfe/semi-icons';

import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { GroupType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import { flylayerClient } from '@/services/core';

import DateFormat from '@/components/date-format';
import { BASE_PATH } from '@/constants/router';
import { useNavigate } from 'react-router-dom';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useLocale } from '@/locales';
import { caseInsensitiveIncludes } from '@/utils/common';


const { Title, Paragraph, Text } = Typography;
export type GroupFilter = {
    query?: string;
}

const useTable = (filterParam: GroupFilter) => {
    const { formatMessage } = useLocale();
    const flynet = useContext(FlynetGeneralContext);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [groups, setGroups] = useState<UserGroup[]>([]);
    const [allGroups, setAllGroups] = useState<UserGroup[]>([]);

    const [editVisible, setEditVisible] = useState(false);
    const [delVisible, setDelVisible] = useState(false);

    const [selectedGroup, setSelectedGroup] = useState<UserGroup>();

    const [reloadFlag, setReloadFlag] = useState(false);

    const [groupDnsVisible, setGroupDnsVisible] = useState(false);


    const [filter, setFilter] = useState<GroupFilter>(filterParam);


    const columns = [{
        title: formatMessage({ id: 'users.group.table.name' }),
        dataIndex: 'name',
        render: (field: string, record: UserGroup, index: number) => {
            return <>
                <div style={{ display: 'inline-flex' }}>
                    <div>
                        <Title heading={6}>
                            <a href={`${BASE_PATH}/users?group=${record.id}`} onClick={(e) => { e.stopPropagation() }}>
                                {record.alias}
                            </a>
                        </Title>
                        <Paragraph size='small'>{record.name}</Paragraph>
                    </div>
                </div>
            </>
        },
    },
    {
        width: 460,
        title: formatMessage({ id: 'users.group.table.description' }),
        dataIndex: 'description',
        render: (field: string) => {
            return <Text>{field}</Text>
        },
    },
    {
        width: 160,
        title: formatMessage({ id: 'users.group.table.type' }),
        dataIndex: 'type',
        render: (field: string, record: UserGroup, index: number) => {
            return <>
                {record.type === GroupType.GROUP_STATIC && <Space><IconList />{formatMessage({ id: 'users.group.type.static' })}</Space>}
                {record.type === GroupType.GROUP_DYNAMIC && <Space><IconCheckList />{formatMessage({ id: 'users.group.type.dynamic' })}</Space>}
            </>
        },
    },
    {
        width: 160,
        title: formatMessage({ id: 'users.group.table.userCount' }),
        dataIndex: 'userCount',
        render: (field: number, record: UserGroup, index: number) => {

            return <>{field}</>
        },
    }
        , {
        width: 200,
        title: formatMessage({ id: 'users.group.table.createdAt' }),
        dataIndex: 'createdAt',
        render: (field: string, record: UserGroup, index: number) => {

            return <>
                <DateFormat date={record.createdAt}></DateFormat>

            </>
        }
    },
    {
        width: 100,
        title: '',
        dataIndex: 'operate',
        render: (field: string, record: UserGroup) => {
            return <div className='table-last-col'><Dropdown
                position='bottomRight'
                render={
                    <Dropdown.Menu>
                    <Dropdown.Item onClick={() => {
                        navigate(`${BASE_PATH}/users?group=${record.id}`)
                    }}>{formatMessage({ id: 'users.group.actions.viewUsers' })}</Dropdown.Item>
                        <Dropdown.Item
                            onClick={() => {
                                setSelectedGroup(record)
                                setEditVisible(true)
                            }}
                        >{formatMessage({ id: 'users.group.actions.edit' })}</Dropdown.Item>
                        <Dropdown.Item
                            onClick={() => {
                                setSelectedGroup(record)
                                setGroupDnsVisible(true)
                            }}
                        >{formatMessage({ id: 'users.group.actions.networkSettings' })}</Dropdown.Item>
                        <Dropdown.Divider />

                        <Dropdown.Item type="danger"
                            onClick={() => {
                                setSelectedGroup(record)
                                setDelVisible(true)
                            }}
                        >{formatMessage({ id: 'users.group.actions.delete' })}</Dropdown.Item>
                    </Dropdown.Menu>
                }
            >
                <Button><IconMore className='align-v-center' /></Button>
            </Dropdown></div>;
        },
    }];


    const query = () => {
        setLoading(true)

        flylayerClient.listUserGroups({
            flynetId: flynet.id
        }).then((res) => {
            let list = res.groups
            list.sort((a, b) => {
                return a.priority - b.priority
            })
            setGroups(list)
            setAllGroups(list)
        }).finally(() => {
            setLoading(false);
        })
    }


    const doFilter = (src: Array<UserGroup>, filter: GroupFilter) => {
        if (!src || src.length == 0) {
            return src;
        }

        if (filter.query == '') {
            return src;
        }

        let dst: Array<UserGroup> = [];

        src.forEach((item) => {
            if (filter.query) {
                if (caseInsensitiveIncludes(item.name, filter.query) || caseInsensitiveIncludes(item.description, filter.query) || caseInsensitiveIncludes(item.alias, filter.query)) {
                    dst.push(item);
                }
            }


        })

        return dst;
    }

    useEffect(() => {
        query()
    }, [])

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    useEffect(() => {
        const res = doFilter(allGroups, filterParam)

        setGroups(res)

    }, [filterParam])

    return {
        columns,
        loading,
        allGroups,
        groups,
        selectedGroup,
        setSelectedGroup,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        groupDnsVisible,
        setGroupDnsVisible,
        reloadFlag,
        setReloadFlag,
        filter,
        setFilter
    }

}

export default useTable;