import React, { useState } from 'react'
import { Typography, Table, Row, Col, Button, Space, Breadcrumb, Tag, Divider } from '@douyinfe/semi-ui';
import { getQueryParam } from '@/utils/query';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import TableEmpty from '@/components/table-empty';
import { BASE_PATH } from '@/constants/router';
import GroupAdd from '../group-add';
import GroupEdit from '../group-edit';
import GroupDel from '../group-del';
import useTable from './useTable';
import { GroupFilter } from './useTable';
import SearchFilter, { FilterParam } from '@/components/search-filter';
import GroupDns from '../group-dns';

import { UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { useLocale } from '@/locales';
const { Title } = Typography;


const getGroupFilter = (location: Location): GroupFilter => {
    const query: string = getQueryParam('query', location) as string;
    return {
        query: query || ''
    }
}

const Index: React.FC = () => {
    const { formatMessage } = useLocale();
    const initFilter: GroupFilter = getGroupFilter(useLocation())
    const {
        columns,
        loading,
        allGroups,
        groups,
        selectedGroup,
        setSelectedGroup,
        editVisible,
        setEditVisible,
        delVisible,
        setDelVisible,
        groupDnsVisible,
        setGroupDnsVisible,
        reloadFlag,
        setReloadFlag,
        filter,
        setFilter
    } = useTable(initFilter);
    const navigate = useNavigate();

    const doNavigate = (param: GroupFilter) => {
        const { query } = param;
        navigate(`${BASE_PATH}/users/group/?query=${query}`);
    }

    const handleQueryChange = (value: string) => {
        setFilter({ ...filter, query: value })
        doNavigate({ ...filter, query: value });
    }

    const [createVisible, setCreateVisible] = useState(false);

    const [filterParams, setFilterParams] = useState<FilterParam[]>([{
        name: 'query',
        placeholder: formatMessage({ id: 'users.group.filter.search' }),
        label: formatMessage({ id: 'users.index.button.query' }),
        value: initFilter.query || '',
    }

    ]);

    return <><div className='general-page'><Breadcrumb routes={
        [
            {
                path: `${BASE_PATH}/users`,
                href: `${BASE_PATH}/users`,
                name: formatMessage({ id: 'users.breadcrumb.users' })
            },
            {
                name: formatMessage({ id: 'users.breadcrumb.userGroups' }),
            }
        ]
    }>
    </Breadcrumb>
        <Row className='mb10'>
            <Col span={20}>
                <Title heading={3}>{formatMessage({ id: 'users.group.title' })}</Title>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button theme='solid'
                        onClick={() => setCreateVisible(true)}>{formatMessage({ id: 'users.group.button.addGroup' })}</Button>
                </Space>
            </div></Col>
        </Row>
        <Divider className='mb20'></Divider>
        <SearchFilter onChange={(val: string, filterParam) => {
            setFilter({ ...filter, [filterParam.name]: val })
            doNavigate({ ...filter, [filterParam.name]: val });
            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                }
                return item;
            })
            setFilterParams(newFilterParams);

        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
        <div style={{ height: 20 }} className='mb10'>  {!loading && <Tag>  {formatMessage({ id: 'users.group.totalCount' })} {groups.length}</Tag>} </div>
        <Table
            rowKey={(record?: UserGroup) => record ? record.id + '' : ''}
            expandRowByClick
            // expandAllRows={services.length < 10}
            empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={groups} pagination={false} />
    </div>

        {createVisible && <GroupAdd
            close={() => { setCreateVisible(false); }}
            success={() => {
                setCreateVisible(false)
                setReloadFlag(true)
            }}
        ></GroupAdd>
        }

        {delVisible && selectedGroup && <GroupDel
            close={() => {
                setDelVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setDelVisible(false)
                setSelectedGroup(undefined)
                setReloadFlag(true)
            }}
            record={selectedGroup}
        ></GroupDel>}
        {editVisible && selectedGroup && <GroupEdit
            userGroupId={selectedGroup.id}
            close={() => {
                setEditVisible(false)
                setSelectedGroup(undefined)

            }}
            success={() => {
                setSelectedGroup(undefined)
                setEditVisible(false)
                setReloadFlag(true)
            }}
        ></GroupEdit>}
        {
            groupDnsVisible && selectedGroup && <GroupDns
                groupId={selectedGroup.id}
                groupName={selectedGroup.alias}
                close={() => {
                    setGroupDnsVisible(false)
                    setSelectedGroup(undefined)
                }}
                success={() => {
                    setSelectedGroup(undefined)
                    setGroupDnsVisible(false)
                    setReloadFlag(true)
                }}
            ></GroupDns>
        }
    </>
}

export default Index;
