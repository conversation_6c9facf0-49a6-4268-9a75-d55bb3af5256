// https://www.iana.org/assignments/protocol-numbers/protocol-numbers.xhtml
export function getProtoName (protoNum: Number) {
    if(protoNum === 0x01) {
        return 'ICMPv4'
    }
    if(protoNum === 0x02) {
        return 'IGMP'
    }
    if(protoNum === 0x3a) {
        return 'ICMPv6'
    }
    if(protoNum === 0x06) {
        return 'TCP'
    }
    if(protoNum === 0x11) {
        return 'UDP'
    }
    if(protoNum === 0x21) {
        return 'DCCP'
    }
    if(protoNum === 0x2f) {
        return 'GRE'
    }
    if(protoNum === 0x84) {
        return 'SCTP'
    }
    if(protoNum === 99) {
        return 'TSMP'
    }

    if(protoNum === 0xFF) {
        return 'Fragment'
    }
    
    if(protoNum === 0x00) {
        return 'Unknown'
    }

    return ''
}