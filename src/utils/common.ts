import { GlobalTheme } from "@/hooks/useGlobalTheme";

const env = process.env.NODE_ENV;

console.log('env:',process.env.NODE_ENV);


export function getMockCidrIp(cidrStr?: string) {
    if (!cidrStr) return '';
    let arr = cidrStr.split('/');

    let ipArr = arr[0].split('.');
    
    let newArr = [];
    for (let i = 0; i < ipArr.length - 1; i++) {
        newArr.push(ipArr[i]);
    }
    return newArr.join('.') + '.x';

}

export function getUuid() {
    let timestamp = new Date().getTime();
    let perforNow = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0;
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        let random = Math.random() * 16;
        if (timestamp > 0) {
            random = (timestamp + random) % 16 | 0;
            timestamp = Math.floor(timestamp / 16);
        } else {
            random = (perforNow + random) % 16 | 0;
            perforNow = Math.floor(perforNow / 16);
        }
        return (c === 'x' ? random : (random & 0x3) | 0x8).toString(16);
    });
};

/**
 * JS校验密码强度
 * 等级0（风险密码）：密码长度小于8位，或者只包含4类字符中的任意一类，或者密码与用户名一样，或者密码是用户名的倒写。
 * 等级1（弱密码）：包含两类字符，且组合为（数字+小写字母）或（数字+大写字母），且长度大于等于8位。
 * 等级2（中密码）：包含两类字符，且组合不能为（数字+小写字母）和（数字+大写字母），且长度大于等于8位。
 * 等级3（强密码）：包含三类字符及以上，且长度大于等于8位。
 * @param szPwd
 * @param szUser
 * @returns 
 */
export function getPasswordRank(szPwd: string, szUser?: string) {

    let iRank = 0;
    szPwd.match(/[a-z]/g) && iRank++;
    szPwd.match(/[A-Z]/g) && iRank++;
    szPwd.match(/[0-9]/g) && iRank++;
    szPwd.match(/[^a-zA-Z0-9]/g) && iRank++;
    iRank = iRank > 3 ? 3 : iRank;
    if (
        szPwd.length < 8 ||
        iRank === 1) {
        iRank = 0;
    }
    if (
        szUser && (
            szPwd.includes(szUser) ||
            szPwd.includes(szUser.split('').reverse().join(''))
        )) {
        iRank = 0;
    }
    if (iRank === 2) {
        if (
            (szPwd.match(/[0-9]/g) && szPwd.match(/[a-z]/g)) ||
            (szPwd.match(/[0-9]/g) && szPwd.match(/[A-Z]/g))
        ) {
            iRank = 1;
        }
    }
    return iRank;
}

/**
 * 生成一段密码，包含特殊字符
 */
export function generatePassword(length: number): string {
    const charset = {
        lowercase: 'abcdefghijklmnopqrstuvwxyz',
        uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
        numbers: '0123456789',
        symbols: '!@#$%^&*()_+-=[]{}|;:",.<>/?'
    };

    // Function to get a random character from a string
    const getRandomChar = (chars: string) => chars[Math.floor(Math.random() * chars.length)];

    // Make sure each character set is represented at least once
    let password = [
        getRandomChar(charset.lowercase),
        getRandomChar(charset.uppercase),
        getRandomChar(charset.numbers),
        getRandomChar(charset.symbols)
    ].join('');

    // Fill the rest of the password length with random characters from all sets combined
    const allChars = charset.lowercase + charset.uppercase + charset.numbers + charset.symbols;
    for (let i = password.length; i < length; i++) {
        password += getRandomChar(allChars);
    }

    // Shuffle the password to ensure randomness
    password = password.split('').sort(() => 0.5 - Math.random()).join('');

    return password;
}

export function ipRangeToCidr(startIp: string, endIp: string) {
    // 将IP地址字符串分割为四部分
    var startParts = startIp.split('.');
    var endParts = endIp.split('.');

    // 初始化网络前缀长度为0
    var prefixLength = 0;

    for (var i = 0; i < 4; i++) {
        // 获取当前位置上的起始值和结束值
        var startValue = parseInt(startParts[i]);
        var endValue = parseInt(endParts[i]);

        if (startValue === endValue) {
            // 如果起始值等于结束值，则说明这段连续的IP地址在此位置没有变化，直接加1到prefixLength中
            prefixLength += 8;

        } else {
            // 否则需要遍历每个bit位进行比较
            while ((startValue & 128) !== (endValue & 128)) {
                startValue <<= 1;
                endValue <<= 1;

                // 更新prefixLength
                prefixLength++;
            }

            break;
        }
    }

    return `${startIp}/${prefixLength}`;
}



// 生成随机ID
export function generateRandomID(): bigint {
    // 生成一个随机整数作为 ID
    const randomInt = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER);

    // 转换为 BigInt 类型
    const randomBigIntID = BigInt(randomInt);

    return randomBigIntID;
}

const separators: Record<string, boolean> = {
    ' ': true,
    '.': true,
    '@': true,
    '_': true,
};

export function sanitizeLabel(label: string): string {
    const maxLabelLength = 64;
    const sb: string[] = [];
    let start = 0;
    let end = label.length;

    // Length check
    if (end > maxLabelLength) {
        end = maxLabelLength;
    }

    // Find starting alphanumeric character
    while (start < end && !isalphanum(label[start])) {
        start++;
    }

    // Find ending alphanumeric character
    while (start < end && !isalphanum(label[end - 1])) {
        end--;
    }

    // Sanitization loop
    for (let i = start; i < end; i++) {
        const boundary = i === start || i === end - 1;

        if (!boundary && separators[label[i]]) {
            sb.push('-');
        } else if (isdnschar(label[i])) {
            sb.push(label[i].toLowerCase());
        }
    }

    return sb.join('');
}
function islower(c: string): boolean {
    return 'a' <= c && c <= 'z';
}

function isupper(c: string): boolean {
    return 'A' <= c && c <= 'Z';
}

function isalpha(c: string): boolean {
    return islower(c) || isupper(c);
}

function isalphanum(c: string): boolean {
    return isalpha(c) || ('0' <= c && c <= '9');
}

function isdnschar(c: string): boolean {
    return isalphanum(c) || c === '-';
}

function tolower(c: string): string {
    if (isupper(c)) {
        return String.fromCharCode(c.charCodeAt(0) + 'a'.charCodeAt(0) - 'A'.charCodeAt(0));
    } else {
        return c;
    }
}

export function getEchartsTheme(globalTheme: GlobalTheme) {
    return `${globalTheme.theme}-${globalTheme.colorMode}`
}

// 判断是否为IP地址格式URL(http|https可不填，且URL里可包含路径), 如果是则启用地址伪装
export function isIPUrl(val: string) {
    let reg = /^((https|http):\/\/)?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(:\d{1,5})?(\/.*)?$/;
    if (reg.test(val)) {
        return true;
    } else {
        return false;
    }
}

export function caseInsensitiveIncludes(str1: string, str2: string): boolean {
    return str1.toLowerCase().includes(str2.toLowerCase());
}

export function validateParamCombo(value: string, paramLength: number) : string{
    if (!value) {
        return '参数组合不能为空';
    }

    
    if (value.trim().startsWith('and') || value.trim().startsWith('or') || value.trim().startsWith('not')) {
        return "参数组合不能以and、or、not开头";
    }
    if (value.trim().endsWith('and') || value.trim().endsWith('or') || value.trim().endsWith('not')) {
        return "参数组合不能以and、or、not结尾";
    }
    if (value.trim().includes('  ')) {
        return "参数组合不能包含连续的空格";
    }
    if (value.trim().includes(' and and ') || value.trim().includes(' or or ') || value.trim().includes(' not not ')) {
        return "参数组合不能包含连续的运算符";
    }
    if (value.trim().includes(' and or ') || value.trim().includes(' or and ') || value.trim().includes(' and not ') || value.trim().includes(' not and ') || value.trim().includes(' or not ') || value.trim().includes(' not or ')) {
        return "参数组合不能包含连续的运算符";
    }
    if (value.trim().includes('()')) {
        return "参数组合不能包含连续的括号";
    }
    if (value.trim().includes(')(')) {
        return "参数组合不能包含连续的括号";
    }
    if (value.trim().includes(') and') || value.trim().includes(') or') || value.trim().includes(') not')) {
        return "参数组合不能包含连续的运算符";
    }

    // 触发参数数字为 1 - expressions.length
    const regex = /(\d+)/g;
    const matches = value.match(regex);
    if (matches) {
        for (let i = 0; i < matches.length; i++) {
            const match = matches[i];
            if (parseInt(match) < 1 || parseInt(match) > paramLength) {
                return `参数组合中的数字必须在1-${paramLength}之间`;
            }
        }
    }
    
    return '';
};

export function getRadioEntitlementVal(entitlementKey: string, entitlements: object): boolean {
    let val = true;
    if(entitlements) {
        Object.keys(entitlements).map(key=>{
            const entitlement = (entitlements as any)[key];
            if (entitlement[entitlementKey]) {
                if(entitlement[entitlementKey] == 'false') {
                    val = false
                }
            }
        })
    }
    return val
}