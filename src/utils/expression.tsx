import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { AttributeTemplate } from '@/interface/attribute-template';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { Form, Col } from '@douyinfe/semi-ui';


const getDataType = (node: AttributeTemplate): string => {

    if (node) {
        if (node.format == 'date-time') {
            return 'datetime';
        } else if (node.format == 'date') {
            return 'date';
        } else if (node.type == 'object') {
            return 'string';
        } else {
            return node.type;
        }
    }
    return 'string'
}


const getAttributeNode = (attr: string, key: string, expressionsTemplate: AttributeTemplate): AttributeTemplate | undefined => {
    var node: AttributeTemplate | undefined = undefined;
    if (key == attr) {
        return expressionsTemplate;
    }
    if (!expressionsTemplate.properties) {
        return undefined;
    }
    Object.keys(expressionsTemplate.properties).forEach((childKey) => {
        let childDataType = getAttributeNode(attr, key + '.' + childKey, expressionsTemplate.properties[childKey]);
        if (childDataType) {
            node = childDataType;
        }
    })
    return node;
};

const buildTreeData = (key: string, expressionsTemplate: AttributeTemplate) => {
    let treeData: TreeNodeData = {
        label: expressionsTemplate.title,
        value: key,
        key: key,
        children: []
    }
    if (!expressionsTemplate.properties) {
        return treeData;
    }
    Object.keys(expressionsTemplate.properties).forEach((childKey) => {
        let child = buildTreeData(key + '.' + childKey, expressionsTemplate.properties[childKey]);
        treeData.children?.push(child);
    })

    return treeData;
}

const parseAttributeByTemplate = (attr: string, templateString: string) => {

    let initDataType = 'string';
    const expressionsTemplate: AttributeTemplate = JSON.parse(templateString);
    const initTreeData: TreeNodeData[] = [];
    let enumValues: string[] = [];
    if (!expressionsTemplate || !expressionsTemplate.properties) {
        return {
            treeData: initTreeData,
            dataType: initDataType,
            enumValues: enumValues
        }
    }
    Object.keys(expressionsTemplate.properties).forEach((key) => {
        if (!expressionsTemplate.properties[key]) {
            return;
        }
        let treeDataItem = buildTreeData(key, expressionsTemplate.properties[key]);
        initTreeData.push(treeDataItem);
        let node = getAttributeNode(attr, key, expressionsTemplate.properties[key]);

        if (node) {
            if(node.format == 'date-time') {
                initDataType = 'datetime';
            } else if(node.format == 'date') {
                initDataType = 'date';
            }else if (node.type == 'object') {
                initDataType = 'string';
            } else if (node.type == 'boolean') {
                initDataType = 'boolean';
            } else if (node.type == 'array') {
                initDataType = 'array';
            } else if (node.type == 'number') {
                initDataType = 'number';
            } else {
                initDataType = 'string';
            }
            if (node.enum) {
                enumValues = node.enum
            }
        }
    })

    return {
        treeData: initTreeData,
        dataType: initDataType,
        enumValues: enumValues
    }
}

const parseAttribute = (attr: string, expressionsTemplate: AttributeTemplate) => {

    let initDataType = 'string';
    const initTreeData: TreeNodeData[] = [];
    let enumValues: string[] = [];
    if (!expressionsTemplate || !expressionsTemplate.properties) {
        return {
            treeData: initTreeData,
            dataType: initDataType,
            enumValues: enumValues
        }
    }
    Object.keys(expressionsTemplate.properties).forEach((key) => {
        if (!expressionsTemplate.properties[key]) {
            return;
        }
        let treeDataItem = buildTreeData(key, expressionsTemplate.properties[key]);
        initTreeData.push(treeDataItem);
        let node = getAttributeNode(attr, key, expressionsTemplate.properties[key]);

        if (node) {
            if (node.format == 'date-time') {
                initDataType = 'datetime';
            } else if (node.format == 'date') {
                initDataType = 'date';
            } else if (node.type == 'object') {
                initDataType = 'string';
            } else if (node.type == 'boolean') {
                initDataType = 'boolean';
            } else if (node.type == 'array') {
                initDataType = 'array';
            } else if (node.type == 'number') {
                initDataType = 'number';
            } else {
                initDataType = 'string';
            }
            if (node.enum) {
                enumValues = node.enum
            }
        }
    })

    return {
        treeData: initTreeData,
        dataType: initDataType,
        enumValues: enumValues
    }
}




const buildAttribute = (template: string) => {
    const attributes: AttributeTemplate = JSON.parse(template);
    return attributes;
}

const buildAttributeElement = (template: AttributeTemplate) => {
    let element = <></>
    if (template.type === 'object') {
        element = <>
            {Object.keys(template.properties).map(key => {

                return <div key={key}>
                    {template.properties[key].type === 'string' &&
                        <Col span={12}>
                            <Form.Input field={key} label={template.properties[key].title}></Form.Input>
                        </Col>
                    }

                    {buildAttributeElement(template.properties[key])}
                </div>
            })}
        </>
    }

    return element;
}

const getAttributeKeys = (template: AttributeTemplate) => {
    let keys: string[] = [];
    
    const propertyKeys = Object.keys(template.properties);
    propertyKeys.forEach(key => {
        let obj = template.properties[key];
        if(obj) {

            if(obj.type === 'string') {
                keys.push(key);
            }
        
            if(obj.properties) {
                keys = keys.concat(getAttributeKeys(obj));
            }
        }
        
        
        
    })

    return keys;
}

export {
    buildTreeData,
    getDataType,
    parseAttribute,
    getAttributeNode,
    buildAttribute,
    buildAttributeElement,
    getAttributeKeys
}
