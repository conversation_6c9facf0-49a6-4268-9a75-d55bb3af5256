import moment from 'moment'
import { Timestamp } from "@bufbuild/protobuf";
import { number } from 'echarts';

/**
 * 将版本号格式化为更易于阅读的版本。（去除尾部修订号）
 */
export function formatIPNVersion(str: string) {
    return str.replace(/-t[\da-f].*/, "").replace(/-g\w+/, "")
}


export function formatRelativeMin(date: Date, now: Date = new Date()) {
    const diffTime = moment(date).diff(moment(now));//计算相差的分钟数

    return Math.round(diffTime / (1000 * 60)) + '分钟';
}

export function formatDefaultTimestamp(time?: Timestamp) {
    if (!time) {
        return ''
    }
    return moment(time.toDate()).format('YYYY/MM/DD HH:mm:ss')
}

export function formatDefaultDate(d: Date) {
    return moment(d).format('YYYY/MM/DD HH:mm:ss')
}

export function formatDisplayDate(time?: Timestamp) {
    if (!time) {
        return ''
    }
    return moment(time.toDate()).format('YYYY年MM月DD日')
}


export function formatDisplayTimestamp(time?: Timestamp) {
    if (!time) {
        return ''
    }
    return moment(time.toDate()).format('YYYY年MM月DD日 HH:mm:ss')
}

// 格式化文件大小
export function formatByteSize(count: bigint | number) {
    let bytes = Number(count);
    if (bytes >= 1073741824) {
        return (bytes / 1073741824).toFixed(2) + 'G';
    } else if (bytes >= 1048576) {
        return (bytes / 1048576).toFixed(2) + 'M';
    } else if (bytes >= 1024) {
        return (bytes / 1024).toFixed(2) + 'K';
    } else if (bytes > 1) {
        return bytes.toFixed(2) + '';
    } else if (bytes == 1) {
        return '1';
    } else {
        return '0';
    }
}


// 格式化文件大小
export function formatByteSizeYAxis(count: bigint | number) {
    let bytes = Number(count);

    if(bytes >= 1024) {
        return (bytes / 1024).toFixed(2) + 'G';
    }else if (bytes >= 1) {
        return (bytes).toFixed(0) + 'M';
    } else if (bytes < 1 && bytes > 0) {
        return (bytes * 1024).toFixed(2) + 'K';   
    } else if (bytes == 0) {
        return 0;
    }
}





export function formatIEC(n: number): string {
    let absN = Math.abs(n);
    if (absN < Math.pow(2, 10)) {
        return (absN / Math.pow(2, 0)).toFixed(2) + "  ";
    } else if (absN < Math.pow(2, 20)) {
        return (absN / Math.pow(2, 10)).toFixed(2) + "Ki";
    } else if (absN < Math.pow(2, 30)) {
        return (absN / Math.pow(2, 20)).toFixed(2) + "Mi";
    } else {
        return (absN / Math.pow(2, 30)).toFixed(2) + "Gi";
    }
}

export function formatSI(n: number, toFixed: boolean = true): string {
    let absN = Math.abs(n);
    if (absN < Math.pow(10, 3)) {
        return (absN / Math.pow(10, 0)).toFixed(toFixed ? 2 : 0) + " ";
    } else if (absN < Math.pow(10, 6)) {
        return (absN / Math.pow(10, 3)).toFixed(2) + "k";
    } else if (absN < Math.pow(10, 9)) {
        return (absN / Math.pow(10, 6)).toFixed(2) + "M";
    } else {
        return (absN / Math.pow(10, 9)).toFixed(2) + "G";
    }
}

function ipToNumber (ip: string): number {
    const ips = ip.split('.');
    return (Number(ips[0]) << 24) + (Number(ips[1]) << 16) + (Number(ips[2]) << 8) + Number(ips[3]);
}


// 判断某个ipV4地址是否在某个网段或者CIDR格式中
export function isIPV4InCIDR(ip: string, cidr: string): boolean {
    const [cidrIP, cidrMask] = cidr.split('/');
    const cidrIPNum = ipToNumber(cidrIP);
    const ipNum = ipToNumber(ip);
    const mask = 0xffffffff << (32 - Number(cidrMask));
    return (cidrIPNum & mask) === (ipNum & mask);
}

// 判断某个ipV4地址是否在某个IPV4网段中
export function isIPV4InIPV4Range(ip: string, range: string): boolean {
    const [startIP, endIP] = range.split('-');
    const startIPNum = ipToNumber(startIP);
    const endIPNum = ipToNumber(endIP);
    const ipNum = ipToNumber(ip);
    return ipNum >= startIPNum && ipNum <= endIPNum;
}



export function isIPInRange(ip: string, range: string): boolean {
    // 判断是否是合法的IPV4地址
    const IPV4RegExp: RegExp = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!IPV4RegExp.test(ip)) {
        return false;
    }

    if (range.includes('/')) {
        return isIPV4InCIDR(ip, range);
    } else if (range.includes('-')) {
        const IPRangeRegExp: RegExp = /^(\d{1,3}\.){3}\d{1,3}-(\d{1,3}\.){3}\d{1,3}$/;

        if (IPRangeRegExp.test(range)) {

            return isIPV4InIPV4Range(ip, range);
        } else {
            // 替换掉IP段中的最后一个值
            var range1Arr = range.split('-')[0].split('.')   
            range1Arr[3] = range.split('-')[1]
            return isIPV4InIPV4Range(ip, range.split('-')[0] + '-' + range1Arr.join('.'))
            
        }
    } else {
        return ip === range;
    }
}
