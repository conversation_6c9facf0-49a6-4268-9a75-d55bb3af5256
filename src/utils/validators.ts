import { RoutingPoolType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { isIPInRange } from "./format";

// 来自 https://regexlib.com/
const DNSRegExp: RegExp = /^[\dA-Za-z-]+$/

/**
 * 验证设备名称是否合法
 * @param name 
 * @returns 
 */
export function isValidDeviceName(name: string): string {
    let errMessage = "";
    if (name.length === 0) {
        errMessage = "名称不能为空";
    }
    if (name.length > 63) {
        errMessage = "名称长度必须小于64个字符";
    }
    if (name.startsWith("-") || name.endsWith("-")) {
        errMessage = "名称不能以'-'开头或结尾";
    }
    if (!DNSRegExp.test(name)) {
        errMessage = "名称只能包含字母、数字和'-'";
    }

    return errMessage;
}



/** 验证CIDR格式 */
export function isValidCIDR(cidr: string): string {
    let errMessage = "";
    if (cidr.length === 0) {
        errMessage = "子网路由不能为空";
    }
    if (cidr.length > 18) {
        errMessage = "子网路由长度必须小于18个字符";
    }


    // 验证CIDR正则表达式
    const CIDRRegExp: RegExp = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/;
    if (!CIDRRegExp.test(cidr)) {
        errMessage = "子网路由格式不正确";
    }

    if (cidr.indexOf('0.0.0.0/') >= 0) {
        errMessage = "子网路由不能以0.0.0.0开头";
    }

    if (cidr.indexOf('::/') >= 0) {
        errMessage = "子网路由不能以::开头";
    }


    return errMessage;
}

export function isValidIPOrIpRangeOrCIDR(src: string): string {
    let errMessage = "";
    if (src.length === 0) {
        errMessage = "IP地址不能为空";
    }
    // if (src.length > 18) {
    //     errMessage = "IP地址长度必须小于18个字符";
    // }

    let cidrRes = isValidIPRangeOrCIDR(src);
    let ipRes = isValidIP(src);

    if (cidrRes != '' && ipRes != '') {
        errMessage = "IP地址格式不正确";
    }


    return errMessage;
}

export function isValidIP(ip: string): string {
    let errMessage = "";
    if (ip.length === 0) {
        errMessage = "IP地址不能为空";
    }
    if (ip.length > 15) {
        errMessage = "IP地址长度必须小于15个字符";
    }

    // 验证IP正则表达式
    const IPRegExp: RegExp = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!IPRegExp.test(ip)) {
        errMessage = "IP地址格式不正确";
    }

    return errMessage;
}

/** 验证字段是否是IP段或者为CIDR格式 */
export function isValidIPRangeOrCIDR(srcStr: string): string {

    let src = srcStr ? srcStr.trim() : '';
    let errMessage = "";
    if (src.length === 0) {
        errMessage = "IP段或CIDR不能为空";
    }
    // if (src.length > 18) {
    //     errMessage = "IP段或CIDR长度必须小于18个字符";
    // }


    // 验证CIDR正则表达式
    const CIDRRegExp: RegExp = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/;
    if (!CIDRRegExp.test(src)) {
        // 验证IP段正则表达式
        const IPRangeRegExp: RegExp = /^(\d{1,3}\.){3}\d{1,3}-(\d{1,3}\.){3}\d{1,3}$/;
        if (!IPRangeRegExp.test(src)) {
            const IPRange2RegExp: RegExp = /^(\d{1,3}\.){3}\d{1,3}-\d{1,3}$/;
            if (!IPRange2RegExp.test(src)) {
                errMessage = "IP段或CIDR格式不正确";
            } else {

                const ipRange = src.split('-');
                for (let i = 0; i < 1; i++) {
                    for (let j = 0; j < 4; j++) {

                        if (Number(ipRange[i].split('.')[j]) > 255 || Number(ipRange[i].split('.')[j]) < 0) {
                            errMessage = "IP段格式不正确";
                        }
                    }
                }
                if (errMessage === "") {
                    if (Number(ipRange[0].split('.')[3]) > Number(ipRange[1])) {
                        errMessage = "IP段格式不正确";
                    } else if (Number(ipRange[1]) > 255 || Number(ipRange[1]) < 0) {
                        errMessage = "IP段格式不正确";
                    } else if (Number(ipRange[0].split('.')[3]) < 0 || Number(ipRange[0].split('.')[3]) > 255) {
                        errMessage = "IP段格式不正确";
                    }
                }
            }
        } else {
            const ipRange = src.split('-');
            for (let i = 0; i < ipRange.length; i++) {
                for (let j = 0; j < 4; j++) {

                    if (Number(ipRange[i].split('.')[j]) > 255 || Number(ipRange[i].split('.')[j]) < 0) {
                        errMessage = "IP段格式不正确";
                    }
                }
            }

            if (errMessage === "") {
                // 检查区间
                const ipRange1 = ipRange[0];
                const ipRange2 = ipRange[1];
                // IP地址转换成数字
                const ipRange1Num = ipRange1.split('.').reduce((sum, cur, index) => {
                    return sum + Number(cur) * Math.pow(256, 3 - index);
                }, 0);
                const ipRange2Num = ipRange2.split('.').reduce((sum, cur, index) => {
                    return sum + Number(cur) * Math.pow(256, 3 - index);
                }, 0);
                if (ipRange1Num > ipRange2Num) {
                    errMessage = "IP段格式不正确";
                }
            }
        }
    }

    return errMessage;
}

// 名称通用验证
export function isValidName(val: string) {
    const value = val.trim()
    if (!value) {
        return '名称不能为空';
    }
    if (!/^[\dA-Za-z-]+$/.test(value)) {
        return "名称只能包含字母、数字和'-'";
    }
    return '';
}
/**
 * 所有端口：*
单个端口：例如22或80
连续端口：例如1-65535或100-20000
多个端口：例如22,80,443或100-200,300-400或22,3389-3399
 */
export function isValidPortInput(input: string): boolean {
    
    // Check for all ports
    if (input === '*') {
        return true;
    }

    // Check for single port
    const singlePortRegex = /^\d+$/;
    if (singlePortRegex.test(input)) {


        if(Number(input) > 65535 || Number(input) < 0) {
            return false;
        }
        return true;
    }

    // Check for range of ports
    const rangePortRegex = /^\d+-\d+$/;
    if (rangePortRegex.test(input)) {
        const [start, end] = input.split('-');
        if(Number(start) > 65535 || Number(start) < 0) {
            return false;
        }
        if(Number(end) > 65535 || Number(end) < 0) {
            return false;
        }
        if (Number(start) > Number(end)) {
            return false;
        }
        return true;
    }

    // Check for multiple ports or ranges
    const multiplePortsRegex = /^(\d+(-\d+)?,)*\d+(-\d+)?$/;
    if (multiplePortsRegex.test(input)) {
        const ports = input.split(',');
        for (const port of ports) {
            if (!isValidPortInput(port)) {
                return false;
            }
        }

        return true;
    }

    

    // If none of the above conditions are met, return false
    return false;
}


/** 支持以下格式：
单个IP：**********
CIDR格式：**********/24
IP范围：**********-************或**********-255 */
export function isValidIPOrIpRangeOrCIDRByType(srcStr: string, type: RoutingPoolType) {
    let src = srcStr ? srcStr.trim() : '';
    let errMessage = "";
    if (src.length === 0) {
        errMessage = "IP段或CIDR不能为空";
    }

    interface IPRange {
        start: string;
        end: string;
    }

    const localIPRanges = [
        { start: "10.0.0.0", end: "**************" },
        { start: "**********", end: "**************" },
        { start: "***********", end: "***************" }
    ];
    const publicIPRanges = [
        { start: "*******", end: "*************" },
        { start: "1*******", end: "**************" },
        { start: "***********", end: "***************" },
        { start: "*********", end: "***************" },
        { start: "***********", end: "**************" },
        { start: "**********", end: "***************" },
        { start: "*********", end: "***********" },
        { start: "*********", end: "***********" },
        { start: "***********", end: "*************" },
        { start: "***********", end: "***************" },
        { start: "************", end: "**************" },
        { start: "************", end: "**************" },
        { start: "***********", end: "*************" },
        { start: "*********", end: "23*************" }, // 多播地址段
        { start: "240.0.0.0", end: "***************" } // 保留地址段
    ];

    const virturlIPRanges = [
        { start: "**********", end: "***************" }
    ];

    const isIpInRange = (ip: string, ipRanges: Array<IPRange>): boolean => {
        const ipToLong = (ip: string) => ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0);
        const longIP = ipToLong(ip);

        return ipRanges.some(range => {
            return longIP >= ipToLong(range.start) && longIP <= ipToLong(range.end);
        });
    }

    const getTypeError = (type: RoutingPoolType): string => {
        let message = '';

        switch (type) {
            case RoutingPoolType.VIRTUAL_IP:
                message = 'IP地址范围不对，虚拟IP范围为**********/10';
                break;
            case RoutingPoolType.LOCAL_IP:
                message = 'IP地址范围不对，本地IP范围为 10.0.0.0/8,**********/12,***********/16';
                break;
            case RoutingPoolType.PUBLIC_IP:
                message = 'IP地址范围不对，请输入公网IP地址';
                break;
        }
        return message;
    }


    const isValidIP = (ip: string, ipRanges: Array<IPRange>, type: RoutingPoolType): string => {
        let message = '';

        const ipRegex = /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$/;
        if (!ipRegex.test(ip)) {
            return '无效的IP地址';
        }

        let isInRange = isIpInRange(ip, ipRanges);

        if (!isInRange) {
            message = getTypeError(type);
        }

        return message;
    }

    const isValidCIDR = (cidr: string, ipRanges: Array<IPRange>, type: RoutingPoolType): string => {
        let message = '';
        const cidrRegex = /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/;
        if (!cidrRegex.test(cidr)) {
            message = '无效的CIDR格式';
        }

        const [baseIP, prefixLength] = cidr.split('/');

        let isInRange = isIpInRange(baseIP, ipRanges) && parseInt(prefixLength) >= 8 && parseInt(prefixLength) <= 24;
        if (!isInRange) {
            message = getTypeError(type);
        }
        return message;
    }

    const isValidIPRange = (range: string, ipRanges: Array<IPRange>, type: RoutingPoolType): string => {
        let message = '';
        const rangeRegex1 = /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])-(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$/;
        const rangeRegex2 = /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])-((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9]))$/;

        if (!rangeRegex1.test(range) && !rangeRegex2.test(range)) return '无效的IP地址范围';

        const [startIP, endIP] = range.split('-');

        let isInRange = isIpInRange(startIP, ipRanges) && (isValidIP(endIP, ipRanges, type) ? isIpInRange(endIP, ipRanges) : true);

        if (!isInRange) {
            message = getTypeError(type);
        }

        return message;
    }

    let ipRanges: Array<IPRange> = [];
    switch (type) {
        case RoutingPoolType.VIRTUAL_IP:
            ipRanges = virturlIPRanges;
            break;
        case RoutingPoolType.LOCAL_IP:
            ipRanges = localIPRanges;
            break;
        case RoutingPoolType.PUBLIC_IP:
            ipRanges = publicIPRanges;
            break;
        default:
            break;
    }

    let ipMessage = isValidIP(src, ipRanges, type);
    let cidrMessage = isValidCIDR(src, ipRanges, type);
    let ipRangeMessage = isValidIPRange(src, ipRanges, type);

    if (ipMessage == '' || cidrMessage == '' || ipRangeMessage == '') {
        return ''
    }

    if (ipMessage.indexOf('IP地址范围不对') >= 0 || cidrMessage.indexOf('IP地址范围不对') >= 0 || ipRangeMessage.indexOf('IP地址范围不对') >= 0) {
        if (ipMessage != '') {
            return ipMessage
        } else if (cidrMessage != '') {
            return cidrMessage
        } else {
            return ipRangeMessage
        }
    }


    return errMessage;
}

export function isValidDomain(domain: string): string {
    // 正则表达式用于验证域名格式
    const domainRegex = /^(?!-)[A-Za-z0-9-]{1,63}(?<!-)\.(?!-)([A-Za-z0-9-]{1,63}\.)*[A-Za-z]{2,63}$/;
    if(domainRegex.test(domain)) {
        return '';
    } else {
        return '域名格式不正确';
    }
}

export function validSearchDomainName(domain: string): string {

    if (!domain) {
        return '请输入名称'
    }
    

    if (domain.length > 253) return '总长度不能大于253'; // 总长度限制

    const labels = domain.split('.');
    // 校验第一段（允许包含 *）
    const first = labels[0];
    const firstIsValid = (
        /^(\*|[a-zA-Z0-9-]+|\*[\w-]+|[\w-]+\*)$/.test(first) &&
        !/^[-]|[-]$/.test(first) &&
        first.length <= 63
      );

    if (!firstIsValid) return "域名格式不正确";

    // 校验其余标签（不能包含 *，并必须是标准 label）
    const standardLabelPattern = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
    for (let i = 1; i < labels.length; i++) {
        const label = labels[i];
        if (label.includes('*')) return "*号位置只能在第一个标签";
        if (!standardLabelPattern.test(label)) return "域名格式不正确";
        if (label.length > 63) return "域名长度不能大于63"; // 每个标签长度限制
    }

    return "";
}