import React from 'react';
import { AclOrigin, AclGroup, AclOrigin_ResourceType, AclOrigin_Resource } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';

// 取得策略显示名称
export function getSimpleAclOriginName(origin: AclOrigin) {
    if(origin.description) {
        return `${origin.description}(${origin.name})`
    } else {
        return origin.name
    }
}

// 取得策略组显示名称
export function getSimpleAclGroupName(group: AclGroup) {
    if(group.alias) {
        return `${group.alias}(${group.name})`
    } else {
        return group.name
    }
}

// 对Acl_Origin的条目进行去重
export function uniqueAclOriginResource(origins: AclOrigin_Resource[]) {
    let newList = new Array<AclOrigin_Resource>();
    let map = new Map<string, AclOrigin_Resource>()


    origins.forEach(origin => {
        let key = origin.name + '-' + origin.type + '-' + origin.value
        if(!map.has(key)) {
            newList.push(origin)
            map.set(key, origin)
        }
    })
    return newList
}

export function getAclOriginElement(origin: AclOrigin): React.ReactElement {
    return <></>
}
