import { Service, ServiceGroup, ServiceNode, ServiceType, ServiceNodeType, ServiceRouteMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { Typography, Row, Col } from '@douyinfe/semi-ui';
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { SystemService } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/system_services_pb';

export const VITE_LOCAL_PAGER_AND_FILTER = import.meta.env.VITE_LOCAL_PAGER_AND_FILTER === 'true' ? true : false;
// 开启license功能
export const VITE_USE_LICENSE = import.meta.env.VITE_USE_LICENSE === 'true' ? true : false;
// 是否显示密钥功能
export const VITE_USE_KEY = import.meta.env.VITE_USE_KEY === 'true' ? true : false;
// 是否显示开发中功能
export const VITE_USE_DEVELOP_FEATURE = import.meta.env.VITE_USE_DEVELOP_FEATURE === 'true' ? true : false;

// 是否打策略补丁
export const VITE_USE_POLICY_PATCH = import.meta.env.VITE_USE_POLICY_PATCH === 'true' ? true : false;

const { Title, Paragraph, Text } = Typography;
// 获取服务显示名称
export function getSimpleServiceName(service: Service) {
    if (service.description) {
        return `${service.description}(${service.name})`
    } else {
        return service.name
    }
}


export function getSimpleNetworkServiceName(service: NetworkService) {
    if (service.description) {
        return `${service.description}(${service.name})`
    } else {
        return service.name
    }
}

export function getSimpleSystemServiceName(service: SystemService) {
    if (service.description) {
        return `${service.description}(${service.name})`
    } else {
        return service.name
    }
}

// 获取服务组显示名称
export function getSimpleServiceGroupName(group: ServiceGroup) {
    if (group.alias) {
        return `${group.alias}(${group.name})`
    } else {
        return group.name
    }
}

// 服务节点显示
export function getServiceNodeDisplay(record: Service, nameWidth: number = 200, ipWidth: number = 120) {
    let gatewayNodes: Array<ServiceNode> = [];
    let subnetNodes: Array<ServiceNode> = [];
    if (record.type == ServiceType.SYSTEM_DAEMON) {
        record.serviceNodes.forEach((item) => {
            if (item.type == ServiceNodeType.GATEWAY) {
                gatewayNodes.push(item)
            } else if (item.type == ServiceNodeType.SUBNET) {
                subnetNodes.push(item)
            }
        })
    }

    subnetNodes.sort((a, b) => {
        return a.rank - b.rank
    })
    gatewayNodes.sort((a, b) => {
        return a.rank - b.rank
    })

    const getNodeDisplay = (key: React.Key, node: ServiceNode) => {
        return <div key={key} style={{display: 'flex'}}>
            <Paragraph ellipsis style={{ maxWidth: nameWidth, flexGrow: 1, marginRight:10 }}>{node.name}</Paragraph>
            <Paragraph  style={{ width: ipWidth }} copyable>{node.ipv4}</Paragraph>
        </div>
    }

    return <>
        {record.type == ServiceType.REMOTE_DESKTOP &&
            <>
                {record.serviceNodes.map((n, i) => getNodeDisplay(i, n))}
            </>}
        {record.type == ServiceType.SYSTEM_DAEMON && record.routeMode == ServiceRouteMode.DIRECT && <>
            <div>
                {subnetNodes.map((n, i) => getNodeDisplay(i, n))}
            </div></>}
        {record.type == ServiceType.SYSTEM_DAEMON && record.routeMode == ServiceRouteMode.FORWARD && <>
            <div>
                <div className='mb10'>
                    <Title heading={6} style={{ fontSize: 14 }} >连接器</Title>
                    {gatewayNodes.map((n, i) => getNodeDisplay(i, n))}

                </div>
                <div>
                    <Title heading={6} style={{ fontSize: 14 }}>子网节点</Title>
                    {subnetNodes.map((n, i) => getNodeDisplay(i, n))}
                </div>
            </div>
        </>}
    </>
}

export function getNetworkServiceNodeDisplay(record: NetworkService, nameWidth: number = 200, ipWidth: number = 120) {
    

    return <>
        {record.nodes.map((n, i) => {
            return <div key={i} style={{display: 'flex'}}>
                <Paragraph ellipsis style={{ maxWidth: nameWidth, flexGrow: 1, marginRight:10 }}>{n.name}</Paragraph>
                <Paragraph  style={{ width: ipWidth }} copyable>{n.ip}</Paragraph>
            </div>
        })}
    </>
}

export function getSystemServiceNodeDisplay(record: SystemService, nameWidth: number = 200, ipWidth: number = 120) {
    return <>
        {record.nodes.map((n, i) => {
            return <div key={i} style={{display: 'flex'}}>
                <Paragraph ellipsis style={{ maxWidth: nameWidth, flexGrow: 1, marginRight:10 }}>{n.name}</Paragraph>
                <Paragraph  style={{ width: ipWidth }} copyable>{n.ip}</Paragraph>
            </div>
        })}
    </>
}

