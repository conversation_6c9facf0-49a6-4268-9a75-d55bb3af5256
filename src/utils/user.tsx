import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { Account } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/accounts_pb";
import { Avatar, Select, Col, Form, Typography, Tooltip } from '@douyinfe/semi-ui'
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import avatarDefault from '@/assets/avatar_default.jpg';
import Handlebars from 'handlebars';

import DOMPurify from 'dompurify';
import moment from "moment";

const { Text } = Typography;

export interface ParsedAccount extends Account {
    attrs: object
};

export interface ParsedUser extends User {
    account: ParsedAccount
}

export function parseUser(user: User): ParsedUser {
    if (!user.account) {
        user.account = new Account();
        user.account.attrsJson = "{}";
    }
    try {

        const attrs = JSON.parse(user.account.attrsJson);

        const obj =
        {
            ...user,
            account: {
                ...user.account,
                attrs
            }
        };
    
        return obj as ParsedUser;
    } catch (e) {
        return { ...user, account: { ...user.account, attrs: {} } } as ParsedUser;
    }
}


export function getUserDisplayName(user: User, templateUserListTitle: string) {

    const templateFunc = Handlebars.compile(templateUserListTitle);

    let val = "";
    try {
        const parsedUser = parseUser(user);

        val = templateFunc({ user: parsedUser });
    } catch (e) {
        console.error(e)
    }
    return <span dangerouslySetInnerHTML={{
        __html: DOMPurify.sanitize(val, { USE_PROFILES: { html: true } })
    }} />;
}


export function renderSelectedItem(optionNode: any, selectedUser?:User): React.ReactNode {
    const label = optionNode.label;
    const value = optionNode.value;
    
    if(!selectedUser) {
        return <span>{label}</span>;
    }
    
    const item: User = selectedUser;
    if (!item || !item.loginName) {
        return null;
    }
    return <div style={{ display: 'flex', alignItems: 'center' }}>
        <Avatar size="extra-small" style={{ flexShrink: 0 }} src={item.avatarUrl ? item.avatarUrl : avatarDefault} />
        <div style={{ marginLeft: 8 }}>
            <div style={{ fontSize: 14 }}>{item.displayName}(<Tooltip zIndex={9000} content={item.loginName}><Text ellipsis style={{maxWidth: 100}}>{item.loginName}</Text></Tooltip> )</div>
        </div></div>
};

export function renderCustomOption(item: User, index: number, userField: string, isDisable: boolean): React.ReactNode {
    const optionStyle = {
        display: 'flex',
        paddingLeft: 24,
        paddingTop: 10,
        paddingBottom: 10,
    };
    return (
        <Select.Option value={(item as any)[userField] + ''} style={optionStyle} showTick={true} key={index + 1} {...item} disabled={isDisable ? true : false}>
            <Avatar style={{ flexShrink: 0 }} size="extra-small" src={item.avatarUrl ? item.avatarUrl : avatarDefault} />
            <div style={{ marginLeft: 8 }}>
                <div style={{ fontSize: 14 }}><Text style={{width: 48}} ellipsis>{item.displayName}</Text>(<Tooltip zIndex={9000} content={`${item.displayName}(${item.loginName})`}><Text ellipsis style={{maxWidth: 100}}>{item.loginName}</Text></Tooltip> )</div>
            </div>
        </Select.Option>
    );
};

export function renderFormElement(accountAvailableFields: Field[]): React.ReactNode {
    let nodes: React.ReactNode[] = [];
    let nodeIndex = 0;
    accountAvailableFields.forEach((field, index) => {
        if (field.type == 'string') {
            let isRequired = false, isDate = false, isDateTime = false, isEmail = false, isHostname = false, isIpv4 = false, isIpv6 = false, isUri = false, isPassword = false;
            field.rules.forEach(rule => {
                if(rule.required) {
                    isRequired = true;
                } else if (rule.format) {
                    if (rule.format == 'date') {
                        isDate = true;
                    } else if (rule.format == 'date-time') {
                        isDateTime = true;
                    } else if (rule.format == 'email') {
                        isEmail = true;
                    } else if (rule.format == 'hostname') {
                        isHostname = true;
                    } else if (rule.format == 'ipv4') {
                        isIpv4 = true;
                    } else if (rule.format == 'ipv6') {
                        isIpv6 = true;
                    } else if (rule.format == 'uri') {
                        isUri = true;
                    } else if (rule.format == 'password') {
                        isPassword = true;
                    }
                }
            });

            if (isDate) {
                nodes.push(<Col span={12} key={index}>
                    <Form.DatePicker field={field.key}
                        validate={(value) => {
                            if(isRequired && !value) {
                                return `${field.label} 不能为空`;
                            }
                            return '';
                        }}
                        label={field.label} defaultValue={field.default} placeholder={field.placeholder} />
                </Col>)
            } else if (isDateTime) {
                nodes.push(<Col span={12} key={index}>
                    <Form.DatePicker type="dateTime" field={field.key}
                        validate={(value) => {
                            if(isRequired && !value) {
                                return `${field.label} 不能为空`;
                            }
                            return '';
                        }}
                        label={field.label} defaultValue={field.default} placeholder={field.placeholder} />
                </Col>)
            } else {
                nodes.push(<Col span={12} key={index}>
                    <Form.Input field={field.key} label={field.label} defaultValue={field.default} placeholder={field.placeholder}
                        validate={(value) => {
                            if(isRequired && !value) {
                                return `${field.label} 不能为空`;
                            } else if(isEmail && value && !/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isHostname && value && !/^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isIpv4 && value && !/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isIpv6 && value && !/^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isUri && value && !/^(http|https):\/\/[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isPassword && value && !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else {
                                
                                for (let i = 0; i < field.rules.length; i++) {
                                    let rule = field.rules[i];
                                    if (rule.pattern) {
                                        if (!new RegExp(rule.pattern).test(value)) {
                                            return `${field.label} 格式不正确`;
                                        }
                                    } else if (rule.minLength && rule.minLength > 0) {
                                        if (value.length < rule.minLength) {
                                            return `${field.label} 长度不能小于${rule.minLength}`;
                                        }
                                    } else if (rule.maxLength && rule.maxLength > 0) {
                                        if (value.length > rule.maxLength) {
                                            return `${field.label} 长度不能大于${rule.maxLength}`;
                                        }
                                    }

                                }
                            } 

                            return '';
                        }} />
                </Col>)
            }
            nodeIndex++;
        } else if (field.type == 'number') {
            let node = <Col span={12} key={index}>
                <Form.InputNumber field={field.key}
                    label={field.label} defaultValue={field.default} placeholder={field.placeholder}></Form.InputNumber>
            </Col>
            nodes.push(node);
            nodeIndex++;
        } else if (field.type == 'boolean') {
            let node = <Col span={12} key={index}>
                <Form.Switch field={field.key}  label={field.label} defaultValue={field.default} />
            </Col>
            nodes.push(node);
            nodeIndex++;
        } else if (field.type == 'array') {
            let node = <Col span={12} key={index}>
                <Form.TagInput field={field.key} addOnBlur
                    validate={(value) => {
                        if (field.rules.length > 0) {
                            for (let i = 0; i < field.rules.length; i++) {
                                let rule = field.rules[i];
                                if (rule.required) {
                                    if (!value) {
                                        return `${field.label} 不能为空`;
                                    }
                                }
                            }
                        }
                        return '';
                    }} label={field.label} defaultValue={field.default} placeholder={field.placeholder} />
            </Col>
            nodes.push(node);
            nodeIndex++;
        }
        if(nodeIndex % 2 == 0) {
            nodes.push(<div key={'span' + index} style={{width: '100%', float:'left'}}></div>);
        }

    });
    return nodes;

}

export function getFieldValue(val:any, field: Field):string {
    
    let value = '';
    if(field.type == 'string') {
        let isRequired = false, isDate = false, isDateTime = false;
        field.rules.forEach(rule => {
            if(rule.required) {
                isRequired = true;
            } else if (rule.format) {
                if (rule.format == 'date') {
                    isDate = true;
                } else if (rule.format == 'date-time') {
                    isDateTime = true;
                }
            }
        });

        if (isDate) {
            if(val) {
                let date = val as Date;
                value = moment(date).format('YYYY-MM-DD');
            }
        } else if (isDateTime) {
            if(val) {
                let date = val as Date;
                value = moment(date).format('YYYY-MM-DD HH:mm:ss');
            }
        } else {
            value = val ? val : '';
        }
    } else if (field.type == 'number') {
        value = val;
    } else if (field.type == 'boolean') {
        value = val;
    } else if (field.type == 'array') {
        value = val ? val.join(',') : '';
    }

    return value;
}

export function validateFields(errors: { [key: string]: string }, values: { [key: string]: any }, fields: Field[]) {
    fields.forEach(field => {
        let value = values[field.key];
        if (field.rules.length > 0) {
            for (let i = 0; i < field.rules.length; i++) {
                let rule = field.rules[i];
                if (rule.required) {
                    if (!value) {
                        errors[field.key] = `${field.label} 不能为空`;
                    }
                }
                if (rule.format) {
                    if (rule.format == 'date') {
                        if (value && !moment(value).isValid()) {
                            errors[field.key] = `${field.label} 格式不正确`;
                        }
                    } else if (rule.format == 'date-time') {
                        if (value && !moment(value).isValid()) {
                            errors[field.key] = `${field.label} 格式不正确`;
                        }
                    } else if (rule.format == 'email') {
                        if (value && !/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                            errors[field.key] = `${field.label} 格式不正确`;
                        }
                    } else if (rule.format == 'hostname') {
                        if (value && !/^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                            errors[field.key] = `${field.label} 格式不正确`;
                        }
                    } else if (rule.format == 'ipv4') {
                        if (value && !/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(value)) {
                            errors[field.key] = `${field.label} 格式不正确`;
                        }
                    } else if (rule.format == 'ipv6') {
                        if (value && !/^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(value)) {
                            errors[field.key] = `${field.label} 格式不正确`;
                        }
                    } else if (rule.format == 'uri') {
                        if (value && !/^(http|https):\/\/[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                            errors[field.key] = `${field.label} 格式不正确`;
                        }
                    }
                }
                if (rule.pattern) {
                    if (!new RegExp(rule.pattern).test(value)) {
                        errors[field.key] = `${field.label} 格式不正确`;
                    }
                }
                if(value) {
                    if (rule.minLength && rule.minLength > 0) {
                        if (value.length < rule.minLength) {
                            errors[field.key] = `${field.label} 长度不能小于${rule.minLength}`;
                        }
                    }
                    if (rule.maxLength) {
                        if (value.length > rule.maxLength && rule.maxLength > 0) {
                            errors[field.key] = `${field.label} 长度不能大于${rule.maxLength}`;
                        }
                    }
                }
            }
        }
    }
    );
}