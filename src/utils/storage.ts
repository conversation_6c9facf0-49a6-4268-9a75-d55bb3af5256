
export const LOCAL_STORAGE_FLYNET_ID = 'flynetId'
export const LOCAL_STORAGE_FLYNET_NAME = 'flynetName'
export const LOCAL_STORAGE_FLYNET_ALIAS = 'flynetAlias'

export const LOCAL_STORAGE_IS_SIGNIN = 'isSignIn';
export const LOCAL_STORAGE_AVATAR = 'avatar';

export const LOCAL_STORAGE_GUIDE_TYPE = 'GUIDE_TYPE';

export const LOCAL_GUIDE_FLAG = 'GUIDE_FLAG';

export const LOCAL_STORAGE_USER_ROLE = "userRole";

export const LOCAL_STORAGE_COLOR_MODE = "colorMode";

export const LOCAL_STORAGE_THEME = "theme";

export function setLocalStorage (key:string, val:string) {
   localStorage.setItem(key, val);
}

export function getLocalStorage(key:string) {
    return localStorage.getItem(key);
}
export function removeLocalStorage(key:string) {
    localStorage.removeItem(key);
}

export function clearLocalStorage() {
    localStorage.clear();
}

