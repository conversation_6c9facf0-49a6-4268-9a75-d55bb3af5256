import React, { useEffect, useState } from 'react';
import { LOCAL_STORAGE_COLOR_MODE, LOCAL_STORAGE_THEME, getLocalStorage, setLocalStorage } from "@/utils/storage";

const initColorMode = getLocalStorage(LOCAL_STORAGE_COLOR_MODE) || 'light';

const initTheme = getLocalStorage(LOCAL_STORAGE_THEME) || 'feiyue-cloud';


// 全局主题
export interface GlobalTheme {
    colorMode: string;
    theme: string;
}

// 全局主题hooks
export const useGlobalTheme = () => {

    const [theme, setTheme] = useState(initTheme);

    const [colorMode, setColorMode] = useState(initColorMode);

    // 切换颜色模式
    const switchColorMode = () => {

        const body = document.body;
        if (body.hasAttribute('theme-mode')) {
            
            body.removeAttribute('theme-mode');
            // 以下这行代码，window.setMode仅用于当通过本Demo切换时，通知Semi官网Header记录更新当前模式（只用于演示）。在您的代码里无需存在。
            
            setLocalStorage(LOCAL_STORAGE_COLOR_MODE, 'light');
            setColorMode('light')
        } else {
            
            body.setAttribute('theme-mode', 'dark');
            setLocalStorage(LOCAL_STORAGE_COLOR_MODE, 'dark');
            setColorMode('dark');
        }
    };

    return { colorMode, switchColorMode, theme, setTheme }
}


// 全局主题上下文
export const GlobalThemeContext = React.createContext<GlobalTheme>({ colorMode: initColorMode, theme: initTheme });