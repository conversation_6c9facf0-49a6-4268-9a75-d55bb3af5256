import React, { useEffect, useState } from 'react';
// import { License } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/license_pb';

import { Notification } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';

const useLicense = () => {
    const [license, setLicense] = useState<{
        activated: boolean,
        machineCode: string,
        expired: boolean,
        disabledExpiry: boolean,
        expiredAt?: Date,
        customer: {
            id: string,
            name: string,
            type: string,
        },
        entitlements: object

    }>({
        activated: false,
        machineCode: '',
        expired: false,
        disabledExpiry: false,
        expiredAt: undefined,
        customer: {
            id: '',
            name: '',
            type: '',
        },
        entitlements: {}
    });


    const getLicense = async () => {
        try {
            const response = await flylayerClient.getLicense({});
            if (response.license) {
                const machineCode = response.license.machineCode;

                if (response.license.licenseKey) {
                    try {
                        const json = JSON.parse(response.license.licenseKey);
                        setLicense({
                            activated: true,
                            machineCode: machineCode,
                            expired: json.expired,
                            // expired: true,
                            disabledExpiry: json.disable_expiry || false,
                            expiredAt: json.expired_at ? new Date(json.expired_at) : undefined,
                            customer: {
                                id: json.customer.id,
                                name: json.customer.name,
                                type: json.customer.type,
                            },
                            entitlements: json.entitlements
                        });
                    } catch (error) {
                        Notification.error({
                            content: 'licenseKey无效，请重新授权。',
                        });
                    }

                } else {
                    setLicense({
                        activated: false,
                        machineCode: machineCode,
                        expired: false,
                        disabledExpiry: false,
                        expiredAt: undefined,
                        customer: {
                            id: '',
                            name: '',
                            type: '',
                        },
                        entitlements: {}
                    });
                }
            }
        } catch (error) {
            Notification.error({
                content: '获取license信息失败, 请稍后重试。',

            });
        }
    }

    useEffect(() => {
        getLicense();
    }, []);

    return {
        license,
    };
};

// License 上下文
export const LicenseContext = React.createContext<{
    activated: boolean,
    machineCode: string,
    expired: boolean,
    expiredAt?: Date,
    disabledExpiry: boolean,
    customer: {
        id: string,
        name: string,
        type: string,
    },
    entitlements: object
}>({
    activated: true,
    machineCode: '',
    expired: false,
    expiredAt: undefined,
    disabledExpiry: false,
    customer: {
        id: '',
        name: '',
        type: '',
    },
    entitlements: {}
});

export { useLicense };