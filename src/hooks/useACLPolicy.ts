import { useState, useEffect, useCallback, useContext } from 'react'
import { flylayerClient } from '@/services/core';
import { ACLPolicy, ACL, SSHRule } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { LOCAL_STORAGE_AVATAR, LOCAL_STORAGE_IS_SIGNIN, LOCAL_STORAGE_FLYNET_ID, getLocalStorage, setLocalStorage, removeLocalStorage, clearLocalStorage } from '@/utils/storage';

/** 全局策略信息 */
const useAclPolicy = () => {
    // 全局策略
    const [aclPolicy, setAclPolicy] = useState<ACLPolicy>();
    // 是否正在查询全局策略
    const [isAclPolicyQuery, setIsAclPolicyQuery] = useState(true);

    useEffect(() => {
        const flynetIdStr = getLocalStorage(LOCAL_STORAGE_FLYNET_ID);
        if(!flynetIdStr) {
            return
        }

        const flynetId = BigInt(flynetIdStr);

        flylayerClient.getACLPolicy({
            flynetId: flynetId
        }).then(res => {
            if (res.policy) {
                setAclPolicy(res.policy);
            }
        }).catch(err => {
            console.error(err);
        }).finally(() => {
            setIsAclPolicyQuery(false);
        })
    }, [])

    return {isAclPolicyQuery, aclPolicy}
}

export default useAclPolicy;    

