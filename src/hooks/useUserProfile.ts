import { UserProfile } from '@/interface/user-profile';
import axios from 'axios';
import React, { useEffect, useRef, useState } from 'react';
import avatarDefault from '@/assets/avatar_default.jpg';
import { LOCAL_STORAGE_AVATAR, LOCAL_STORAGE_IS_SIGNIN, LOCAL_STORAGE_FLYNET_ID, getLocalStorage, setLocalStorage, removeLocalStorage, clearLocalStorage } from '@/utils/storage';


// 用户信息hooks
const useUserProfile = () => {
    let initAvator = getLocalStorage(LOCAL_STORAGE_AVATAR);
    if (!initAvator) {
        initAvator = avatarDefault;
    }

    /** 用户信息 */
    const [userProfile, setUserProfile] = useState<UserProfile>({
        id: '',
        identity: {
            schema_url: '',
            id: '',
            traits: {
                name: '',
                email: '',
                picture: initAvator
            }
        }
    });undefined

    let initSignIn = false;
    if (getLocalStorage(LOCAL_STORAGE_IS_SIGNIN)) {
        initSignIn = true;
    }

    /** 是否登录 */
    const [isSignIn, setIsSignIn] = useState(initSignIn);
    /** 是否正在查询用户信息 */
    const [isSignQuery, setIsSignQuery] = useState(true);

    const queryUser = () => {
        const flynetId = getLocalStorage(LOCAL_STORAGE_FLYNET_ID);
        // 查询用户信息
        axios.get('/auth/sessions/whoami', {
            headers: {
                "X-FlYNET-ID": flynetId ? flynetId : ''
            }
        }).then((res) => {
            const user: UserProfile = res.data;
            if (user.identity && user.identity.traits && !user?.identity?.traits?.picture) {
                user.identity.traits.picture = avatarDefault;
            }

            setUserProfile(user)
            setIsSignIn(true)
            setLocalStorage(LOCAL_STORAGE_IS_SIGNIN, 'isSignIn');
            const avatar = user.identity?.traits?.picture;
            setLocalStorage(LOCAL_STORAGE_AVATAR, avatar ? avatar : '');
        }, () => {
            removeLocalStorage(LOCAL_STORAGE_IS_SIGNIN);

            setIsSignIn(false)
        }).finally(() => setIsSignQuery(false))
    
    }

    useEffect(() => {
        queryUser();
        
    }, [])


    return { isSignIn, isSignQuery, userProfile }

}


/** 登出系统 */
export const doLogout = () => {
    return new Promise((resolve, reject) => {
        const flynetId = getLocalStorage(LOCAL_STORAGE_FLYNET_ID);
        axios.get('/auth/self-service/logout/browser').then(res => {
            if (res.data.logout_url) {
                clearLocalStorage();
                window.location.href = res.data.logout_url;
            }
        }, err => { console.error(err); reject(err) })
    });
}

export default useUserProfile;

// 用户配置信息上下文
export const UserProfileContext = React.createContext<UserProfile>({
    id: '',
    identity: {
        id: '',
        schema_url: '',
        traits: {
            name: '',
            email: '',
            picture: avatarDefault
        }
    }
})