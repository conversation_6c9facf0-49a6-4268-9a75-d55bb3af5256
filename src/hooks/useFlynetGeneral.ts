import React, { useEffect, useState } from 'react';
import { FlynetGeneral, getUserFlynetList } from '@/services/flynet';
import { Notification } from '@douyinfe/semi-ui';

import { LOCAL_STORAGE_FLYNET_ID, LOCAL_STORAGE_FLYNET_NAME, LOCAL_STORAGE_FLYNET_ALIAS, getLocalStorage, setLocalStorage, LOCAL_STORAGE_USER_ROLE } from '@/utils/storage';

import { UserFlynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";


/** 简略企业信息hooks */
const useFlynetGeneral = () => {

    let initID = getLocalStorage(LOCAL_STORAGE_FLYNET_ID);
    let initName = getLocalStorage(LOCAL_STORAGE_FLYNET_NAME);
    let initAlias = getLocalStorage(LOCAL_STORAGE_FLYNET_ALIAS);


    // flynet列表信息是否选中
    const [isFlynetGeneralSelected, setIsFlynetGeneralSelected] = useState(false);
    const [flynetGeneral, setFlynetGeneral] = useState<FlynetGeneral>({
        id: initID ? BigInt(initID) : BigInt(0),
        name: initName ? initName : '',
        alias: initAlias ? initAlias : '',
        disabledExpiry: true,
        expired: false,
        userRole: '',
        applicationEnabled: false
    });


    const selectFlynetGeneral = (flynetGeneral: FlynetGeneral) => {
        setFlynetGeneral(flynetGeneral);
        setIsFlynetGeneralSelected(true);
        setLocalStorage(LOCAL_STORAGE_FLYNET_ID, flynetGeneral.id.toString())
        setLocalStorage(LOCAL_STORAGE_FLYNET_NAME, flynetGeneral.name)
        setLocalStorage(LOCAL_STORAGE_FLYNET_ALIAS, flynetGeneral.alias)
        setLocalStorage(LOCAL_STORAGE_USER_ROLE, flynetGeneral.userRole)
    }


    /** 是否正在查询用户信息 */
    const [isFlynetQuery, setIsFlynetQuery] = useState(true);

    const [userFlynets, setUserFlynets] = useState<UserFlynet[]>([])

    const queryGeneralFlynet = async () => {
        getUserFlynetList().then(res => {
            let flynet: UserFlynet | undefined = undefined;
            if (initID) {
                let id = BigInt(initID);
                flynet = res.find(flynet => flynet.flynet?.id === id)
            }

            if (flynet) {
                if (flynet.flynet && flynet.flynet.activated) {
                    selectFlynetGeneral({
                        id: flynet.flynet?.id,
                        name: flynet.flynet?.name,
                        alias: flynet.flynet?.alias,
                        disabledExpiry: flynet.flynet.disabledExpiry,
                        expiresAt: flynet.flynet.expiresAt,
                        expired: flynet.flynet.expired,
                        userRole: flynet.user ? flynet.user.role.toString() : '',
                        applicationEnabled: flynet.flynet.applicationEnabled
                    })
                }
                else {
                    setIsFlynetGeneralSelected(false);
                }
            } else if (res && res.length == 1) {
                const fg = res[0].flynet;
                if (fg && fg.activated) {
                    selectFlynetGeneral({
                        id: fg.id,
                        name: fg.name,
                        alias: fg.alias,
                        disabledExpiry: fg.disabledExpiry,
                        expiresAt: fg.expiresAt,
                        expired: fg.expired,
                        userRole: res[0].user ? res[0].user.role.toString() : '',
                        applicationEnabled: fg.applicationEnabled
                    })
                }
            }

            setIsFlynetQuery(false)
            setUserFlynets(res)
        }, err => {
            Notification.error({ content: '查询网络异常，请稍后再试', position: "bottomRight" })
        }).finally(() => {
        })


    }


    useEffect(() => {
        queryGeneralFlynet()
    }, [])


    return { flynetGeneral, userFlynets, isFlynetQuery, isFlynetGeneralSelected, selectFlynetGeneral, queryGeneralFlynet }
}

export default useFlynetGeneral;


// 用户配置信息上下文
export const FlynetGeneralContext = React.createContext<FlynetGeneral>(
    {
        id: BigInt(0),
        name: '',
        alias: '',
        disabledExpiry: true,
        expired: false,
        userRole: '',
        applicationEnabled: false
    }
)