import { useContext, createContext } from 'react';

export const LocaleContext = createContext<{
	locale: string;
	switchLocale: (newLocale: string) => void;
}>({
	locale: 'zh_CN',
	switchLocale: () => {}
});

// 语言切换Hook
export const useLocaleSwitch = () => {
	const context = useContext(LocaleContext);
	if (!context) {
		throw new Error('useLocaleSwitch must be used within LocaleContext');
	}
	return context;
};
