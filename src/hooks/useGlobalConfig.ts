import React, { useEffect, useState } from 'react';

import { GlobalConfig } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb'
import { Notification } from '@douyinfe/semi-ui';

import { flylayerClient } from '@/services/core';

/** 全局配置信息 */
const useGlobalConfig = () => {
    // 全局配置
    const [globalConfig, setGlobalConfig] = useState<GlobalConfig>(new GlobalConfig({
        name: '',
        code: "",
        logo: "",
        favicon: "",
        flylayer: false,
        template: {
            userListTitle: ''
        }
        
    }));
    // 是否正在查询全局配置
    const [isGlobalConfigQuery, setIsGlobalConfigQuery] = useState(true);

    useEffect(() => {
        flylayerClient.getGlobalConfig({}).then((configRes) => {
            
            if (configRes.globalConfig) {
                setGlobalConfig(configRes.globalConfig)

            }



        }, () => {
            Notification.error({ content: '查询全局配置异常，请稍后再试', position: "bottomRight" })
        }).finally(() => {
            setIsGlobalConfigQuery(false);
        });

    }, [])

    return { isGlobalConfigQuery, globalConfig }
}

export default useGlobalConfig;

// 全局配置信息上下文
export const GlobalConfigContext = React.createContext<GlobalConfig>(new GlobalConfig({
    name: '',
    code: "",
    logo: "",
    favicon: "",
    flylayer: false,
    template: {
        userListTitle: ''
    }
})
);