import { FC, useContext } from 'react'
import { Popover, Avatar, Divider, Typography } from '@douyinfe/semi-ui';
import { UserProfileContext, doLogout } from '@/hooks/useUserProfile';
import { IconExit, IconBox, IconUserCircle } from '@douyinfe/semi-icons';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { useNavigate } from 'react-router-dom'
import styles from './index.module.scss';
import { BASE_PATH } from '@/constants/router';
import { LocaleFormatter } from '@/locales';

interface Props {
    userId?: string
}

const { Title, Paragraph } = Typography;
/** 网页右上角用户信息模块 */
const UserIndicator: FC<Props> = () => {
    

    const userProfile = useContext(UserProfileContext)
    const flynetGeneral = useContext(FlynetGeneralContext)

    const navigate = useNavigate();

    return <>
        <Popover
            content={
                <>
                    <div className={styles.userInfo}>
                        <Title heading={6} style={{ marginBottom: 0 }}>{userProfile?.identity?.traits?.name}</Title>
                        <Paragraph size='small' title={userProfile?.identity?.traits?.email} ellipsis={{
                        }} style={{ width: 150 }}>{userProfile?.identity?.traits?.email}</Paragraph>
                    </div>
                    <Divider></Divider>
                    <a className={styles.exit} href='/profile/'><LocaleFormatter id="components.userIndicator.profile" /> <IconUserCircle /></a>
                    <Divider></Divider>
                    {flynetGeneral.applicationEnabled && <><a onClick={() => { navigate(`${BASE_PATH}/start`) }} className={styles.exit}><LocaleFormatter id="components.userIndicator.appPanel" /> <IconBox /></a>
                    <Divider></Divider></>}
                    <a onClick={() => { doLogout().then(() => { navigate('/') }) }} className={styles.exit}><LocaleFormatter id="components.userIndicator.logout" /> <IconExit /></a></>
            }
        >
            <div className={styles.avatarCol}>
                <Avatar size='small' alt='User'><img src={userProfile?.identity?.traits?.picture} /></Avatar>
            </div>
        </Popover>

    </>
};

export default UserIndicator;
