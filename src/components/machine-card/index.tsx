import { FC } from 'react'
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'
import {
  Typography,
  Card,
  Descriptions,
  Button,
  Row,
  Col,
} from '@douyinfe/semi-ui'

import {
    IconDeleteStroked,
    
  } from '@douyinfe/semi-icons'
const {
    Title,
    Paragraph,
    Text
  } = Typography
  
const Index: FC<{
    machine: Machine
    onClear?: () => void,
    hideDelete?: boolean
  }> = (props) => {
    const machine = props.machine
    return <Card style={{ minWidth: 400 }}>
      <Row className="mb20">
        <Col span={20}>
          <Title heading={4}>设备:&nbsp;{machine.name}</Title>
        </Col>
        <Col span={4} style={{display: 'flex', justifyContent: 'flex-end'}}>
          {!props.hideDelete && <Button icon={<IconDeleteStroked />} onClick={() => {
            if (props.onClear) {
              props.onClear()
            }
          }}></Button>}
          {/*
                  <Button icon={<IconDeleteStroked />} onClick={() => {
                      if (props.onClear) {
                          props.onClear()
                      }
                  }}></Button> */}
        </Col>
      </Row>
  
      <Descriptions>
        <Descriptions.Item itemKey="用户">{machine.user?.displayName} <Text
          type="tertiary"> ({machine.user?.loginName})</Text></Descriptions.Item>
        <Descriptions.Item itemKey="IPv4">{machine.ipv4}</Descriptions.Item>
        <Descriptions.Item itemKey="IPv6">{machine.ipv6}</Descriptions.Item>
        <Descriptions.Item itemKey="操作系统">{machine.os}</Descriptions.Item>
      </Descriptions>
    </Card>
  }

  export default Index;