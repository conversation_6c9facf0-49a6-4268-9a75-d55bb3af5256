import React, { useContext } from 'react';
import Editor from '@monaco-editor/react';

import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { Button, Modal, Space } from "@douyinfe/semi-ui";
import { IconMaximize, IconMinimize } from '@douyinfe/semi-icons';

const Index: React.FC<{
    title: string,
    onCancel: () => void,
    onOk: (template: string) => void,
    value: string,
    language?: string,
    width: number,
    height?: number,
    loading?: boolean
}> = (props) => {
    const globalTheme = useContext(GlobalThemeContext);

    const [value, setValue] = React.useState(props.value);

    const [fullScreen, setFullScreen] = React.useState(false);

    return (
        <Modal
            title={
                <>{props.title}&nbsp;<Space>
                    {fullScreen ?
                        <Button size="small" onClick={() => setFullScreen(false)} icon={<IconMinimize />}></Button>
                        :
                        <Button size="small" onClick={() => setFullScreen(true)} icon={<IconMaximize />}></Button>
                    }
                </Space></>
            }
            
            visible={true}
            onCancel={props.onCancel}
            onOk={()=>{
                props.onOk(value || '')}}
            width={props.width}
            height={props.height}
            okButtonProps={{ loading: props.loading }}
            closeOnEsc={true}
            maskClosable={false}
            fullScreen={fullScreen}
        >
            <Editor
                height={'100%'}
                loading={props.loading}
                // height={props.height ? props.height - 100 : undefined}
                defaultLanguage={props.language || "javascript"}  // 设置默认语言
                value={props.value}

                onChange={(val) => {
                    setValue(val || '')
                }} // 设置值改变的回调 
                theme={globalTheme.colorMode === 'dark' ? 'vs-dark' : 'vs-light'}
            />
        </Modal>
    );
};

export default Index;