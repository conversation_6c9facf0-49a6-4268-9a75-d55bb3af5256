import { useEffect, useState, FC } from 'react';

import { IconSearch, IconFilter, IconChevronDown, IconPlusCircle, IconClose } from '@douyinfe/semi-icons';
import { Input, Dropdown, Button, Space, ButtonGroup, Row, Col } from '@douyinfe/semi-ui'

import FilterItem from './filter-item';
import { useLocale } from '@/locales';

// 过滤标签
export interface FilterParam {
    name: string,
    label: string,
    placeholder: string,
    value: string | any,
    filterComponent?: FC<{
        value: string | string[] | any,
        onChange: (val: string | string[] | any) => void
    }>,
    funGetDisplayValue?: (val: string | any) => string
}

const SearchFilter: FC<{
    className?: string,
    styles?: React.CSSProperties,
    filterSpan?: {
        left: number,
        right: number
    }
    filterParams: FilterParam[],

    onChange: (val: string, filterParam: FilterParam) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [step, setStep] = useState(0);

    const [usedFilterParams, setUsedFilterParams] = useState<FilterParam[]>([]);
    const [unUsedFilterParams, setUnUsedFilterParams] = useState<FilterParam[]>([]);

    useEffect(() => {
        const usedFilterParams = props.filterParams.filter((param, index) => index != 0 && param.value != '');
        const unUsedFilterParams = props.filterParams.filter((param, index) => index != 0 && param.value == '');
        setUsedFilterParams(usedFilterParams);
        setUnUsedFilterParams(unUsedFilterParams);
    }, [props.filterParams])

    const [filterDropdownVisible, setFilterDropdownVisible] = useState<boolean>(false);
    const [filterDisplayVisible, setFilterDisplayVisible] = useState<boolean>(false);
    const [filterDisplayIndex, setFilterDisplayIndex] = useState<number>(-1);

    const {
        keywords, setKeywords,
        filterParams, handleFilterChange,
        currentFilter, setCurrentFilter } = useFilterParams(props.filterParams);

    return <>
        <div className={props.className} style={props.styles}>
            <Row gutter={40}>
                <Col sm={24} md={10}>
                    <Row gutter={10}>
                        <Col span={16} className='mb10'>
                            <Input
                                value={keywords}
                                onChange={(value) => {
                                    setKeywords(value)
                                    if (filterParams.length > 0) {
                                        handleFilterChange(filterParams[0].name, value)
                                        props.onChange(value, filterParams[0])
                                    }
                                }}
                                prefix={<IconSearch />}
                                showClear
                                placeholder={filterParams.length > 0 ? filterParams[0].placeholder : ''} />
                        </Col>
                        {filterParams.length > 1 &&
                            <Col span={8} className='mb10'>
                                <Dropdown
                                    style={{ minWidth: 300 }}
                                    onVisibleChange={(visible) => {
                                        setFilterDropdownVisible(visible);
                                        if (!visible) {
                                            setStep(0);
                                        } else {
                                            setStep(1);
                                        }

                                        if (!visible) setCurrentFilter(undefined)
                                    }}
                                    visible={filterDropdownVisible}
                                    trigger='click'
                                    position='bottomRight'
                                    zIndex={1}
                                    render={
                                        currentFilter ? <FilterItem
                                            onChange={(val, filterParam) => {
                                                props.onChange(val, filterParam)
                                                setFilterDropdownVisible(false)
                                            }

                                            } back={() => {
                                                setCurrentFilter(undefined)
                                                setStep(1);
                                            }} filterParam={currentFilter} /> :
                                            <Dropdown.Menu>
                                                {
                                                    unUsedFilterParams.map((param, index) => {
                                                        return <Dropdown.Item style={{ minWidth: 150 }} key={index} onClick={(e) => {
                                                            e.stopPropagation();
                                                            setCurrentFilter(param)
                                                            setStep(2);
                                                        }}>
                                                            <IconPlusCircle />{param.label}
                                                        </Dropdown.Item>
                                                    })
                                                }
                                            </Dropdown.Menu>
                                    }
                                >
                                    <Button icon={<IconFilter />} disabled={unUsedFilterParams.length == 0}><span style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>{formatMessage({ id: 'common.filter' })}&nbsp;<IconChevronDown /></span></Button>
                                </Dropdown>
                            </Col>
                        }
                    </Row>
                </Col>
                <Col sm={24} md={14}>

                    {usedFilterParams.length > 0 &&
                        <div>
                            <Space>
                                {usedFilterParams.map((param, index) => {
                                    return <ButtonGroup key={index} style={{ flexWrap: 'nowrap' }}>
                                        <Dropdown trigger='click' zIndex={1}
                                            visible={filterDisplayVisible && filterDisplayIndex == index}
                                            onVisibleChange={(visible) => { setFilterDisplayVisible(visible); setFilterDisplayIndex(index) }}
                                            position='bottomLeft' render={
                                                <FilterItem onChange={(val, filterParam) => {
                                                    setFilterDisplayVisible(false)
                                                    setFilterDisplayIndex(-1)
                                                    props.onChange(val, filterParam)


                                                }} back={() => setCurrentFilter(undefined)} filterParam={param} />
                                            }><Button>{param.label}:{param.funGetDisplayValue ? param?.funGetDisplayValue(param.value) : param.value}</Button></Dropdown>
                                        <Button icon={<IconClose />} onClick={() => props.onChange('', param)} />
                                    </ButtonGroup>

                                })}
                            </Space>
                        </div>
                    }
                </Col>
            </Row>


        </div>
    </>
}

const useFilterParams = (params: FilterParam[]) => {
    const [keywords, setKeywords] = useState<string>('');
    const [filterParams, setFilterParams] = useState<FilterParam[]>(params);
    const [filterValues, setFilterValues] = useState<Record<string, string>>({});
    const [currentFilter, setCurrentFilter] = useState<FilterParam | undefined>(undefined);

    useEffect(() => {
        if (params.length > 0) setKeywords(params[0].value)
        setFilterParams(params)
    }, [params])

    const handleFilterChange = (name: string, value: string) => {
        setFilterValues({
            ...filterValues,
            [name]: value
        })
    }

    useEffect(() => {
        const newFilterParams = filterParams.map(param => {
            return {
                ...param,
                value: filterValues[param.name] || ''
            }
        })
        setFilterParams(newFilterParams)
    }, [filterValues])

    return {
        keywords, setKeywords,
        filterParams,
        handleFilterChange,
        currentFilter, setCurrentFilter
    }
}

export default SearchFilter;