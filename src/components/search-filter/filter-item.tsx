import { FC } from 'react';
import styles from './index.module.scss';
import { IconChevronLeft } from '@douyinfe/semi-icons';
import { Button } from '@douyinfe/semi-ui'
import { FilterParam } from '.';
const FilterItem: FC<{
    className?: string,
    styles?: React.CSSProperties,
    filterParam: FilterParam | undefined,
    back: Function,
    onChange: (val: string, filterParam: FilterParam) => void
}> = (props) => {
    const { filterParam } = props;
    return <>
        <div className={styles.filterItem}>
            <div className={styles.header}>
                <Button
                    style={{ marginRight: 10 }}
                    icon={<IconChevronLeft />}
                    size='small' onClick={() => props.back()} />
                <span>{filterParam?.label}</span>
            </div>
            <div className={styles.body}>
                {
                    filterParam?.filterComponent && <filterParam.filterComponent value={filterParam.value}
                        onChange={(val: string) => { props.onChange(val, filterParam); props.back() }} />}
            </div>

        </div>
    </>
}

export default FilterItem;