import { FC, useState } from 'react'
import { Typography, Modal, Notification, Tag, TabPane, Form, Row, Col } from '@douyinfe/semi-ui';
import { DNSConfig, Routes } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';

import IconSplitDns from "@/assets/icon/split-dns.svg";
import styles from './index.module.scss';
import { DOMAIN_STATIC_IP } from '@/constants';
import { useLocale } from '@/locales';
const { Paragraph, Title } = Typography;

interface Props {
    flynetId: bigint,
    close: () => void,
    success: (record: DNSConfig) => void,
    saveLoading: boolean
    record: DNSConfig,
    data: {
        ip: string,
        domain: string,
        useSplitDNS: boolean
    }
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()


    const handleOk = async () => {
        if (!formApi) {
            return
        }
        const values = formApi?.getValues();
        values.ip = values.ip.trim();
        if (values.domain) {
            values.domain = values.domain.toLowerCase();
            values.domain = values.domain.trim();
        }
        formApi.validate().then((res) => {

            let config = { ...props.record }
            if (values.useSplitDNS && values.domain) {


                if (props.data.useSplitDNS) {
                    if (config.routes) {
                        if (config.routes[props.data.domain]) {
                            config.routes[props.data.domain].routes = config.routes[props.data.domain].routes.filter((val) => val != props.data.ip)
                            if (config.routes[props.data.domain].routes.length == 0) {
                                delete config.routes[props.data.domain]
                            }
                            
                        }
                    }
                }

                else {
                    if (config.nameservers) {
                        config.nameservers = config.nameservers.filter((val) => val != props.data.ip)
                    }
                }


                if (config.routes) {
                    if (config.routes[values.domain]) {
                        config.routes[values.domain].routes.push(values.ip)
                    } else {
                        config.routes[values.domain] = new Routes({ routes: [values.ip] })
                    }

                } else {
                    config.routes = {
                        [values.domain]: new Routes({ routes: [values.ip] })
                    }
                }
            } else if (values.ip) {

                if (props.data.useSplitDNS) {
                    if (config.routes) {
                        if (config.routes[props.data.domain]) {
                            config.routes[props.data.domain].routes = config.routes[props.data.domain].routes.filter((val) => val != props.data.ip)
                            if(config.routes[props.data.domain].routes.length == 0  ) {
                                delete config.routes[props.data.domain]
                            }
                        }
                    }

                    if (config.nameservers) {
                        config.nameservers.push(values.ip)
                    } else {
                        config.nameservers = [values.ip]
                    }
                }

                else {
                    if (config.nameservers) {
                        config.nameservers = config.nameservers.map((val) => {
                            if (val == props.data.ip) {
                                return values.ip
                            }
                            return val;
                        })
                    }
                }

            }

            props.success(new DNSConfig(config));
            
        }).catch((err) => {
            console.error(err)
        })


    }


    const validateIP = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'devices.dnsManager.validation.ipRequired' });
        }
        if (value == DOMAIN_STATIC_IP) {
            return formatMessage({ id: 'devices.dnsManager.validation.cannotUseDefaultIP' });
        }
        const reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'devices.dnsManager.validation.invalidIP' });
        }

        const values = formApi?.getValues();


        if (props.data.useSplitDNS) {
            if (values.useSplitDNS) {
                if (values.domain == props.data.domain && values.ip != props.data.ip) {
                    if (props.record.routes) {
                        if (props.record.routes[values.domain]) {
                            if (props.record.routes[values.domain].routes.includes(value)) {
                                return formatMessage({ id: 'devices.dnsManager.validation.ipExists' });
                            }
                        }
                    }
                }

                else if (values.domain != props.data.domain) {
                    if (props.record.routes) {
                        if (props.record.routes[values.domain]) {
                            if (props.record.routes[values.domain].routes.includes(value)) {
                                return formatMessage({ id: 'devices.dnsManager.validation.ipExists' });
                            }
                        }
                    }
                }

            }

            else {
                if (props.record.nameservers) {
                    if (props.record.nameservers.includes(value)) {
                        return formatMessage({ id: 'devices.dnsManager.validation.ipExists' });
                    }
                }
            }
        }

        else {


            if (values.useSplitDNS) {
                if (props.record.routes) {
                    if (props.record.routes[values.domain]) {
                        if (props.record.routes[values.domain].routes.includes(value)) {
                            return formatMessage({ id: 'devices.dnsManager.validation.ipExists' });
                        }
                    }
                }
            }

            else {

                if (values.ip != props.data.ip) {
                    if (props.record.nameservers) {
                        if (props.record.nameservers.includes(value)) {
                            return formatMessage({ id: 'devices.dnsManager.validation.ipExists' });
                        }
                    }
                }
            }

        }

        return "";
    }

    const validateDomain = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'devices.dnsManager.validation.domainRequired' });
        }
        const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'devices.dnsManager.validation.invalidDomainFormat' });
        }
        return "";
    }

    return <>
        <Modal
            width={500}
            title={formatMessage({ id: 'devices.dnsManager.editDNSServer' })}
            visible={true}
            okButtonProps={{ loading: props.saveLoading }}
            onOk={handleOk}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Form
                getFormApi={SetFormApi}
                initValues={props.data}
                render={(formState) => {
                    return <> <Title heading={6}>{formatMessage({ id: 'devices.dnsManager.title' })}</Title>
                        <Paragraph type="tertiary">{formatMessage({ id: 'devices.dnsManager.ipDescription' })}</Paragraph>
                        <Form.Input noLabel className='mb20' field='ip' validate={validateIP}></Form.Input>
                        <Row>
                            <Col span={12}>
                                <Title heading={6}>{formatMessage({ id: 'devices.dnsManager.applyToSpecificDomain' })}<Tag style={{ marginLeft: 8 }}> <img className={styles.iconSplitDNS} src={IconSplitDns}></img>&nbsp;{formatMessage({ id: 'devices.dnsManager.restrictedDomain' })}</Tag></Title>
                            </Col>
                            <Col span={12}>
                                <div className={styles.colFormItem}>
                                    <Form.Switch noLabel field='useSplitDNS' onChange={() => setTimeout(() => formApi?.validate(), 100)} />
                                </div>
                            </Col>
                        </Row>
                        <Paragraph type="tertiary" className='mb20'>{formatMessage({ id: 'devices.dnsManager.splitDNSDescription' })}</Paragraph>
                        {formState.values.useSplitDNS && <>

                            <Title heading={6}>{formatMessage({ id: 'devices.dnsManager.domain' })}</Title>
                            <Form.Input placeholder={'example.com'} onChange={() => setTimeout(() => formApi?.validate(), 100)} validate={validateDomain} className='mb0' noLabel field='domain'></Form.Input>
                            <Paragraph type='tertiary'>
                                {formatMessage({ id: 'devices.dnsManager.domainUsageDescription' })}
                            </Paragraph>
                        </>}
                    </>
                }}
            >

            </Form>
        </Modal>
    </>
}

export default Index;