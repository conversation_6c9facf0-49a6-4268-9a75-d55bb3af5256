import { FC, useState } from "react";
import { Typo<PERSON>, Button, Dropdown, List, Tag, Row, Col, Banner, Popover, Switch, Popconfirm } from '@douyinfe/semi-ui';
import { IconMore, IconGlobeStroke, IconInfoCircle } from '@douyinfe/semi-icons';
import { DNSConfig, Routes } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import AddNameserver from "@/components/dns-manager/add-nameserver";
import EditNameserver from "@/components/dns-manager/edit-nameserver";

import IconSplitDns from "@/assets/icon/split-dns.svg";
import styles from './index.module.scss';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;

interface Props {
    flynetId: bigint,
    onSave: (dnsConfig: DNSConfig) => void
    dnsConfig: DNSConfig,
    saveLoading: boolean
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    const getRoutes = (domain: string, config: DNSConfig): string[] => {
        let routes = config.routes[domain];
        if (routes) {
            return routes.routes
        }
        return [];
    }
    const [dnsConfig, setDnsConfig] = useState<DNSConfig>(props.dnsConfig);

    let initSplitRoutes: Array<{
        domain: string,
        routes: Array<string>
    }> = []

    Object.keys(dnsConfig.routes).forEach(domain => {
        initSplitRoutes.push({
            domain,
            routes: getRoutes(domain, dnsConfig)
        })
    })
    const [splitRoutes, setSplitRoutes] = useState<{
        domain: string,
        routes: Array<string>
    }[]>(initSplitRoutes);
    const [editNameserverData, setEditNameserverData] = useState({
        ip: '',
        domain: '',
        useSplitDNS: false
    });
    const [addNameserverVisible, setAddNameserverVisible] = useState(false);
    const [editNameserverVisible, setEditNameserverVisible] = useState(false);

    const [overrideLocalDnsLoading, setOverrideLocalDnsLoading] = useState(false);
    const handleOverrideLocalDnsChange = (checked: boolean) => {
        // setOverrideLocalDnsLoading(true);
        if (dnsConfig) {
            let config = new DNSConfig({
                ...dnsConfig,
                overrideLocalDns: checked
            });
            setDnsConfig(config);
            props.onSave(config);
        } else {
            let config = new DNSConfig({
                overrideLocalDns: checked
            });
            setDnsConfig(config);
            props.onSave(config);
        }

    }



    const handleDeleteGlobalNameserver = (nameserver: string) => {
        let config = new DNSConfig({
            ...dnsConfig,
            nameservers: dnsConfig?.nameservers?.filter(item => item !== nameserver),
        });

        setDnsConfig(config);
        props.onSave(config);
    }


    const handleDeleteNameserver = (domain: string, ip: string) => {
        
        let routers = new Map<string, Routes>();


        let initSplitRoutes: Array<{
            domain: string,
            routes: Array<string>
        }> = []
        if (dnsConfig?.routes) {
            Object.keys(dnsConfig?.routes).forEach(key => {
                if (key != domain) {
                    routers.set(key, dnsConfig?.routes[key]);
                    initSplitRoutes.push({
                        domain: key,
                        routes: dnsConfig?.routes[key].routes
                    })
                } else {
                    let routes = dnsConfig?.routes[key];
                    if (routes) {
                        routes.routes = routes.routes.filter(item => item !== ip);
                        if (routes.routes.length > 0) {
                            routers.set(key, routes);
                            initSplitRoutes.push({
                                domain: key,
                                routes: routes.routes
                            })
                        } else {
                            routers.delete(key);
                            delete dnsConfig.routes[key];
                        }
                    }
                }
            })
        }

        let config = {
            ...dnsConfig,
            routers: routers,
        };

        setSplitRoutes(initSplitRoutes);
        setDnsConfig(new DNSConfig(config));
        props.onSave(new DNSConfig(config));

    }

    return <>
        <section className="mb40">
            <Title heading={4} className="mb2">{formatMessage({ id: 'devices.dnsManager.title' })}</Title>
            <Paragraph type='tertiary' className='mb20'>{formatMessage({ id: 'devices.dnsManager.description' })}</Paragraph>
            <div className={styles.nameForm}>
                {
                    splitRoutes.map((item, index) => {
                        return <div key={index} className={styles.nameForm}>
                            <Title
                                className={styles.splitDNSTitle}
                                heading={6}>
                                <IconGlobeStroke className={styles.splitDNSIcon} /> {item.domain}
                                <Tag style={{ marginLeft: 8 }}> <img className={styles.iconSplitDNS} src={IconSplitDns}></img>&nbsp;{formatMessage({ id: 'devices.dnsManager.restrictedDomain' })}</Tag>
                                {/* <SortBar curIndex={countSplitRoute - 1} total={splitRouteCount} onChange={() => { }}></SortBar> */}
                            </Title>
                            <List className={styles.ipList}>
                                {item.routes.map(ip => {
                                    return <List.Item key={ip} className={styles.ipListItem}>{ip}<Dropdown
                                        trigger={'hover'}
                                        position={'bottomLeft'}
                                        render={
                                            <Dropdown.Menu>
                                                <Dropdown.Item onClick={() => {
                                                    setEditNameserverData({
                                                        ip: ip,
                                                        domain: item.domain,
                                                        useSplitDNS: true
                                                    });
                                                    setEditNameserverVisible(true);

                                                }}>{formatMessage({ id: 'components.common.edit' })}</Dropdown.Item>
                                                <Dropdown.Divider></Dropdown.Divider>

                                                <Popconfirm
                                                    title={formatMessage({ id: 'resources.delete.confirmTitle' })}
                                                    content={formatMessage({ id: 'resources.delete.confirmContent' })}
                                                    onConfirm={() => handleDeleteNameserver(item.domain, ip)}
                                                ><Dropdown.Item type="danger">{formatMessage({ id: 'components.common.delete' })}</Dropdown.Item>
                                                </Popconfirm>
                                            </Dropdown.Menu>
                                        }
                                    >
                                        <IconMore />
                                    </Dropdown></List.Item>
                                })}
                            </List>
                        </div>
                    })
                }
                <Row>
                    <Col span={12}> <Title className="mb10" heading={6} >{formatMessage({ id: 'devices.dnsManager.globalDNS' })}</Title></Col>
                    <Col span={12}>
                        <div className={styles.overrideLocalDns}>
                            <Popover content={<div className="p10 mw400">{formatMessage({ id: 'devices.dnsManager.overrideLocalDNSDescription' })}</div>}>
                                <IconInfoCircle />
                            </Popover>&nbsp;
                            {formatMessage({ id: 'devices.dnsManager.overrideLocalDNS' })}&nbsp;<Switch disabled={dnsConfig?.nameservers.length == 0} size="small" loading={overrideLocalDnsLoading} onChange={handleOverrideLocalDnsChange} checked={dnsConfig?.overrideLocalDns}></Switch>
                        </div>
                    </Col>
                </Row>
                {dnsConfig?.overrideLocalDns && dnsConfig?.nameservers.length == 0 ? <Banner className={styles.banner} type="info" description={formatMessage({ id: 'devices.dnsManager.noGlobalDNSConfigured' })} closeIcon={null} ></Banner> : <List className={styles.ipList}>
                    {!dnsConfig?.overrideLocalDns ? <List.Item className={styles.ipListItem} style={{ color: 'var(--semi-color-text-2)' }}>{formatMessage({ id: 'devices.dnsManager.localDNSSettings' })}</List.Item>
                        : ''}
                    {dnsConfig?.nameservers.map((item, index) => {
                        return <List.Item key={index} className={styles.ipListItem}>
                            {item}

                            <Dropdown
                                trigger={'hover'}
                                position={'bottomLeft'}
                                render={
                                    <Dropdown.Menu>
                                        <Dropdown.Item onClick={() => {
                                            setEditNameserverData({
                                                ip: item,
                                                domain: '',
                                                useSplitDNS: false
                                            });
                                            setEditNameserverVisible(true);
                                        }}>{formatMessage({ id: 'components.common.edit' })}</Dropdown.Item>
                                        <Dropdown.Divider></Dropdown.Divider>
                                        <Popconfirm
                                            title={formatMessage({ id: 'resources.delete.confirmTitle' })}
                                            content={formatMessage({ id: 'resources.delete.confirmContent' })}
                                            onConfirm={() => handleDeleteGlobalNameserver(item)}
                                        ><Dropdown.Item type="danger">{formatMessage({ id: 'components.common.delete' })}</Dropdown.Item>
                                        </Popconfirm>
                                    </Dropdown.Menu>
                                }
                            >
                                <IconMore />
                            </Dropdown>

                        </List.Item>
                    })}
                </List>}

                <Button theme="solid" onClick={() => setAddNameserverVisible(true)}>{formatMessage({ id: 'devices.dnsManager.addDNSServer' })}</Button>
            </div>
        </section>

        {addNameserverVisible && dnsConfig ? <AddNameserver
            flynetId={props.flynetId}
            record={dnsConfig}
            saveLoading={props.saveLoading}
            success={(record: DNSConfig) => {


                let initSplitRoutes: Array<{
                    domain: string,
                    routes: Array<string>
                }> = []
                Object.keys(record.routes).forEach(domain => {
                    initSplitRoutes.push({
                        domain,
                        routes: getRoutes(domain, dnsConfig)
                    })
                })
                setSplitRoutes(initSplitRoutes);

                props.onSave(record);
                setDnsConfig(record);
                setAddNameserverVisible(false);
            }}
            close={() => setAddNameserverVisible(false)}
        ></AddNameserver> : null}

        {editNameserverVisible && dnsConfig ? <EditNameserver
            flynetId={props.flynetId}
            record={dnsConfig}
            saveLoading={props.saveLoading}
            success={(record: DNSConfig) => {


                let initSplitRoutes: Array<{
                    domain: string,
                    routes: Array<string>
                }> = []
                Object.keys(record.routes).forEach(domain => {
                    initSplitRoutes.push({
                        domain,
                        routes: getRoutes(domain, dnsConfig)
                    })
                })
                setSplitRoutes(initSplitRoutes);
                props.onSave(record);
                setDnsConfig(record);
                setEditNameserverVisible(false);
            }}
            close={() => setEditNameserverVisible(false)}
            data={editNameserverData}
        ></EditNameserver> : null}
    </>
}


export default Index;
