import { FC, useState } from 'react';
import styles from './index.module.scss';
import { IconChevronLeft } from '@douyinfe/semi-icons';
import { Button, Divider } from '@douyinfe/semi-ui'
import { FilterParam } from '.';
import { useLocale } from '@/locales';
const FilterItem: FC<{
    className?: string,
    styles?: React.CSSProperties,
    filterParam: FilterParam | undefined,
    back: Function,
    onChange: (val: string, filterParam: FilterParam) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const { filterParam } = props;
    const [value, setValue] = useState<string | string[] | any>(filterParam?.value);
    return <>
        <div className={styles.filterItem}>
            <div className={styles.header}>
                <Button
                    style={{ marginRight: 10 }}
                    icon={<IconChevronLeft />}
                    size='small' onClick={() => props.back()} />
                <span>{filterParam?.label}</span>
            </div>
            <div className={styles.body}>
                {
                    filterParam?.filterComponent && <filterParam.filterComponent value={value} 
                        onChange={(val: string) => { setValue(val)  }} />}

                    <Divider className='mb10' style={{marginTop:10}}/>
                    <Button block onClick={() => {
                        props.onChange(value, filterParam!);                        
                        props.back()
                    }}>{formatMessage({ id: 'common.apply' })}</Button>
            </div>

        </div>
    </>
}

export default FilterItem;