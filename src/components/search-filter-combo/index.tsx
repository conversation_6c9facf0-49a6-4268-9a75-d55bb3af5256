import { useEffect, useState, FC } from 'react';

import { IconSearch, IconFilter, IconChevronDown, IconPlusCircle, IconClose } from '@douyinfe/semi-icons';
import { Input, Dropdown, Button, Space, ButtonGroup } from '@douyinfe/semi-ui'
import FilterItem from './filter-item';
import { useLocale } from '@/locales';
// 过滤标签
export interface FilterParam {
    name: string,
    label: string,
    placeholder: string,
    value: string | any,
    fixed?: boolean,
    filterComponent?: FC<{
        value: string | string[] | any,
        onChange: (val: string | string[] | any) => void
    }>,
    funGetDisplayValue?: (val: string | any) => string
}

const SearchFilterCombo: FC<{
    className?: string,
    styles?: React.CSSProperties,
    filterSpan?: {
        left: number,
        right: number
    }
    filterParams: FilterParam[],

    onChange: (val: string, filterParam: FilterParam) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [usedFilterParams, setUsedFilterParams] = useState<FilterParam[]>([]);
    const [unUsedFilterParams, setUnUsedFilterParams] = useState<FilterParam[]>([]);

    useEffect(() => {
        const usedFilterParams = props.filterParams.filter((param, index) => index != 0 && param.value != '' && !param.fixed);
        const unUsedFilterParams = props.filterParams.filter((param, index) => index != 0 && param.value == '' && !param.fixed);
        setUsedFilterParams(usedFilterParams);
        setUnUsedFilterParams(unUsedFilterParams);
    }, [props.filterParams])

    const [filterDropdownVisible, setFilterDropdownVisible] = useState<boolean>(false);
    const [filterDisplayVisible, setFilterDisplayVisible] = useState<boolean>(false);
    const [filterDisplayIndex, setFilterDisplayIndex] = useState<number>(-1);

    const {
        keywords, setKeywords,
        filterParams, handleFilterChange,
        currentFilter, setCurrentFilter } = useFilterParams(props.filterParams);

    return <>
        <div className={props.className} style={props.styles}>
            <Space style={{ flexWrap: 'wrap' }}>
                <Input
                    value={keywords}
                    onChange={(value) => {
                        setKeywords(value)
                        if (filterParams.length > 0) {
                            handleFilterChange(filterParams[0].name, value)
                            props.onChange(value, filterParams[0])
                        }
                    }}
                    prefix={<IconSearch />}
                    showClear
                    style={{ width: 400 }}
                    placeholder={filterParams.length > 0 ? filterParams[0].placeholder : ''} />
                {filterParams.length > 0 && filterParams.map((filterParam, index) => {
                    if (index == 0 || !filterParam.fixed || !filterParam.filterComponent) return null;
                    return <filterParam.filterComponent key={index} value={filterParam.value}
                        onChange={(val: string) => {
                            handleFilterChange(filterParam.name, val)
                            props.onChange(val, filterParam)
                        }} />
                })}
                {(unUsedFilterParams.length > 0 || usedFilterParams.length > 0) &&
                    <Dropdown
                        onVisibleChange={(visible) => {
                            setFilterDropdownVisible(visible);
                            if (!visible) setCurrentFilter(undefined)
                        }}
                        visible={filterDropdownVisible}
                        trigger='click'
                        position='bottomRight'
                        zIndex={1}
                        render={
                            currentFilter ? <FilterItem onChange={(val, filterParam) => {
                                props.onChange(val, filterParam)
                                setFilterDropdownVisible(false)
                            }} back={() => {
                                setCurrentFilter(undefined)
                            }} filterParam={currentFilter} /> :
                                <Dropdown.Menu>
                                    {
                                        unUsedFilterParams.map((param, index) => {
                                            if (param.fixed) return null;
                                            return <Dropdown.Item style={{ minWidth: 150 }} key={index} onClick={(e) => {
                                                e.stopPropagation();
                                                setCurrentFilter(param)
                                            }}>
                                                <IconPlusCircle />{param.label}
                                            </Dropdown.Item>
                                        })
                                    }
                                </Dropdown.Menu>
                        }
                    >
                        <Button icon={<IconFilter />} disabled={unUsedFilterParams.length == 0}><span style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>{formatMessage({ id: 'common.filter' })}&nbsp;<IconChevronDown /></span></Button>
                    </Dropdown>}
                {usedFilterParams.length > 0 &&

                    usedFilterParams.map((param, index) => {
                        return <ButtonGroup key={param.name + index} style={{ flexWrap: 'nowrap' }}>
                            <Dropdown trigger='click' zIndex={1}
                                visible={filterDisplayVisible && filterDisplayIndex == index}
                                onVisibleChange={(visible) => { setFilterDisplayVisible(visible); setFilterDisplayIndex(index) }}
                                position='bottomLeft' render={
                                    <FilterItem onChange={(val, filterParam) => {
                                        setFilterDisplayVisible(false)
                                        setFilterDisplayIndex(-1)
                                        props.onChange(val, filterParam)

                                    }} back={() => setCurrentFilter(undefined)} filterParam={param} />
                                }><Button>{param.label}:{param.funGetDisplayValue ? param?.funGetDisplayValue(param.value) : param.value}</Button></Dropdown>
                            <Button icon={<IconClose />} onClick={() => props.onChange('', param)} />
                        </ButtonGroup>

                    })


                }
            </Space>


        </div>
    </>
}

const useFilterParams = (params: FilterParam[]) => {
    const [keywords, setKeywords] = useState<string>('');
    const [filterParams, setFilterParams] = useState<FilterParam[]>(params);
    const [filterValues, setFilterValues] = useState<Record<string, string>>({});
    const [currentFilter, setCurrentFilter] = useState<FilterParam | undefined>(undefined);

    useEffect(() => {
        if (params.length > 0) setKeywords(params[0].value)
        setFilterParams(params)
    }, [params])

    const handleFilterChange = (name: string, value: string) => {
        setFilterValues({
            ...filterValues,
            [name]: value
        })
    }

    useEffect(() => {
        const newFilterParams = filterParams.map(param => {
            return {
                ...param,
                value: filterValues[param.name] || ''
            }
        })
        setFilterParams(newFilterParams)
    }, [filterValues])

    return {
        keywords, setKeywords,
        filterParams,
        handleFilterChange,
        currentFilter, setCurrentFilter
    }
}

export default SearchFilterCombo;