import { FC, useState } from 'react'
import { withField, Input, InputGroup, Select } from '@douyinfe/semi-ui';

const IpRangeInput: FC<{ value: string, onChange?: (value: string) => void }> = (props) => {
    const { value, onChange } = props;


    const [inputType, setInputType] = useState('single');
    const [ipInput, setIpInput] = useState(value);
    const [error, setError] = useState('');
    const [isValid, setIsValid] = useState(false);


    // IP地址验证正则表达式
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const cidrRegex = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/;
    const ipRangeRegex = /^(\d{1,3}\.){3}\d{1,3}-(\d{1,3}\.){3}\d{1,3}$/;
    const ipRangeShortRegex = /^(\d{1,3}\.){3}\d{1,3}-\d{1,3}$/;

    // 验证IP地址每个段是否在0-255之间
    const isValidIPSegments = (ip: string) => {
        const segments = ip.split('.');
        return segments.every(segment => {
            const num = parseInt(segment);
            return num >= 0 && num <= 255;
        });
    };

    // 验证CIDR中的掩码是否有效
    const isValidCIDR = (cidr: string) => {
        const [ip, mask] = cidr.split('/');
        const maskNum = parseInt(mask);
        return isValidIPSegments(ip) && maskNum >= 0 && maskNum <= 32;
    };

    // 验证IP范围
    const isValidIPRange = (range: string) => {
        if (range.includes('-')) {
            // 处理完整IP范围格式
            if (ipRangeRegex.test(range)) {
                const [start, end] = range.split('-');
                return isValidIPSegments(start) && isValidIPSegments(end);
            }
            // 处理短格式IP范围
            else if (ipRangeShortRegex.test(range)) {
                const [ip, endNum] = range.split('-');
                const endNumInt = parseInt(endNum);
                return isValidIPSegments(ip) && endNumInt >= 0 && endNumInt <= 255;
            }
        }
        return false;
    };

    const validateInput = () => {
        setError('');
        setIsValid(false);

        switch (inputType) {
            case 'single':
                if (!ipRegex.test(ipInput)) {
                    setError('请输入有效的IP地址格式 (例如: **********)');
                    return;
                }
                if (!isValidIPSegments(ipInput)) {
                    setError('IP地址的每个段必须在0-255之间');
                    return;
                }
                break;

            case 'cidr':
                if (!cidrRegex.test(ipInput)) {
                    setError('请输入有效的CIDR格式 (例如: **********/24)');
                    return;
                }
                if (!isValidCIDR(ipInput)) {
                    setError('CIDR格式无效，掩码必须在0-32之间');
                    return;
                }
                break;

            case 'range':
                if (!ipRangeRegex.test(ipInput) && !ipRangeShortRegex.test(ipInput)) {
                    setError('请输入有效的IP范围格式 (例如: **********-************ 或 **********-255)');
                    return;
                }
                if (!isValidIPRange(ipInput)) {
                    setError('IP范围格式无效');
                    return;
                }
                break;
        }

        setIsValid(true);
    };

    const getPlaceholder = () => {
        switch (inputType) {
            case 'single':
                return '**********';
            case 'cidr':
                return '**********/24';
            case 'range':
                return '**********-************ 或 **********-255';
            default:
                return '';
        }
    };


    return <InputGroup>
            <Input value={ipInput}
                style={{ width: 220 }}
                placeholder={getPlaceholder()}
                onChange={(val) => {
                    setIpInput(val);
                    setError('');
                    setIsValid(false);
                    onChange?.(val);
                }} />
                <Select
                    style={{ width: 100 }}
                    value={inputType}
                    onChange={(val) => {
                        const value = val as string;
                        setInputType(value);
                        setIpInput('');
                    }}
                >
                    <Select.Option value="single">单个IP</Select.Option>
                    <Select.Option value="cidr">CIDR</Select.Option>
                    <Select.Option value="range">IP范围</Select.Option>
                </Select>
        </InputGroup>
}

const FormIpRangeInput = withField(IpRangeInput, { valueKey: 'value', onKeyChangeFnName: 'onChange' });

export default FormIpRangeInput;