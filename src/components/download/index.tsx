import React, { useEffect, useState, useContext } from 'react';
import { Typo<PERSON>, Card, Select, Tabs, TabPane, Button, Divider, Skeleton, Notification, Banner, Popover } from '@douyinfe/semi-ui';
import { IconInfoCircle, IconHelpCircle } from '@douyinfe/semi-icons';
import QRCode from "react-qr-code";
import { getQueryParam } from '@/utils/query';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import {
    isDesktop,
    isAndroid,
    isIOS,
    isMacOs,
    isWindows,
} from 'react-device-detect';
import android from '@/assets/icon/android.svg';
import iOS from '@/assets/icon/iOS.svg';
import linux from '@/assets/icon/linux.svg';
import macOS from '@/assets/icon/macOS.svg';
import windows from '@/assets/icon/windows.svg';
import DOMPurify from 'dompurify';
import { CreateAuthKeyRequest } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/auth_keys_pb";

import styles from './index.module.scss';
import { flylayerClient } from '@/services/core';
import { Platform, Platform_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/install_pb';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import UserAuthKey from '@/components/user-auth-key';
import { Duration } from "@bufbuild/protobuf";
import { useLocale } from '@/locales';

const { Paragraph, Title, Text } = Typography;

const Index: React.FC<{
    currentTab?: Platform_Type;
    navOnTab?: boolean;
    hiddenTab?: boolean;
}> = (props) => {
    const navigate = useNavigate();
    const { formatMessage } = useLocale();

    const reactLocation = useLocation();

    const globalConfig = useContext(GlobalConfigContext);
    const flynetGeneral = useContext(FlynetGeneralContext);

    const [showBanner, setShowBanner] = useState(false);
    // Loading state for opening auth link
    const [openAuthUrlLoading, setOpenAuthUrlLoading] = useState(false);
    // Open auth link
    const openAuthUrl = () => {
        setOpenAuthUrlLoading(true);
        let request = new CreateAuthKeyRequest({
            flynetId: flynetGeneral.id,
            ephemeral: false,
            reusable: false,
            tags: [],
            expiry: new Duration({ seconds: BigInt(10 * 60), nanos: 0 }) // Expires in 10 minutes
        });
        flylayerClient.createAuthKey(request).then((res) => {
            if (res.authKey) {
                let qrStr = `${globalConfig?.code}:${res.value}`
                let proto = "flylayer:";
                if (!globalConfig.flylayer) {
                    proto = `fyy-${globalConfig.code}:`
                }
                let url = `${proto}//login?auth-key=${encodeURIComponent(window.btoa(qrStr))}`
                setShowBanner(true);

                // window.open(url);
                location.href = url;

            }
        }, (err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'download.authFailed' }), position: "bottomRight" })
        }).finally(() => setOpenAuthUrlLoading(false))
    }

    // Get react router page route parameters
    const params = useParams<{
        platformName?: string;
        '*': string;
    }>();

    let networkName = '';
    if (location.hostname) {
        let nameArr = location.hostname.split('.');
        if (nameArr && nameArr.length > 0) {
            networkName = nameArr[0];
        }
    }

    let initTab: Platform_Type = Platform_Type.LINUX;
    let initDistroName = '';
    if (props.currentTab) {
        initTab = props.currentTab;
    } else {
        const paramPlatformName = params.platformName?.toLowerCase();
        if (paramPlatformName && (paramPlatformName == 'linux' || paramPlatformName == 'windows' || paramPlatformName == 'macos' || paramPlatformName == 'ios' || paramPlatformName == 'android')) {
            if (paramPlatformName == 'windows') {
                initTab = Platform_Type.WINDOWS;
            } else if (paramPlatformName == 'macos') {
                initTab = Platform_Type.MACOS;
            } else if (paramPlatformName == 'ios') {
                initTab = Platform_Type.IOS;
            } else if (paramPlatformName == 'android') {
                initTab = Platform_Type.ANDROID;
            } else if (isDesktop) {
                initTab = Platform_Type.LINUX;
            } else {
                initTab = Platform_Type.ANDROID;
            }

            if (paramPlatformName == 'linux') {
                const distroName: string = getQueryParam('distro', reactLocation) as string;
                // const distroName = params['*'];
                if (distroName) {
                    initDistroName = distroName;
                }
            }
        } else if (isMacOs) {
            initTab = Platform_Type.MACOS;
        } else if (isWindows) {
            initTab = Platform_Type.WINDOWS;
        } else if (isAndroid) {
            initTab = Platform_Type.ANDROID;
        } else if (isIOS) {
            initTab = Platform_Type.IOS;
        } else if (isDesktop) {
            initTab = Platform_Type.LINUX;
        } else {
            initTab = Platform_Type.ANDROID;
        }
    }


    // Linux distribution index
    const [linuxDistroName, setLinuxDistroName] = useState(initDistroName);
    // Linux distribution instructions
    const [linuxDistroInstructions, setLinuxDistroInstructions] = useState('');


    const isPC = isDesktop;

    const [currentTab, setCurrentTab] = useState(initTab.toString());

    // Platform information
    const [platforms, setPlatforms] = useState<Platform[]>();
    // Platform information loading state
    const [platformsLoading, setPlatformsLoading] = useState(true);

    // Show auth key state
    const [showAuthKey, setShowAuthKey] = useState(false);

    useEffect(() => {
        flylayerClient.listInstall({
            query: ''
        }).then(res => {
            setPlatforms(res.platforms);
            let hasInitTab = false;

            if (res.platforms && res.platforms.length > 0) {
                // let distroName = '';
                let distroInstructions = '';
                for (let i = 0; i < res.platforms.length; i++) {
                    if (res.platforms[i].type.toString() == initTab.toString()) {
                        hasInitTab = true;
                    }
                    if (res.platforms[i].type === Platform_Type.LINUX) {
                        if (res.platforms[i].packages && res.platforms[i].packages.length > 0) {
                            for (let j = 0; j < res.platforms[i].packages.length; j++) {
                                if (res.platforms[i].packages[j].name.toLowerCase() == initDistroName.toLowerCase()) {
                                    distroInstructions = res.platforms[i].packages[j].instructions;
                                    break;
                                }
                            }
                        }

                        if (!distroInstructions) {
                            distroInstructions = res.platforms[i].packages[0]?.instructions;
                            setLinuxDistroName(res.platforms[i].packages[0]?.name);
                        }

                    }
                }
                // setLinuxDistroName(distroName);
                if (!hasInitTab) {
                    setCurrentTab(res.platforms[0].type.toString());
                }
                setLinuxDistroInstructions(distroInstructions);
            }

        }).catch(err => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'download.getPackageInfoFailed' }) })
        }).finally(() => {
            setPlatformsLoading(false);
        });
    }, [])

    // Tab change handler
    const handleTabChange = (activeKey: string) => {
        setCurrentTab(activeKey);
        if (!props.navOnTab) {
            return;
        }
        // Switch route
        if (activeKey == Platform_Type.WINDOWS.toString()) {
            navigate(`${BASE_PATH}/download/windows`)
        } else if (activeKey == Platform_Type.MACOS.toString()) {
            navigate(`${BASE_PATH}/download/macos`)
        } else if (activeKey == Platform_Type.IOS.toString()) {
            navigate(`${BASE_PATH}/download/ios`)
        } else if (activeKey == Platform_Type.ANDROID.toString()) {
            navigate(`${BASE_PATH}/download/android`)
        } else if (activeKey == Platform_Type.LINUX.toString()) {
            navigate(`${BASE_PATH}/download/linux`)
        }
    }
   
    const getiOSDownloadLink = (url: string) => {
        if (url.indexOf('itms-services://') > 0) {
            return `${globalConfig.ctrlUrl}/d/ios` ;  
        } 
        return url;  
    }

    return <>
        {!globalConfig.customized && <Banner type='info' bordered className='mb20' closeIcon={null}
            description={<>{formatMessage({ id: 'download.zeroTrustAccessUrl' })} <Text copyable underline>{globalConfig.ctrlUrl} </Text></>} />}

        <Skeleton loading={platformsLoading} placeholder={<>
            <Skeleton.Title style={{ marginBottom: 32, height: 70 }}></Skeleton.Title>
            <Skeleton.Image style={{ height: 400 }} />
        </>}>
            <Tabs type="button"
                onChange={handleTabChange}
                activeKey={currentTab.toString()}
                tabBarClassName={isPC ? styles.tabBar : props.hiddenTab ? styles.tabBarMobileHide : styles.tabBarMobile}>
                {platforms && platforms.map((platform, index) => {
                    return <TabPane key={platform.type.toString()}
                        tab={<div className={isPC ? styles.tabItem : styles.tabItemMobile}>
                            <img src={
                                platform.type === Platform_Type.LINUX ? linux :
                                    platform.type === Platform_Type.WINDOWS ? windows :
                                        platform.type === Platform_Type.MACOS ? macOS :
                                            platform.type === Platform_Type.IOS ? iOS :
                                                platform.type === Platform_Type.ANDROID ? android : ''

                            }></img>
                            <span>{platform.name}</span></div>}
                        itemKey={platform.type.toString()}>
                        <div className={props.navOnTab ? isPC ? styles.tabPaneWrap : styles.tabPaneWrapMobile : ''}>
                            {platform.packages.length > 0 ? <Paragraph className={styles.versionName} type='tertiary'>{formatMessage({ id: 'download.version' })}：{platform.versionName}</Paragraph> : null}
                            {platform.type === Platform_Type.MACOS ? <>
                                <div className={isPC ? styles.downloadBlock : styles.downloadBlockMobile}>
                                    <div>{platform.packages.map((pkg, index) => {
                                        return <Button key={index} type="primary" className='mb20'
                                            theme='solid'
                                            size='large'
                                            style={{ marginLeft: '10px', marginRight: '10px' }}
                                            onClick={() => { window.open(pkg.downloadLink) }}>{formatMessage({ id: 'download.download' })} {pkg.arch}{formatMessage({ id: 'download.chipVersion' })}</Button>
                                    })}
                                    </div>
                                    <Popover position='bottom' content={<div className='p10'>
                                        <Paragraph>{formatMessage({ id: 'download.macChipStep1Complete' })}<img src={iOS} style={{ width: 12, height: 12 }}></img>{formatMessage({ id: 'download.macChipStep1End' })}</Paragraph>
                                        <Paragraph>{formatMessage({ id: 'download.macChipStep2Complete' })}</Paragraph>
                                    </div>}><a style={{ marginBottom: 10, cursor: 'pointer', verticalAlign: 'top', display: 'inline-block', textDecoration: 'underline' }}>{formatMessage({ id: 'download.howToCheckChipType' })}</a><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'middle' }} /> </Popover>

                                    <Paragraph className='mb20' >{formatMessage({ id: 'download.supportedVersions' }).replace('{version}', platform.supportedVersions)}</Paragraph>
                                </div>
                                <Divider />
                                <div className={isPC ? styles.memo : styles.memoMobile} dangerouslySetInnerHTML={{
                                    __html: DOMPurify.sanitize(platform.instructions, { USE_PROFILES: { html: true } })
                                }}>

                                </div>
                            </> : null}

                            {platform.type === Platform_Type.IOS ? <>
                                {platform.packages.length > 0 ? <>
                                    <div className={isDesktop ? styles.ScanBlock : styles.ScanBlockMobile}>
                                        {isPC ? <div className={styles.qr}>
                                        <QRCode value={getiOSDownloadLink(platform.packages[0].downloadLink)} ></QRCode> 
                                        </div> : ''}
                                       
                                        <div className={styles.qrmemo} style={{ paddingTop: 0 }}>
                                            {isPC && <Paragraph className='mb20'>{formatMessage({ id: 'download.scanQROrClick' })} </Paragraph>}
                                            {platform.packages.length > 0 ? <Button type="primary" className='mb20'
                                                theme='solid'
                                                size='large'
                                                onClick={() => { window.open(platform.packages[0].downloadLink) }}>{formatMessage({ id: 'download.installClient' }).replace('{platform}', 'iOS').replace('{appName}', globalConfig.name)}</Button> : ''}

                                            <Paragraph className='mb20'>{formatMessage({ id: 'download.requiresVersion' }).replace('{version}', platform.supportedVersions)}</Paragraph>
                                            {isIOS ?
                                                <>
                                                    <Button loading={openAuthUrlLoading} className='mb10' type='danger' onClick={() => openAuthUrl()} theme='solid'>{formatMessage({ id: 'download.authorizeAuth' })}</Button>
                                                    <Paragraph type='tertiary' size='small' className='mb10'>{formatMessage({ id: 'download.authAfterInstallFull' })}</Paragraph>
                                                    {showBanner && <Banner type='warning' onClose={() => setShowBanner(false)} title={<Paragraph className={styles.bannerTitle}>{formatMessage({ id: 'download.authFailedBannerTitle' })}</Paragraph>} className='mb20'>
                                                        <Paragraph className={styles.bannerListTitle}>{formatMessage({ id: 'download.troubleshootMethodsFull' })}</Paragraph>
                                                        <ol className={styles.bannerList}>
                                                            <li>{formatMessage({ id: 'download.confirmAppInstalledFull' }).replace('{appName}', globalConfig.name)}</li>
                                                            <li>{formatMessage({ id: 'download.confirmSystemBrowserFull' })}</li>
                                                        </ol>


                                                    </Banner>}
                                                </> : <Button type='danger' onClick={() => setShowAuthKey(true)} theme='solid'>{formatMessage({ id: 'download.viewAuthCode' })}</Button>}
                                        </div>
                                    </div>
                                    <Divider />
                                    <div className={styles.memo} dangerouslySetInnerHTML={{
                                        __html: DOMPurify.sanitize(platform.instructions, { USE_PROFILES: { html: true } })
                                    }}>

                                    </div>

                                </> : <>
                                    <div className={isPC ? styles.memo : styles.memoMobile} style={{ paddingTop: 44 }}>
                                        <Card className='mb20' style={{ textAlign: 'center' }}>{formatMessage({ id: 'download.comingSoon' })}</Card>
                                        <div style={{ height: 256 }}></div>
                                    </div>
                                </>}
                            </> : null}

                            {platform.type === Platform_Type.WINDOWS ? <>

                                <div className={isPC ? styles.downloadBlock : styles.downloadBlockMobile}>
                                    <div>{platform.packages.map((pkg, index) => {
                                        // if (pkg.arch === "") return null
                                        // if (pkg.arch === "amd64") {
                                        //     return <Button key={index} type="danger" className='mb20'
                                        //         theme='solid'
                                        //         size='large'
                                        //         style={{ marginLeft: '10px', marginRight: '10px', width: 300 }}
                                        //         onClick={() => { window.open(pkg.downloadLink) }}>{formatMessage({ id: 'download.download64BitVersion' })}
                                        //         <span style={{ fontSize: '11px', marginLeft: '15px' }} > <IconInfoCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /> {formatMessage({ id: 'download.mostUsersChoose' })}</span>
                                        //     </Button>

                                        // } else {
                                        //     return <Button key={index} type="primary" className='mb20'
                                        //         theme='solid'
                                        //         size='large'
                                        //         style={{ marginLeft: '10px', marginRight: '10px', width: 170 }}
                                        //         onClick={() => { window.open(pkg.downloadLink) }}>{formatMessage({ id: 'download.download32BitVersion' })}</Button>
                                        // }

                                        return <Button key={index} type="primary" className='mb20'
                                            theme='solid'
                                            size='large'
                                            style={{ marginLeft: '10px', marginRight: '10px' }}
                                            onClick={() => { window.open(pkg.downloadLink) }}>{formatMessage({ id: 'download.downloadWindowsClient' })}</Button>
                                    })}
                                    </div>
                                    {/* <Popover position='bottom' content={<div className='p10'>
                                        <Paragraph>{formatMessage({ id: 'download.windowsSystemStep1' })}</Paragraph>
                                        <Paragraph>2. 在“设备规格”或“系统”项，查看系统类型是“64位操作系统”还是“32位操作系统”</Paragraph>
                                        <Paragraph>{formatMessage({ id: 'download.windowsSystemStep3' })}</Paragraph>
                                    </div>}><a style={{ marginBottom: 10, cursor: 'pointer', verticalAlign: 'top', display: 'inline-block', textDecoration: 'underline' }}>{formatMessage({ id: 'download.howToCheckSystemType' })}</a><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'middle' }} /> </Popover> */}

                                    <Paragraph className='mb20' >{formatMessage({ id: 'download.supportedVersions' }).replace('{version}', platform.supportedVersions)}</Paragraph>
                                </div>
                                <Divider />
                                <div className={isPC ? styles.memo : styles.memoMobile} dangerouslySetInnerHTML={{
                                    __html: DOMPurify.sanitize(platform.instructions, { USE_PROFILES: { html: true } })
                                }}>

                                </div>
                            </> : null}

                            {platform.type === Platform_Type.LINUX ? <>
                                <Title className='mb20' heading={3} style={{ textAlign: 'center' }}>
                                    {formatMessage({ id: 'download.oneClickInstall' })}</Title>

                                <Paragraph className='mb40 copyable-code' style={{ textAlign: 'center' }} copyable >
                                    {platform.oneCommand}
                                </Paragraph>
                                <Divider />
                                <div className={isPC ? styles.memo : styles.memoMobile}>
                                    <Title className='mb10' heading={3} style={{ textAlign: 'center' }}>
                                        {formatMessage({ id: 'download.manualInstall' })}</Title>
                                    <div style={{ textAlign: 'center', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                        <Select value={linuxDistroName} onChange={(val) => {
                                            setLinuxDistroName(val as string)
                                            platform.packages.map((platform) => {
                                                if (platform.name === val) {
                                                    setLinuxDistroInstructions(platform.instructions)
                                                }
                                            })
                                            if (props.navOnTab) {
                                                navigate(`${BASE_PATH}/download/linux?distro=${val}`)
                                            }
                                        }}>
                                            {platform.packages.map((platform, index) => {
                                                return <Select.Option key={index} value={platform.name}>{platform.name}</Select.Option>
                                            })}
                                        </Select>
                                    </div>
                                    <div className={isPC ? styles.memo : styles.memoMobile} dangerouslySetInnerHTML={{
                                        __html: DOMPurify.sanitize(linuxDistroInstructions, { USE_PROFILES: { html: true } })
                                    }}>

                                    </div>

                                </div>
                            </> : null}

                            {platform.type === Platform_Type.ANDROID ? <>
                                <div className={isDesktop ? styles.ScanBlock : styles.ScanBlockMobile}>
                                    {isPC ? <div className={styles.qr}>
                                        {/* {platform.packages.length > 0 ? <QRCode value={globalConfig.flylayer || true ? `https://flylayer.com/download/android/?code=${globalConfig.code}` : `${location.protocol}//${location.host}${BASE_PATH}/download/android`}></QRCode> : ''} */}
                                        {globalConfig.code == 'ane56' ?
                                            platform.packages.length > 0 ? <QRCode value={`${location.protocol}//${location.host}/download/android`}></QRCode> : ''
                                            : platform.packages.length > 0 ? <QRCode value={globalConfig.flylayer || true ? `${globalConfig.ctrlUrl}/d/android` : `${location.protocol}//${location.host}${BASE_PATH}/download/android`}></QRCode> : ''
                                        }

                                    </div> : ''}

                                    <div className={styles.qrmemo} style={{ paddingTop: globalConfig.flylayer ? 0 : 20 }}>
                                        {isPC ? <Paragraph className='mb20'>{formatMessage({ id: 'download.scanQROrClick' })}</Paragraph> : ''}

                                        {platform.packages.length > 0 ? <Button type="primary" className='mb20'
                                            theme='solid'
                                            size='large'
                                            onClick={() => { window.open(platform.packages[0].downloadLink) }}>{formatMessage({ id: 'download.installClient' }).replace('{platform}', 'Android').replace('{appName}', globalConfig.name)}</Button> : ''}

                                        <Paragraph className='mb20'>{formatMessage({ id: 'download.requiresVersion' }).replace('{version}', platform.supportedVersions)}</Paragraph>
                                        {isAndroid ?
                                            <>
                                                <Button loading={openAuthUrlLoading} className='mb10' type='danger' onClick={() => openAuthUrl()} theme='solid'>{formatMessage({ id: 'download.authorizeAuth' })}</Button>
                                                <Paragraph type='tertiary' size='small' className='mb10'>{formatMessage({ id: 'download.authAfterInstallFull' })}</Paragraph>
                                                {showBanner && <Banner type='warning' onClose={() => setShowBanner(false)} title={<Paragraph className={styles.bannerTitle}>{formatMessage({ id: 'download.authFailedBannerTitle' })}</Paragraph>} className='mb20'>
                                                    <Paragraph className={styles.bannerListTitle}>{formatMessage({ id: 'download.troubleshootMethodsFull' })}</Paragraph>
                                                    <ol className={styles.bannerList}>
                                                        <li>{formatMessage({ id: 'download.confirmAppInstalledFull' }).replace('{appName}', globalConfig.name)}</li>
                                                        <li>{formatMessage({ id: 'download.confirmSystemBrowserFull' })}</li>
                                                    </ol>


                                                </Banner>}
                                            </>
                                            : <Button type='danger' onClick={() => setShowAuthKey(true)} theme='solid'>{formatMessage({ id: 'download.viewAuthCode' })}</Button>}
                                        {/* <Button type='danger' onClick={() => setShowAuthKey(true)} theme='solid'>{formatMessage({ id: 'download.viewAuthCode' })}</Button> */}

                                    </div>
                                </div>

                                <Divider />
                                <div className={isPC ? styles.memo : styles.memoMobile} dangerouslySetInnerHTML={{
                                    __html: DOMPurify.sanitize(platform.instructions, { USE_PROFILES: { html: true } })
                                }}>

                                </div></> : null}
                        </div>
                    </TabPane>
                })}
            </Tabs>
        </Skeleton>
        {showAuthKey && <UserAuthKey close={() => { setShowAuthKey(false) }}></UserAuthKey>}
    </>
}
export default Index;
