.selectJump {
    background-color: var(--semi-color-fill-0);
    height: 36px;
    display: flex;
    padding-left: 32px;
    align-items: center;
    cursor: pointer;
    border-top: 1px solid var(--semi-color-border);
    border-radius: 0 0 6px 6px;
    color: var(--semi-color-link);
}

.tabBar {
    display: flex;
    align-items: center;
    justify-content: center;
}

.tabBarMobile {
    display: flex;
    justify-content: center;
}
.tabBarMobileHide {
    display: none;
} 

.tabItem {

    color: var(--semi-color-text-3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 4rem;
    img {
        width: 1.8rem;
        height: 1.8rem;
        margin-bottom: 5px;
    }
}

.tabItemMobile {
    color: var(--semi-color-text-3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
        width: 1.8rem;
        height: 1.8rem;
        margin-bottom: 5px;
    }
}

.tabItemDisable {
    opacity: 0.3;
    color: var(--semi-color-text-3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
        width: 1.8rem;
        height: 1.8rem;
        margin-bottom: 5px;
    }
}
.versionName {
    text-align: center;
    padding-top: 22px;
    padding-bottom: 22px;
}
.downloadBlock {
    
    text-align: center;
}

.downloadBlockMobile {
    
    text-align: center;
    display: flex;
    align-items: center;
    flex-direction: column;
}

/** 说明文字 */
.memo {
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;

    ol {
        padding-left: 20px;
    }
}

.memoMobile {
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;

    ol {
        padding-left: 20px;
    }
}

.ScanBlock {
    padding-left: 4.6rem;
    padding-right: 4.6rem;
    margin-bottom: 2rem;
    display: flex;
    justify-content: left;
    .qr {
        padding: 0.5rem;
        width: 9rem;
        height: 9rem;
        border-radius: var(--semi-border-radius-medium);
        border: 1px solid var(--semi-color-border);
        display: flex;
        align-items: center;
        justify-content: center;
        img, svg {
            width: 8rem;
            height: 8rem;
        }
    }
    
    .qrmemo {
        padding-left: 1.8rem;
        padding-top: 1.2rem;
        text-align: left;
    }
}


.ScanBlockMobile {
    // padding-top: 2rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    margin-bottom: 2rem;
    display: block;
    // flex-wrap: wrap;
    // align-items: center;
    // justify-content: center;
    .qr {
        padding: 0.5rem;
        width: 100%!important;
        height: 12rem;
        border-radius: var(--semi-border-radius);
        border: 1px solid var(--semi-color-border);
        display: flex;
        align-items: center;
        justify-content: center;
        img, svg {
            width: 11rem;
            height: 11rem;
        }
    }
    
    .qrmemo {
        padding-top: 1.8rem;
        text-align: center;
    }
}

.cmd {
    margin-top: 10px;
    margin-bottom: 10px;
    border-radius: var(--semi-border-radius-medium);
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-fill-0);
    padding: 10px;
}

.tabPaneWrap {
    padding-left: 24px;
    padding-right: 24px;
}

.tabPaneWrapMobile {

}

.bannerTitle {
    color: var(--semi-color-text-1);
    // font-size: 14px;
}

.bannerListTitle {
    text-align: left;
    color: var(--semi-color-text-1);
    padding-left: 5px;
    margin-bottom: 5px;
    font-size: 14px;
}
.bannerList {
    padding-left: 20px;
    margin: 0;
    text-align: left;
    li {
        color: var(--semi-color-text-1);
    }
    font-size: 14px;
    margin-bottom: 10px;
}