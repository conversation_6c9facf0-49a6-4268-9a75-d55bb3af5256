import { useEffect, useState, FC, ReactNode } from 'react';
import { Typography, Modal, List, Notification, TabPane, Input } from '@douyinfe/semi-ui';

import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { flylayerClient } from '@/services/core';
const { Paragraph, Title, Text } = Typography;
import { Link, useNavigate } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import { useLocale } from '@/locales';
import styles from './index.module.scss';
// 用户设备列表
const UserDevice: FC<{ record: User }> = (props) => {
    const { formatMessage } = useLocale();
    // 设备列表
    const [devices, setDevices] = useState<Machine[]>([]);
    useEffect(() => {
        flylayerClient.listMachinesByUser({ userId: props.record.id }).then((res) => {
            setDevices(res.machines)
        })
            .catch(err => {
                console.error(err)
                Notification.error({ content: formatMessage({ id: 'userDevice.fetchFailed' }) })
            })
    }, [])
    return (<>
        <List
            className='mb20'
            header={<Title heading={6}>{formatMessage({ id: 'userDevice.deviceList' })}</Title>}
            footer={
                <Text type='tertiary' link={{
                    href: `${BASE_PATH}/devices?keywords=${props.record.loginName}`,
                    target: "_blank",
                }}>{formatMessage({ id: 'userDevice.viewUserDevices' })}</Text>
            }
        ><div className={styles.list}>{
            devices.map(d => {
                return <List.Item>
                    <Text link={{ href: `${BASE_PATH}/devices/${d.ipv4}`, type: 'tertiary', target:"_blank" }} style={{fontWeight: 'normal'}}>{d.name}</Text>

                </List.Item>
            })
        }
            </div>

        </List></>)
}
export default UserDevice;