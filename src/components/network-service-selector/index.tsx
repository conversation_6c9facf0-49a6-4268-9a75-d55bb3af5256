import { FC, useState, useContext, useEffect } from 'react'
import { withField, Select, Spin } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { NetworkService } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';
import { ServiceStatus } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { flylayerClient } from '@/services/core';


const NetworkServiceSelector: FC<{ value: Array<bigint>, onChange?: (value: Array<bigint>) => void }> = (props) => {

    const flynet = useContext(FlynetGeneralContext);

    // 是否正在加载中
    const [loading, setLoading] = useState(false);

    // 查询网络服务列表
    const [networkServices, setNetworkServices] = useState<Array<NetworkService>>([]);

    const query = async () => {
        setLoading(true);

        flylayerClient.listNetworkServices({
            flynetId: flynet.id
        }).then((res) => {
            let services: Array<NetworkService> = [];
            if (res.services) {
                services = res.services.filter((service) => {
                    return service.status === ServiceStatus.PUBLISHED;
                });
            }
            setNetworkServices(services);
        }).finally(() => {
            setLoading(false);
        })
    };

    useEffect(() => {
        query();
    }, []);

    return <>
        { loading ? <Spin /> : <Select
            placeholder="请选择网络服务"
            value={
                props.value ? props.value[0] + '' : ''
            }
            style={{ width: '100%' }}
            optionList={networkServices.map((service) => {
                return {
                    label: service.name,
                    value: service.id + ''
                }
            })}
            onChange={(value) => {
                if (props.onChange) {
                    props.onChange([BigInt(value as string)]);
                }
            }}
        ></Select>}
    </>
}

const FormNetworkServiceSelector = withField(NetworkServiceSelector);

export default FormNetworkServiceSelector;