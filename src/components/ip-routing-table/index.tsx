import { FC, useEffect, useState, useContext } from 'react'
import { <PERSON>, Row, Col, Button, Typography, Tag, Tooltip, Notification, Dropdown, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List, Banner, Tabs, TabPane, Collapse, Modal } from '@douyinfe/semi-ui';

interface NetworkRoutingTable {
    network: string;
    mask: string;
    gateway: string;
    interface: string;
    metric: number;
    flags: string;
}

interface Props {}
const Index: FC<Props> = () => {

    const [networkRoutingTable, setNetworkRoutingTable] = useState<NetworkRoutingTable[]>([{
        network: '0.0.0.0',
        mask: '0.0.0.0',
        gateway: '***********',
        interface: 'eth0',
        metric: 10,
        flags: 'USH'
    }, {
        network: '***********',
        mask: '*************',
        gateway: '0.0.0.0',
        interface: 'eth0',
        metric: 10,
        flags: 'USH'
    }, {
        network: '***********',
        mask: '*************',
        gateway: '0.0.0.0',
        interface: 'eth0',
        metric: 10,
        flags: 'GH'
    }]);

    const columns = [
        {
            title: '目标地址',
            dataIndex: 'network',
            key: 'network',
        },
        {
            title: '子网掩码',
            dataIndex: 'mask',
            key: 'mask',
        },
        {
            title: '网关',
            dataIndex: 'gateway',
            key: 'gateway',
        },
        {
            title: '接口',
            dataIndex: 'interface',
            key: 'interface',
        },
        {
            title: '跳数',
            dataIndex: 'metric',
            key: 'metric',
        },
        {
            title: '标志',
            dataIndex: 'flags',
            key: 'flags',
        },
    ];

    return (
        <div>
            <Table
                columns={columns}
                dataSource={networkRoutingTable}
                pagination={false}
            />
        </div>
    )
}

export default Index
