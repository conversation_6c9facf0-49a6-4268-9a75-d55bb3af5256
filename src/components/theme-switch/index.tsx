import { FC } from 'react'
import { SideSheet, List } from '@douyinfe/semi-ui';
import iconThemeFeiyueCloud from '@/assets/images/theme-feiyue-cloud.png';
import iconThemeFeiyueCloudColor from '@/assets/images/theme-feiyue-cloud-color.png';
import styles from './index.module.scss';
import { LOCAL_STORAGE_THEME, getLocalStorage, LOCAL_STORAGE_COLOR_MODE } from '@/utils/storage';
import { BASE_PATH } from '@/constants/router';
import { LocaleFormatter } from '@/locales';


const initColorMode = getLocalStorage(LOCAL_STORAGE_COLOR_MODE) || 'light';

const initTheme = getLocalStorage(LOCAL_STORAGE_THEME) || 'feiyue-cloud';
document.addEventListener('DOMContentLoaded', () => {
    const body = document.body;
    if (initColorMode == 'dark') {
        body.setAttribute('theme-mode', 'dark');
    } else if (initColorMode == 'light') {
        body.removeAttribute('theme-mode');
    }
    let link = document.querySelector('#semiThemeSwitcher');
    if (link) {
        (link as any).href = `${BASE_PATH}/theme/semi-theme-${initTheme}/semi.css`
    }
});

const Index: FC<{
    visible: boolean;
    theme: string;
    setTheme: (theme: string) => void;
    onClose: () => void;
}> = (props) => {
    const {theme, setTheme} = props;
    const applyTheme = (themeName: string) => {
        let link = document.querySelector('#semiThemeSwitcher');
        if (link) {
            (link as any).href = `${BASE_PATH}/theme/semi-theme-${themeName}/semi.css`
        }
        localStorage.setItem(LOCAL_STORAGE_THEME, themeName);
    }

    const switchTheme = (themeName: string) => {
        applyTheme(themeName);
        setTheme(themeName);
    }


    return <SideSheet
        closeOnEsc
        closable
        onCancel={() => props.onClose()}
        visible={props.visible}
    >
        
        <List>
            <List.Item className={theme == 'feiyue-cloud' ? styles.themeItemSelected : styles.themeItem} onClick={() => switchTheme('feiyue-cloud')}>
                <img className={styles.themeThumb} src={iconThemeFeiyueCloud} />
                <h3><LocaleFormatter id="components.themeSwitch.feiyueCloud" /></h3>
            </List.Item>
            <List.Item className={theme == 'feiyue-cloud-color' ? styles.themeItemSelected : styles.themeItem} onClick={() => switchTheme('feiyue-cloud-color')}>
                <img className={styles.themeThumb} src={iconThemeFeiyueCloudColor} />
                <h3><LocaleFormatter id="components.themeSwitch.feiyueCloudColor" /></h3>
            </List.Item>
        </List>
    </SideSheet>
}

export default Index;
