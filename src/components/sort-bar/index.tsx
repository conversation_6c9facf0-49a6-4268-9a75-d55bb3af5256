import React, { FC, useEffect } from "react";
import { Button, Space } from '@douyinfe/semi-ui';
import { IconAlignTop, IconChevronUp, IconChevronDown, IconAlignBottom } from '@douyinfe/semi-icons';

import styles from './index.module.scss'
interface Props {
    curIndex: number,
    total: number,
    onChange: (index: number) => void
}

/** 排序工具栏 */
const SortBar: FC<Props> = (props) => {

    return <>
        <div className={styles.sortBar}>
            <Space className={styles.sortBarInner}>
         
                <Button size="small" icon={<IconAlignTop />} aria-label="移到最前" />
                <Button size="small" icon={<IconChevronUp />} aria-label="上移" />
                <Button size="small" icon={<IconChevronDown />} aria-label="下移" />
                <Button size="small" icon={<IconAlignBottom />} aria-label="移到最后" />
            </Space>
        </div>
    </>
}

export default SortBar;