import { FC, useState } from 'react'
import { withField, Input, Button, Space, InputGroup } from '@douyinfe/semi-ui';
import styles from './index.module.scss';
import { IconPlus, IconMinusCircle } from '@douyinfe/semi-icons';

const ArrayInput: FC<{ value: Array<string>, onChange?: (value: Array<string>) => void }> = (props) => {
    const { value, onChange } = props;
    const [inputValues, setInputValues] = useState(value || ['']);
    return <>
        <div className={styles.arrayInput}>
            <div className={styles.inputs}>
                {inputValues.map((inputValue, index) => {
                    return <Space key={index} className='mb10'><Input
                        value={inputValue}
                        onChange={(e) => {
                            const newInputValues = [...inputValues];
                            newInputValues[index] = e;
                            setInputValues(newInputValues);
                            onChange && onChange(newInputValues);
                        }}
                    />
                        <Button type='danger' icon={<IconMinusCircle
                            onClick={() => {
                                const newInputValues = [...inputValues];
                                newInputValues.splice(index, 1);
                                setInputValues(newInputValues);
                                onChange && onChange(newInputValues);
                            }}
                        />} />
                    </Space>
                })}
            </div>
            <div className={styles.addBtn}>
                <Button
                    icon={<IconPlus />}
                    onClick={() => {
                        const newInputValues = [...inputValues, ''];
                        setInputValues(newInputValues);
                        onChange && onChange(newInputValues);
                    }}
                />
            </div>
        </div>


    </>
}

const FormArrayInput = withField(ArrayInput, { valueKey: 'value', onKeyChangeFnName: 'onChange' });

export default FormArrayInput;