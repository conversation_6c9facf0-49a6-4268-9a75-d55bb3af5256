import React, { useState, useEffect } from 'react'
import { Typography, Table, Modal, Row, Col, Button, Select, Space, Input, Dropdown, Tag } from '@douyinfe/semi-ui';
import useTable, { ServicesFilter } from './useTable';


import { ServiceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';

import TableEmpty from '@/components/table-empty';
import SearchFilter, { FilterParam } from '@/components/search-filter';

import useServicesGroup from '@/pages/services/useServicesGroup';

const Index: React.FC<{
    multi: boolean;
    value?: NetworkService | NetworkService[];
    serviceType?: ServiceType;
    onChange: (value: NetworkService | NetworkService[]) => void;
    close: () => void,
}
> = (props) => {
    const initFilter: ServicesFilter = {
        query: '',
        source: '',
        type: '',
        lastSeen: ''
    };
    const { columns, loading, allServices, services, selectedService, setSelectedService, reloadFlag, setReloadFlag, filter, setFilter, selectedRowKeys, onRowSelect, onRow, page, pageSize, total, doPage, handleFilterChange, handleSort } = useTable(initFilter, props.multi, props.value, props.serviceType);
    const { getMapTreeData } = useServicesGroup();

    const handleQueryChange = (value: string) => {
        setFilter({ ...filter, query: value })
    }

    const [filterParams, setFilterParams] = useState<FilterParam[]>([{
        name: 'query',
        placeholder: '根据服务名称或描述搜索',
        label: '查询',
        value: initFilter.query || '',
    }
    ]);

    return <><Modal
        width={770}
        title="选择服务"
        visible={true}
        onOk={() => { }}
        onCancel={props.close}
        footer={null}
        className='semi-modal'
    >
        <SearchFilter onChange={(val: string, filterParam) => {
            handleFilterChange({ ...filter, [filterParam.name]: val })

            const newFilterParams = filterParams.map((item) => {
                if (item.name == filterParam.name) {
                    item.value = val;
                    
                }
                return item;
            })
            setFilterParams(newFilterParams);

        }} filterParams={filterParams} className='mb20 search-bar'></SearchFilter>
        <div style={{ height: 20 }} className='mb10' >  {!loading && <Tag>  服务总数 {total}</Tag>} </div>

        <div>
            <Table
                className='mb20'
                rowSelection={{
                    disabled: true,
                    selectedRowKeys: selectedRowKeys as any,
                    onSelect: onRowSelect as any,
                }}
                onChange={handleSort}
                onRow={onRow as any}
                rowKey={(record?: NetworkService) => record ? record.id + '' : ''}
                empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={services} pagination={{
                    currentPage: page,
                    pageSize: pageSize,
                    total: total,
                    onChange: (page:number) => {
                        doPage(page);
                    }
                }} />
        </div>
        <Row className='mb10'>
            <Col span={20}>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button onClick={() => { props.close() }}>取消</Button>
                    <Button theme='solid' disabled={!selectedService || selectedService.length == 0} onClick={() => {
                        if (selectedService) {
                            if (props.multi) {
                                props.onChange(selectedService)
                            } else {
                                props.onChange(selectedService[0])
                            }
                        }

                    }}>确定</Button>
                </Space>
            </div></Col>
        </Row>
    </Modal>

    </>
}

export default Index;
