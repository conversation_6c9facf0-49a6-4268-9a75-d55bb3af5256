import { useEffect, useState, useContext } from 'react';

import { Typography, Popover, Space, Notification, Button } from '@douyinfe/semi-ui';
import { IconMore } from '@douyinfe/semi-icons';

import { ServiceType, ServiceNodeType, ServiceNode, ServiceRouteMode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { NetworkService, NetworkServiceNode } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/network_services_pb';

import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { getNetworkServiceNodeDisplay, getServiceNodeDisplay } from '@/utils/service';

export type ServicesFilter = {
    query?: string;
    source?: string;
    proto?: string;
    type?: string;
    lastSeen?: string;
    group?: string;
}

const { Title, Paragraph, Text } = Typography;
const useTable = (filterParam: ServicesFilter,
    multi: boolean,
    initSelectedValue?: NetworkService | NetworkService[],
    serviceType?: ServiceType
) => {
    const flynet = useContext(FlynetGeneralContext);

    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 服务列表
    const [services, setServices] = useState<NetworkService[]>([]);
    // 全部服务列表
    const [allServices, setAllServices] = useState<NetworkService[]>([]);

    // 编辑弹出框是否可见
    const [editVisible, setEditVisible] = useState(false);
    // 删除弹出框是否可见
    const [delVisible, setDelVisible] = useState(false);
    // 当前菜单选中服务
    const [selectedServices, setSelectedServices] = useState<NetworkService[]>();

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);

    // 过滤参数
    const [filter, setFilter] = useState<ServicesFilter>(filterParam);
    // 当前页码
    const [page, setPage] = useState(1);

    const pageSize = 5;
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    // 表格列
    const columns = [
        {
            width: 280,
            title: '服务名称',
            dataIndex: 'name',
            sorter: true,
            render: (field: string, record: NetworkService, index: number) => {
                return <>
                    <Space>
                        <Title heading={5} ellipsis style={{maxWidth:260}}>
                            {field}
                        </Title>
                    </Space>
                    <Paragraph size='small'>{record.description}&nbsp;</Paragraph>
                </>
            },
        },
        

        {
            width: 80,
            title: '服务节点',
            dataIndex: 'servicesNodes',
            render: (field: string, record: NetworkService, index: number) => {
                return <div className='table-last-col'><Popover content={<div className='p10'>{getNetworkServiceNodeDisplay(record)}</div>}><Button><IconMore className='align-v-center' /></Button></Popover></div>
            }
        },


    ];

    // 过滤数据
    const doFilter = (page: number, src: Array<NetworkService>, filter: ServicesFilter) => {
        if (!src || src.length == 0) {
            return src.slice((page - 1) * pageSize, page * pageSize);
        }
        if (filter.query == '' && filter.source == '' && filter.type == '' && filter.proto == '' && filter.lastSeen == '' && filter.group == '') {
            return src.slice((page - 1) * pageSize, page * pageSize);
        }
        const filteredList = src.filter((item) => {
            if (filter.query) {
                if (item.name.indexOf(filter.query) < 0 && item.description.indexOf(filter.query) < 0) {
                    return false;
                }
            }
            
            if (filter.group) {
                const groups = filter.group.split(',');
                let found = false;
                for (let i = 0; i < groups.length; i++) {
                    const g = groups[i];
                    for (let j = 0; j < item.serviceGroups.length; j++) {
                        const sg = item.serviceGroups[j];
                        if (sg.id + '' == g) {
                            found = true;
                            break;
                        }
                    }
                    if (found) {
                        break;
                    }
                }
                if (!found) {
                    return false;
                }
            }

            return true;
        });

        setTotal(filteredList.length);
        return filteredList.slice((page - 1) * pageSize, page * pageSize);
    }

    // 加载数据
    const query = () => {
        setLoading(true);

        flylayerClient.listNetworkServices({
            flynetId: flynet.id,
        }).then(res => {
            if (res && res.services) {
                setServices(doFilter(page, res.services, filter));
                setAllServices(res.services);
                setTotal(res.services.length);
            }
        }).catch(err => {
            Notification.error({
                content: '获取服务列表失败',
                position: 'bottomRight',
            });
        }).finally(() => {
            setLoading(false);
        });

    }

    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])

    const handleFilterChange = (newFilter: ServicesFilter) => {
        setFilter(newFilter);
        setPage(1)
        setServices(doFilter(1, allServices, newFilter))
    }



    const [selectedRowKeys, setSelectedRowKeys] = useState<String[]>([]);
    const onRowSelect = (record: NetworkService, selected: boolean, selectedRows: { id: string }[]) => {
        if (multi) {

            let newSelectedRowKeys: any = selectedRows.map((item) => item.id + '');
            setSelectedRowKeys(newSelectedRowKeys);

            let newSelectedServices: NetworkService[] = [];
            for (let i = 0; i < allServices.length; i++) {
                const s = allServices[i];
                if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                    newSelectedServices.push(s);
                }
            }
            setSelectedServices(newSelectedServices);
        } else {
            let selectKeys: any = [record.id + ''];
            setSelectedRowKeys(selectKeys)
            setSelectedServices([record]);
        }

    };

    const onRow = (record: NetworkService, index: number) => {
        return {
            onClick: () => {
                if (multi) {
                    let id = record.id + '';
                    let newSelectedRowKeys: any = [...selectedRowKeys];
                    if (newSelectedRowKeys.indexOf(id) >= 0) {
                        newSelectedRowKeys = newSelectedRowKeys.filter((item: any) => item != id);
                    }
                    else {
                        newSelectedRowKeys.push(id);
                    }
                    setSelectedRowKeys(newSelectedRowKeys);
                    let newSelectedServices: NetworkService[] = [];
                    for (let i = 0; i < allServices.length; i++) {
                        const s = allServices[i];
                        if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                            newSelectedServices.push(s);
                        }
                    }
                    setSelectedServices(newSelectedServices);

                } else {

                    let selectKeys: any = [record.id + ''];
                    setSelectedRowKeys(selectKeys)
                    setSelectedServices([record]);
                }
            }, // 点击行

        };
    }

    
    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;

        let sortedAllDate = [...allServices];

        if (sortOrder == 'descend' || sortOrder == 'ascend') {


            if (dataIndex == 'name') {

                sortedAllDate.sort((a, b) => {
                    if (a.name > b.name) {
                        return sortOrder == 'ascend' ? 1 : -1;
                    } else {
                        return sortOrder == 'ascend' ? -1 : 1;
                    }
                })
            }
            

            if (dataIndex == 'createdAt') {

                sortedAllDate.sort((a, b) => {
                    if (!a || !b) return -1;
                    if (a.createdAt && b.createdAt) {
                        return 1;
                    }
                    if (a.createdAt && !b.createdAt) {
                        return -1;
                    }
                    if (!a.createdAt && b.createdAt) {
                        return 1;
                    }
                    if (b.createdAt && a.createdAt && a.createdAt.seconds > b.createdAt.seconds) {
                        return -1;
                    }
                    return -1;
                })
            }
            if (dataIndex == 'serviceGroups') {

                sortedAllDate.sort((a, b) => {
                    if (a.serviceGroups.length > b.serviceGroups.length) {
                        return sortOrder == 'ascend' ? 1 : -1;
                    } else {
                        return sortOrder == 'ascend' ? -1 : 1;
                    }
                })
            }
            if (dataIndex == 'servicesNodes') {

                sortedAllDate.sort((a, b) => {
                    if (a.nodes.length > b.nodes.length) {
                        return sortOrder == 'ascend' ? 1 : -1;
                    } else {
                        return sortOrder == 'ascend' ? -1 : 1;
                    }
                })
            }
        } else {
            sortedAllDate = [...allServices];

        }

        setAllServices(sortedAllDate);
        setPage(1);
        setServices(doFilter(1, sortedAllDate, filterParam));



    }



    const doPage = (page: number) => {
        setServices(doFilter(page, allServices, filter))
        setPage(page);
    }

    return { columns, loading, allServices, services, selectedService: selectedServices, setSelectedService: setSelectedServices, delVisible, setDelVisible, editVisible, setEditVisible, reloadFlag, setReloadFlag, filter, setFilter, selectedRowKeys, onRowSelect, onRow, total, page, pageSize, doPage, handleFilterChange, handleSort }
}


export default useTable;