import React, { useEffect, useState, useContext } from 'react';
import { Typo<PERSON>, <PERSON>, Tabs, TabPane, Button, Skeleton, Notification, Popover } from '@douyinfe/semi-ui';
import QRCode from "react-qr-code";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import {
    isDesktop,
    isAndroid,
    isIOS,
    isMacOs,
    isWindows,
} from 'react-device-detect';
import android from '@/assets/icon/android.svg';
import iOS from '@/assets/icon/iOS.svg';
import linux from '@/assets/icon/linux.svg';
import macOS from '@/assets/icon/macOS.svg';
import windows from '@/assets/icon/windows.svg';


import styles from './index.module.scss';
import { flylayerClient } from '@/services/core';
import { Platform, Platform_Type } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/install_pb';
import { useNavigate, useParams } from 'react-router-dom';
import { BASE_PATH } from '@/constants/router';
import UserAuthKey from '@/components/user-auth-key';
const { Paragraph, Title } = Typography;

const Index: React.FC<{
    currentTab?: Platform_Type;
    currentTabOnly?: boolean
    navOnTab?: boolean;
    hideTabs?: Platform_Type[];
    onTabChange?: (activeKey: Platform_Type) => void;
}> = (props) => {
    const navigate = useNavigate();
    const flynet = useContext(FlynetGeneralContext);
    // 全局配置信息
    const globalConfig = useContext(GlobalConfigContext);


    // 取得react router页面路由参数
    const params = useParams<{
        platformName?: string;
        '*': string;
    }>();

    let networkName = '';
    if (location.hostname) {
        let nameArr = location.hostname.split('.');
        if (nameArr && nameArr.length > 0) {
            networkName = nameArr[0];
        }
    }

    let initTab: Platform_Type = Platform_Type.LINUX;
    let initDistroName = '';
    if (props.currentTab) {
        initTab = props.currentTab;
    } else {
        const paramPlatformName = params.platformName?.toLowerCase();
        if (paramPlatformName && (paramPlatformName == 'linux' || paramPlatformName == 'windows' || paramPlatformName == 'macos' || paramPlatformName == 'ios' || paramPlatformName == 'android')) {
            if (paramPlatformName == 'windows') {
                initTab = Platform_Type.WINDOWS;
            } else if (paramPlatformName == 'macos') {
                initTab = Platform_Type.MACOS;
            } else if (paramPlatformName == 'ios') {
                initTab = Platform_Type.IOS;
            } else if (paramPlatformName == 'android') {
                initTab = Platform_Type.ANDROID;
            } else {
                initTab = Platform_Type.LINUX;
            }

            if (paramPlatformName == 'linux') {
                const distroName = params['*'];
                if (distroName) {
                    initDistroName = distroName;
                }
            }
        } else if (isMacOs) {
            initTab = Platform_Type.MACOS;
        } else if (isWindows) {
            initTab = Platform_Type.WINDOWS;
        } else if (isAndroid) {
            initTab = Platform_Type.ANDROID;
        } else if (isIOS) {
            initTab = Platform_Type.IOS;
        } else {
            initTab = Platform_Type.LINUX;
        }
    }


    // linux发行版索引
    const [linuxDistroName, setLinuxDistroName] = useState(initDistroName);
    // linux发行版说明
    const [linuxDistroInstructions, setLinuxDistroInstructions] = useState('');


    const isPC = isDesktop;

    const [currentTab, setCurrentTab] = useState(initTab.toString());

    // 平台信息
    const [platforms, setPlatforms] = useState<Platform[]>();
    // 平台信息是否正在加载
    const [platformsLoading, setPlatformsLoading] = useState(true);

    useEffect(() => {
        flylayerClient.listInstall({
            query: ''
        }).then(res => {
            if (props.currentTabOnly && props.currentTab) {
                setPlatforms([res.platforms.find(p => p.type === props.currentTab)!]);
            } else if (props.hideTabs) {
                setPlatforms(res.platforms.filter(p => !props.hideTabs?.includes(p.type)));
            } else {
                setPlatforms(res.platforms);
            }


            if (res.platforms && res.platforms.length > 0) {
                // let distroName = '';
                let distroInstructions = '';
                for (let i = 0; i < res.platforms.length; i++) {
                    if (res.platforms[i].type === Platform_Type.LINUX) {
                        if (res.platforms[i].packages && res.platforms[i].packages.length > 0) {
                            for (let j = 0; j < res.platforms[i].packages.length; j++) {
                                if (res.platforms[i].packages[j].name.toLowerCase() == initDistroName.toLowerCase()) {
                                    distroInstructions = res.platforms[i].packages[j].instructions;
                                    break;
                                }
                            }
                        }

                        if (!distroInstructions) {
                            distroInstructions = res.platforms[i].packages[0].instructions;
                            setLinuxDistroName(res.platforms[i].packages[0].name);
                        }
                        break;
                    }
                }
                // setLinuxDistroName(distroName);
                setLinuxDistroInstructions(distroInstructions);
            }

        }).catch(err => {
            console.error(err);
            Notification.error({ content: '获取软件包信息失败，请稍后重试' })
        }).finally(() => {
            setPlatformsLoading(false);
        });
    }, [])

    // 标签页切换处理
    const handleTabChange = (activeKey: string) => {
        setCurrentTab(activeKey);

        // 切换路由
        if (activeKey == Platform_Type.WINDOWS.toString()) {
            props.onTabChange && props.onTabChange(Platform_Type.WINDOWS);
        } else if (activeKey == Platform_Type.MACOS.toString()) {
            props.onTabChange && props.onTabChange(Platform_Type.MACOS);
        } else if (activeKey == Platform_Type.IOS.toString()) {
            props.onTabChange && props.onTabChange(Platform_Type.IOS);
        } else if (activeKey == Platform_Type.ANDROID.toString()) {
            props.onTabChange && props.onTabChange(Platform_Type.ANDROID);
        } else if (activeKey == Platform_Type.LINUX.toString()) {
            props.onTabChange && props.onTabChange(Platform_Type.LINUX);
        }
        if (!props.navOnTab) {
            return;
        }
        // 切换路由
        if (activeKey == Platform_Type.WINDOWS.toString()) {
            navigate(`${BASE_PATH}/download/windows`)
        } else if (activeKey == Platform_Type.MACOS.toString()) {
            navigate(`${BASE_PATH}/download/macos`)
        } else if (activeKey == Platform_Type.IOS.toString()) {
            navigate(`${BASE_PATH}/download/ios`)
        } else if (activeKey == Platform_Type.ANDROID.toString()) {
            navigate(`${BASE_PATH}/download/android`)
        } else if (activeKey == Platform_Type.LINUX.toString()) {
            navigate(`${BASE_PATH}/download/linux`)
        }
    }

    // 查看认证密钥是否显示
    const [showAuthKey, setShowAuthKey] = useState(false);
    return <>

        <Skeleton loading={platformsLoading} placeholder={<>
            <Skeleton.Title style={{ marginBottom: 32, height: 70 }}></Skeleton.Title>
            <Skeleton.Image style={{ height: 400 }} />
        </>}>
            <Tabs type="button"
                onChange={handleTabChange}
                activeKey={currentTab.toString()}
                tabBarClassName={props.currentTabOnly ? styles.tabBarLinuxOnly : isPC ? styles.tabBar : styles.tabBarMobile}>
                {platforms && platforms.map((platform, index) => {
                    return <TabPane key={platform.type.toString()}
                        tab={<div className={isPC ? styles.tabItem : styles.tabItemMobile}>
                            <img src={

                                platform.type === Platform_Type.LINUX ? linux :
                                    platform.type === Platform_Type.WINDOWS ? windows :
                                        platform.type === Platform_Type.MACOS ? macOS :
                                            platform.type === Platform_Type.IOS ? iOS :
                                                platform.type === Platform_Type.ANDROID ? android : ''

                            }></img>
                            <span>{platform.name}</span></div>}
                        itemKey={platform.type.toString()}>
                        <div className={props.navOnTab ? isPC ? styles.tabPaneWrap : styles.tabPaneWrapMobile : ''}>

                            {platform.type === Platform_Type.MACOS ? <>
                                <div className={isPC ? styles.downloadBlock : styles.downloadBlockMobile}>
                                    <div>{platform.packages.map((pkg, index) => {
                                        return <Button key={index} type="primary" className='mb20'
                                            theme='solid'
                                            size='large'
                                            style={{ marginLeft: '10px', marginRight: '10px', width: 170 }}
                                            onClick={() => { window.open(pkg.downloadLink) }}>下载{pkg.arch}芯片版</Button>
                                    })}
                                    </div>
                                    <Popover position='bottom' content={<div className='p10'>
                                        <Paragraph>1. 在左上角，点击“<img src={iOS} style={{ width: 12, height: 12 }}></img>”-“关于本机”</Paragraph>
                                        <Paragraph>2. 在“概览”页的“芯片”或“处理器”中，查看是“Intel”还是“Apple”</Paragraph>
                                    </div>}><a style={{ marginBottom: 10, cursor: 'pointer', verticalAlign: 'top', display: 'inline-block', textDecoration: 'underline' }}>如何确定芯片类型?</a></Popover>

                                    <Paragraph className='mb20' >适用于 {platform.supportedVersions} 或更高版本</Paragraph>
                                </div>


                            </> : null}

                            {platform.type === Platform_Type.IOS ? <>
                                {platform.packages.length > 0 ? <>
                                    <div className={isDesktop ? styles.ScanBlock : styles.ScanBlockMobile}>
                                        <div className={styles.qr}>
                                            {platform.packages.length > 0 ? <QRCode value={`${location.protocol}//${location.host}${BASE_PATH}/download/ios`}></QRCode> : ''}

                                        </div>
                                        <div className={styles.qrmemo}>
                                            <Paragraph className='mb10'>扫描二维码或使用下面的链接下载适用于iOS的APP </Paragraph>
                                            {platform.packages.length > 0 ? <Button type="primary" className='mb20'
                                                theme='solid'
                                                size='large'
                                                onClick={() => { window.open(platform.packages[0].downloadLink) }}>下载适用于iOS的{globalConfig.name}客户端</Button> : ''}

                                            <Paragraph>需要 {platform.supportedVersions} 或更高版本</Paragraph>
                                        </div>
                                    </div>
                                </> : <>
                                    <div className={isPC ? styles.memo : styles.memoMobile} style={{ paddingTop: 44 }}>
                                        <Card className='mb20' style={{ textAlign: 'center' }}>即将上线</Card>
                                        <div style={{ height: 256 }}></div>
                                    </div>
                                </>}
                            </> : null}

                            {platform.type === Platform_Type.WINDOWS ? <>

                                <div className={isPC ? styles.downloadBlock : styles.downloadBlockMobile}>
                                    {platform.packages.length > 0 ? <Button type="primary" className='mb20'
                                        theme='solid'
                                        size='large'
                                        onClick={() => { window.open(platform.packages[0].downloadLink) }}>下载适用于Windows的{globalConfig.name}客户端</Button> : ''}

                                    <Paragraph className='mb20' >需要 {platform.supportedVersions} 或更高版本
                                    </Paragraph>
                                </div>
                            </> : null}

                            {platform.type === Platform_Type.LINUX ? <>
                                {props.currentTabOnly ? '' : <Title className='mb20' heading={3} style={{ textAlign: 'center', paddingTop: 44 }}>
                                    使用命令行一键安装</Title>}


                                <Paragraph className='mb40 copyable-code' style={{ textAlign: 'center' }} copyable >
                                    {platform.oneCommand}
                                </Paragraph>

                            </> : null}

                            {platform.type === Platform_Type.ANDROID ? <>
                                <div className={isDesktop ? styles.ScanBlock : styles.ScanBlockMobile}>
                                    {isPC ? <div className={styles.qr}>
                                        {platform.packages.length > 0 ? <QRCode value={globalConfig.flylayer ? 'https://flylayer.com/download/android/' : `${location.protocol}//${location.host}${BASE_PATH}/download/android`}></QRCode> : ''}

                                    </div> : ''}

                                    <div className={styles.qrmemo} style={{ paddingTop: globalConfig.flylayer ? 0 : 20 }}>
                                        {isPC ? <Paragraph className='mb20'>扫描二维码或点击下方链接下载</Paragraph> : ''}

                                        {platform.packages.length > 0 ? <Button type="primary" className='mb20'
                                            theme='solid'
                                            size='large'
                                            onClick={() => { window.open(platform.packages[0].downloadLink) }}>下载适用于Android的{globalConfig.name}客户端</Button> : ''}

                                        <Paragraph>需要 {platform.supportedVersions} 或更高版本</Paragraph>
                                        {globalConfig.flylayer && <Button type='danger' onClick={() => setShowAuthKey(true)} theme='solid'>查看认证码</Button>}
                                    </div>


                                </div></> : null}
                        </div>
                    </TabPane>
                })}
            </Tabs>
        </Skeleton>
        {showAuthKey && <UserAuthKey close={() => { setShowAuthKey(false) }}></UserAuthKey>}
    </>
}
export default Index;
