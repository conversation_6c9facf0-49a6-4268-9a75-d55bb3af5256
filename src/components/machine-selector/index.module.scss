:global {
    .radioTable {
        margin-bottom: 20px;

        .semi-table-selection-disabled {
            display: none;
        }

        input:disabled {
            display: none;
        }

        span[class="semi-checkbox-inner-checked"], 
        span[class="semi-checkbox-inner-display"], 
        span[role="img"] {
            border-radius: 10px;
            background: transparent!important;
        }
        .semi-checkbox-inner-display {
            border-radius: 10px;
            svg {
                background-color: var(--semi-color-primary);
                border-radius: 10px;
                color: var(--semi-color-fill);
            }
        }
    }


    .checkboxTable {
        margin-bottom: 20px;

        .semi-table-selection-disabled {
            display: none;
        }

        input:disabled {
            display: none;
        }
    }
}
