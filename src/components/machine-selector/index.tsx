import { FC } from 'react'

import useTable from './useTable';

import TableEmpty from '@/components/table-empty';
import { IconSearch } from '@douyinfe/semi-icons';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { Layout, Input, Select, Space, Button, Row, Col, Table, Modal } from '@douyinfe/semi-ui';
import { LocaleFormatter, useLocale } from '@/locales';

interface Props {
    multi: boolean;
    value?: Machine | Machine[] | string[];
    avilibleOs?: string[]; // 可选的操作系统
    onChange: (value: Machine | Machine[]) => void;
    close: () => void,
    gateway?: boolean;
}
const { Content, Sider } = Layout;
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    // 设备列表HOOKS
    const { columns, devices, loading, selectedDevices, filterParam, setFilterParam, page, total, doPage, pageSize, handleSort, selectedRowKeys, onRowSelect, onRow } = useTable({
        keywords: '',
        os: [],
        connectStatus: ''
    }, props.gateway ? true : false, props.multi, props.value, props.avilibleOs);


    const listConnectStatus = [
        { value: '', label: formatMessage({ id: 'components.common.all' }) },
        { value: 'online', label: formatMessage({ id: 'components.common.online' }) },
        { value: 'offline', label: formatMessage({ id: 'components.common.offline' }) }
    ];
    const listOs = [
        { value: 'macOS', label: 'macOS' },
        { value: 'iOS', label: 'iOS' },
        { value: 'windows', label: 'Windows' },
        { value: 'linux', label: 'Linux' },
        { value: 'android', label: 'Android' },
    ];


    const handleConnectStatusChange = (value: any) => {
        setFilterParam({ ...filterParam, connectStatus: value })

    }
    const handleOsChange = (value: any) => {

        setFilterParam({ ...filterParam, os: value })

    }

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, keywords: value })
    }



    return <><Modal
        width={830}
        title={formatMessage({ id: 'components.machineSelector.title' })}
        visible={true}
        onOk={() => { }}
        onCancel={props.close}
        footer={null}
        className='semi-modal'
    >
        <Layout className='mb20 search-bar' style={{ paddingTop: 10 }}>
            <Layout>
                <Content className='pr10'>
                    <Input value={filterParam.keywords}
                        onChange={handleQueryChange}
                        style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={formatMessage({ id: 'components.machineSelector.searchPlaceholder' })}></Input>
                </Content>
                <Sider> <Space>
                    <Select style={{ width: 200 }}
                        optionList={listConnectStatus}
                        insetLabel={formatMessage({ id: 'components.common.status' })}
                        onChange={handleConnectStatusChange}
                        value={filterParam.connectStatus}></Select>

                    <Select multiple
                        maxTagCount={1}
                        style={{ width: 200 }}
                        optionList={listOs}
                        insetLabel={formatMessage({ id: 'components.common.operatingSystem' })}
                        onChange={handleOsChange}
                        value={filterParam.os}></Select>
                </Space></Sider>
            </Layout>

        </Layout>

        <Row className='mb10'>
            <Col span={20}>
            </Col>
            <Col span={4}><div className='btn-right-col'>
            </div></Col>
        </Row>
        <div className='mb20'>
            <Table
                rowSelection={
                    {
                        selectedRowKeys: selectedRowKeys as any,
                        onSelect: onRowSelect as any,
                        disabled: true
                    }
                }
                className={props.multi ? 'checkboxTable' : 'radioTable'}
                onRow={onRow as any}
                rowKey={(record?: Machine) => record ? record.id + '' : ''}
                onChange={handleSort}
                empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={devices}
                pagination={total > pageSize ? {
                    currentPage: page,
                    pageSize: pageSize,
                    total: total,
                    onChange: (page:number) => {
                        doPage(page);
                    }
                } : false} />
        </div>
        <Row className='mb10'>
            <Col span={20}>
            </Col>
            <Col span={4}><div className='btn-right-col'>
                <Space>
                    <Button onClick={() => { props.close() }}><LocaleFormatter id="components.common.cancel" /></Button>
                    <Button theme='solid' disabled={!selectedDevices || selectedDevices.length == 0} onClick={() => {
                        if (selectedDevices) {

                            if (props.multi) {
                                props.onChange(selectedDevices)
                            } else {
                                props.onChange(selectedDevices[0])
                            }
                        }

                    }}><LocaleFormatter id="components.common.confirm" /></Button>
                </Space>
            </div></Col>
        </Row>
    </Modal>
    </>
}

export default Index;
