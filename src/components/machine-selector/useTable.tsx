import { useEffect, useState, FC, useContext } from 'react';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { Typography, Tag, List, Badge, Popover, Notification } from '@douyinfe/semi-ui';
import { listMachines } from '@/services/device';
import Copyable from '@douyinfe/semi-ui/lib/es/typography/copyable';
const { Title, Paragraph, Text } = Typography;

import styles from './index.module.scss';
export type DeviceFilter = {
    keywords: string,
    os: Array<string>,
    connectStatus: 'online' | 'offline' | ''
}

const useTable = (
    initFilter: DeviceFilter,
    gatewayNode: boolean,
    multi: boolean,
    initSelectedValue?: Machine | Machine[] | string[],
    avilibleOs?: string[]
) => {
    const flynet = useContext(FlynetGeneralContext);

    // 设备是否正在加载中
    const [loading, setLoading] = useState(true);
    // 设备列表
    const [devices, setDevices] = useState<Machine[]>([]);
    // 设备列表
    const [allDevices, setAllDevices] = useState<Machine[]>([]);

    // 当前页码
    const [page, setPage] = useState(1);

    const pageSize = 5;

    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);
    // 当前菜单选中设备
    const [selectedDevices, setSelectedDevices] = useState<Machine[]>();
    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filter, setFilter] = useState<DeviceFilter>(initFilter);

    const columns = [{
        width: 270,
        title: '设备',
        dataIndex: 'name',
        sorter: true,
        render: (field: string, record: Machine, index: number) => {
            // 设备名称
            const name = record.autoGeneratedName ? record.name : record.givenName
            return (
                <div>
                    <Title heading={5}>
                        {name}&nbsp;
                        {record.connected ?
                            <span><Badge dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> </span> :
                            <><span><Badge dot type='tertiary' /> </span></>}
                    </Title>
                    {record.tags && record.tags.length > 0 ? <Paragraph>
                        {record.tags.map((tag, index) => {
                            return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 5, marginBottom: 5 }}>{tag}</Tag>
                        })}
                    </Paragraph> : <Paragraph>{record.user?.displayName} <Text type="tertiary" className={styles.loginName} size='small' ellipsis={{ showTooltip: true }}>{record.user?.loginName}</Text></Paragraph>
                    }
                </div>
            );
        },
    },
    {
        width: 160,
        title: 'IP',
        dataIndex: 'ipV4',
        render: (field: string, record: Machine) => {
            return (
                <div className={styles.ipCopyable} style={{ display: 'flex', alignItems: 'center' }}>

                    <Popover content={<List
                        bordered>

                        {[record.ipv4, record.ipv6].map((val, index) => {
                            return <List.Item key={index}>
                                <Paragraph copyable>
                                    {val}</Paragraph>
                            </List.Item>
                        })}

                    </List>
                    }>
                        <Text className={styles.ipLine}>{record.ipv4}</Text>
                    </Popover>
                    <Copyable style={{ lineHeight: 1 }} content={record.ipv4}></Copyable>
                </div>

            );
        },
    },

    {
        width: 100,
        title: '操作系统',
        dataIndex: 'os',

        render: (field: string, record: Machine) => {
            return (
                <div className='layout-left-icon'>
                    <div>
                        <Paragraph>{field}</Paragraph>
                    </div>
                </div>
            );
        },
    },

    ];

    let initSelectedRowKeys: string[] = [];

    if (initSelectedValue) {

        if (initSelectedValue instanceof Machine) {
            initSelectedRowKeys.push(initSelectedValue.id + '');
        } else {
            initSelectedValue.forEach(val => {
                if (val instanceof Machine) {
                    initSelectedRowKeys.push(val.id + '');
                }
            })
        }
    }

    const [selectedRowKeys, setSelectedRowKeys] = useState<String[]>(initSelectedRowKeys);
    const onRowSelect = (record: Machine, selected: boolean, selectedRows: { id: string }[], e: MouseEvent) => {
        if (multi) {
            let newSelectedRowKeys: any = selectedRows.map(item => item.id + '');
            setSelectedRowKeys(newSelectedRowKeys);

            let newSelectedServices: Machine[] = [];
            for (let i = 0; i < allDevices.length; i++) {
                const s = allDevices[i];
                if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                    newSelectedServices.push(s);
                }
            }
            setSelectedDevices(newSelectedServices);
        } else {
            let selectKeys: any = [record.id + ''];
            setSelectedRowKeys(selectKeys);
            setSelectedDevices([record]);
        }
    };



    const onRow = (record: Machine, index: number) => {
        return {
            onClick: () => {
                if (multi) {
                    let id = record.id + '';
                    let newSelectedRowKeys: any = [...selectedRowKeys];
                    if (newSelectedRowKeys.indexOf(id) >= 0) {
                        newSelectedRowKeys = newSelectedRowKeys.filter((item: any) => item != id);
                    }
                    else {
                        newSelectedRowKeys.push(id);
                    }
                    setSelectedRowKeys(newSelectedRowKeys);
                    let newSelectedServices: Machine[] = [];
                    console.log('all devices', allDevices);
                    for (let i = 0; i < allDevices.length; i++) {
                        const s = allDevices[i];
                        if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                            newSelectedServices.push(s);
                        }
                    }
                    setSelectedDevices(newSelectedServices);

                } else {

                    let selectKeys: any = [record.id + ''];
                    setSelectedRowKeys(selectKeys);
                    setSelectedDevices([record]);
                }
            }, // 点击行

        };
    }



    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;

        let sortOrder = '';
        if (sorter.sortOrder == 'ascend') {
            sortOrder = 'ASC';
        } else if (sorter.sortOrder == 'descend') {
            sortOrder = 'DESC';
        }

        query({
            page: 1,
            sortOrder: sortOrder,
            sortField: dataIndex,
        })
    }

    const query = (params?: {
        filter?: DeviceFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
    }) => {
        if(gatewayNode){
            queryAllDevices(params);
        } else {
            queryDevicesPager(params);
        }
        
    }


    const queryAllDevices = (params?: {
        filter?: DeviceFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
    }) => {
        listMachines(flynet.id).then(res => {
            const machines = res.machines.filter((machine: Machine) => {
                if (machine.user?.disabled) {
                    return false;
                }
                if (!machine.authorized) {
                    return false;
                }
                if (!machine.keyExpiryDisabled) {
                    if (machine.expiresAt) {
                        if (machine.createdAt && machine.createdAt.toDate().getTime() > machine.expiresAt.toDate().getTime()) {
                            // 创建时间大于过期时间，说明是旧数据，未过期
                        } else {
                            const expiry = machine.expiresAt.toDate()
                            const now = new Date()
                            if (expiry < now) {
                                return false;
                            }
                        }
                    }
                }
                if (gatewayNode) {
                    if (machine.advertisedRoutes && machine.advertisedRoutes.length > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
                if (avilibleOs && avilibleOs.length > 0) {
                    if (avilibleOs.indexOf(machine.os) < 0) {
                        return false;
                    }
                }
                return true;
            });
            setTotal(Number(machines.length));
            setAllDevices(machines);
            setDevices(doFilter(page, machines, filter));
            setLoading(false);
        }).catch(e => {
            console.error(e)
            Notification.error({ content: '获取设备列表失败, 请稍后重试' })
        })
    }

    const queryDevicesPager = (params?: {
        filter?: DeviceFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
    }) => {
        let queryArray: Array<string> = [];

        let limit = pageSize;
        let curPage = page;

        let filterKeywords = filter.keywords || '';
        let filterOs = filter.os || '';
        let filterConnectStatus = filter.connectStatus || '';



        let filterSortOrder = sortOrder;
        let filterSortField = sortField;

        if (params) {
            if (params.filter) {
                setFilter(params.filter);

                filterKeywords = params.filter.keywords;
                filterOs = params.filter.os;
                filterConnectStatus = params.filter.connectStatus;
            }
            if (params.sortOrder && params.sortField) {
                setSortOrder(params.sortOrder as any);
                setSortField(params.sortField);

                filterSortOrder = params.sortOrder as any;
                filterSortField = params.sortField;
            } else {
                filterSortOrder = undefined;
                filterSortField = '';
                setSortOrder(undefined);
                setSortField('');
            }

            if (params.page) {
                curPage = params.page;
                setPage(params.page);
            }

            if (params.pageSize) {
                limit = params.pageSize;
            }
        }

        if (filterKeywords) {
            queryArray.push(`keywords=${filterKeywords}`);
        }
        if (filterOs) {
            queryArray.push(`os=${filterOs}`);
        }
        if (filterConnectStatus) {
            queryArray.push(`connectStatus=${filterConnectStatus}`);
        }

        if (filterSortOrder && filterSortField) {
            setSortOrder(filterSortOrder as any);
            setSortField(filterSortField);
            let order_by = encodeURIComponent(`${filterSortField} ${filterSortOrder}`);
            queryArray.push(`order_by=${order_by}`);
        }


        const offset = (curPage - 1) * limit;

        queryArray.push(`limit=${limit}`);
        queryArray.push(`offset=${offset}`);

        setLoading(true)

        listMachines(flynet.id, queryArray.join('&')).then(res => {
            const machines = res.machines.filter((machine: Machine) => {
                if (machine.user?.disabled) {
                    return false;
                }
                if (!machine.authorized) {
                    return false;
                }
                if (!machine.keyExpiryDisabled) {
                    if (machine.expiresAt) {
                        if (machine.createdAt && machine.createdAt.toDate().getTime() > machine.expiresAt.toDate().getTime()) {
                            // 创建时间大于过期时间，说明是旧数据，未过期
                        } else {
                            const expiry = machine.expiresAt.toDate()
                            const now = new Date()
                            if (expiry < now) {
                                return false;
                            }
                        }
                    }
                }
                if (gatewayNode) {
                    if (machine.advertisedRoutes && machine.advertisedRoutes.length > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
                if (avilibleOs && avilibleOs.length > 0) {
                    if (avilibleOs.indexOf(machine.os) < 0) {
                        return false;
                    }
                }
                return true;
            });



            setDevices(machines);
            let newAllData = [...allDevices];
            machines.forEach((machine: Machine) => {
                let index = newAllData.findIndex((item: Machine) => item.id == machine.id);
                if (index < 0) {
                    newAllData.push(machine);
                }
            });

            setAllDevices(newAllData);
            setTotal(Number(res.total));
        }).catch(e => {
            console.error(e)
            Notification.error({ content: '获取设备列表失败, 请稍后重试' })
        }).finally(() => setLoading(false))




    }


    // 过滤结果
    const doFilter = (page: number, src: Array<Machine>, filter: DeviceFilter): Array<Machine> => {
        if (!src || src.length == 0) {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }

        if (filter.keywords == '' && filter.os.length == 0 && filter.connectStatus == '') {
            setTotal(src.length)
            return src.slice(0, page * pageSize);
        }
        const filteredList = src.filter(record => {
            let { keywords: query, os, connectStatus } = filter;
            if (query) query = query.trim();
            let passQuery = true, passOs = true, passConnectStatus = true, passMeshEnabled = true;
            let passGroup = true;
            if (query) {
                var containsTag = false;
                if (record.tags && record.tags.length > 0) {
                    record.tags.forEach(tag => {
                        if (tag.indexOf(query) >= 0) {
                            containsTag = true;
                        }
                    })
                }

                if (record.givenName.indexOf(query) >= 0 ||
                    record.user && record.user.displayName.indexOf(query) >= 0 ||
                    record.user && record.user.loginName.indexOf(query) >= 0 ||
                    record.ipv4.indexOf(query) >= 0 ||
                    record.ipv6.indexOf(query) >= 0 ||
                    record.clientVersion.indexOf(query) >= 0 ||
                    record.description.indexOf(query) >= 0 ||
                    containsTag
                ) {
                    passQuery = true;
                } else {
                    passQuery = false;
                }
            }

            if (os.length > 0) {
                passOs = os.indexOf(record.os) >= 0;
            }

            if (connectStatus == 'online') {
                passConnectStatus = record.connected;
            } else if (connectStatus == 'offline') {
                passConnectStatus = !record.connected;
            }
            
            return passQuery && passOs && passConnectStatus && passGroup && passMeshEnabled;
        })

        setTotal(filteredList.length)

        return filteredList.slice(0, page * pageSize)
    }

    const doPage = (p: number) => {
        query({
            page: p
        })
    }
    // useEffect(() => {
    //     query()
    // }, []);

    useEffect(() => {
        setReloadFlag(false)

        if (reloadFlag) {
            query({
                page: 1
            })
        }
    }, [reloadFlag])
    useEffect(() => {
        query({
            page: 1,
            filter: filter
        })
    }, [filter])
    return { columns, loading, allDevices, devices, selectedDevices, setSelectedDevices, reloadFlag, setReloadFlag, filterParam: filter, setFilterParam: setFilter, page, setPage, total, doPage, pageSize, handleSort, selectedRowKeys, onRowSelect, onRow }

}

export default useTable;
