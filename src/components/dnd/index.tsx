import { FC, ReactElement, useRef } from 'react'
import styles from './index.module.scss';
import { Typography } from '@douyinfe/semi-ui';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useLocale } from '@/locales';

const { Text } = Typography;

interface DraggableItemProps {
    item: ReactElement;
    index: number;
    dndItemType: string;
    moveItem: (dragIndex: number, hoverIndex: number) => void;
    itemClassName?: string;
}

const DraggableItem: FC<DraggableItemProps> = ({ item, index, dndItemType, moveItem, itemClassName }) => {
    const ref = useRef<HTMLDivElement>(null);

    const [, drop] = useDrop({
        accept: dndItemType,
        hover: (draggedItem: { index: number }) => {
            if (!ref.current) {
                return;
            }
            const dragIndex = draggedItem.index;
            const hoverIndex = index;
            if (dragIndex === hoverIndex) {
                return;
            }
            moveItem(dragIndex, hoverIndex);
            draggedItem.index = hoverIndex;
        }
    });

    const [{ isDragging }, drag] = useDrag({
        type: dndItemType,
        item: { index },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });
    drag(drop(ref));

    return <div
        ref={ref}
        style={{ opacity: isDragging ? 0.5 : 1 }}
        className={itemClassName ? itemClassName : styles.dragItem}>
        {item}
    </div>
}


interface Props {
    containerClassName?: string;
    itemClassName?: string;
    items: ReactElement[];
    moveItem: (dragIndex: number, hoverIndex: number) => void;
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    return (
        <div>
            <DndProvider backend={HTML5Backend}>
                <div className={props.containerClassName ? props.containerClassName : styles.dragContainer}>
                    {props.items.map((item, index) => (
                        <DraggableItem
                            key={index}
                            item={item}
                            index={index}
                            dndItemType="ITEM"
                            moveItem={props.moveItem}
                            itemClassName={props.itemClassName}
                        />
                    ))}
                </div>
            </DndProvider>

            <div className='mb10'></div>
            <Text type='tertiary'>{formatMessage({ id: 'components.dnd.dragToSort' })}</Text>
        </div>
    )
}

export default Index;