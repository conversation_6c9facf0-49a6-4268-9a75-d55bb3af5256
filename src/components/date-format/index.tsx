import { FC } from 'react'

import moment from 'moment'
import { Timestamp } from "@bufbuild/protobuf";
interface Props {
    date?: Timestamp
}

/** 表格空数据占位符 */
const DateFormat:FC<Props> = (props) => {
    let displayStr = '';
    if(props.date) {
       displayStr = moment(props.date.toDate()).format('YYYY/MM/DD HH:mm:ss')
    }
    return <>
        {displayStr}
    </>
}

export default DateFormat;