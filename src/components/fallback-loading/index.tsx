import React, { <PERSON> } from 'react'
import { <PERSON>, <PERSON> } from '@douyinfe/semi-ui'
import { LocaleFormatter } from '@/locales'

interface FallbackMessageProps {
	message: string
	description?: string
}

const SuspendFallbackLoading: FC<FallbackMessageProps> = ({ message: _message, description }) => {
	return (
		<Spin tip={<LocaleFormatter id="components.loading.text" />}>
			<Banner
				fullMode={false}
				type="info"
				bordered
				icon={null}
				closeIcon={null}
				description={<div>{description}</div>}
			/>
		</Spin>
	)
}

export default SuspendFallbackLoading
