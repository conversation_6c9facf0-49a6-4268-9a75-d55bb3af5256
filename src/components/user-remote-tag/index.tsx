import { FC, useState, useContext, useEffect } from 'react';
import { Spin, Tag } from '@douyinfe/semi-ui';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';


import { flylayerClient } from '@/services/core';

const mapLoginName: Map<string, string> = new Map();
const Index: FC<{
    loginName: string,
    style?: React.CSSProperties,
    className?: string,
    onClose?: () => void,
    plain?: boolean,
}> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    // 用户加载标识，用于骨架屏
    const [userLoading, setUserLoading] = useState(false);
    const [displayName, setDisplayName] = useState<string>();
    const [hasUser, setHasUser] = useState(true);
    const query = () => {
        if (mapLoginName.has(props.loginName)) {
            setDisplayName(mapLoginName.get(props.loginName));
            return;
        }

        setUserLoading(true);
        flylayerClient.getUser({
            flynetId: flynetGeneral.id,
            identifier: {
                case: 'loginName',
                value: props.loginName
            }
        }).then(res => {
            if (res.user) {
                setDisplayName(res.user.displayName);
                mapLoginName.set(props.loginName, res.user.displayName);
            } else {
                setHasUser(false);
            }
        }).catch(err => {
            console.error(err)
            setHasUser(false);
        }).finally(() => {
            setUserLoading(false);
        });
    }
    useEffect(() => {
        query();
    }, []);
    if (!hasUser) {
        return <></>
    }
    if (props.plain) {
        return <>{userLoading ? <><Spin />({props.loginName})</> : <>{displayName}({props.loginName})</>}</>;
    } else {
        return <Tag closable size='large'
            style={props.style}
            onClose={() => {
                if (props.onClose) {
                    props.onClose();
                }
            }}>{userLoading ? <><Spin />({props.loginName})</> : <>{displayName}({props.loginName})</>}</Tag>;
    }
};

export default Index;