import { FC, useState, useContext } from 'react'
import { Typo<PERSON>, Modal, Notification, TextArea, Row, Col, Divider, Button } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { LicenseContext } from '@/hooks/useLicense';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { License } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/license_pb';
import { IconKey } from '@douyinfe/semi-icons';
import { LocaleFormatter, useLocale } from '@/locales';
const { Paragraph } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
}
const Index: FC<Props> = (props) => {
    const license = useContext(LicenseContext);
    const { formatMessage } = useLocale();

    const globalConfig = useContext(GlobalConfigContext);
    const [licenseKey, setLicenseKey] = useState('')
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false);


    const handleOk = () => {

        setLoading(true)

        flylayerClient.updateLicense({
            license: new License({
                machineCode: license.machineCode,
                licenseKey: licenseKey
            })
        }).then(() => {
            Notification.success({
                content: formatMessage({ id: 'components.activation.success' }),
                position: "bottomRight",
                onClose: () => {
                    props.success && props.success();
                }
            })
        }).catch((err) => {
            console.error(err);
            Notification.error({
                title: formatMessage({ id: 'components.activation.failed' }),
                content: err.message,
                position: "bottomRight"
            })
        }).finally(() => setLoading(false))

    };

    const handleCancel = () => {
        props.close();
    };



    return <><Modal
        width={800}
        title={<><LocaleFormatter id="components.activation.title" /> {license.customer.name ? ' - ' + license.customer.name : ''}</>}
        visible={true}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        closeOnEsc={true}
        okButtonProps={{ loading, disabled: !licenseKey }}
    >

        <Paragraph className='mb10' type='secondary'><LocaleFormatter id="components.activation.machineCode" /></Paragraph>
        <Paragraph className='mb20' type='tertiary' style={{ wordBreak: 'break-all', wordWrap: 'break-word' }} copyable>{license.machineCode}</Paragraph>
        <Divider className='mb10' />
        <Row>
            <Col span={12}><Paragraph className='mb10' type='secondary'><LocaleFormatter id="components.activation.licenseKey" /></Paragraph></Col>
            <Col span={12} className='btn-right-col'>
                {globalConfig.name.indexOf('飞越云') > -1 && <Button className='mb10' theme='solid' icon={<IconKey/>} onClick={()=>window.open(`https://flylayer.com/license/get?machineCode=${license.machineCode}`)}><LocaleFormatter id="components.activation.getLicenseKey" /></Button>}
            </Col>
        </Row>

        <TextArea className='mb20' placeholder={formatMessage({ id: 'components.activation.placeholder' })} autosize={{
            minRows: 8
        }} onChange={(value) => setLicenseKey(value)} value={licenseKey} />

    </Modal></>

}

export default Index;