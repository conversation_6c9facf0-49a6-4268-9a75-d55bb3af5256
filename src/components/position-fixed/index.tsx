import React, { useRef, useState, FC, ReactNode, useEffect, useLayoutEffect } from 'react';

const Index: FC<{
    children: ReactNode,
    fixedTop ?: number,
}> = (props) => {
    const containerRef = useRef<HTMLDivElement>(null);


    useLayoutEffect(() => {
        if (!containerRef || !containerRef.current) {
            return;
        }
        const rect = containerRef.current.getBoundingClientRect();
        const divTop = rect.top + document.documentElement.scrollTop;
        
        const onScroll = () => {
            if (!containerRef || !containerRef.current) {
                return;
            }
            if (divTop < window.scrollY + 100) {
                let top = divTop;
                if (props.fixedTop) {
                    top = props.fixedTop;
                }
                containerRef.current.style.position = "fixed";
                containerRef.current.style.zIndex = '100';
                containerRef.current.style.top = top + "px";
                containerRef.current.style.left = rect.left + "px";
                containerRef.current.style.width = rect.width + "px";
            } else {
                containerRef.current.style.position = "";
            }
        };
        window.addEventListener("scroll", onScroll);
        return () => window.removeEventListener("scroll", onScroll);
    }, []);

    return <div ref={containerRef}>
        {props.children}
    </div>
}

export default Index;