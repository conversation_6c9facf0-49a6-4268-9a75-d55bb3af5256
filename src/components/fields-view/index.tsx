import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Form, Notification, Button, Row, Col, Space, Tag, Popover, ArrayField, Select, Divider } from '@douyinfe/semi-ui';
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import styles from './index.module.scss';
import { IconEdit, IconDelete } from '@douyinfe/semi-icons';

const { Text } = Typography;

const Index: FC<{
    editable?: boolean
    fields: Array<Field>
    onDel?: (field: Field, index: number) => void
    onEdit?: (field: Field, index: number) => void
}> = (props) => {
    const editable = props.editable ? true : false;;
    console.log('editable', editable)
    return <>
        <Row className={styles.tableTitle}>
            <Col span={3}>字段名</Col>
            <Col span={3}>标签</Col>
            <Col span={3}>类型</Col>
            <Col span={3}>默认值</Col>
            <Col span={4}>占位符</Col>
            <Col span={8}>扩展信息</Col>
        </Row>
        {props.fields.map((field, index) => {
            return <Row key={index} className={styles.tableBody}>
                <Col span={3}>{field.key}</Col>
                <Col span={3}>{field.label}</Col>
                <Col span={3}>{field.type}</Col>
                <Col span={3}>{field.default}</Col>
                <Col span={4}>{field.placeholder}</Col>
                <Col span={8}><Text>{field.extra}</Text>
                    {editable && <Space style={{
                        float: 'right'
                    
                    }}>
                        <Button size='small' onClick={()=>{
                            if(props.onEdit){
                                props.onEdit(field, index)
                            }
                        }} icon={<IconEdit />} />
                        <Button size='small' icon={<IconDelete />} type='danger' onClick={()=>{
                            if(props.onDel){
                                props.onDel(field, index)
                            }
                        }} />
                        </Space>}
                </Col>
            </Row>
        })}
    </>
}

export default Index
