.editor {
    border: 1px solid var(--semi-color-border);
    padding-top: 26px;
    width: 100%;
    background-color: var(--semi-color-bg-0);
}

.toolbar {
    display: none;
    position: absolute;
}

.fullScreen {
    background-color: var(--semi-color-bg-0);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    height: 100vh;
    // padding-top: 26px;
    .toolbar {
        display: block;
        position: absolute;
        z-index: 10;
        top: 0;
        left: 50%;
        offset: -50%;
    }
}

.normal {
    position: relative;

    .toolbar {
        position: absolute;
        z-index: 10;
        top: 0;
        left: 50%;
        offset: -50%;
    }
}
.normal:hover .toolbar {
    display: flex;
}