import React, { FC, useContext } from 'react';
import Editor from '@monaco-editor/react';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import { Button, Tooltip } from "@douyinfe/semi-ui";

import { IconMaximize, IconMinimize } from '@douyinfe/semi-icons';
import styles from './index.module.scss';

const Index: FC<{
    value: string,
    language?: string,
    height?: string,
}> = (props) => {
    const globalTheme = useContext(GlobalThemeContext);

    const [fullScreen, setFullScreen] = React.useState(false);
    return <div style={{ height: fullScreen ? '100vh' : props.height }} className={fullScreen ? styles.fullScreen : styles.normal}>
    <div className={styles.toolbar}>{fullScreen ?
        <Tooltip content={'退出全屏'} zIndex={9999} position='bottom'>
            <Button size="small" onClick={() => setFullScreen(false)} icon={<IconMinimize />}></Button>
        </Tooltip>
        :
        <Tooltip content={'全屏'}>
            <Button size="small" onClick={() => setFullScreen(true)} icon={<IconMaximize />}></Button>
        </Tooltip>
    }
    </div>

    <Editor
        options={{
            readOnly: true,
            minimap: { enabled: false },
        }}
        
        className={styles.editor}
        defaultLanguage={props.language || "javascript"}  // 设置默认语言
        value={props.value} // 设置默认值
        height={'100%'} // 可以根据需要调整编辑器的高度
        theme={globalTheme.colorMode === 'dark' ? 'vs-dark' : 'vs-light'}
    />
    </div>

}

export default Index;