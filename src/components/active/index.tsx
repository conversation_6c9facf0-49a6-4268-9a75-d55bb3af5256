import { FC, useState } from 'react'
import { Typography, Modal, Form, Notification, Collapsible } from '@douyinfe/semi-ui';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss'
import { IconChevronRight, IconChevronDown } from '@douyinfe/semi-icons';

import { FlynetGeneral } from '@/services/flynet';
import { CUSTOMER_SERVICE_QR } from '@/constants';
const { Paragraph } = Typography;
const { Input } = Form
interface Props {
    close: () => void,
    success?: () => void,
    flynetGeneral : FlynetGeneral
}
const Index: FC<Props> = (props) => {
    
    const [formApi, SetFormApi] = useState<FormApi<{ newName: string, autoGeneratedName: boolean }>>()
    const [name, setName] = useState('')
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false); const handleOk = () => {
        if (!formApi) {
            return
        }
        let err = formApi.getError('newName');
        if (err) {
            return;
        }
        setLoading(true)
        flylayerClient.activateFlynet({
            flynetId: props.flynetGeneral.id, 
            code: name
        }).then((res) => {
            Notification.success({ 
                content: "激活网络成功", 
                position: "bottomRight",
                onClose: () => {
                    props.success && props.success();
                }
            })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: "激活网络失败，请稍后重试", position: "bottomRight" })
        }).finally(() => setLoading(false))
  
    };
    function isValidDeviceName(name: string): string {
        let errMessage = "";
        if (name.length === 0) {
            errMessage = "名称不能为空";
        }
        if (name.length > 63) {
            errMessage = "名称长度必须小于64个字符";
        }

        return errMessage;
    }

    const handleCancel = () => {
        props.close();
    };

    const [qrShow, setQrShow] = useState(false);
    

    return <><Modal
        width={400}
        title={`激活网络 - ${props.flynetGeneral.alias}`}
        visible={true}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        closeOnEsc={true}
        okButtonProps={{ loading }}
    >

        <Form getFormApi={SetFormApi} render={({ formState }) => (<>
            <Input field='name' validate={isValidDeviceName} onChange={(value) => setName(value)} label="激活码"></Input>
            
        </>)} />
        <Paragraph type={'tertiary'} className={styles.qrToggle} onClick={() => setQrShow(!qrShow)}>{qrShow ? <IconChevronDown /> : <IconChevronRight />}&nbsp;联系客服获取激活码</Paragraph>

        <Collapsible isOpen={qrShow}><div className={styles.qr}><img src={CUSTOMER_SERVICE_QR} /></div> </Collapsible>
        

    </Modal></>

}

export default Index;