import { FC, useEffect, useState, useContext } from 'react';
import { TreeSelect, Divider, Button, Input } from '@douyinfe/semi-ui'
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { Service } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

import { flylayerClient } from '@/services/core';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';

const Index: FC<{
    value: string | string[],
    onChange: (val: string[]) => void,
    multiple?: boolean,
    insetLabel?: string,
    style?: React.CSSProperties,
    placeholder?: string,
    maxTagCount?: number,
}> = (props) => {
    const flynet = useContext(FlynetGeneralContext);

    const [treeData, setTreeData] = useState<TreeNodeData[]>([]);

    const buildTreeData = (services: Service[]): TreeNodeData[] => {
        let treeData: TreeNodeData[] = [];



        services.forEach(service => {
            // 遍历服务服务组，找到对应的服务组
            service.serviceGroups.forEach(group => {
                // 找到对应的节点
                let node: TreeNodeData | undefined = undefined;
                for (let i = 0; i < treeData.length; i++) {
                    if (treeData[i].value == group.id + '') {
                        node = treeData[i];
                        break;
                    }
                }
                // 如果找到节点，把服务挂到节点下
                if (node) {

                    const description = service.description ? `(${service.description})` : ''
                    node.children?.push({
                        label: `${service.name}${description}`,
                        value: node.key + '-' + service.id + '',
                        key: node.key + '-' + service.id + '',
                        isLeaf: true,
                        children: []
                    })
                }
                // 如果没找到节点，创建节点，把服务挂到节点下
                else {
                    const description = service.description ? `(${service.description})` : ''
                    treeData.push({
                        label: `${group.name}${description}`,
                        value: group.id + '',
                        key: group.id + '',
                        isLeaf: false,
                        children: [{
                            label: `${service.name}${description}`,
                            value: group.id + '-' + service.id + '',
                            key: group.id + '-' + service.id + '',
                            children: [],
                            isLeaf: true
                        }]
                    })
                }

            })
        });

        return treeData;
    }

    useEffect(() => {
        flylayerClient.listServices({
            flynetId: flynet.id,
        }).then(res => {
            if (res) {
                setTreeData(buildTreeData(res.services));
            }
        })

    }, [])

    return <TreeSelect
        insetLabel={props.insetLabel}
        style={props.style}
        placeholder={props.placeholder}
        maxTagCount={props.maxTagCount}
        value={props.value}
        onChange={(val) => {
            props.onChange(val as string[])
        }}
        filterTreeNode
        showFilteredOnly
        showClear={true}
        leafOnly={true}
        multiple={props.multiple}
        treeData={treeData}
        dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}
    ></TreeSelect>
}

export default Index;