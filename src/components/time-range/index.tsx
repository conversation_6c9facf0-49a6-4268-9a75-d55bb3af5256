import { FC, useEffect, useState, useContext } from 'react';
import { Typography, Col, Space, Row, Tooltip, DatePicker, Button, Tag, Switch, Select, Modal, TreeSelect, Dropdown } from '@douyinfe/semi-ui';
import { IconChevronDown, IconActivity } from '@douyinfe/semi-icons';
import { DisabledDateOptions } from '@douyinfe/semi-ui/lib/es/datePicker';
import { TimerangeShortcut } from '@/interface/timerange-shortcut';
const { Title, Text } = Typography;
import styles from './index.module.scss'
import moment from 'moment';
import { useLocale } from '@/locales';

export const DEFAULT_TIMERANGE_NAME = '5m';

// 基础快捷方式配置（不包含标签）
const baseShortCuts = [{
    name: '5m',
    value: 5 * 60 * 1000,
    labelKey: 'timeRange.last5Minutes'
}, {
    name: '15m',
    value: 15 * 60 * 1000,
    labelKey: 'timeRange.last15Minutes'
}, {
    name: '30m',
    value: 30 * 60 * 1000,
    labelKey: 'timeRange.last30Minutes'
}, {
    name: '1h',
    value: 60 * 60 * 1000,
    labelKey: 'timeRange.last1Hour'
}, {
    name: '3h',
    value: 3 * 60 * 60 * 1000,
    labelKey: 'timeRange.last3Hours'
}, {
    name: '6h',
    value: 6 * 60 * 60 * 1000,
    labelKey: 'timeRange.last6Hours'
}, {
    name: '24h',
    value: 24 * 60 * 60 * 1000,
    labelKey: 'timeRange.last24Hours'
}, {
    name: '3d',
    value: 3 * 24 * 60 * 60 * 1000,
    labelKey: 'timeRange.last3Days'
}, {
    name: '7d',
    value: 7 * 24 * 60 * 60 * 1000,
    labelKey: 'timeRange.last7Days'
}, {
    name: '30d',
    value: 30 * 24 * 60 * 60 * 1000,
    labelKey: 'timeRange.last30Days'
}, {
    name: '90d',
    value: 90 * 24 * 60 * 60 * 1000,
    labelKey: 'timeRange.last90Days'
}
];
const Index: FC<{
    defaultValue?: string[],
    timerangeName: string,
    onChange?: (start: string, end: string, shortCut?: TimerangeShortcut) => void
}> = (props) => {
    const { formatMessage } = useLocale();

    // 创建国际化的快捷方式数组
    const shortCuts: TimerangeShortcut[] = baseShortCuts.map(item => ({
        ...item,
        label: formatMessage({ id: item.labelKey })
    }));

    const [useShortCut, setUseShortCut] = useState(props.defaultValue && props.defaultValue.length == 2 ? false : true);

    let initShortCut: TimerangeShortcut = shortCuts[0];
    if (props.timerangeName) {
        const find = shortCuts.find((item) => {
            return item.name == props.timerangeName;
        });
        if (find) {
            initShortCut = find;
        }
    }



    const [shortCut, setShortCut] = useState<TimerangeShortcut | undefined>(initShortCut);

    const [start, setStart] = useState<string>(props.defaultValue && props.defaultValue.length == 2 ? props.defaultValue[0] : "");
    const [end, setEnd] = useState<string>(props.defaultValue && props.defaultValue.length == 2 ? props.defaultValue[1] : "");

    const [shortCutStart, setShortCutStart] = useState<string>("");
    const [shortCutEnd, setShortCutEnd] = useState<string>("");

    const [dropdownVisible, setDropdownVisible] = useState(false);

    const handleDateRangeChange = (range: any) => {
        if (!range || !Array.isArray(range) || range.length < 2) {
            return;
        }


        const start = moment(range[0]).format('YYYY-MM-DD HH:mm:ss');
        const end = moment(range[1]).format('YYYY-MM-DD HH:mm:ss');

        setStart(start);
        setEnd(end);

    }




    return <Dropdown
        trigger='click'
        visible={dropdownVisible}
        onVisibleChange={v=>setDropdownVisible(v)}
        render={<div className={styles.pop}>
            <div className={styles.container}>
                <div className={styles.leftCol}>
                    <Title heading={6} className='mb10'>{formatMessage({ id: 'timeRange.manualSelect' })}</Title>
                    <DatePicker
                        
                        className={styles.datePicker}
                        type='dateTimeRange'
                        showClear={true}
                        zIndex={1060}
                        style={{ width: 370 }}
                        value={[start, end]}
                        onChange={handleDateRangeChange}
                        defaultValue={props.defaultValue}
                        startDateOffset={(selectedDate?: Date) => {
                            
                            if (selectedDate) {
                                return moment(selectedDate).subtract(90, 'days').toDate();
                            }
                            return new Date();
                        }}
                        endDateOffset={(selectedDate?: Date) => {
                            
                            if (selectedDate) {
                                return moment(selectedDate).add(0, 'days').toDate();
                            }
                            return new Date();
                        }}
                        disabledDate={(date?: Date, options?: DisabledDateOptions) => {
                            // 禁用当前日期90天之外的日期
                            if (!date) return false;
                            if (moment(date) < moment().subtract(91, 'days')) {
                                return true;
                            }
                            if (moment(date) > moment().add(0, 'days')) {
                                return true;
                            }
                            return false;
                        }}
                        
                        
                    ></DatePicker>
                    <Button theme='solid' disabled={!start || !end} onClick={() => {
                        setUseShortCut(false);
                        if (props.onChange) {
                            props.onChange(start, end, undefined);
                            setDropdownVisible(false)
                        }
                    }}>{formatMessage({ id: 'components.common.confirm' })}</Button>
                </div>
                <div className={styles.rightCol}>

                    <Title heading={6} className='mb10'>{formatMessage({ id: 'timeRange.quickSelect' })}</Title>
                    <div className={styles.tags}>
                        {shortCuts.map((item, index) => {
                            return <Tag size='large' style={{ cursor: 'pointer' }} key={index}
                                onClick={() => {
                                    setShortCut(item);
                                    setUseShortCut(true);
                                    setStart('');
                                    setEnd('');

                                    if (props.onChange) {
                                        props.onChange('', '', item);
                                        setDropdownVisible(false)
                                    }
                                }}>{item.label}</Tag>
                        })}
                    </div>
                </div>
            </div>


        </div >}>
        <Button icon={<IconChevronDown />} iconPosition="right">
            <span className={styles.displayArea}>
                <IconActivity style={{ fontSize: '14px', marginRight: 10 }} />
                {useShortCut ? <Tooltip onVisibleChange={(visible) => {
                    if (visible) {
                        if (!shortCut) {
                            return;
                        }
                        let startTime = new Date(new Date().getTime() - shortCut.value);
                        setShortCutStart(moment(startTime).format('YYYY-MM-DD HH:mm:ss'));
                        setShortCutEnd(moment(new Date()).format('YYYY-MM-DD HH:mm:ss'));
                    }
                }} content={`${shortCutStart} - ${shortCutEnd}`}>
                    <span style={{ marginLeft: 4 }}>{shortCut ? shortCut.label : ''}</span>
                </Tooltip> : start && end ? `${start} - ${end}` : ''}


            </span>
        </Button>
    </Dropdown >
}


const getTimeRange = (timerangeName: string) => {

    const find = baseShortCuts.find((item) => {
        return item.name == timerangeName;
    });

    if (find) {
        const start = moment(new Date(new Date().getTime() - find.value + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000)).format('YYYY-MM-DD HH:mm:ss');
        const end = moment(new Date(new Date().getTime() + (new Date()).getTimezoneOffset() / 60 * 3600 * 1000)).format('YYYY-MM-DD HH:mm:ss');
        return {
            start,
            end
        }
    }
    return {
        start: '',
        end: ''
    }
}

export default Index;
export {
    getTimeRange
}