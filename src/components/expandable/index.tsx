import { useState, FC } from 'react'
import { Collapsible } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

const Expandable: FC<{
    expand: boolean
    collapseHeight: number,
    children: React.ReactNode,
    collapseChildren?: React.ReactNode
}> = ({expand, children, collapseHeight, collapseChildren }) => {
    const { formatMessage } = useLocale();
    const [isExpanded, setIsExpanded] = useState(false)
    const shortChildren = collapseChildren ? collapseChildren : children
    const toggle = () => {
        setIsExpanded(!isExpanded);
    };
    const maskStyle = isExpanded
        ? {}
        : {
            WebkitMaskImage:
                'linear-gradient(to bottom, black 0%, rgba(0, 0, 0, 1) 60%, rgba(0, 0, 0, 0.2) 80%, transparent 100%)',
        };
    return <>{!expand ? <>{children}</> :
        <div style={{ position: 'relative' }}>
            <Collapsible isOpen={isExpanded} collapseHeight={collapseHeight} style={{ ...maskStyle }}>
                { isExpanded ? children : shortChildren }
            </Collapsible>
            
                <a onClick={toggle} style={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    textAlign: 'right',
                    bottom: -10,
                    fontWeight: 700,
                    cursor: 'pointer',
                    color: '#6b7785',
                    fontSize: 12,
                }}>
                    {isExpanded ? formatMessage({ id: 'components.expandable.collapse' }) : formatMessage({ id: 'components.expandable.expand' })}

                </a>
            
        </div>}</>
}

export default Expandable
