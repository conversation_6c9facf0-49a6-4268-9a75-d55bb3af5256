import React, { useContext } from 'react';
import Editor from '@monaco-editor/react';

import { IconMaximize, IconMinimize } from '@douyinfe/semi-icons';
import { Button, Tooltip } from "@douyinfe/semi-ui";
import styles from './index.module.scss';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';

const Index: React.FC<{
    value: string,
    language?: string,
    height?: string,
    onChange: (value: string) => void,
}> = (props) => {
    const globalTheme = useContext(GlobalThemeContext);

    const [fullScreen, setFullScreen] = React.useState(false);
    return <div style={{ height: fullScreen ? '100vh' : props.height }} className={fullScreen ? styles.fullScreen : styles.normal}>
        <div className={styles.toolbar}>{fullScreen ?
            <Tooltip content={'退出全屏'} zIndex={9999} position='bottom'>
                <Button size="small" onClick={() => setFullScreen(false)} icon={<IconMinimize />}></Button>
            </Tooltip>
            :
            <Tooltip content={'全屏'}>
                <Button size="small" onClick={() => setFullScreen(true)} icon={<IconMaximize />}></Button>
            </Tooltip>
        }
        </div>
        <Editor
            className={styles.editor}
            height={'100%'} // 可以根据需要调整编辑器的高度
            defaultLanguage={props.language || "javascript"}  // 设置默认语言
            value={props.value} // 设置默认值
            onChange={(value) => props.onChange(value || '')} // 设置值改变的回调 
            //   theme="vs-dark" // 设置主题
            theme={globalTheme.colorMode === 'dark' ? 'vs-dark' : 'vs-light'}
        />
    </div>
        ;
};

export default Index;