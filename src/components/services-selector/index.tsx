import { FC, useEffect, useState, useContext } from 'react';
import { Select, Divider, Button, Input } from '@douyinfe/semi-ui'
import { Service } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import { flylayerClient } from '@/services/core';


const Index: FC<{
    value: string | string[],
    onChange: (val: string|string[]) => void,
    multiple?: boolean,
    insetLabel?:string,
    style?: React.CSSProperties,
    placeholder?: string,
    maxTagCount?: number,
}> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const [currentValue, setCurrentValue] = useState(props.value);
    const [optList, setOptList] = useState<Array<{
        value: string,
        label: string
    }>>([]);
    const [filteredOptList, setFilteredOptList] = useState<Array<{
        value: string,
        label: string
    }>>([]);

    const [servicesLoaded, setServicesLoaded] = useState(false);

    useEffect(() => {
        flylayerClient.listServices({
            flynetId: flynet.id,
        }).then(res => {
            if (res) {
                const optList = res.services.map(service => {
                    const description = service.description ? `(${service.description})` : ''
                    return {
                        value: 'svc:' + service.name,
                        label: `${service.name}${description}`
                    }
                });
                setFilteredOptList(optList);
                setOptList(optList);
                setServicesLoaded(true);
            }
        })
    }, []);

    const [filterTxt, setFilterTxt] = useState('');
    const handleFilterTxtChange = (val: string) => {
        setFilterTxt(val);

        const filteredOptList = optList.filter(opt => opt.label.includes(val));
        setFilteredOptList(filteredOptList);


    }
    return <>

        <Select
            filter
            style={props.style}
            multiple={props.multiple}
            insetLabel={props.insetLabel}
            value={currentValue}
            optionList={filteredOptList}
            placeholder={props.placeholder}
            maxTagCount={props.maxTagCount}
            onChange={val => {
                setCurrentValue(val as any)
                props.onChange(val as any)
            }}>

        </Select>
    </>
}

export default Index;
