import { FC } from 'react'
import { Typography, Modal } from '@douyinfe/semi-ui';

import styles from './index.module.scss'

import { CUSTOMER_SERVICE_QR } from '@/constants';
import { LocaleFormatter } from '@/locales';
const { Paragraph } = Typography;
interface Props {
    close: () => void,
    success?: () => void
}
const Index: FC<Props> = (props) => {
    

    const handleCancel = () => {
        props.close();
    };

    

    return <><Modal
        width={400}
        title={<LocaleFormatter id="components.customerService.title" />}
        visible={true}
        footer={null}
        onCancel={handleCancel}
        maskClosable={false}
        closeOnEsc={true}

    >
        <div className={styles.qr}><img src={CUSTOMER_SERVICE_QR} /></div>
        <Paragraph className='mb20' style={{textAlign:'center'}}>
            <LocaleFormatter id="components.customerService.description" />
        </Paragraph>
        

    </Modal></>

}

export default Index;