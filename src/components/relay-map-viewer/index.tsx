import { FC, useEffect, useState } from "react";
import { Ty<PERSON>graphy, Button, Space, Popover, Row, Col } from '@douyinfe/semi-ui';
import { IconInfoCircle } from '@douyinfe/semi-icons';

import CodeEditor from '@/components/code-editor';
import CodeViewer from '@/components/code-viewer';
const { Title, Paragraph, Text } = Typography;

interface Props {
  flynetId: bigint,
  value: Uint8Array,
  currentValue: Uint8Array,
  className?: string,
  onEdit?: () => void,
  label?: string
  nav?: string
}

const Index: FC<Props> = (props) => {

  const decoder = new TextDecoder('utf-8');
  let result = decoder.decode(props.value);
  const [jsonEditorValue, setJsonEditorValue] = useState<string>(JSON.stringify(JSON.parse(result), null, 2));

  let currentResult = decoder.decode(props.currentValue);
  const [currentJsonEditorValue, setCurrentJsonEditorValue] = useState<string>(JSON.stringify(JSON.parse(currentResult), null, 2));


  useEffect(() => {
    let result = decoder.decode(props.value);
    setJsonEditorValue(JSON.stringify(JSON.parse(result), null, 2));
  }, [props.value])

  useEffect(() => {
    let currentResult = decoder.decode(props.currentValue);
    setCurrentJsonEditorValue(JSON.stringify(JSON.parse(currentResult), null, 2));
  }, [props.currentValue])

  const handleEdit = () => {
    if (props.onEdit) {
      props.onEdit();
    }
  }
  return <div className={props.className}>

    <Row>
      <Col span={18}>
        <Title heading={4} className='mb2'>中继服务器配置</Title>
        <Space className='mb20'><Paragraph type='tertiary'>
          中继服务器配置包含了中继服务器的名称、地址、ID等信息。配置示例:
        </Paragraph>
          <Popover content={<div className='p10' style={{ width: 800 }}>
            <CodeViewer language='json' height="300px" value={`
{
  "Regions": {
    "1": {
      "RegionID": 1,
      "RegionCode": "r1",
      "RegionName": "r1",
      "Nodes": [
        {
          "Name": "1c1",
          "RegionID": 1,
          "HostName": "derp1c.flylayer.com",
          "IPv4": "*************",
          "CanPort80": true
        }
      ]
    }
  }
}
                `} />
          </div>}>
            <IconInfoCircle />
          </Popover>
        </Space>

      </Col>
      <Col span={6}>
        <div className="btn-right-col">
          <Button theme="solid" onClick={handleEdit}>修改当前设备中继服务器配置</Button>
        </div>

      </Col>
    </Row>

    <Row gutter={32}>
      <Col span={12}>
        <Paragraph type='tertiary' className="mb10">当前设置配置</Paragraph>
        <CodeViewer height='320px'
          value={currentJsonEditorValue}
          language='json'
        ></CodeViewer>
      </Col>
      <Col span={12}>
        <Space  className="mb10">
          <Paragraph type='tertiary'>最终配置</Paragraph>
          {props.nav || props.label ? <a href={props.nav} target="_blank"><Text type='tertiary' size="small">{props.label}</Text></a> : <Text type='tertiary'>{props.label}</Text>}
        </Space>

        <CodeViewer height='320px'
          value={jsonEditorValue}
          language='json'
        ></CodeViewer>
      </Col>
    </Row>


    {/* <Space style={{ marginTop: 10 }}>
            <Button type="primary" loading={props.saveLoading} onClick={handleSave} theme='solid'>保存配置</Button>
        </Space> */}
  </div>
}

export default Index
