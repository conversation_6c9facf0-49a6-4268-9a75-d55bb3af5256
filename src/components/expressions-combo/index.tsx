import { FC, useState, ReactElement, useEffect } from 'react';
import { Button, Space, Tag } from "@douyinfe/semi-ui";
import { IconClose } from '@douyinfe/semi-icons';

import styles from './index.module.scss';
interface Props {
    onAdd: (index: number) => void;
    onDel: (index: number) => void;
    children: Array<ReactElement>;
    operators: Array<string>;
    setOperators: (operators: Array<string>) => void;
}

const Index: FC<Props> = (props) => {

    const [operators, setOperators] = useState(props.operators);
    
    useEffect(() => {
        setOperators(props.operators);
    }, [props.operators]);

    return <>
        {props.children.map((child, index) => {
            let operator = '';
            if (index > 0 && index < props.operators.length + 1) {
                operator = props.operators[index - 1];
            }

            return <div key={index}>
                {operator == 'and' && <div className={styles.andRow}><Tag>And</Tag></div>}
                {operator == 'or' && <div className={styles.orRow}><Tag>Or</Tag></div>}
                <div className={styles.contentRow}>
                    <div className={styles.content}>
                        {child}
                    </div>
                    <div className={styles.operator}>
                        <Space>
                            <Button onClick={() => {
                                let newOperators = [...operators];
                                newOperators.splice(index, 0, 'and');
                                props.onAdd(index);
                                
                                props.setOperators(newOperators);
                            }}>And</Button>
                            {(index == props.children.length - 1) &&
                                <Button onClick={() => {
                                    let newOperators = [...operators];
                                    newOperators.push('or');
                                    
                                    props.setOperators(newOperators);
                                    props.onAdd(index);
                                }}>Or</Button>}

                            {props.children.length > 1 &&
                                <Button onClick={() => {
                                    props.onDel(index)
                                    let newOperators = [...operators];
                                    newOperators.splice(index-1, 1);
                                    
                                    props.setOperators(newOperators);
                                }} icon={<IconClose />}></Button>}
                        </Space>
                    </div>

                </div>
            </div>

        })}
    </>;
}

export default Index;
