
.contentRow {
    display: flex;
    .content {
        flex-grow: 1;
    }
    .operator {
        width: 150px;
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;
    }
}
.andRow {
    position: relative;
    height: 40px;
    display: flex;
    align-items: center;
    &::before {
        height: 10px;
        border-left: 1px solid rgba(var(--semi-grey-5), 0.15);
        position: absolute;
        left: 16px;
        top: 0;
        content: ' ';
        
    }
    &::after {
        height: 10px;
        border-left: 1px solid rgba(var(--semi-grey-5), 0.15);
        position: absolute;
        left: 16px;
        bottom: 0;
        content: ' ';
        
    }
}
.orRow {
    padding-top: 20px;
    padding-bottom: 20px;
}