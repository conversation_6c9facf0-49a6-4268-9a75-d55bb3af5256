.titleRow {
    background-color: var(--semi-color-bg-1);
    color: var(--semi-color-text-2);
    font-size: 14px;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid var(--semi-color-border);
    padding: 8px 16px;
    vertical-align: middle;
    overflow-wrap: break-word;
    position: relative;
}


.inlineSwitch {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0 !important;

    >div {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}

.rightColumn {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0 !important;
}

.bigRightColumn {
    padding-right: 0 !important;
}

.bodyRow {
    
    border-bottom: 1px solid var(--semi-color-border);
    
    box-sizing: border-box;
    
    vertical-align: middle;
    font-size: 14px;

    .bodyRowCell {
        
        overflow-wrap: break-word;
        border-left: none;
        border-right: none;
        padding: 16px !important;
        box-sizing: border-box;
        
        vertical-align: middle;
        font-size: 14px;
    
    }
}


.bodyRow:last-of-type {
    border-bottom: none;
}



.bodyText {
    font-size: 14px;
    color: var(--semi-color-text-2);
    font-weight: 400;
    overflow-wrap: break-word;
    line-height: 24px!important;
}



.tableTitle {
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 8px;
    color: var(--semi-color-text-2);
    font-weight: 600;
    font-size: 14px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
        line-height: 32px;
    }
}



.tableBody {
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--semi-color-border);
    margin-bottom: 10px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}

.tableBody:last-child {
    border-bottom: none;
}



.tableBodyError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);

    padding-top: 5px;
    padding-bottom: 5px;

    >div {
        padding-right: 8px;
        padding-bottom: 8px;
    }
}



