import { FC, useState, useContext, useEffect } from 'react'
import { Typo<PERSON>, Modal, Form, Button, Row, Col, Tag, Space, ArrayField, Select, Divider } from '@douyinfe/semi-ui';
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { IconAlertCircle, IconPlusCircle, IconPlus, IconMinusCircle } from '@douyinfe/semi-icons';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';

const Index: FC<{
    cancel: () => void
    success: (field: Field) => void
}> = (props) => {

    const [formApi, setFormApi] = useState<FormApi<Field>>();

    const handleSubmit = async () => {
        if (!formApi) {
            return;
        }

        await formApi.validate();

        const values = formApi.getValues();

        const field = new Field({
            key: values.key,
            type: values.type,
            extra: values.extra,
            label: values.label,
            default: values.default,
            placeholder: values.placeholder,
            rules: []
        });

        props.success(field);
    }

    return <>
        <Modal
            width={560}
            title='新建配置'
            visible={true}
            onCancel={props.cancel}
            onOk={handleSubmit}
            className='semi-modal'
            maskClosable={false}
        >
            <div className='p10'>
                <Form
                    getFormApi={setFormApi}
                    initValues={{
                        type: 'string',
                        label: '',
                        extra: '',
                        default: '',
                        placeholder: '',
                        rules: []
                    }}
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Input
                                field='key'
                                label='字段名'
                                placeholder='请输入字段名'

                            ></Form.Input>
                        </Col>
                        <Col span={12}>
                            <Form.Select
                                field='type'
                                label='类型'
                                placeholder='请选择类型'
                                style={{ width: '100%' }}
                            >
                                <Form.Select.Option value={'string'}>string</Form.Select.Option>
                                <Form.Select.Option value={'number'}>number</Form.Select.Option>
                                <Form.Select.Option value={'boolean'}>boolean</Form.Select.Option>
                            </Form.Select>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Input
                                required
                                field='label'
                                label='标签'
                                placeholder='请输入标签'
                            />
                        </Col>
                        <Col span={12}>
                            <Form.Input
                                field='extra'
                                label='扩展信息'
                                placeholder='请输入扩展信息'
                            />
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={12}>

                            <Form.Input
                                field='default'
                                label='默认值'
                                placeholder='请输入默认值'
                            />
                        </Col>
                        <Col span={12}>

                            <Form.Input
                                field='placeholder'
                                label='占位符'
                                placeholder='请输入占位符'
                            />
                        </Col>
                        <Col span={12}></Col>
                    </Row>

                    <ArrayField field={`rules`}>
                        {({ add, arrayFields, addWithInitValue }) => (
                            <>
                                {arrayFields.map(({ field, key, remove }, i) => (
                                    <div style={{ display: "flex" }} key={key}>
                                        <Form.Input
                                            field={`${field}[itemName]`}
                                            label={`${field}.itemName`}
                                            noLabel
                                            style={{ width: 100, marginRight: 12 }}
                                        ></Form.Input>
                                        <Form.Select
                                            field={`${field}[type]`}
                                            label={`${field}.type`}
                                            noLabel
                                            style={{ width: 100 }}
                                            optionList={[
                                                { label: "包含", value: "include" },
                                                { label: "不包含", value: "exclude" },
                                            ]}
                                        ></Form.Select>
                                        <Button
                                            type="danger"
                                            theme="borderless"

                                            icon={<IconMinusCircle />}
                                            onClick={remove}
                                        />
                                        <Button
                                            icon={<IconPlusCircle />}
                                            disabled={i !== arrayFields.length - 1}
                                            onClick={() => {
                                                addWithInitValue({
                                                    itemName: `条件${arrayFields.length + 1}`,
                                                    type: "include",
                                                });
                                            }}
                                        />
                                    </div>
                                ))}
                            </>
                        )}
                    </ArrayField>


                </Form>
            </div>
        </Modal>

    </>
}

export default Index