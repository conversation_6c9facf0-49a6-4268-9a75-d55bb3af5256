import { FC, useState, useContext, useEffect } from 'react'
import { Typography, ButtonGroup, Form, Notification, Button, Row, Col, Space, Popover, ArrayField, Select, Divider, Tooltip } from '@douyinfe/semi-ui';
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import FieldAdd from './field-add';
import FieldEdit from './field-edit';
import { IconPlus, IconGridView, IconListView } from '@douyinfe/semi-icons';
import styles from './index.module.scss';

import FieldsRender from '../fields-render/index';
import FieldsView from '../fields-view/index';

const { Title } = Typography;

const Index: FC<{
    fields: Array<Field>
    onChange: (fields: Array<Field>) => void
}> = (props) => {
    const [fields, setFields] = useState<Array<Field>>(props.fields);
    useEffect(() => {
        setFields(props.fields)
    }, [props.fields])

    const [addVisible, setAddVisible] = useState(false);

    const [viewMode, setViewMode] = useState<'render' | 'view'>('view');

    const [selectField, setSelectField] = useState<Field | null>(null);
    const [selectFieldIndex, setSelectFieldIndex] = useState<number | null>(null);

    const [editVisible, setEditVisible] = useState(false);

    return <>
        <Row className='mb10'>
            <Col span={12}><Title heading={4}>字段管理</Title></Col>
            <Col span={12} className={styles.rightColumn}>
                <Space>
                    <ButtonGroup>
                        <Tooltip content="预览">
                            <Button theme={viewMode == 'render' ? 'solid' : 'light'}  onClick={() => { setViewMode('render') }} icon={<IconGridView />} /></Tooltip>
                        <Tooltip content="列表">
                            <Button theme={viewMode == 'view' ? 'solid' : 'light'} onClick={() => { setViewMode('view') }} icon={<IconListView />} /></Tooltip>
                    </ButtonGroup>

                    <Button onClick={() => setAddVisible(true)} icon={<IconPlus />} />
                </Space>
            </Col>
        </Row>
        <Divider></Divider>
        {viewMode == 'render' && <FieldsRender fields={fields}></FieldsRender>}
        {viewMode == 'view' && <FieldsView fields={fields} editable={true}
        onEdit={(field, index) => {
            setSelectField(field)
            setSelectFieldIndex(index)
            setEditVisible(true)
        }}
        onDel={(field, index) => {
            const newFields = [...fields]
            newFields.splice(index, 1)
            setFields(newFields)
            props.onChange(newFields)
        }}></FieldsView>}
        
        {editVisible && selectField && selectFieldIndex !== null && <FieldEdit
            field={selectField}
            cancel={() => setEditVisible(false)}
            success={(field) => {
                const newFields = [...fields]
                newFields[selectFieldIndex] = field
                setFields(newFields)
                props.onChange(newFields)
                setEditVisible(false)
            }}></FieldEdit>}



        {addVisible && <FieldAdd
            cancel={() => setAddVisible(false)}
            success={(field) => {
                setFields([...fields, field])
                props.onChange([...fields, field])
                setAddVisible(false)
            }}></FieldAdd>}

    </>
}

export default Index