import { FC, useState } from 'react'

import { Table, Typography } from '@douyinfe/semi-ui';
import { useLocale } from '@/locales';

const { Paragraph } = Typography

const Index: FC = () => {
    const { formatMessage } = useLocale();
    const columns = [{
        title: formatMessage({ id: 'domainManager.type' }),
        dataIndex: 'type'
    }, {
        title: formatMessage({ id: 'domainManager.example' }),
        dataIndex: 'example'
    }, {
        title: formatMessage({ id: 'domainManager.description' }),
        dataIndex: 'memo'
    }];

    const data = [{
        type: formatMessage({ id: 'domainManager.nameFormat.wildcard' }),
        example: '*',
        memo: formatMessage({ id: 'domainManager.nameFormat.wildcardDesc' })
    }, {
        type: formatMessage({ id: 'domainManager.nameFormat.suffixMatch' }),
        example: '*abc',
        memo: formatMessage({ id: 'domainManager.nameFormat.suffixMatchDesc' })
    }, {
        type: formatMessage({ id: 'domainManager.nameFormat.prefixMatch' }),
        example: 'abc*',
        memo: formatMessage({ id: 'domainManager.nameFormat.prefixMatchDesc' })
    }, {
        type: formatMessage({ id: 'domainManager.nameFormat.exactMatch' }),
        example: 'abc',
        memo: formatMessage({ id: 'domainManager.nameFormat.exactMatchDesc' })
    }, {
        type: formatMessage({ id: 'domainManager.nameFormat.wildcardSubdomain' }),
        example: '*.abc',
        memo: formatMessage({ id: 'domainManager.nameFormat.wildcardSubdomainDesc' })
    }, {
        type: formatMessage({ id: 'domainManager.nameFormat.suffixSubdomain' }),
        example: '*abc.d',
        memo: formatMessage({ id: 'domainManager.nameFormat.suffixSubdomainDesc' })
    }, {
        type: formatMessage({ id: 'domainManager.nameFormat.prefixSubdomain' }),
        example: 'abc*.d',
        memo: formatMessage({ id: 'domainManager.nameFormat.prefixSubdomainDesc' })
    }, {
        type: formatMessage({ id: 'domainManager.nameFormat.exactDomain' }),
        example: 'abc.d',
        memo: formatMessage({ id: 'domainManager.nameFormat.exactDomainDesc' })
    }];

    return <>

        <Paragraph className='mb10'>{formatMessage({ id: 'domainManager.nameFormatDescription' })}</Paragraph>
        <Table
            size='small'
            pagination={false}
            rowKey='type'
            columns={columns}
            dataSource={data}></Table>
    </>
}
export default Index