import React, { FC, useEffect, useState } from "react";
import { Typography, Button, Dropdown, List, Descriptions, Collapsible, Banner, Popconfirm } from '@douyinfe/semi-ui';
import { IconMore, IconChevronDown, IconChevronRight } from '@douyinfe/semi-icons';
import { Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';

import AddSearchdomain from "./add-searchdomain";
import EditSearchdomain from "./edit-searchdomain";
import styles from './index.module.scss';
import { useLocale } from '@/locales';

const { Title, Paragraph } = Typography;


interface Props {
    records: Array<Record>
    flynetId: bigint,
    onSave: (records: Array<Record>) => void
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    let initSearchDomains: Array<{
        domain: string,
        extraRecords: Array<Record>
    }> = [];

    const [addSearchdomainVisible, setAddSearchdomainVisible] = useState(false);
    const [editSearchdomainVisible, setEditSearchdomainVisible] = useState(false);
    const [editSearchdomainData, setEditSearchdomainData] = useState<{
        domain: string,
        extraRecords: {
            name: string,
            value: string,
            type: string,
            sys: boolean
        }[]
    }>({
        domain: '',
        extraRecords: []
    });

    props.records.forEach(record => {
        let find = false;
        initSearchDomains.forEach(searchDomain => {
            if (record.domain === searchDomain.domain) {
                searchDomain.extraRecords.push(record);
                find = true;
            }
        });
        if (!find) {
            initSearchDomains.push({
                domain: record.domain,
                extraRecords: [record]
            })
        }
    })

    const [searchDomains, setSearchDomains] = useState<{
        domain: string,
        extraRecords: Array<Record>
    }[]>(initSearchDomains);

    useEffect(() => {
        let searchDomains: {
            domain: string,
            extraRecords: Array<Record>
        }[] = [];
        props.records.forEach(record => {
            let find = false;
            searchDomains.forEach(searchDomain => {
                if (record.domain === searchDomain.domain) {
                    searchDomain.extraRecords.push(record);
                    find = true;
                }
            });
            if (!find) {
                searchDomains.push({
                    domain: record.domain,
                    extraRecords: [record]
                })
            }
        })
        setSearchDomains(searchDomains);
    }, [props.records])



    const handleDeleteSearchDomain = (domain: string) => {
        const records = props.records.filter(record => record.domain !== domain || record.sys);
        props.onSave(records);
    }


    const SearchdomainItem: React.FC<{
        // sortBar: JSX.Element,
        domain: string,
        extraRecords: Array<Record>,
        handleDeleteSearchDomain: (domain: string) => void
    }> = (props) => {
        const { domain, extraRecords, handleDeleteSearchDomain } = props;

        const [isCollapsible, setIsCollapsible] = useState(false);

        let hasSys = false;
        extraRecords.forEach((record) => {
            if (record.sys) {
                hasSys = true;
            }
        })
        return <List.Item key={domain} className={styles.ipListItem}>
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                <span style={{ display: 'flex', alignItems: 'center', flexGrow: 1, paddingRight: '20px' }}
                    className={styles.toggleCollapsible}
                ><span style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', color: 'var(--semi-color-text-1)' }} onClick={() => setIsCollapsible(!isCollapsible)}>{isCollapsible ? <IconChevronDown /> : <IconChevronRight />}&nbsp;{domain}</span></span>
                <Dropdown
                    trigger={'hover'}
                    position={'bottomLeft'}
                    render={
                        <Dropdown.Menu>
                            <Dropdown.Item
                                onClick={() => {
                                    setEditSearchdomainData({
                                        domain,
                                        extraRecords: extraRecords.map(record => {
                                            return {
                                                name: record.name,
                                                value: record.value,
                                                type: record.type,
                                                sys: record.sys
                                            }
                                        })
                                    })
                                    setEditSearchdomainVisible(true)
                                }}
                            > {formatMessage({ id: 'components.common.edit' })}</Dropdown.Item>
                            <Dropdown.Divider></Dropdown.Divider>

                            <Popconfirm
                                title={formatMessage({ id: 'resources.delete.confirmTitle' })}
                                content={formatMessage({ id: 'resources.delete.confirmContent' })}
                                onConfirm={() => handleDeleteSearchDomain(domain)}
                            ><Dropdown.Item disabled={hasSys}  type={hasSys ? "tertiary": 'danger'}>{formatMessage({ id: 'components.common.delete' })}</Dropdown.Item>
                            </Popconfirm>
                        </Dropdown.Menu>
                    }
                >
                    <IconMore />
                </Dropdown>
            </div>
            <Collapsible isOpen={isCollapsible}>
                <Descriptions className={styles.searchdomainDetail} size="small">
                    {extraRecords.map((record: Record, index: number) => {
                        return <Descriptions.Item key={index}
                        style={{
                            opacity: record.sys ? 0.3 : 1,
                        }} 
                            itemKey={`${record.name}`}>
                            {record.type === 'A' ? formatMessage({ id: 'domainManager.aRecord' }) : ''}
                            {record.type === 'CNAME' ? 'CNAME' : ''}
                             &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{record.value}
                        </Descriptions.Item>
                    })}
                </Descriptions>
            </Collapsible>
        </List.Item>
    }


    return <>
        <section className="mb40" style={{}}>
            <Title heading={4} className="mb2">{formatMessage({ id: 'domainManager.title' })}</Title>
            <Paragraph type='tertiary' className='mb10'>{formatMessage({ id: 'domainManager.description' })}</Paragraph>
            {searchDomains.length == 0 ? <Banner className={styles.banner} type="info" description={formatMessage({ id: 'domainManager.noDomainConfigured' })} closeIcon={null} ></Banner> :
                <List className={styles.ipList}>
                    {searchDomains.map((item, index) => {
                        return <SearchdomainItem
                            key={index}
                            domain={item.domain}
                            extraRecords={item.extraRecords}
                            handleDeleteSearchDomain={handleDeleteSearchDomain}
                        ></SearchdomainItem>
                    })}
                </List>
            }
            <Button onClick={() => setAddSearchdomainVisible(true)} theme="solid" >{formatMessage({ id: 'domainManager.addCustomDomain' })}</Button>

        </section>
        {addSearchdomainVisible ? <AddSearchdomain
            flynetId={props.flynetId}
            records={props.records}
            onSave={(records) => {
                props.onSave(records);
                setAddSearchdomainVisible(false);
            }}
            close={() => setAddSearchdomainVisible(false)} /> : null
        }

        {editSearchdomainVisible ? <EditSearchdomain
            flynetId={props.flynetId}
            records={props.records}
            onSave={(records) => {
                props.onSave(records);
                setEditSearchdomainVisible(false);
            }}
            close={() => setEditSearchdomainVisible(false)}
            data={editSearchdomainData}
        ></EditSearchdomain> : null}
    </>
}

export default Index;