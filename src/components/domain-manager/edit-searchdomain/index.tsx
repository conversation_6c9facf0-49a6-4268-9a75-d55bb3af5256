import { FC, useState } from 'react'
import { Typography, Modal, Popover, Tabs, TabPane, Form, Row, Col, ArrayField, Button } from '@douyinfe/semi-ui';
import { DNSConfig, Routes, Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import { flylayerClient } from '@/services/core';
import { IconPlusCircle, IconMinusCircle, IconHelpCircle } from '@douyinfe/semi-icons';
import styles from './index.module.scss';
import { DOMAIN_STATIC_IP } from '@/constants';
const { Paragraph, Title } = Typography;
import { validSearchDomainName } from '@/utils/validators';
import SearchdomainNameInfo from '@/components/domain-manager/searchdomain-name-info';
import { useLocale } from '@/locales';

interface Props {
    flynetId: bigint,
    close: () => void,
    onSave: (records: Array<Record>) => void
    records?: Array<Record>,
    data: {
        domain: string,
        extraRecords: {
            name: string,
            type: string,
            value: string,
            sys: boolean
        }[]
    }
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, SetFormApi] = useState<FormApi>()
    const [loading, setLoading] = useState(false);

    const handleOk = () => {
        if (!formApi) {
            return
        }
        formApi.validate().then((res) => {

            const values: {
                domain: string,
                extraRecords: {
                    name: string,
                    type: string,
                    value: string,
                    sys: boolean
                }[]
            } = formApi?.getValues();


            if (values.domain) {
                values.domain = values.domain.toLowerCase();
                values.domain = values.domain.trim();
            }
            setLoading(true)

            let records: Array<Record> = props.records ? [...props.records] : [];
            if (values.domain && values.extraRecords) {
                if (records) {


                    let newRecords = records.filter(val => val.domain && val.domain != values.domain && val.domain != props.data.domain);
                    values.extraRecords.forEach((record) => {

                        const name = record.name.trim().toLocaleLowerCase();
                        newRecords?.push(new Record({
                            domain: values.domain,
                            name: name,
                            type: record.type,
                            value: record.value,
                            sys: record.sys
                        }))
                    })
                    records = newRecords;
                } else {
                    records = [
                    ]

                    values.extraRecords.forEach((record) => {
                        const name = record.name.trim().toLocaleLowerCase();
                        records.push(new Record({
                            domain: values.domain,
                            name: name,
                            type: record.type,
                            value: record.value
                        }))
                    })
                }
            }


            if(records.length > 0) {
                props.onSave(records);
            }
        }).catch((err) => {
            console.error(err);
        });



    }


    const validateIP = (value: string, values: any) => {

        if (!value) {
            return formatMessage({ id: 'domainManager.validation.ipRequired' });
        }
        if (value == DOMAIN_STATIC_IP) {
            return formatMessage({ id: 'domainManager.validation.cannotUseDefaultIP' });
        }
        const reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'domainManager.validation.invalidIP' });
        }
        return "";
    }


    const validateDomain = (value: string) => {
        if (!value) {
            return formatMessage({ id: 'domainManager.validation.domainRequired' });
        }
        const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
        if (!reg.test(value)) {
            return formatMessage({ id: 'domainManager.validation.invalidDomainFormat' });
        }

        if (props.data.domain != value) {
            let findDomain = false;
            props.records?.forEach(val => {
                if (val.domain === value) {
                    findDomain = true
                }
            })
            if (findDomain) {
                return formatMessage({ id: 'domainManager.validation.domainExists' });
            }
        }
        return "";
    }

    return <>
        <Modal
            width={760}
            title={formatMessage({ id: 'domainManager.editCustomDomain' })}
            visible={true}
            okButtonProps={{ loading: loading }}
            onOk={handleOk}
            onCancel={() => props.close()}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Form
                initValues={props.data}
                getFormApi={SetFormApi}
            >
                <> <Title heading={6}>{formatMessage({ id: 'domainManager.domain' })}</Title>

                    <Form.Input field='domain' validate={validateDomain} placeholder={formatMessage({ id: 'domainManager.enterDomain' })} noLabel className='mb0' ></Form.Input>
                    <ArrayField field='extraRecords'>
                        {
                            ({ add, arrayFields, addWithInitValue }) => {
                                return <>
                                    <Row className='tableTitle' gutter={16}>
                                        <Col span={6}>{formatMessage({ id: 'domainManager.name' })}  <Popover content={<div className='p10' style={{ width: 400 }}>
                                            <SearchdomainNameInfo></SearchdomainNameInfo>
                                        </div>}><IconHelpCircle style={{ fontSize: 14, verticalAlign: 'text-top' }} /></Popover></Col>
                                        <Col span={4}>{formatMessage({ id: 'domainManager.type' })}</Col>
                                        <Col span={11}>{formatMessage({ id: 'domainManager.value' })}</Col>
                                        <Col span={3} className='btn-right-col'><Button icon={<IconPlusCircle />} onClick={() => { addWithInitValue({ name: '', type: 'A', value: '' }); }} style={{}}>{formatMessage({ id: 'components.common.add' })}</Button></Col>
                                    </Row>

                                    {arrayFields.map(({ field, key, remove }, i) => {
                                        return <Row key={key} gutter={16} className={styles.tableRow}>
                                            <Col span={6}> <Form.Input
                                                field={`${field}[name]`}
                                                noLabel
                                                placeholder={formatMessage({ id: 'domainManager.name' })}
                                                validate={validSearchDomainName}
                                                style={{ width: '100%' }}
                                                disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                            >
                                            </Form.Input></Col>
                                            <Col span={4}><Form.Select
                                                field={`${field}[type]`}
                                                noLabel
                                                style={{ width: '100%' }}
                                                optionList={[
                                                    { label: formatMessage({ id: 'domainManager.aRecord' }), value: 'A' },
                                                    { label: 'CNAME', value: 'CNAME' },
                                                ]}
                                                disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                            >
                                            </Form.Select></Col>
                                            <Col span={11}>{formApi?.getValue('extraRecords')?.[i]?.type == "A" && <Form.Input
                                                field={`${field}[value]`}
                                                noLabel
                                                validate={validateIP}
                                                placeholder={formatMessage({ id: 'domainManager.ipv4Address' })}
                                                style={{ width: '100%' }}
                                                disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                            />}
                                                {formApi?.getValue('extraRecords')?.[i]?.type == "CNAME" && <Form.Input
                                                    field={`${field}[value]`}
                                                    noLabel
                                                    validate={(value: string, values: any) => {
                                                        let vals: {
                                                            domain: string,
                                                            extraRecords: {
                                                                name: string,
                                                                type: string,
                                                                value: string
                                                            }[]
                                                        } = values;
                                                        if (!value) {
                                                            return formatMessage({ id: 'domainManager.validation.cnameRequired' });
                                                        }

                                                        const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
                                                        if (!reg.test(value)) {
                                                            return formatMessage({ id: 'domainManager.validation.cnameFormat' });
                                                        }

                                                        let findSame = false;
                                                        const thisItem = vals.extraRecords[i];

                                                        vals.extraRecords.forEach((val, index) => {
                                                            if (index == i) {
                                                                return;
                                                            }
                                                            if (val.type == thisItem.type && val.value == value && val.name == thisItem.name) {
                                                                findSame = true;
                                                            }
                                                        })

                                                        if (findSame) {
                                                            return formatMessage({ id: 'domainManager.validation.recordExists' });
                                                        }


                                                        let refList: { name: string, type: string, value: string }[] = [];
                                                        var getAllRefrence = (
                                                            domain: string,
                                                            thisItem: { name: string, type: string, value: string },
                                                            thisIndex: number,
                                                            extraRecords: { name: string, type: string, value: string }[], list: { name: string, type: string, value: string }[]) => {

                                                            extraRecords.forEach((val, index) => {
                                                                if (index == thisIndex) {
                                                                    return;
                                                                }
                                                                if (val.type == "CNAME" && thisItem.value == val.name + "." + domain) {
                                                                    list.push(val);
                                                                    if (list.length >= extraRecords.length) {
                                                                        return;
                                                                    }
                                                                    getAllRefrence(domain, val, index, extraRecords, list);
                                                                }
                                                            })

                                                        }

                                                        getAllRefrence(vals.domain, thisItem, i, vals.extraRecords, refList);


                                                        if (refList.length >= vals.extraRecords.length) {
                                                            return formatMessage({ id: 'domainManager.validation.circularReference' });
                                                        }

                                                        return "";
                                                    }}
                                                    placeholder={formatMessage({ id: 'domainManager.cnameTarget' })}
                                                    style={{ width: '100%' }}
                                                    disabled={formApi?.getValue('extraRecords')?.[i]?.sys}
                                                />}</Col>
                                            <Col span={3} className='btn-right-col'>
                                                <Button
                                                    type='danger'
                                                    theme='borderless'
                                                    icon={<IconMinusCircle />}
                                                    onClick={remove}
                                                    style={{ margin: 12 }}
                                                    disabled={arrayFields.length<=1 || formApi?.getValue('extraRecords')?.[i]?.sys}
                                                />
                                            </Col>

                                        </Row>
                                    }

                                    )}
                                </>
                            }
                        }
                    </ArrayField>



                </>
            </Form>
        </Modal>
    </>
}

export default Index;