import { useEffect, useState, useContext } from 'react';
import { Typography, Avatar, Tag, Notification } from '@douyinfe/semi-ui';

import DOMPurify from 'dompurify';
import { User, UserRole } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { flylayerClient } from '@/services/core';

import avatarDefault from '@/assets/avatar_default.jpg';

import { useNavigate } from 'react-router-dom';
import styles from './index.module.scss';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { UserProfileContext } from '@/hooks/useUserProfile';
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
const { Title, Paragraph, Text } = Typography;

import Handlebars from 'handlebars';
import { parseUser } from '@/utils/user';
export type UserFilter = {
    keywords: string,
    roles: Array<string>,
    status: 'enable' | 'disable' | ''
}


const useTable = (initFilter: UserFilter, multi: boolean, initSelectedValue?: User | User[]) => {

    const globalConfig = useContext(GlobalConfigContext);


    const templateUserListTitle = Handlebars.compile(globalConfig.template?.userListTitle);
    const flynet = useContext(FlynetGeneralContext);
    const userProfile = useContext(UserProfileContext);
    const navigate = useNavigate();

    // 用户是否正在加载中
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<Array<User>>([]);
    const [allData, setAllData] = useState<Array<User>>([]);

    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');

    // 当前页码
    const [page, setPage] = useState(1);
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    const pageSize = 5;

    // 编辑用户组弹出框是否可见
    const [addGroupVisible, setAddGroupVisible] = useState(false);

    // 当前菜单选中设备
    // const [curUser, setCurUser] = useState<User>();
    // 当前菜单选中用户
    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filter, setFilter] = useState<UserFilter>(initFilter)
    const columns = [
        {
            title: '用户',
            dataIndex: 'name',
            // sorter: true,
            // sorter: (a?: User, b?: User) => (a && b && a.displayName > b.displayName ? 1 : -1),
            render: (_: string, record: User) => {
                const parsedUser = parseUser(record);
                const val = templateUserListTitle({ user: parsedUser });

                return (

                    <div className={styles.name}><Avatar
                        src={record.avatarUrl ? record.avatarUrl : avatarDefault}
                        style={{ marginRight: 12 }}
                    ></Avatar>
                        <div>
                            <Title heading={6}>
                                <span dangerouslySetInnerHTML={{
                                    __html: DOMPurify.sanitize(val, { USE_PROFILES: { html: true } })
                                }} /></Title>
                            <Paragraph className='mb2'>
                                {record.loginName}
                            </Paragraph>
                            <div>{record.disabled ? <Tag size="small"> 禁用 </Tag> : ''}</div>
                        </div>
                    </div>
                );
            },
        },
        {
            width: 150,
            title: '角色',
            dataIndex: 'role',
            // sorter: true,
            // sorter: (a?: User, b?: User) => (a && b && a.role > b.role ? 1 : -1),
            render: (field: string, record: User) => {

                let roleName = ''
                switch (record.role) {
                    case UserRole.FLYNET_ADMIN: roleName = '管理员'; break;
                    case UserRole.FLYNET_USER: roleName = "普通用户"; break;
                    case UserRole.SUPER_ADMIN: roleName = "企业管理员"; break;
                    default: roleName = "未知"; break;
                }

                return roleName
            }
        },
       
    ];

    const handleSort = (param: any) => {
        
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;

        let sortOrder = '';
        if (sorter.sortOrder == 'ascend') {
            sortOrder = 'ASC';
        } else if (sorter.sortOrder == 'descend') {
            sortOrder = 'DESC';
        }
        queryUser({
            page: 1,
            sortOrder: sortOrder,
            sortField: dataIndex
        })


    }
    

    const query = () => {
        queryUser();
    }

    const doPage = (p:number) => {
        queryUser({
            page: p
        })
    }
    let initSelectedRowKeys: string[] = [];
    if(initSelectedValue) {
        if(initSelectedValue instanceof User) {
            initSelectedRowKeys?.push(initSelectedValue.id + '');
        } else {
            initSelectedValue.forEach(user => {
                initSelectedRowKeys?.push(user.id + '');
            })
        }
    }

    const [selectedRowKeys, setSelectedRowKeys] = useState<String[]>(initSelectedRowKeys);
    const onRowSelect = (record: User, selected: boolean, selectedRows: Array<User>, e: MouseEvent)  => {
        if (multi) {
            let newSelectedRowKeys: string[] = selectedRows.map(record => record.id + '') 
            setSelectedRowKeys(newSelectedRowKeys);

            let newSelectedUsers: User[] = [];
            for (let i = 0; i < allData.length; i++) {
                const s = allData[i];

                if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                    newSelectedUsers.push(s);
                }
            }
            setSelectedUsers(newSelectedUsers);
        } else {
            let selectKeys: any = [record.id + ''];
            setSelectedRowKeys(selectKeys);
            setSelectedUsers([record])
        }
    }

    const onRow = (record: User, index: number) => {
        return {
            onClick: () => {
                if (multi) {
                    let newSelectedRowKeys: any = selectedRowKeys;
                    if (newSelectedRowKeys.indexOf(record.id + '') >= 0) {
                        newSelectedRowKeys.splice(newSelectedRowKeys.indexOf(record.id + ''), 1);
                    } else {
                        newSelectedRowKeys.push(record.id + '');
                    }
                    setSelectedRowKeys(newSelectedRowKeys);

                    let newSelectedUsers: User[] = [];
                    for (let i = 0; i < allData.length; i++) {
                        const s = allData[i];
                        if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                            newSelectedUsers.push(s);
                        }
                    }
                    setSelectedUsers(newSelectedUsers);
                } else {
                    
                    let selectKeys: any = [record.id + ''];
                    setSelectedRowKeys(selectKeys);
                    // setCurUser(record);
                    setSelectedUsers([record]);
                }
            }, // 点击行

        };
    }


    const queryUser = (params?: {
        filter?: UserFilter,
        sortOrder?: string,
        sortField?: string,
        page?: number,
        pageSize?: number,
    }) => {
        let queryArray = [];

        let limit = pageSize;
        let curPage = page;

        let filterRoles = filter.roles || [];
        let filterStatus = filter.status;
        
        let filterKeywords = filter.keywords;
        let filterSortOrder = sortOrder;
        let filterSortField = sortField;

        if (params) {
            if (params.filter) {
                setFilter(params.filter);

                filterRoles = params.filter.roles;
                filterStatus = params.filter.status;
                filterKeywords = params.filter.keywords;
            }

            if (params.sortOrder && params.sortField) {
                setSortOrder(params.sortOrder as any);
                setSortField(params.sortField);

                filterSortOrder = params.sortOrder as any;
                filterSortField = params.sortField;
            } else {
                filterSortOrder = undefined;
                filterSortField = '';
                setSortOrder(undefined);
                setSortField('');
            }

            if (params.page) {
                curPage = params.page;
                setPage(params.page);
            }

            if (params.pageSize) {
                limit = params.pageSize;
            }
        }


        if (filterRoles && filterRoles.length > 0) {
            queryArray.push(`roles=${filterRoles.join(',')}`);
        }


        if (filterStatus == 'enable') {
            queryArray.push(`disabled=false`);
        } else if (filterStatus == 'disable') {
            queryArray.push('disabled=true');
        }


        if (filterKeywords != "" && filterKeywords.trim() != "") {
            queryArray.push(`keywords=${encodeURIComponent(filterKeywords)}`);
        }


        if (filterSortOrder && filterSortField) {
            setSortOrder(filterSortOrder as any);
            setSortField(filterSortField);

            let order_by = encodeURIComponent(`${filterSortField} ${filterSortOrder}`);
            queryArray.push(`order_by=${order_by}`);
        }

        const offset = (curPage - 1) * limit;

        queryArray.push(`limit=${limit}`);
        queryArray.push(`offset=${offset}`);
        queryArray.push(`disabled=false`);

        setLoading(true);
        flylayerClient.listUsers({
            flynetId: flynet.id,
            query: queryArray.join('&')
        }).then(res => {
            setData(res.users);

            let newAllData = [...allData];
            res.users.forEach((user) => {
                let found = false;
                for (let i = 0; i < allData.length; i++) {
                    if (allData[i].id == user.id) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    newAllData.push(user);
                }
            });
            setAllData(newAllData);
            

            setTotal(Number(res.total))
        }).catch(err => {
            Notification.error({ content: '获取用户列表失败, 请稍后重试' })
            console.error(err)
        }).finally(() => {
            setLoading(false);
        })

    }

    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setReloadFlag(false)
        if (reloadFlag) {
            queryUser({
                page: 1,
            })
        }
    }, [reloadFlag])
    useEffect(() => {
        queryUser({
            page: 1,
            filter: filter
        })

    }, [filter])

    return { columns, loading, allData, data, reloadFlag, setReloadFlag, 
        filter, setFilter, 
        page, pageSize, doPage, setPage, total, 
        handleSort, addGroupVisible, 
        setAddGroupVisible, selectedRowKeys, 
        onRowSelect, onRow, selectedUsers, setSelectedUsers }
}

export default useTable;