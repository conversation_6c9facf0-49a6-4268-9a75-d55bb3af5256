import { FC, useState, useEffect } from 'react'
import { Table, Layout, Input, Space, Select, Tag, Row, Col, Button, Modal } from '@douyinfe/semi-ui';
import { IconSearch } from '@douyinfe/semi-icons';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";

import styles from './index.module.scss';
import useTable, { UserFilter } from './useTable';
import TableEmpty from '@/components/table-empty';
import { useLocale } from '@/locales';
const { Sider, Content } = Layout;

interface Props {
    multi: boolean;
    value?: User | User[];
    onChange: (value: User | User[]) => void;
    close: () => void,
}
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    // 过滤参数
    const initFilter: UserFilter = {
        keywords: '',
        status: '',
        roles: []
    }
    // 过滤参数改变时跳转路由


    // 查询参数
    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        // 查询参数从有值变化为无值时，重新加载数据
        if (location.search == '' && search != '') {
            setFilter(initFilter);
        }
        setSearch(location.search);
    }, [location])


    const { columns, loading, data, allData, setReloadFlag, filter, setFilter, page, setPage, pageSize, doPage, total, handleSort, selectedRowKeys, onRowSelect, onRow, selectedUsers, setSelectedUsers } = useTable(initFilter, props.multi, props.value);


    const listStatus = [
        { value: '', label: formatMessage({ id: 'components.common.all' }) },
        { value: 'enable', label: formatMessage({ id: 'policies.action.enabled' }) },
        { value: 'disable', label: formatMessage({ id: 'policies.action.disabled' }) }
    ];
    const listRole = [
        { value: '8', label: formatMessage({ id: 'userModal.normalUser' }) },
        { value: '3', label: formatMessage({ id: 'userModal.admin' }) }
    ];

    const handleStatusChange = (value: any) => {
        setFilter({ ...filter, status: value })

    }
    const handleRoleChange = (value: any) => {
        setFilter({ ...filter, roles: value })

    }

    const handleQueryChange = (value: string) => {
        setFilter({ ...filter, keywords: value })

    }
    return <>
        <Modal
            width={830}
            title={formatMessage({ id: 'userModal.selectUser' })}
            visible={true}
            onOk={() => { }}
            onCancel={props.close}
            footer={null}
            className='semi-modal'
        >
            <Layout className='mb20 search-bar'>

                <Layout>
                    <Content className='pr10'>
                        <Input value={filter.keywords}
                            onChange={handleQueryChange} style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={formatMessage({ id: 'userModal.searchPlaceholder' })}></Input>
                    </Content>
                    {/* <Sider>
                        <Space>
                                    <Select value={filter.roles} onChange={handleRoleChange} multiple maxTagCount={1} style={{ width: 220 }} optionList={listRole} insetLabel={formatMessage({ id: 'userModal.role' })}></Select>
                        </Space></Sider> */}
                </Layout>

            </Layout>
            <div style={{ height: 20 }} className='mb10'> {!loading && <Tag> {formatMessage({ id: 'userModal.totalUsers' })} {total}</Tag>}  </div>

            <div style={{ height: 500 }}>
                <Table
                    className={props.multi ? 'checkboxTable' : 'radioTable'}
                    rowSelection={
                        {
                            selectedRowKeys: selectedRowKeys as any,
                            onSelect: onRowSelect as any,
                            disabled: true
                        }
                    }
                    onRow={onRow as any}
                    rowKey={(record?: User) => record ? record.id + '' : ''}
                    onChange={handleSort}
                    empty={<TableEmpty loading={loading} />} columns={columns} loading={loading} dataSource={data} pagination={ total  > pageSize ? {
                        currentPage: page,
                        pageSize: pageSize,
                        total: total,
                        onChange: (page:number) => {
                            doPage(page);
                        }
                    }: false} />
            </div>
            <Row className='mb10'>
                <Col span={20}>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                    <Space>
                        <Button onClick={() => { props.close() }}>{formatMessage({ id: 'components.common.cancel' })}</Button>
                        <Button theme='solid' disabled={!selectedUsers || selectedUsers.length == 0} onClick={() => {
                            if (selectedUsers && selectedUsers.length > 0) {
                                if(props.multi) {
                                    props.onChange(selectedUsers);
                                } else {
                                    props.onChange(selectedUsers[0]);
                                }
                            }

                        }}>{formatMessage({ id: 'components.common.confirm' })}</Button>
                    </Space>
                </div></Col>
            </Row>
        </Modal>


    </>
}

export default Index;
