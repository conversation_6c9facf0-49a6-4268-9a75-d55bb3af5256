import React, { useState } from 'react'
import ExpressionItem from './expression-item';
import { Space, Button, Row, Col } from '@douyinfe/semi-ui';
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';

interface Props {
    value: string[],
    expressionsTemplate: string,
    onChange: ((val: string[]) => void),
    onerror: () => void,
}

const Index: React.FC<Props> = (props) => {
    let initList = props.value;

    // 表达式列表
    const [expressions, setExpressions] = useState(initList);

    const handleValueChange = (list: Array<string>) => {
        props.onChange(list);
    }

    return <><div>
        <div>
            {expressions.map((exp, index) => {
                return <Row className='mb10' key={index} gutter={4}>
                    <Col span={22}>
                        <ExpressionItem
                            expressionsTemplate={props.expressionsTemplate}
                            value={exp} onChange={(expStr) => {
                                let newList = expressions.map((e, i) => {
                                    if (i == index) {
                                        return expStr;
                                    } else {
                                        return e;
                                    }
                                })
                                setExpressions(newList);
                                handleValueChange(newList);
                            }}
                            onError={() => {
                                props.onerror();
                            }}
                        ></ExpressionItem>
                    </Col>
                    <Col span={2}>
                        <Button onClick={() => {
                            let newExpressions = expressions.filter((item, i) => i != index);
                            setExpressions(newExpressions);
                            handleValueChange(newExpressions);
                            if (newExpressions.length == 0) {
                                props.onerror();
                            }
                        }} icon={<IconMinusCircle />} />
                    </Col>


                </Row>
            })}
        </div>
        <Button onClick={() => {
            let newExpressions = [...expressions, ''];
            setExpressions(newExpressions);
            props.onerror();
        }} icon={<IconPlusCircle />} />
    </div>
    </>
}

export default Index;