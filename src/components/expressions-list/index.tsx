import React, { useState, useContext, useEffect } from 'react'
import { Typography, Modal, Tag, Row, Col, Notification, Divider, Input, Button, Popover, Space, Select, TagInput } from "@douyinfe/semi-ui";
import { IconPlus, IconMinusCircle, IconArrowDown, IconArrowUp, IconMore, IconPlusCircle, IconEyeOpened, IconAlignTop, IconAlignBottom } from '@douyinfe/semi-icons';
import Expressions from './expressions';
import styles from './index.module.scss'

const Index: React.FC<{
    expressions: Array<string[]>,
    onChange: ((expressions: Array<string[]>) => void),
    onerror: () => void
}> = (props) => {


    const [expressions, setExpressions] = useState<Array<string[]>>(props.expressions);
    // 无效的行
    const [invalidLines, setInvalidLines] = useState<number[]>([]);
    // 无效的表达式
    const [errorExpressionIndexs, setErrorExpressionIndexs] = useState<number[]>([]);

    const [expressionsTemplate, setExpressionsTemplate] = useState<string>(`
    {
        "$id": "https://example.com/person.schema.json",
        "$schema": "https://json-schema.org/draft/2020-12/schema",
        "type": "object",
        "title": "Expressions",
        "description": "表达式",
        "properties": {
            "time": {
                "type": "object",
                "title": "时间",
                "description": "用于时间范围",
                "format": "date-time"
            },
            "input": {
                "type": "object",
                "title": "输入",
                "description": "输入",
                "properties": {
                    "Request": {
                        "type": "object",
                        "title": "请求",
                        "description": "请求描述",
                        "properties": {
                            "Host": {
                                "type": "string",
                                "title": "站点",
                                "description": "站点"
                            }
                        }
                    },
                    "Device": {
                        "type": "object",
                        "title": "设备",
                        "description": "设备",
                        "properties": {
                            "Name": {
                                "type": "string",
                                "title": "设备名",
                                "description": "设备名"
                            },
                            "User": {
                                "type": "string",
                                "title": "用户",
                                "description": "用户",
                                "properties": {
                                    "IsAdmin": {
                                        "type": "boolean",
                                        "title": "是否管理员",
                                        "description":""
                                    },
                                    "Name": {
                                        "type": "string",
                                        "title": "用户名",
                                        "description": "用户名"
                                    },
                                    "LoginName": {
                                        "type": "string",
                                        "title": "登录名",
                                        "description": "登录名"
                                    },
                                    "Account": {
                                        "type": "string",
                                        "title": "账号",
                                        "description": "账号",
                                        "properties": {
                                            "Attrs": {
                                                "type": "object",
                                                "title": "属性",
                                                "description": "属性",
                                                "properties": {
                                                    "departName": {
                                                        "type": "string",
                                                        "title": "部门",
                                                        "description": "部门"
                                                    }
                                                }
                                            },
                                            "LoginCount": {
                                                "type": "number",
                                                "title": "登录次数",
                                                "description": "用户的总登录次数"
                                            },
                                            "LoginName": {
                                                "type": "string",
                                                "title": "登录名",
                                                "description": "登录名"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }    
    `);
    return <>
        <Row className={styles.tableTitle} >
            <Col span={22}>
                <Row>
                    <Col span={22}>
                        <Row>
                            <Col span={8}>属性</Col>
                            <Col span={8}>操作</Col>
                            <Col span={8}>值</Col>
                        </Row>
                    </Col>
                    <Col>
                    </Col>

                </Row>
            </Col>
            <Col span={2} className='btn-right-col'><Button onClick={() => {
                setInvalidLines([...invalidLines, expressions.length])
                setExpressions([...expressions, ['']])
            }} icon={<IconPlus />} /></Col>
        </Row>
        {expressions.map((value, index) => {
            return <Row className={invalidLines.indexOf(index) >= 0 ? styles.tableBodyError : styles.tableBody} key={index} >
                <Col xs={24} sm={22}>
                    {expressionsTemplate &&
                        <Expressions
                            value={value}
                            expressionsTemplate={expressionsTemplate}
                            onChange={(exp_val) => {
                                const newGroups = expressions.map((val, i) => {
                                    if (i != index) {
                                        return val;
                                    } else return exp_val
                                });
                                if (exp_val) {
                                    setInvalidLines(invalidLines.filter((i) => i != index))
                                }
                                setExpressions(newGroups)
                                props.onChange(newGroups)
                                if(invalidLines.length > 0) {
                                    props.onerror();
                                }
                            }}
                            onerror={() => {
                                if (!invalidLines.includes(index)) {
                                    setInvalidLines([...invalidLines, index])
                                }
                                props.onerror();
                            }}
                        ></Expressions>}
                </Col>
                <Col xs={24} sm={2} className='btn-right-col'>
                    {/* 弹出排序操作 */}
                    <Popover
                        position='left'
                        style={{
                            padding: 5,
                        }}
                        content={<>
                            <Space key={'index'}>
                                <Button disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconAlignTop />}
                                    onClick={() => {
                                        let newGroups = expressions.map((val, i) => {
                                            return val;
                                        });
                                        let item = newGroups[index];
                                        newGroups[index] = newGroups[0];
                                        newGroups[0] = item;
                                        setExpressions(newGroups)
                                    }}
                                ></Button>
                                <Button
                                    onClick={() => {
                                        const newGroups = expressions.map((val, i) => {
                                            if (i == index) {
                                                return expressions[index - 1];
                                            } else if (i == index - 1) {
                                                return expressions[index]
                                            } else {
                                                return val
                                            }
                                        });
                                        setExpressions(newGroups)
                                    }} disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconArrowUp />}></Button>
                                <Button
                                    onClick={() => {
                                        const newGroups = expressions.map((val, i) => {
                                            if (i == index) {
                                                return expressions[index + 1];
                                            } else if (i == index + 1) {
                                                return expressions[index]
                                            } else {
                                                return val
                                            }
                                        });
                                        setExpressions(newGroups)
                                    }}
                                    disabled={invalidLines.indexOf(index) >= 0 || index == expressions.length - 1} icon={<IconArrowDown />}></Button>
                                <Button disabled={invalidLines.indexOf(index) >= 0 || index == expressions.length - 1} icon={<IconAlignBottom />} onClick={() => {
                                    let newGroups = expressions.map((val, i) => {
                                        return val;
                                    });
                                    let item = newGroups[index];
                                    newGroups[index] = newGroups[newGroups.length - 1];
                                    newGroups[newGroups.length - 1] = item;
                                    setExpressions(newGroups)
                                }}></Button>
                                <Button
                                    type='danger'
                                    theme='borderless'
                                    icon={<IconMinusCircle />}
                                    onClick={() => {
                                        const newGroups = expressions.filter((g, i) => i != index)
                                        setExpressions(newGroups)

                                        setInvalidLines(invalidLines.filter((i) => i != index))
                                        setErrorExpressionIndexs(errorExpressionIndexs.filter((i) => i != index))

                                    }}

                                />
                            </Space>
                        </>}><Button icon={<IconMore />}></Button></Popover>

                </Col>
            </Row>
        })}
    </>
}

export default Index;