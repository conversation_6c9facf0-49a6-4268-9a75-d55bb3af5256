import { FC, useEffect, useState, useContext } from 'react'
import { withField, Form, Row, Col, Button, Input } from '@douyinfe/semi-ui';
import { IconPlusCircle, IconMinusCircle } from "@douyinfe/semi-icons";

import styles from './index.module.scss'

interface KeyValue {
    key: string;
    value: string;
}

// [{"key":"xxx","value":"xxx1"}]
const KeyValuesInput: FC<{ value: string, onChange?: (value: string) => void }> = (props) => {
    const { value, onChange } = props;
 
    const [keyValues, setKeyValues] = useState<KeyValue[]>(JSON.parse(value || '[]'));
    
    return <div className={styles.keyValuesInput}>
        <Row gutter={20} className='tableTitle' >
            <Col span={10}>键</Col>
            <Col span={10}>值</Col>
            <Col span={4} className='btn-right-col'>
                <Button onClick={() => {
                    setKeyValues([...keyValues, { key: '', value: '' }]);
                }} icon={<IconPlusCircle/>}></Button>
            </Col>
        </Row>
        {keyValues.map((keyValue, index) => {
            return <Row key={index} gutter={20} className='tableTitle'>
                <Col span={10}>
                    <Input value={keyValue.key} onChange={(e) => {
                        keyValue.key = e;
                        setKeyValues([...keyValues]);
                        if(onChange) {onChange(JSON.stringify(keyValues));}
                        
                    }} />
                </Col>
                <Col span={10}>
                    <Input value={keyValue.value} onChange={(e) => {
                        keyValue.value = e;
                        setKeyValues([...keyValues]);
                        if(onChange) {onChange(JSON.stringify(keyValues));}
                    }} />
                </Col>
                <Col span={4} className='btn-right-col'>
                    <Button type='danger' onClick={() => {
                        keyValues.splice(index, 1);
                        setKeyValues([...keyValues]);
                        if(onChange) {onChange(JSON.stringify(keyValues));}
                    }} icon={<IconMinusCircle/>}></Button>
                </Col>
            </Row>
        })}
    </div>
}

const FormKeyValuesInput = withField(KeyValuesInput, { valueKey: 'value', onKeyChangeFnName: 'onChange' });

export default FormKeyValuesInput;
export { KeyValuesInput };