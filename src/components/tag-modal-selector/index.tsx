import React, { FC, useCallback, useContext, useEffect, useState } from 'react'
import { flylayerClient } from '@/services/core';

import { RadioGroup, Radio, Layout, Input, Select, Typography, Spin, Space, Button, Row, Col, Table, BackTop, Tag, Modal } from '@douyinfe/semi-ui';

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

interface Props {
    multi: boolean;
    value?: string | string[];
    onChange: (value: string | string[]) => void;
    close: () => void,

}

const Index: FC<Props> = (props) => {

    const flynet = useContext(FlynetGeneralContext);

    const [tag, setTag] = useState('');
    const [tags, setTags] = useState<Array<string>>([]);

    // 获取访问控制策略
    const getACLPolicy = useCallback(() => {

        flylayerClient.getACLPolicy({
            flynetId: flynet.id
        }).then(res => {
            let tags: Array<string> = []
            if (res.policy) {
                tags = Object.keys(res.policy.tagowners)
                setTags(tags);
            }

        }).catch(err => {
            console.error(err);

        })
    }, [])

    useEffect(() => {
        getACLPolicy()
    }, [])

    return <>
        <Modal
            width={400}
            title="选择标签"
            visible={true}
            onOk={() => { }}
            onCancel={props.close}
            footer={null}
            className='semi-modal'
        >

            <RadioGroup onChange=
                {e => {
                    console.log(e)
                    setTag(e.target.value)
                }} className='mb20'>
                {tags.map(t => {
                    return <Radio value={t}>{t}</Radio>
                })}
            </RadioGroup>
            <Row className='mb10'>
                <Col span={20}>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                    <Space>
                        <Button onClick={() => { props.close() }}>取消</Button>
                        <Button theme='solid' disabled={!tag} onClick={() => {
                            if (tag) {
                                props.onChange(tag)
                            }

                        }}>确定</Button>
                    </Space>
                </div></Col>
            </Row>
        </Modal>
    </>
}

export default Index;