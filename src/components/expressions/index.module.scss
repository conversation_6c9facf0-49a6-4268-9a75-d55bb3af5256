
.tableTitle {
    
    // border-bottom: 1px solid var(--semi-color-border);
    // margin-bottom: 8px;
    // color: var(--semi-color-text-2);
    // font-weight: 600;
    // font-size: 14px;
    >div {
        padding-right: 8px;
        padding-bottom: 8px;
        line-height: 32px;
    }
}
.tableBody {
    padding-top: 5px;
    padding-bottom: 5px;
    >div {
        padding-bottom: 8px;
    }
}

.tableBodyError {
    background-color: var(--semi-color-danger-light-default);
    border: 1px solid var(--semi-color-danger-light-default);
    
    padding-top: 5px;
    padding-bottom: 5px;
    >div {

        padding-bottom: 8px;
    }
}


.rightColumn {
    display: flex!important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0!important;
}
