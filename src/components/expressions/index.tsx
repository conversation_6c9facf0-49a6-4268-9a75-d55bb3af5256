import React, { useState } from 'react'
import { Typography, Tag, Row, Col, Button, Popover, Space } from "@douyinfe/semi-ui";
import { IconPlus, IconMinusCircle, IconArrowDown, IconArrowUp, IconMore, IconAlignTop, IconAlignBottom, IconArrowUpRight } from '@douyinfe/semi-icons';
import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

import ExpressionItem from './expression-item';
import TableEmpty from '@/components/table-empty';

const { Text } = Typography;
import styles from './index.module.scss';
import { BASE_PATH } from '@/constants/router';
import { AttributeTemplate } from '@/interface/attribute-template';


const Index: React.FC<{
    expressions: Array<Expression>,
    onChange: (expressions: Array<Expression>) => void,
    onError: () => void,
    attributeTemplate: AttributeTemplate,
}> = (props) => {
    const [expressions, setExpressions] = useState<Array<Expression>>(props.expressions)

    // 无效的行
    const [invalidLines, setInvalidLines] = useState<number[]>([]);
    // 无效的表达式
    const [errorExpressionIndexs, setErrorExpressionIndexs] = useState<number[]>([]);

    return <>
        <Row className='mb10' >
            <Col span={20}><Text type='tertiary'>触发参数</Text>
                <a className='link-external' target='_blank' title='触发参数属性设置' href={`${BASE_PATH}/settings/schema`} onClick={(e) => { e.stopPropagation() }}>
                    <IconArrowUpRight />
                </a></Col>
            <Col span={4} className={styles.rightColumn}>
                <Button type='primary' onClick={() => {
                    setInvalidLines([...invalidLines, expressions.length]);
                    setExpressions(expressions.concat([
                        new Expression({
                            name: '',
                            op: '',
                            value: ''
                        })
                    ]))
                    props.onError();
                }} icon={<IconPlus />}></Button>
            </Col>
        </Row>
        {!expressions || expressions.length == 0 ? <TableEmpty loading={false}></TableEmpty> : expressions.map((value, index) => {
            return <Row className={invalidLines.indexOf(index) >= 0 ? styles.tableBodyError : styles.tableBody} key={index} >
                <Col xs={24} sm={2}>
                    <Tag style={{ height: 32, width: 32, textAlign: 'center' }} color={index % 2 == 0 ? 'grey' : 'white'} >{index + 1}</Tag>
                </Col>
                <Col xs={24} sm={20}>
                    <ExpressionItem
                        attributeTemplate={props.attributeTemplate}
                        value={value} onChange={(expStr) => {
                            let newList = expressions.map((e, i) => {
                                if (i == index) {
                                    return expStr;
                                } else {
                                    return e;
                                }
                            })
                            let newInvalidLines = [];
                            if (expStr) {
                                newInvalidLines = invalidLines.filter((i) => i != index)
                                setInvalidLines(newInvalidLines);
                            } else {
                                newInvalidLines = invalidLines
                            }

                            if (newInvalidLines.length > 0) {
                                props.onError();
                            } else {
                                props.onChange(newList);
                            }

                            setExpressions(newList);
                        }}
                        onError={() => {
                            if (!invalidLines.includes(index)) {
                                setInvalidLines([...invalidLines, index])
                            }
                            props.onError();
                        }}
                    ></ExpressionItem>
                </Col>
                <Col xs={24} sm={2} className={styles.rightColumn}>
                    {/* 弹出排序操作 */}
                    <Popover
                        position='left'
                        style={{
                            padding: 5,
                        }}
                        content={<>
                            <Space key={'index'}>
                                <Button disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconAlignTop />}
                                    onClick={() => {
                                        let newGroups = expressions.map((val, i) => {
                                            return val;
                                        });
                                        let item = newGroups[index];
                                        newGroups[index] = newGroups[0];
                                        newGroups[0] = item;
                                        setExpressions(newGroups)
                                        props.onChange(newGroups)
                                    }}
                                ></Button>
                                <Button
                                    onClick={() => {
                                        const newGroups = expressions.map((val, i) => {
                                            if (i == index) {
                                                return expressions[index - 1];
                                            } else if (i == index - 1) {
                                                return expressions[index]
                                            } else {
                                                return val
                                            }
                                        });
                                        setExpressions(newGroups)
                                        props.onChange(newGroups)
                                    }} disabled={invalidLines.indexOf(index) >= 0 || index == 0} icon={<IconArrowUp />}></Button>
                                <Button
                                    onClick={() => {
                                        const newGroups = expressions.map((val, i) => {
                                            if (i == index) {
                                                return expressions[index + 1];
                                            } else if (i == index + 1) {
                                                return expressions[index]
                                            } else {
                                                return val
                                            }
                                        });
                                        setExpressions(newGroups)
                                        props.onChange(newGroups)
                                    }}
                                    disabled={invalidLines.indexOf(index) >= 0 || index == expressions.length - 1} icon={<IconArrowDown />}></Button>
                                <Button disabled={invalidLines.indexOf(index) >= 0 || index == expressions.length - 1} icon={<IconAlignBottom />} onClick={() => {
                                    let newGroups = expressions.map((val, i) => {
                                        return val;
                                    });
                                    let item = newGroups[index];
                                    newGroups[index] = newGroups[newGroups.length - 1];
                                    newGroups[newGroups.length - 1] = item;
                                    setExpressions(newGroups)
                                    props.onChange(newGroups)
                                }}></Button>
                                <Button
                                    type='danger'
                                    theme='borderless'
                                    icon={<IconMinusCircle />}
                                    onClick={() => {
                                        const newGroups = expressions.filter((g, i) => i != index)
                                        setExpressions(newGroups)

                                        setInvalidLines(invalidLines.filter((i) => i != index))
                                        setErrorExpressionIndexs(errorExpressionIndexs.filter((i) => i != index))
                                        props.onChange(newGroups)
                                    }}

                                />
                            </Space>
                        </>}><Button icon={<IconMore />}></Button></Popover>

                </Col>
            </Row>
        })}
    </>
}

export default Index;
