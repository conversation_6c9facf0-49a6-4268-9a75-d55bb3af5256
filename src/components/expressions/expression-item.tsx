import React, { useState, useEffect } from 'react'
import { Select, TreeSelect, Input, TagInput, Switch, DatePicker, Row, Col } from '@douyinfe/semi-ui';
import { TreeNodeData } from '@douyinfe/semi-ui/lib/es/tree';
import { Expression } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';
import { getAttributeNode, getDataType, parseAttribute } from '@/utils/expression';
import { AttributeTemplate } from '@/interface/attribute-template';

import styles from './index.module.scss';
interface Props {
    value: Expression,
    // expressionsTemplate: string,
        attributeTemplate: AttributeTemplate,
    onChange: ((val: Expression) => void),
    onError: () => void,
}



const Index: React.FC<Props> = (props) => {
    const expressionsTemplate: AttributeTemplate = props.attributeTemplate;

    const isOneOfChar = 'in';

    // 属性
    const [attr, setAttr] = useState(props.value.name);
    // 操作符
    const [opt, setOpt] = useState(props.value.op);
    // 值
    const [val, setVal] = useState(props.value.value);


    useEffect(() => {
        setAttr(props.value.name)
        setOpt(props.value.op)
        setVal(props.value.value)

    }, [props.value]);

    useEffect(() => {
        const { treeData, dataType, enumValues } = parseAttribute(attr, props.attributeTemplate);
        setValType(dataType as any);
        setTreeData(treeData);
        setEnumValues(enumValues);
    }, [props.attributeTemplate])


    const initAttrData = parseAttribute(attr, props.attributeTemplate);



    // 值类型
    const [valType, setValType] = useState<'string' | 'number' | 'boolean' | 'array' | 'datetime' | 'date'>(initAttrData.dataType as any);
    const handleChange = (attr: string, opt: string, val: string | string[]) => {
        let value = '';
        if (val instanceof Array) {
            value = JSON.stringify(val);
        } else {
            value = val;
        }

        let exp = new Expression({
            name: attr,
            op: opt,
            value: value
        });

        props.onChange(exp);
    }

    const [treeData, setTreeData] = useState<TreeNodeData[]>(initAttrData.treeData);
    const [enumValues, setEnumValues] = useState<string[]>(initAttrData.enumValues);

    return <>
        <Row gutter={4}>
            <Col span={8}>
                <TreeSelect
                    value={attr}
                    onChange={(value) => {
                        let attr = value as string;
                        setAttr(attr);

                        let initDataType = '';
                        let _enumValues: string[] = [];
                        Object.keys(expressionsTemplate.properties).forEach((key) => {
                            let node = getAttributeNode(attr, key, expressionsTemplate.properties[key])

                            if (node) {
                                let dataType = getDataType(node);
                                if (dataType) {
                                    initDataType = dataType;
                                }
                                if (node.enum) {
                                    _enumValues = node.enum
                                }
                            }

                        })

                        if (initDataType) {
                            setValType(initDataType as any);
                        }
                        setEnumValues(_enumValues);
                        setOpt('');
                        if (initDataType == 'boolean') {
                            setVal('true');
                        } else {
                            setVal('');
                        }
                        props.onError()
                    }}
                    style={{ width: '100%' }}
                    leafOnly
                    expandAll
                    treeData={treeData}
                    placeholder="请选择"
                    dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}
                />
            </Col>
            <Col span={8}>
                <Select style={{ width: '100%' }} value={opt}
                    onChange={(value) => {
                        let newopt = value as string;
                        if (valType != 'boolean') {
                            setVal('');
                        }
                        setOpt(newopt);
                        if (attr && newopt && val) {
                            handleChange(attr, newopt, val);
                        } else {
                            props.onError()
                        }

                    }}>
                    {(valType == 'number' || valType == 'datetime' || valType == 'date') && <Select.Option value={'>'}>大于</Select.Option>}
                    {(valType == 'number' || valType == 'datetime' || valType == 'date') && <Select.Option value={'<'}>小于</Select.Option>}
                    {valType != 'array' && <Select.Option value={'='}>等于</Select.Option>}
                    {valType != 'array' && <Select.Option value={'!='}>不等于</Select.Option>}

                    {(valType == 'number' || valType == 'datetime' || valType == 'date') && <Select.Option value={'>='}>大于等于</Select.Option>}
                    {(valType == 'number' || valType == 'datetime' || valType == 'date') && <Select.Option value={'<='}>小于等于</Select.Option>}
                    {(valType == 'array' || valType == 'string') && <Select.Option value={'contains'}>包含(模糊匹配)</Select.Option>}
                    {(valType == 'array' || valType == 'string') && <Select.Option value={isOneOfChar}>包含(精确匹配)</Select.Option>}

                </Select>
            </Col>
            <Col span={8}>{opt == isOneOfChar || opt == 'contains' || valType == 'array' ? <> {
                enumValues.length > 0 ? <Select
                    value={val ? JSON.parse(val) : []}
                    optionList={enumValues.map((val) => {
                        return {
                            label: val,
                            value: val
                        }
                    })}
                    multiple
                    onChange={(value) => {
                        setVal(JSON.stringify(value));
                        if (attr && opt && value) {
                            handleChange(attr, opt, value as any);
                        } else {
                            props.onError();
                        }
                    }}
                    style={{ width: '100%' }}></Select> : <TagInput
                        value={val ? JSON.parse(val) : []}
                        onChange={(value) => {
                            setVal(JSON.stringify(value));
                            if (attr && opt && value && value.length > 0) {
                                handleChange(attr, opt, value);
                            } else {
                                props.onError();
                            }
                        }}
                        addOnBlur
                        style={{ width: '100%' }}
                    ></TagInput>
            }


            </> : valType == 'boolean' ? <Switch
                checked={val == 'true'}
                checkedText={"是"}
                uncheckedText={"否"}
                onChange={(value) => {
                    setVal(value ? 'true' : 'false');
                    handleChange(attr, opt, value ? 'true' : 'false');
                }}
                style={{ marginTop: 4 }}
            ></Switch> : valType == 'datetime' ?
                <DatePicker
                    type='dateTime'
                    value={val as string}
                    format='yyyy-MM-dd HH:mm:ss'
                    onChange={(value) => {
                        setVal(value as string);
                        if (attr && opt && value) {
                            let date = new Date(value as string);
                            handleChange(attr, opt, date.toISOString());
                        } else {
                            props.onError();
                        }
                    }}
                    style={{ marginRight: 0 }}
                ></DatePicker> : valType == 'date' ? <DatePicker
                type='date'
                value={val as string}
                format='yyyy-MM-dd'
                onChange={(value) => {
                    setVal(value as string);
                    if (attr && opt && value) {
                        let date = new Date(value as string);
                        handleChange(attr, opt, date.toISOString());
                    } else {
                        props.onError();
                    }
                }}
                style={{ marginRight: 0 }}
            ></DatePicker> : enumValues.length > 0 ? <Select
                    value={val}
                    optionList={enumValues.map((val) => {
                        return {
                            label: val,
                            value: val
                        }
                    })}
                    onChange={(value) => {
                        setVal(value as string);
                        if (attr && opt && value) {
                            handleChange(attr, opt, value as any);
                        } else {
                            props.onError();
                        }
                    }}
                    style={{ width: '100%' }}></Select> :
                    <Input value={val}

                        onChange={(value) => {
                            let val = value as string;
                            if (valType == 'number') {
                                val = val.replace(/[^0-9]/g, '');
                            }
                            setVal(val);
                            if (attr && opt && val) {
                                handleChange(attr, opt, val);
                            } else {
                                props.onError();
                            }
                        }} style={{ width: '100%' }}></Input>}</Col>

        </Row>



    </>
}

export default Index;