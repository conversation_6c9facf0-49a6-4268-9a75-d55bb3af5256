import { FC, useState, useContext, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, Modal, Form, Button, Row, Col, Card, Space, ArrayField, Select, Divider } from '@douyinfe/semi-ui';
import { Field, FieldGroup, FieldRule, FieldItem } from '@buf/flylayer_logs.bufbuild_es/logs/v1/base_pb';
import { IconAlertCircle, IconPlusCircle, IconPlus, IconMinusCircle } from '@douyinfe/semi-icons';
import { FormApi } from '@douyinfe/semi-ui/lib/es/form';
import FieldRuleEdit from '../field-rule-edit'
import FieldItemEdit from '../field-item-edit'
import { useLocale } from '@/locales';
const { Title } = Typography;

const Index: FC<{
    cancel: () => void
    success: (fieldGroup: FieldGroup) => void
    fieldGroup: FieldGroup
}> = (props) => {
    const { formatMessage } = useLocale();
    const [formApi, setFormApi] = useState<FormApi<FieldGroup>>();

    const handleSubmit = async () => {
        if (!formApi) {
            return;
        }

        await formApi.validate();

        let values = formApi.getValues();
        
        let fieldGroup = new FieldGroup({
            key: values.key,
            label: values.label,
            disabled: values.disabled,
            fields: (!values.fields || values.fields.length == 0) ? [] : values.fields.map((field: any) => {
                let f = new Field({
                    key: field.key,
                    label: field.label,
                    type: field.type,
                    extra: field.extra,
                    default: field.default,
                    placeholder: field.placeholder,
                    disabled: field.disabled,
                    hidden: field.hidden,
                    readonly: field.readonly,
                    items: !field.items ? [] : field.items.map((item: any) => {
                        let i = new FieldItem({
                            value: item.value,
                            label: item.label,
                        });
                        return i;
                    }),
                    rules: !field.rules ? [] : field.rules.map((rule: any) => {
                        let r = new FieldRule({
                            required: rule.required,
                            whitespace: rule.whitespace,
                            format: rule.format,
                            pattern: rule.pattern,
                            minLength: rule.minLength,
                            maxLength: rule.maxLength,
                        });
                        return r;
                    })
                });
                return f;
            })
        });



        props.success(fieldGroup);
    }

    return <>
        <Modal
            width={1200}
            title={formatMessage({ id: 'fieldGroups.editTitle', defaultMessage: '编辑字段组' })}
            visible={true}
            onCancel={props.cancel}
            onOk={handleSubmit}
            className='semi-modal'
            maskClosable={false}
        >
            <div className='p10'>
                <Form
                    getFormApi={setFormApi}
                    initValues={props.fieldGroup}
                ><Row gutter={16}>
                        <Col span={8}>
                            <Form.Input
                                field='key'
                                label={formatMessage({ id: 'fieldGroups.key', defaultMessage: '键' })}
                                rules={[{
                                    required: true,
                                    message: formatMessage({ id: 'fieldGroups.inputName', defaultMessage: '请输入名称' })
                                }]}
                            />
                        </Col>
                        <Col span={8}>
                            <Form.Input
                                field='label'
                                label={formatMessage({ id: 'fieldGroups.name', defaultMessage: '名称' })}
                                rules={[{
                                    required: true,
                                    message: formatMessage({ id: 'fieldGroups.inputLabel', defaultMessage: '请输入标签' })
                                }]}
                            />
                        </Col>
                        <Col span={8}>
                            <Form.Switch field='disabled' label={formatMessage({ id: 'fieldGroups.disabled', defaultMessage: '禁用' })}></Form.Switch>
                        </Col>

                    </Row>

                    <ArrayField field="fields" >
                        {({ add, arrayFields, addWithInitValue }) => (<>
                            <Row className='mb10'>
                                <Col span={20}>
                                    <Title heading={6} type='secondary'>{formatMessage({ id: 'fieldGroups.fieldList', defaultMessage: '字段列表' })}</Title>
                                </Col>
                                <Col span={4} className='btn-right-col'>
                                    <Button
                                        icon={<IconPlusCircle />}
                                        theme="solid"
                                        onClick={() => {
                                            addWithInitValue({
                                                key: '',
                                                type: 'input',
                                                extra: '',
                                                label: '',
                                                default: '',
                                                placeholder: '',
                                                disabled: false,
                                                hidden: false,
                                                readonly: false,
                                                rules: [{
                                                    required: false,
                                                    whitespace: false,
                                                    format: '',
                                                    pattern: '',
                                                    minLength: -1,
                                                    maxLength: -1,
                                                }],
                                            });
                                        }}
                                    >
                                        {formatMessage({ id: 'fieldGroups.addField', defaultMessage: '新增字段' })}
                                    </Button>
                                </Col>
                            </Row>


                            {arrayFields.map(({ field, key, remove }, i) => (<Card className='mb10' key={i}>
                                <div className='btn-right-col'>
                                    <Button icon={<IconMinusCircle />} type='danger' onClick={remove} />
                                </div>
                                <Row gutter={4}>
                                    <Col span={3}>
                                        <Form.Input
                                            field={`${field}[key]`}
                                            label={formatMessage({ id: 'fieldGroups.fieldName', defaultMessage: '字段名' })}
                                            placeholder={formatMessage({ id: 'fieldGroups.inputFieldName', defaultMessage: '请输入字段名' })}
                                        ></Form.Input>
                                    </Col>
                                    <Col span={3}>
                                        <Form.Input
                                            required
                                            field={`${field}[label]`}
                                            label={formatMessage({ id: 'fieldGroups.label', defaultMessage: '标签' })}
                                            placeholder={formatMessage({ id: 'fieldGroups.inputLabel', defaultMessage: '请输入标签' })}
                                        />
                                    </Col>
                                    <Col span={3}>
                                        <Form.Select
                                            field={`${field}[type]`}
                                            label={formatMessage({ id: 'fieldGroups.type', defaultMessage: '类型' })}
                                            placeholder={formatMessage({ id: 'fieldGroups.selectType', defaultMessage: '请选择类型' })}
                                            style={{ width: '100%' }}
                                        >
                                            <Form.Select.Option value={'input'}>{formatMessage({ id: 'fieldGroups.input', defaultMessage: '输入框' })}</Form.Select.Option>
                                            <Form.Select.Option value={'select'}>{formatMessage({ id: 'fieldGroups.select', defaultMessage: '下拉选择' })}</Form.Select.Option>
                                            <Form.Select.Option value={'radio'}>{formatMessage({ id: 'fieldGroups.radio', defaultMessage: '单项选择' })}</Form.Select.Option>
                                            <Form.Select.Option value={'checkbox'}>{formatMessage({ id: 'fieldGroups.checkbox', defaultMessage: '多项选择' })}</Form.Select.Option>
                                            <Form.Select.Option value={'inputNumber'}>{formatMessage({ id: 'fieldGroups.inputNumber', defaultMessage: '数字输入框' })}</Form.Select.Option>
                                            <Form.Select.Option value={'switch'}>{formatMessage({ id: 'fieldGroups.switch', defaultMessage: '开关' })}</Form.Select.Option>
                                            <Form.Select.Option value={'date'}>{formatMessage({ id: 'fieldGroups.date', defaultMessage: '日期选择' })}</Form.Select.Option>
                                            <Form.Select.Option value={'time'}>{formatMessage({ id: 'fieldGroups.time', defaultMessage: '时间选择' })}</Form.Select.Option>
                                            <Form.Select.Option value={'datetime'}>{formatMessage({ id: 'fieldGroups.datetime', defaultMessage: '日期时间选择' })}</Form.Select.Option>
                                            <Form.Select.Option value={'textarea'}>{formatMessage({ id: 'fieldGroups.textarea', defaultMessage: '多行文本框' })}</Form.Select.Option>
                                            <Form.Select.Option value={'keyvalues'}>{formatMessage({ id: 'fieldGroups.keyvalues', defaultMessage: '键值对' })}</Form.Select.Option>
                                        </Form.Select>
                                    </Col>
                                    <Col span={3}>
                                        <Form.Input
                                            field={`${field}[default]`}
                                            label={formatMessage({ id: 'fieldGroups.default', defaultMessage: '默认值' })}
                                            placeholder={formatMessage({ id: 'fieldGroups.inputDefault', defaultMessage: '请输入默认值' })}
                                        />
                                    </Col>
                                    <Col span={3}>
                                        <Form.Input
                                            field={`${field}[placeholder]`}
                                            label={formatMessage({ id: 'fieldGroups.placeholder', defaultMessage: '占位符' })}
                                            placeholder={formatMessage({ id: 'fieldGroups.inputPlaceholder', defaultMessage: '请输入占位符' })}
                                        />
                                    </Col>
                                    <Col span={3}>
                                        <Form.Input
                                            field={`${field}[extra]`}
                                            label={formatMessage({ id: 'fieldGroups.extra', defaultMessage: '扩展信息' })}
                                            placeholder={formatMessage({ id: 'fieldGroups.inputExtra', defaultMessage: '请输入扩展信息' })}
                                        />
                                    </Col>
                                    <Col span={2}>
                                        <Form.Switch
                                            field={`${field}[disabled]`}
                                            label={formatMessage({ id: 'fieldGroups.disabled', defaultMessage: '禁用' })}
                                        ></Form.Switch>
                                    </Col>
                                    <Col span={2}>
                                        <Form.Switch
                                            field={`${field}[readonly]`}
                                            label={formatMessage({ id: 'fieldGroups.readonly', defaultMessage: '只读' })}
                                        ></Form.Switch>
                                    </Col>
                                    <Col span={2}>
                                        <Form.Switch
                                            field={`${field}[hidden]`}
                                            label={formatMessage({ id: 'fieldGroups.hidden', defaultMessage: '隐藏' })}
                                        ></Form.Switch>
                                    </Col>
                                </Row>

                                <FieldRuleEdit field={field} />
                                <FieldItemEdit field={field} />

                            </Card>))}

                        </>)}
                    </ArrayField>
                </Form>
            </div>
        </Modal>
    </>
}

export default Index