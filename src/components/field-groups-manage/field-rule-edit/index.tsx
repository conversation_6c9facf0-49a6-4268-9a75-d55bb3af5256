import { Typography, <PERSON>, Button, Row, Col, ArrayField } from '@douyinfe/semi-ui';
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
const { Title } = Typography;
const Index = (props: any) => {
    const { formatMessage } = useLocale();
    const rowStyle = {
        marginTop: 12,
        marginLeft: 12,
    };
    return (
        <ArrayField field={`${props.field}.rules`}>

            {({ add, arrayFields, addWithInitValue }) => (
                <>
                    <Row>
                        <Col span={20}><Title heading={6} type='tertiary'>{formatMessage({ id: 'fieldGroups.ruleListTitle', defaultMessage: '字段规则列表' })}</Title></Col>
                        <Col span={4} className='btn-right-col'>
                            <Button
                                icon={<IconPlusCircle />}
                                onClick={() => {
                                    add();
                                }}
                            >
                                {formatMessage({ id: 'fieldGroups.addRule', defaultMessage: '添加规则' })}
                            </Button>
                        </Col>
                    </Row>
                    <Row className='tableTitle' gutter={16}>

                        <Col span={3}>
                            {formatMessage({ id: 'fieldGroups.required', defaultMessage: '必填' })}
                        </Col>
                        <Col span={3}>
                            {formatMessage({ id: 'fieldGroups.whitespace', defaultMessage: '允许空格' })}
                        </Col>
                        <Col span={4}>
                            {formatMessage({ id: 'fieldGroups.format', defaultMessage: '格式' })}
                        </Col>
                        <Col span={4}>
                            {formatMessage({ id: 'fieldGroups.pattern', defaultMessage: '正则' })}
                        </Col>
                        <Col span={4}>
                            {formatMessage({ id: 'fieldGroups.minLength', defaultMessage: '最小长度' })}
                        </Col>
                        <Col span={4}>
                            {formatMessage({ id: 'fieldGroups.maxLength', defaultMessage: '最大长度' })}
                        </Col>
                        <Col span={2}>
                        </Col>

                    </Row>
                    {arrayFields.map(({ field, key, remove }, i) => (
                        <Row key={key} gutter={16}>
                            <Col span={3}>
                                <Form.Switch
                                    field={`${field}[required]`}
                                    noLabel

                                ></Form.Switch>
                            </Col>
                            <Col span={3}>
                                <Form.Switch
                                    field={`${field}[whitespace]`}
                                    noLabel

                                ></Form.Switch>
                            </Col>
                            <Col span={4}>
                                <Form.Select
                                    field={`${field}[format]`}

                                    noLabel
                                    style={{ width: 100 }}
                                    optionList={[
                                        { label: formatMessage({ id: 'fieldGroups.formatEmail', defaultMessage: '电子邮件' }), value: "email" },
                                        { label: "URL", value: "url" },
                                        { label: formatMessage({ id: 'fieldGroups.formatPhone', defaultMessage: '电话号码' }), value: "datetime" },
                                        { label: "IPV4", value: "ipv4" },
                                        { label: "IPV6", value: "ipv6"},
                                        { label: formatMessage({ id: 'fieldGroups.formatIdCard', defaultMessage: '身份证' }), value: "idcard" },
                                        { label: formatMessage({ id: 'fieldGroups.formatNumber', defaultMessage: '数字' }), value: "number" },
                                        { label: formatMessage({ id: 'fieldGroups.formatInteger', defaultMessage: '整数' }), value: "integer" },
                                        { label: formatMessage({ id: 'fieldGroups.formatFloat', defaultMessage: '浮点数' }), value: "float" },
                                        { label: "JSON", value: "json"},
                                        { label: formatMessage({ id: 'fieldGroups.formatArray', defaultMessage: 'JSON数组' }), value: "array" },

                                        // { label: "base64", value: "base64" },
                                        // { label: "hex", value: "hex" },
                                        // { label: "字母", value: "letter" },
                                        // { label: "大写字母", value: "uppercase" },
                                        // { label: "小写字母", value: "lowercase" },
                                        // { label: "中文", value: "chinese" },
                                        // { label: "银行卡", value: "bankcard" },
                                        // { label: "密码", value: "password" },
                                        // { label: "强密码", value: "strongPassword" },
                                        // { label: "日期", value: "date" },
                                        // { label: "时间", value: "time" },
                                        // { label: "日期时间", value: "datetime" },
                                        // { label: "字母", value: "letter" },
                                        // { label: "大写字母", value: "uppercase" },
                                        // { label: "小写字母", value: "lowercase" },
                                        // { label: "中文", value: "chinese" },
                                        // { label: "银行卡", value: "bankcard" },
                                        // { label: "密码", value: "password" },
                                        // { label: "强密码", value: "strongPassword" },
                                    ]}
                                ></Form.Select>
                            </Col>
                            <Col span={4}>
                                <Form.Input
                                    field={`${field}[pattern]`}
                                    noLabel
                                ></Form.Input>
                            </Col>
                            <Col span={4}>
                                <Form.InputNumber
                                    field={`${field}[minLength]`}
                                    noLabel
                                ></Form.InputNumber>
                            </Col>
                            <Col span={4}>
                                <Form.InputNumber
                                    field={`${field}[maxLength]`}
                                    noLabel
                                ></Form.InputNumber>
                            </Col>
                            <Col span={2}>
                                <Button
                                    type="danger"
                                    theme="borderless"
                                    style={rowStyle}
                                    icon={<IconMinusCircle />}
                                    onClick={remove}
                                />
                            </Col>
                        </Row>

                    ))}
                </>
            )}
        </ArrayField>
    );
};

export default Index;