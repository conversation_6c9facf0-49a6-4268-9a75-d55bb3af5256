import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Descriptions, Button, Row, Col, Space, Tag, Popover, ArrayField, Select, Divider } from '@douyinfe/semi-ui';
import { Field, FieldGroup, FieldRule } from '@buf/flylayer_logs.bufbuild_es/logs/v1/base_pb';
import styles from './index.module.scss';
import { IconEdit, IconDelete, IconMore } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';

const { Text } = Typography;

const Index: FC<{
    editable?: boolean
    fields: Array<Field>
}> = (props) => {
    const { formatMessage } = useLocale();
    const editable = props.editable ? true : false;

    return <>

        {!props.fields || props.fields.length == 0 ?
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                <Text type='secondary'>{formatMessage({ id: 'fieldGroups.noFields', defaultMessage: '暂无字段' })}</Text>
            </div> :
            <><Row className={styles.tableTitle}>
                <Col span={3}>{formatMessage({ id: 'fieldGroups.key', defaultMessage: '键' })}</Col>
                <Col span={3}>{formatMessage({ id: 'fieldGroups.name', defaultMessage: '名称' })}</Col>
                <Col span={3}>{formatMessage({ id: 'fieldGroups.type', defaultMessage: '类型' })}</Col>
                <Col span={2}></Col>
                <Col span={4}>{formatMessage({ id: 'fieldGroups.default', defaultMessage: '默认值' })}</Col>
                <Col span={4}>{formatMessage({ id: 'fieldGroups.placeholder', defaultMessage: '占位符' })}</Col>
                <Col span={3}>{formatMessage({ id: 'fieldGroups.extra', defaultMessage: '扩展信息' })}</Col>
                <Col span={2}>{formatMessage({ id: 'fieldGroups.rules', defaultMessage: '校验规则' })}</Col>
            </Row>
                {props.fields && props.fields.map((field, index) => {
                    return <div key={index}>
                        <Row className={styles.tableBody}>
                            <Col span={3}>{field.key}</Col>
                            <Col span={3}>{field.label}</Col>
                            <Col span={3}>
                                {field.type == 'input' && '输入框'}
                                {field.type == 'select' && '下拉框'}
                                {field.type == 'radio' && '单项选择'}
                                {field.type == 'checkbox' && '多项选择'}
                                {field.type == 'textarea' && '多行文本框'}
                                {field.type == 'inputNumber' && '数字输入框'}
                                {field.type == 'switch' && '开关'}
                                {field.type == 'date' && '日期选择'}
                                {field.type == 'time' && '时间选择'}
                                {field.type == 'datetime' && '日期时间选择'}
                                {field.type == 'keyvalues' && '键值对'}
                            </Col>
                            <Col span={2}>
                                {field.items && field.items.length > 0 ? <Popover position='bottomRight' content={<div className='p10' style={{ width: 160 }}>
                                    <Row className={styles.tableTitle}>
                                        <Col span={12}>标签</Col>
                                        <Col span={12}>值</Col>
                                    </Row>
                                    {field.items.map((item, itemIndex) => {

                                        return <Row className={styles.tableBody} key={itemIndex}>
                                            <Col span={12}>
                                                <Text>{item.label}</Text>
                                            </Col>
                                            <Col span={12}>
                                                <Text>{item.value}</Text>
                                            </Col>
                                        </Row>
                                    })}
                                </div>}><Button icon={<IconMore />} /></Popover> : <Tag>无</Tag>}
                            </Col>
                            <Col span={4}>{field.default}</Col>
                            <Col span={4}>{field.placeholder}</Col>
                            <Col span={3}><Text>{field.extra}</Text></Col>
                            <Col span={2}>
                                {field.rules && field.rules.length > 0 ? <Popover position='bottomRight' content={<div className='p10' style={{ width: 500 }}>
                                    <Row className={styles.tableTitle}>
                                        <Col span={4}>必填</Col>
                                        <Col span={4}>非空</Col>
                                        <Col span={4}>格式</Col>
                                        <Col span={4}>正则表达式</Col>
                                        <Col span={4}>最小长度</Col>
                                        <Col span={4}>最大长度</Col>
                                    </Row>
                                    {field.rules.map((rule, ruleIndex) => {

                                        return <Row className={styles.tableBody} key={ruleIndex}>
                                            <Col span={4}>
                                                <Text>{rule.required}</Text>
                                            </Col>
                                            <Col span={4}>
                                                <Text>{rule.whitespace}</Text>
                                            </Col>
                                            <Col span={4}>
                                                <Text>{rule.format}</Text>
                                            </Col>
                                            <Col span={4}>
                                                <Text>{rule.pattern}</Text>
                                            </Col>
                                            <Col span={4}>
                                                <Text>{rule.minLength}</Text>
                                            </Col>
                                            <Col span={4}>
                                                <Text>{rule.maxLength}</Text>
                                            </Col>
                                        </Row>
                                    })}
                                </div>}><Button icon={<IconMore />} /></Popover> : <Tag>无</Tag>}

                            </Col>
                        </Row>

                    </div>
                })}
            </>}
    </>
}

export default Index
