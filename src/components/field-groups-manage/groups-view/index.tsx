import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Form, Notification, Button, Row, Col, Input, Switch, Space, Tag, Card } from '@douyinfe/semi-ui';
import { Field, FieldGroup, FieldRule } from '@buf/flylayer_logs.bufbuild_es/logs/v1/base_pb';
import { IconEdit, IconMinusCircle } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';

import FieldsView from '../fields-view';

const { Text, Title } = Typography;

const Index: FC<{
    editable?: boolean
    fieldGroups: Array<FieldGroup>
    onDel?: (fieldGroup: FieldGroup, index: number) => void
    onEdit?: (fieldGroup: FieldGroup, index: number) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const editable = props.editable ? true : false;

    return <>
        {props.fieldGroups.map((fieldGroup, index) => {
            return <Card key={index} className='mb10' style={{ paddingTop: 10 }}>
                <Row className='mb10'>
                    <Col span={22}>
                        <Title heading={6} type='secondary'>
                            {fieldGroup.label}&nbsp;
                            <Space>
                                <Text type='tertiary'>({fieldGroup.key})</Text>

                                <Space>
                                    {fieldGroup.disabled ?
                                        <Tag color='orange'>{formatMessage({ id: 'fieldGroups.disabled', defaultMessage: '禁用' })}</Tag>
                                        : <Tag color='green'>{formatMessage({ id: 'fieldGroups.enabled', defaultMessage: '启用' })}</Tag>
                                    }
                                </Space>
                            </Space>
                        </Title>



                    </Col>
                    <Col span={2} className='btn-right-col'>
                        {editable && <Space>
                            <Button icon={<IconEdit />} onClick={() => {
                                props.onEdit && props.onEdit(fieldGroup, index)
                            }}/>
                            <Button type='danger' icon={<IconMinusCircle />} onClick={() => {
                                props.onDel && props.onDel(fieldGroup, index)
                            }}></Button>
                        </Space>
                        }
                    </Col>

                </Row>

                <FieldsView fields={fieldGroup.fields}></FieldsView>

            </Card>
        })}


    </>
}

export default Index;