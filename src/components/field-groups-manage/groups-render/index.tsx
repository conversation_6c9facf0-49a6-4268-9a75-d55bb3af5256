import { FC, useState, useContext, useEffect } from 'react'
import { Typography, Modal, Form, Notification, Button, Row, Col, Tag, Popover, ArrayField, Select, Divider } from '@douyinfe/semi-ui';
import { Field, FieldGroup, FieldRule } from '@buf/flylayer_logs.bufbuild_es/logs/v1/base_pb';
import { useLocale } from '@/locales';

const { Title } = Typography;

const Index: FC<{
    col?: 2 | 3 | 4 | 6 | 12
    gutter?: number
    fieldGroups: Array<FieldGroup>
}> = (props) => {
    const { formatMessage } = useLocale();
    let nodes: React.ReactNode[] = [];
    let nodeIndex = 0;
    const col = props.col || 2;

    return <Row gutter={props.gutter || 16}>

        {props.fieldGroups.map((fieldGroup, index) => {

            return <Form.Section text={formatMessage({ id: fieldGroup.label, defaultMessage: fieldGroup.label })} key={index}>
                {
                    fieldGroup.fields.map((field, index) => {
                        if (field.type == 'input') {
                            return <Form.Input field={field.key} label={field.label} />

                        }

                    })
                }

            </Form.Section>
        })}
    </Row>
}

export default Index;