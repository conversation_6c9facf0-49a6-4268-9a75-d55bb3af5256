import { Typography, <PERSON>, Button, Row, Col, ArrayField } from '@douyinfe/semi-ui';
import { IconPlusCircle, IconMinusCircle } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';
const { Title } = Typography;

const Index = (props: any) => {
    const { formatMessage } = useLocale();
    const rowStyle = {
        marginTop: 12,
        marginLeft: 12,
    };
    return <ArrayField field={`${props.field}.items`}>
        {({ add, arrayFields, addWithInitValue }) => (
            <>
                <Row>
                    <Col span={20}><Title heading={6} type='tertiary'>{formatMessage({ id: 'fieldGroups.optionListTitle', defaultMessage: '字段选项列表（下拉列表，单选，多选等使用）' })}</Title></Col>
                    <Col span={4} className='btn-right-col'>
                        <Button
                            icon={<IconPlusCircle />}
                            onClick={() => {
                                add();
                            }}
                        >
                            {formatMessage({ id: 'fieldGroups.addOption', defaultMessage: '添加选项' })}
                        </Button>
                    </Col>
                </Row>
                <Row className='tableTitle' gutter={16}>
                    <Col span={10}>
                        {formatMessage({ id: 'fieldGroups.optionLabel', defaultMessage: '标签' })}
                    </Col>
                    <Col span={10}>
                        {formatMessage({ id: 'fieldGroups.optionValue', defaultMessage: '值' })}
                    </Col>
                    <Col span={4}>
                    </Col>
                </Row>
                {arrayFields.map(({ field, key, remove }, i) => (
                    <Row key={key} gutter={16}>
                        <Col span={10}>
                            <Form.Input
                                field={`${field}[label]`}
                                noLabel
                            ></Form.Input>
                        </Col>
                        <Col span={10}>
                            <Form.Input
                                field={`${field}[value]`}
                                noLabel
                            ></Form.Input>
                        </Col>                       
                        <Col span={4}>
                            <Button
                                type="danger"
                                theme="borderless"
                                style={rowStyle}
                                icon={<IconMinusCircle />}
                                onClick={remove}
                            />
                        </Col>
                    </Row>
                ))}
            </>
        )}
    </ArrayField>
}

export default Index;