import { FC, useState, useContext, useEffect } from 'react'
import { Typography, ButtonGroup, Input, Switch, Form, Notification, Button, Row, Col, Space, Popover, ArrayField, Select, Divider, Tooltip, List } from '@douyinfe/semi-ui';
import { IconPlus, IconGridView, IconListView } from '@douyinfe/semi-icons';
import { useLocale } from '@/locales';

import GroupAdd from './group-add';
import GroupEdit from './group-edit';


import GroupsRender from './groups-render/index';
import GroupsView from './groups-view/index';

import { Field, FieldGroup, FieldRule } from '@buf/flylayer_logs.bufbuild_es/logs/v1/base_pb';

const { Title, Text } = Typography;

const Index: FC<{
    fieldGroups: Array<FieldGroup>
    onChange: (fieldGroups: Array<FieldGroup>) => void
}> = (props) => {
    const { formatMessage } = useLocale();
    const [fieldGroups, setFieldGroups] = useState<Array<FieldGroup>>(props.fieldGroups);
    useEffect(() => {
        setFieldGroups(props.fieldGroups)
    }, [props.fieldGroups])

    const [addVisible, setAddVisible] = useState(false);

    const [viewMode, setViewMode] = useState<'render' | 'view'>('view');

    const [selectFieldGroup, setSelectFieldGroup] = useState<FieldGroup | null>(null);
    const [selectFieldGroupIndex, setSelectFieldGroupIndex] = useState<number | null>(null);

    const [editVisible, setEditVisible] = useState(false);

    return <>
        <Row className='mb10'>
            <Col span={12}><Title heading={4} type='secondary'>{formatMessage({ id: 'fieldGroups.title', defaultMessage: '字段组' })}</Title></Col>
            <Col span={12} className='btn-right-col'>
                <Space>
                    <ButtonGroup>
                        <Tooltip content={formatMessage({ id: 'fieldGroups.preview', defaultMessage: '预览' })}>
                            <Button theme={viewMode == 'render' ? 'solid' : 'light'} onClick={() => { setViewMode('render') }} icon={<IconGridView />} /></Tooltip>
                        <Tooltip content={formatMessage({ id: 'fieldGroups.list', defaultMessage: '列表' })}>
                            <Button theme={viewMode == 'view' ? 'solid' : 'light'} onClick={() => { setViewMode('view') }} icon={<IconListView />} /></Tooltip>
                    </ButtonGroup>

                    <Button
                        theme='solid'
                        onClick={() => {
                            setAddVisible(true)
                        }}
                        icon={<IconPlus />} />
                </Space>
            </Col>
        </Row>
        <Divider className='mb10' />
        {fieldGroups && fieldGroups.length > 0 ?
            <>

                {viewMode == 'render' && <GroupsRender fieldGroups={fieldGroups}></GroupsRender>}
                {viewMode == 'view' && <div>
                    <GroupsView
                        fieldGroups={fieldGroups}
                        editable={true}
                        onDel={(fg, index) => {
                            const newFieldGroups = [...fieldGroups]
                            newFieldGroups.splice(index, 1)
                            setFieldGroups(newFieldGroups)
                            props.onChange && props.onChange(newFieldGroups)
                        }}
                        onEdit={(fg, index) => {

                            setSelectFieldGroup(fg)
                            setSelectFieldGroupIndex(index)
                            setEditVisible(true)
                        }}
                    />

                </div>}
            </> :
            <div style={{ height: 100, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Button
                    theme='solid'
                    size='large'
                    onClick={() => {
                        setAddVisible(true)
                    }}
                    icon={<IconPlus />} />
            </div>}
        {addVisible && <GroupAdd
            cancel={() => { setAddVisible(false) }}
            success={(fieldGroup) => {
                setFieldGroups([...fieldGroups, fieldGroup])
                setAddVisible(false)
                props.onChange && props.onChange([...fieldGroups, fieldGroup])
            }}
        />}
        {editVisible && selectFieldGroup && selectFieldGroupIndex !== null && <GroupEdit
            cancel={() => { setEditVisible(false) }}
            success={(fieldGroup) => {
                const newFieldGroups = [...fieldGroups]
                newFieldGroups[selectFieldGroupIndex] = fieldGroup
                setFieldGroups(newFieldGroups)
                setEditVisible(false)
                props.onChange && props.onChange(newFieldGroups)
            }}
            fieldGroup={selectFieldGroup}

        />}


    </>
}

export default Index