import { FC, useEffect, useState, useContext } from 'react';
import { Typography, Modal, Form, Notification, ArrayField, Space, Button, Row, Col, Spin, List, Divider } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

interface Props {
    value: string,
    onChange: (value: string) => void
}

const Index: FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);

    const [treeData, setTreeData] = useState<any[]>([]);

    useEffect(() => {
        flylayerClient.listServiceGroups({ 
            flynetId: flynet.id,
         }).then((res) => {
            const treeData = res.serviceGroups.map((item: any) => {
                return {
                    label: item.name,
                    value: item.id,
                    children: []
                }
            })
            setTreeData(res.serviceGroups);
        })
    }, [])

    return <>
        <Form.Cascader
            treeData={
                treeData
            }
            field='group' label='上级服务组'></Form.Cascader>
    </>
}