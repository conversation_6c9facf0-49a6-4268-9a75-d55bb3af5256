import { FC, useState, useEffect } from 'react'
import { Table, Layout, Input, Space, Tag, Row, Col, Button, Modal } from '@douyinfe/semi-ui';
import { IconSearch } from '@douyinfe/semi-icons';
import { AclOrigin } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { useLocale } from '@/locales';

import styles from './index.module.scss';
import useTable, { AclOriginFilter } from './useTable';
import TableEmpty from '@/components/table-empty';
const { Sider, Content } = Layout;

interface Props {
    multi: boolean;
    value?: AclOrigin | AclOrigin[];
    onChange: (value: AclOrigin | AclOrigin[]) => void;
    close: () => void,
}
const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();

    // 过滤参数
    const initFilter: AclOriginFilter = {
        query: '',
        status: '',
        role: []
    }
    // 过滤参数改变时跳转路由


    // 查询参数
    const [search, setSearch] = useState<string>('');
    useEffect(() => {
        // 查询参数从有值变化为无值时，重新加载数据
        if (location.search == '' && search != '') {
            setFilterParam(initFilter);
        }
        setSearch(location.search);
    }, [location])


    const { columns, loading, data, filterParam, setFilterParam, page, pageSize, doPage, total, handleSort, selectedRowKeys, onRowSelect, onRow, selectedAclOrigins, setSelectedAclOrigins } = useTable(initFilter, props.multi, props.value);


    
    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, query: value })

    }
    return <>
        <Modal
            width={830}
            title="选择策略"
            visible={true}
            onOk={() => { }}
            onCancel={props.close}
            footer={null}
            className='semi-modal'
        >
            <Layout className='mb20 search-bar'>

                <Layout>
                    <Content className='pr10'>
                        <Input value={filterParam.query}
                            onChange={handleQueryChange} style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={'根据策略信息搜索'}></Input>
                    </Content>
                    <Sider>
                        <Space>
                            {/* <Select
                                value={filterParam.status}
                                onChange={handleStatusChange}
                                style={{ width: 220 }} optionList={listStatus} insetLabel="策略状态" ></Select> */}
                        </Space></Sider>
                </Layout>

            </Layout>
            <div style={{ height: 20 }} className='mb10'> {!loading && <Tag> {formatMessage({ id: 'users.dataExport.filter.searchPlaceholder' })} {total}</Tag>}  </div>
            <div style={{ height: 500 }}>
                <Table
                    className={props.multi ? 'checkboxTable' : 'radioTable'}
                    rowSelection={
                        {
                            selectedRowKeys: selectedRowKeys as any,
                            onSelect: onRowSelect as any,
                            disabled: true
                        }
                    }
                    onRow={onRow as any}
                    rowKey={(record?: AclOrigin) => record ? record.id + '' : ''}
                    onChange={handleSort}
                    empty={<TableEmpty loading={loading} />} columns={columns} loading={loading} dataSource={data} pagination={ total  > pageSize ? {
                        currentPage: page,
                        pageSize: pageSize,
                        total: total,
                        onChange: (page: number) => {
                            doPage(page);
                        }
                    }: false} />
            </div>
            <Row className='mb10'>
                <Col span={20}>
                </Col>
                <Col span={4}><div className='btn-right-col'>
                    <Space>
                        <Button onClick={() => { props.close() }}>取消</Button>
                        <Button theme='solid' disabled={!selectedAclOrigins || selectedAclOrigins.length == 0} onClick={() => {
                            if (selectedAclOrigins && selectedAclOrigins.length > 0) {
                                if(props.multi) {
                                    props.onChange(selectedAclOrigins);
                                } else {
                                    props.onChange(selectedAclOrigins[0]);
                                }
                            }

                        }}>确定</Button>
                    </Space>
                </div></Col>
            </Row>
        </Modal>


    </>
}

export default Index;
