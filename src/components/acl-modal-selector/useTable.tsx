import { useEffect, useState, useContext } from 'react';
import { Typography, Tag, Notification } from '@douyinfe/semi-ui';

import { AclOrigin } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { caseInsensitiveIncludes } from '@/utils/common';
const { Title, Paragraph } = Typography;

export type AclOriginFilter = {
    query: string,
    role: Array<string>,
    status: 'enable' | 'disable' | ''
}


const useTable = (initFilter: AclOriginFilter, multi: boolean, initSelectedValue?: AclOrigin | AclOrigin[]) => {


    const flynet = useContext(FlynetGeneralContext);

    // 策略是否正在加载中
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<Array<AclOrigin>>([]);
    const [originData, setOriginData] = useState<Array<AclOrigin>>([]);
    const [allData, setAllData] = useState<Array<AclOrigin>>([]);

    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [sortField, setSortField] = useState<string>('');

    // 当前页码
    const [page, setPage] = useState(1);
    // 过滤后总数据条数
    const [total, setTotal] = useState(0);

    const pageSize = 5;
    // 编辑策略组弹出框是否可见
    const [addGroupVisible, setAddGroupVisible] = useState(false);

    // 当前菜单选中策略
    const [selectedAclOrigins, setSelectedAclOrigins] = useState<AclOrigin[]>([]);

    // 是否重新加载数据
    const [reloadFlag, setReloadFlag] = useState(false);
    // 过滤参数
    const [filter, setFilter] = useState<AclOriginFilter>(initFilter)
    const columns = [
        {
            title: '策略',
            dataIndex: 'name',
            sorter: true,

            render: (_: string, record: AclOrigin) => {

                return (

                    <div className={styles.name}>
                        <div>
                            <Title heading={6}>
                                {record.name}</Title>
                            <Paragraph className='mb2'>
                                {record.description}
                            </Paragraph>
                            <div>{record.disabled ? <Tag size="small"> 禁用 </Tag> : ''}</div>
                        </div>
                    </div>
                );
            },
        },

    ];

    const handleSort = (param: any) => {
        const sorter = param.sorter;
        const dataIndex = sorter.dataIndex;
        const sortOrder = sorter.sortOrder;
        console.log(sorter)

        let sortedAllDate = [...allData];

        if (sortOrder == 'ascend') {
            setSortOrder('ascend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: AclOrigin, b?: AclOrigin) => (a && b && a.name < b.name ? 1 : -1))
            }

            setAllData(sortedAllDate);

            setData(doFilter(1, sortedAllDate, filter))
        } else if (sortOrder == 'descend') {
            setSortOrder('descend');

            if (dataIndex == 'name') {
                sortedAllDate.sort((a?: AclOrigin, b?: AclOrigin) => (a && b && a.name > b.name ? 1 : -1))
            }
            if (dataIndex == 'createdAt') {
                sortedAllDate.sort((a?: AclOrigin, b?: AclOrigin) => {
                    if (!a || !b) return 1;

                    return 1;
                });
            }


            setAllData(sortedAllDate);
            setData(doFilter(1, sortedAllDate, filter))
        } else {
            setSortOrder(undefined)
            setAllData(originData)
            setData(doFilter(1, originData, filter))

        }
        setSortField(dataIndex)



    }
    // 过滤结果
    const doFilter = (page: number, src: Array<AclOrigin>, filter: AclOriginFilter): Array<AclOrigin> => {

        if (!src || src.length == 0) {
            setTotal(src.length)
            return src.slice((page - 1) * pageSize, page * pageSize);
        }

        if (filter.query == '' && filter.role.length == 0 && filter.status == '') {
            setTotal(src.length)
            return src.slice((page - 1) * pageSize, page * pageSize);
        }
        const filteredList = src.filter(record => {
            let { query, role, status } = filter;
            query = query.trim()
            let passQuery = true, passRole = true, passStatus = true;
            if (query) {

                if (caseInsensitiveIncludes(record.name, query) ||
                    caseInsensitiveIncludes(record.description, query)
                ) {
                    passQuery = true;
                } else {
                    passQuery = false;
                }
            }


            if (status == 'enable') {
                passStatus = !record.disabled;
            } else if (status == 'disable') {
                passStatus = record.disabled;
            }

            return passQuery && passRole && passStatus;
        })

        setTotal(filteredList.length)

        return filteredList.slice((page - 1) * pageSize, page * pageSize)

    }

    const query = () => {
        setLoading(true)
        flylayerClient.listAclOrigin({
            flynetId: flynet.id
        }).then(res => {
            const acls = res.origins.filter(user => !user.disabled)

            setAllData(acls);
            let copyedData: any = []
            acls.forEach(user => {
                copyedData.push({ ...user })
            })
            setOriginData(copyedData);
            setData(doFilter(page, acls, filter));
        }).catch(err => {
            Notification.error({ content: '获取策略列表失败, 请稍后重试' })
            console.error(err)
        }).finally(() => setLoading(false))
    }

    const doPage = (p: number) => {
        setData(doFilter(p, allData, filter));
        setPage(p)

    }
    let initSelectedRowKeys: string[] = [];
    if (initSelectedValue) {
        if (initSelectedValue instanceof AclOrigin) {
            initSelectedRowKeys?.push(initSelectedValue.id + '');
        } else {
            initSelectedValue.forEach(user => {
                initSelectedRowKeys?.push(user.id + '');
            })
        }
    }

    const [selectedRowKeys, setSelectedRowKeys] = useState<String[]>(initSelectedRowKeys);
    const onRowSelect = (record: AclOrigin, selected: boolean, selectedRows: Array<AclOrigin>, e: MouseEvent) => {
        if (multi) {
            let newSelectedRowKeys: any = selectedRows.map(record => record.id + '')
            setSelectedRowKeys(newSelectedRowKeys);

            let newSelectedAclOrigins: AclOrigin[] = [];
            for (let i = 0; i < allData.length; i++) {
                const s = allData[i];
                if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                    newSelectedAclOrigins.push(s);
                }
            }
            setSelectedAclOrigins(newSelectedAclOrigins);
        } else {
            let selectKeys: any = [record.id + ''];
            setSelectedRowKeys(selectKeys);
            setSelectedAclOrigins([record])
        }
    }



    const onRow = (record: AclOrigin, index: number) => {
        return {
            onClick: () => {
                if (multi) {
                    let newSelectedRowKeys: any = selectedRowKeys;
                    if (newSelectedRowKeys.indexOf(record.id + '') >= 0) {
                        newSelectedRowKeys.splice(newSelectedRowKeys.indexOf(record.id + ''), 1);
                    } else {
                        newSelectedRowKeys.push(record.id + '');
                    }
                    setSelectedRowKeys(newSelectedRowKeys);

                    let newSelectedAclOrigins: AclOrigin[] = [];
                    for (let i = 0; i < allData.length; i++) {
                        const s = allData[i];
                        if (newSelectedRowKeys.indexOf(s.id + '') >= 0) {
                            newSelectedAclOrigins.push(s);
                        }
                    }
                    setSelectedAclOrigins(newSelectedAclOrigins);
                } else {

                    let selectKeys: any = [record.id + ''];
                    setSelectedRowKeys(selectKeys);
                    // setCurAclOrigin(record);
                    setSelectedAclOrigins([record]);
                }
            }, // 点击行

        };
    }

    useEffect(() => {
        query()
    }, []);

    useEffect(() => {
        setPage(1)
        setReloadFlag(false)
        if (reloadFlag) {
            query()
        }
    }, [reloadFlag])
    useEffect(() => {
        setPage(1)
        setData(doFilter(1, allData, filter))
    }, [filter])

    return { columns, loading, allData, data, reloadFlag, setReloadFlag, filterParam: filter, setFilterParam: setFilter, page, pageSize, doPage, setPage, total, handleSort, addGroupVisible, setAddGroupVisible, selectedRowKeys, onRowSelect, onRow, selectedAclOrigins, setSelectedAclOrigins }
}

export default useTable;