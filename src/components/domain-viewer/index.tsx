import React, { FC, useEffect, useState } from "react";
import { Typography, Button, Dropdown, List, Descriptions, Collapsible, Banner, Row, Col, Space, Card } from '@douyinfe/semi-ui';
import { IconMore, IconChevronDown, IconChevronRight } from '@douyinfe/semi-icons';
import { Record } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { GetMachineDNSConfigResponse } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import styles from './index.module.scss'
import { BASE_PATH } from "@/constants/router";

const { Title, Paragraph, Text } = Typography;


interface Props {
    flynetId: bigint,
    fullDNSConfig: GetMachineDNSConfigResponse,

    onEdit?: () => void
}


const ConfigSource: React.FC<{
    domain: string,
    record: Record,
    fullDNSConfig: GetMachineDNSConfigResponse
}> = (props) => {
    const { domain, record, fullDNSConfig } = props;

    const ConfigDisplay: React.FC<{
        label: string,
        value: string,
        nav?: string
    }> = (props) => {
        return <div style={{ marginBottom: 2 }}>
            <Space ><Text type='secondary' style={{ minWidth: 50 }}>{props.label}</Text>
                {props.nav ? <a href={props.nav} target="_blank"><Text type='tertiary' size="small">{props.value}</Text></a> : <Text type='tertiary'>{props.value}</Text>}
            </Space>
        </div>
    }

    return <>
        {fullDNSConfig.config && fullDNSConfig.config.extraRecords && fullDNSConfig.config.extraRecords.length > 0 && fullDNSConfig.config.extraRecords.map((item, index) => {
            if (item.domain === domain && item.name === record.name && item.value === record.value) {
                return <ConfigDisplay key={index} label="当前设备" value=""></ConfigDisplay>
            }
            return null;
        })}
        {fullDNSConfig.groupConfigs && fullDNSConfig.groupConfigs.length > 0 && fullDNSConfig.groupConfigs.map((groupConfig, index) => {
            if (groupConfig.config && groupConfig.config.extraRecords && groupConfig.config.extraRecords.length > 0) {
                return groupConfig.config.extraRecords.map((item, index) => {
                    if (item.domain === domain && item.name === record.name && item.value === record.value) {
                        let groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;
                        return <ConfigDisplay key={index} label="设备组"
                            nav={`${BASE_PATH}/devices/group/?query=${groupDisplay}`}
                            value={groupDisplay || ''}></ConfigDisplay>
                    }
                    return null;
                })
            }
            return null;
        })}
        {fullDNSConfig.userConfig && fullDNSConfig.userConfig.config && fullDNSConfig.userConfig.config.extraRecords && fullDNSConfig.userConfig.config.extraRecords.length > 0 && fullDNSConfig.userConfig.config.extraRecords.map((item, index) => {
            if (item.domain === domain && item.name === record.name && item.value === record.value) {
                return <ConfigDisplay key={index} label="用户"
                    nav={`${BASE_PATH}/users/${fullDNSConfig.userConfig?.user?.loginName}`}
                    value={fullDNSConfig.userConfig?.user?.displayName || ''}></ConfigDisplay>
            }
            return null;
        })}
        {fullDNSConfig.userGroupConfigs && fullDNSConfig.userGroupConfigs.length > 0 && fullDNSConfig.userGroupConfigs.map((userGroupConfig, index) => {
            if (userGroupConfig.config && userGroupConfig.config.extraRecords && userGroupConfig.config.extraRecords.length > 0) {
                return userGroupConfig.config.extraRecords.map((item, index) => {
                    if (item.domain === domain && item.name === record.name && item.value === record.value) {
                        let groupDisplay = userGroupConfig.group?.alias ? userGroupConfig.group?.alias : userGroupConfig.group?.name;
                        return <ConfigDisplay key={index} label="用户组"
                            nav={`${BASE_PATH}/users/group/?query=${groupDisplay}`}
                            value={groupDisplay || ''}></ConfigDisplay>
                    }
                    return null;
                })
            }
            return null;
        })}
        {fullDNSConfig.flynetConfig && fullDNSConfig.flynetConfig.extraRecords && fullDNSConfig.flynetConfig.extraRecords.length > 0 && fullDNSConfig.flynetConfig.extraRecords.map((item, index) => {
            if (item.domain === domain && item.name === record.name && item.value === record.value) {
                return <ConfigDisplay key={index} label="全局" value=""></ConfigDisplay>
            }
            return null;
        })}

    </>
}


const SearchdomainItem: React.FC<{
    domain: string,
    extraRecords: Array<Record>,
    fullDNSConfig: GetMachineDNSConfigResponse
}> = (props) => {
    const { domain, extraRecords } = props;
    // 是否折叠
    const [isCollapsible, setIsCollapsible] = useState(false);

    return <List.Item key={domain} className={styles.ipListItem}>
        <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
            <span style={{ display: 'flex', alignItems: 'center', flexGrow: 1, paddingRight: '20px' }}
                className={styles.toggleCollapsible}
            ><span style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', color: 'var(--semi-color-text-1)' }} onClick={() => setIsCollapsible(!isCollapsible)}>{isCollapsible ? <IconChevronDown /> : <IconChevronRight />}&nbsp;{domain}</span></span>
        </div>
        <Collapsible isOpen={isCollapsible} style={{ width: '100%' }}>
            <div style={{ paddingTop: 10 }}>
                {extraRecords.map((record: Record, index: number) => {
                    return <Row key={index} className={styles.dnsRow}>
                        <Col span={12}>
                            <Space>
                                <Text type='tertiary' style={{ marginRight: 20, minWidth: 100 }}>{record.name}</Text>
                                <Paragraph style={{ marginRight: 20 }}>
                                    {record.type === 'A' ? 'A记录' : ''}
                                    {record.type === 'CNAME' ? 'CNAME' : ''}
                                    </Paragraph>
                                <Paragraph>{record.value}</Paragraph>
                            </Space>
                        </Col>
                        <Col span={12}>
                            <ConfigSource domain={record.domain} record={record} fullDNSConfig={props.fullDNSConfig}></ConfigSource>
                        </Col>
                    </Row>
                })}
            </div>
        </Collapsible>
    </List.Item>
}

const Index: FC<Props> = (props) => {

    // 自定义域名解析数据
    let initSearchDomains: Array<{
        domain: string,
        extraRecords: Array<Record>
    }> = [];

    // 编辑自定义域名解析数据
    const [editSearchdomainData, setEditSearchdomainData] = useState<{
        domain: string,
        extraRecords: {
            name: string,
            value: string,
            type: string
        }[]
    }>({
        domain: '',
        extraRecords: []
    });

    props.fullDNSConfig.finalConfig?.extraRecords.forEach(record => {
        let find = false;
        initSearchDomains.forEach(searchDomain => {
            if (record.domain === searchDomain.domain) {
                searchDomain.extraRecords.push(record);
                find = true;
            }
        });
        if (!find) {
            initSearchDomains.push({
                domain: record.domain,
                extraRecords: [record]
            })
        }
    })

    // 自定义域名解析数据
    const [searchDomains, setSearchDomains] = useState<{
        domain: string,
        extraRecords: Array<Record>
    }[]>(initSearchDomains);

    useEffect(() => {
        let searchDomains: {
            domain: string,
            extraRecords: Array<Record>
        }[] = [];
        props.fullDNSConfig.finalConfig?.extraRecords.forEach(record => {
            let find = false;
            searchDomains.forEach(searchDomain => {
                if (record.domain === searchDomain.domain) {
                    searchDomain.extraRecords.push(record);
                    find = true;
                }
            });
            if (!find) {
                searchDomains.push({
                    domain: record.domain,
                    extraRecords: [record]
                })
            }
        })
        setSearchDomains(searchDomains);
    }, [props.fullDNSConfig])


    // 
    const handleEdit = () => {
        if (props.onEdit) {
            props.onEdit();
        }
    }

    return <>
        <section className="mb40" style={{}}>
            <Row>
                <Col span={18}>
                    <Title heading={4} className="mb2">自定义域名解析</Title>
                    <Paragraph type='tertiary' className='mb10'>查看本设备域名解析结果</Paragraph>
                </Col>
                <Col span={6}>
                    <div className="btn-right-col">
                        <Button theme="solid" onClick={handleEdit}>修改当前设备自定义域名解析</Button>
                    </div>
                </Col>
            </Row>
            {searchDomains.length == 0 ? <Banner className={styles.banner} type="info" description="您还没有设置自定义域名解析" closeIcon={null} ></Banner> :
                <List className={styles.ipList}>
                    {searchDomains.map((item, index) => {
                        return <SearchdomainItem
                            key={index}
                            domain={item.domain}
                            extraRecords={item.extraRecords}

                            fullDNSConfig={props.fullDNSConfig}
                        ></SearchdomainItem>
                    })}
                </List>
            }


        </section>

    </>
}

export default Index;