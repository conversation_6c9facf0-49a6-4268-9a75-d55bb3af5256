import { FC, useState } from 'react';
import { Cascader, Checkbox, Divider, Dropdown, Button, TreeSelect } from '@douyinfe/semi-ui'


import  useServicesGroup  from '@/pages/services/useServicesGroup';

interface GroupTreeData {
    label: string,
    value: string,
    key: string
    parentId?: string,
    children: Array<GroupTreeData>
}
export const getGroupDisplayValue = (value: string, mapTreeData?: Map<string, GroupTreeData>) => {

    let values = value.split(',');
    let displayValues: Array<string> = [];
    values.forEach(val => {
        if (mapTreeData) {
            let treeItem = mapTreeData.get(val)
            if (treeItem)
                displayValues.push(treeItem.label)
        }
    })
    return displayValues.join(',');
}


const GroupSelector: FC<{ value: string, onChange: (val: string) => void }> = (props) => {
    const { value, onChange } = props;
    const [checkedList, setCheckedList] = useState<string[]>(value.split(','));

    const { serviceGroupTreeData, getMapTreeData } = useServicesGroup();
    const onGroupChange = (val: string[]) => { setCheckedList(val); };
    return <><TreeSelect
        style={{ width: '100%' }}
        className='mb10'
        zIndex={3000}
        multiple
        checkRelation='unRelated'
        expandAll
        value={checkedList}
        onChange={(val) => {
            setCheckedList(val as string[]);
        }}
        treeData={
            serviceGroupTreeData
        }
        dropdownStyle={{ maxHeight: 300, minWidth: '400px', overflow: 'auto' }}
    ></TreeSelect>
        <Divider className='mb10'></Divider>
        <Button block onClick={() => {
            let newCheckedList = checkedList.filter((item) => item != '');
            onChange(newCheckedList.join(','))
        }}>应用</Button>
    </>
}

export default GroupSelector;