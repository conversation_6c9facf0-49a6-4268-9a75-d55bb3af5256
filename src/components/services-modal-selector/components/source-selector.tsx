import { FC } from 'react';
import { Select, Col, Input, Dropdown, Button, Space } from '@douyinfe/semi-ui'


export const getSourceDisplayValue = (value: string) => {
    if(value == 'Config') {
        return '系统配置';
    } else if(value == 'Detect') {
        return '自动发现';
    }
    return value;
}

const SourceSelector: FC<{ value: string, onChange: (val: string) => void }> = (props) => {
    const { value, onChange } = props;
    return <Select value={value}
    zIndex={9999}
        onChange={(value) => { onChange(value as string) }}
        style={{ width: '100%' }}>
        <Select.Option value='Config'>系统配置</Select.Option>
        <Select.Option value='Detect'>自动发现</Select.Option>

    </Select>
}

export default SourceSelector;