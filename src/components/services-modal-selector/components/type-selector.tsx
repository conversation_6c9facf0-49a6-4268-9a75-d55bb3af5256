import { FC, useState } from 'react';
import { CheckboxGroup, Checkbox, Divider, Cascader, Button, Space } from '@douyinfe/semi-ui'


export const getTypeDisplayValue = (value: string) => {
    let values = value.split(',');
    let displayValues = [];
    if (values.indexOf('1') >= 0) {
        displayValues.push('远程桌面');
    }
    if (values.indexOf('2') >= 0) {
        displayValues.push('系统服务');
    }
    return displayValues.join(',');
}


const TypeSelector: FC<{ value: string, onChange: (val: string) => void }> = (props) => {
    const { value, onChange } = props;

    const [checkedList, setCheckedList] = useState<string[]>(value.split(','));

    return <><CheckboxGroup className='mb10' style={{ width: '100%' }} value={checkedList} onChange={value => {
        setCheckedList(value);
    }}>
        {/* <Checkbox checked={value.indexOf('APP') >= 0} value="APP">应用</Checkbox> */}
        {/* <Checkbox checked={value.indexOf('1') >= 0} value="1">远程桌面</Checkbox> */}
        <Checkbox checked={value.indexOf('2') >= 0} value="2">系统服务</Checkbox>
    </CheckboxGroup>
        <Divider className='mb10'></Divider>
        <Button block onClick={() => {
            let newCheckedList = checkedList.filter((item) => item != '');
            onChange(newCheckedList.join(','))
        }}>应用</Button>
    </>
}

export default TypeSelector;