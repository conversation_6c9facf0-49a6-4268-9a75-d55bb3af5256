import { useEffect, useState, FC, ReactNode } from 'react';
import { Row, Col, Popover, Dropdown, Button, Space, Typography, Divider } from '@douyinfe/semi-ui'
import { IconClose, IconArrowDown, IconArrowUp, IconMinusCircle, IconMore, IconPlusCircle, IconChevronLeft, IconAlignTop, IconAlignBottom, IconEdit } from '@douyinfe/semi-icons';

import { AclOrigin_Resource, AclOrigin_ResourceType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/acl_pb';
import TableEmpty from '@/components/table-empty'
import styles from './index.module.scss';
import { uniqueAclOriginResource } from '@/utils/acl';
// 选择器中的单个元素
export interface SelectItemParam {
    name: string,
    label: string,
    placeholder: string,
    value: AclOrigin_Resource,
    optList?: Array<{
        value: string,
        label: string
    }>,
    // 单选组件
    selectComponent?: FC<{
        value: AclOrigin_Resource,
        existValues: AclOrigin_Resource[],
        onChange: (val: AclOrigin_Resource) => void,
        optList?: Array<{
            value: string,
            label: string
        }>,

    }>,
    // 多选组件
    multiSelectComponent?: FC<{
        type: AclOrigin_ResourceType,
        existValues: AclOrigin_Resource[],
        onChange: (val: AclOrigin_Resource[]) => void,
        optList?: Array<{
            value: string,
            label: string
        }>,
    }>,
    funGetDisplayValue?: (val: AclOrigin_Resource) => string
}

const {
    Title,
    Paragraph,
    Text
} = Typography
const SelectItem: FC<{
    className?: string,
    styles?: React.CSSProperties,
    selectItemParam: SelectItemParam | undefined,
    back: Function,
    type: AclOrigin_ResourceType,
    existValues: AclOrigin_Resource[],

    onChange: (val: AclOrigin_Resource|AclOrigin_Resource[], filterParam: SelectItemParam) => void
}> = (props) => {
    const { selectItemParam: filterParam, } = props;
    return <>
        <Row>
            <Col span={20}>
            </Col>
            <Col span={4}>
            </Col>
        </Row>
        <div className={styles.filterItem}>
            <div className={styles.header}>
                <Button
                    style={{ marginRight: 10 }}
                    icon={<IconChevronLeft />}
                    size='small' onClick={() => props.back()} />
                <span>{filterParam?.label}</span>
            </div>
            <div className={styles.body}>
                {filterParam?.multiSelectComponent ? 
                <filterParam.multiSelectComponent
                    type={props.type}
                    optList={filterParam.optList}
                    existValues={props.existValues}
                    onChange={val=>{
                        props.onChange(val, filterParam);
                        props.back();
                    }}
                ></filterParam.multiSelectComponent> : filterParam?.selectComponent ? 
                <filterParam.selectComponent
                    value={filterParam.value}
                    existValues={props.existValues}
                    optList={filterParam.optList}
                    onChange={(val: AclOrigin_Resource) => { props.onChange(val, filterParam); props.back() }} /> : null}

            </div>
        </div>
    </>
}

const EditItem: FC<{
    className?: string,
    styles?: React.CSSProperties,
    selectItemParam: SelectItemParam | undefined,
    close: Function,
    editAcl: AclOrigin_Resource,
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource, filterParam: SelectItemParam) => void
}> = (props) => {
    const { selectItemParam: filterParam } = props;
    return <>
        <div className={styles.filterItem}>
            <div className={styles.header}>
                <Button
                    style={{ marginRight: 10 }}
                    icon={<IconClose />}
                    size='small' onClick={() => props.close()} />
                <span>{filterParam?.label}</span>
            </div>
            <div className={styles.body}>
                {
                    filterParam?.selectComponent && props.editAcl && <filterParam.selectComponent
                        value={props.editAcl}
                        existValues={props.existValues}
                        optList={filterParam.optList}
                        onChange={(val: AclOrigin_Resource) => { props.onChange(val, filterParam); props.close() }} />}
            </div>
        </div>
    </>
}

const Index: FC<{
    label: string,
    className?: string,
    styles?: React.CSSProperties,
    selectItems: SelectItemParam[]
    getDisplayValue?: (val: AclOrigin_Resource) => string,
    value: AclOrigin_Resource[],
    existValues: AclOrigin_Resource[],
    onChange: (val: AclOrigin_Resource[], selectItem: SelectItemParam) => void
}> = (props) => {
    const [currentSelect, setCurrentSelect] = useState<SelectItemParam>();
    const [step, setStep] = useState(0);
    const [values, setValues] = useState<AclOrigin_Resource[]>(props.value ? props.value : []);


    const [showEdit, setShowEdit] = useState(false);
    const [editAcl, setEditAcl] = useState<AclOrigin_Resource>();
    const [editSelectItemParam, setEditSelectItemParam] = useState<SelectItemParam>();
    const [eidtIndex, setEditIndex] = useState<number>(-1);

    return <div className={styles.className} style={props.styles}>
        <Row className='mb10'>
            <Col span={20}>
                <Paragraph style={{ lineHeight: '30px' }}>{props.label}</Paragraph>
            </Col>
            <Col span={4} className={styles.rightColumn}>
                <Dropdown
                    visible={step > 0}
                    style={{width:285}}
                    trigger='click'
                    position='bottomRight'
                    onVisibleChange={(visible) => {
                        if (!visible) {
                            setStep(0);
                        } else {
                            setStep(1);
                        }
                    }}
                    render={
                        currentSelect ? <SelectItem
                            existValues={props.existValues}
                            type={currentSelect.value.type}
                            back={() => { setCurrentSelect(undefined) }}
                            selectItemParam={currentSelect} onChange={(val, selectItem) => {
                                if(Array.isArray(val)) {
                                    
                                    let newValues = uniqueAclOriginResource([...values, ...val]);
                                    setValues(newValues);
                                    props.onChange(newValues, selectItem);
                                }else if (val.type === AclOrigin_ResourceType.USER) {
                                    if (val.value.indexOf(',') > -1) {
                                        const newValues = uniqueAclOriginResource([...values, ...val.value.split(',').map((v) => {
                                            return new AclOrigin_Resource({
                                                type: AclOrigin_ResourceType.USER,
                                                value: v
                                            })
                                        })]);

                                        setValues(newValues);

                                        props.onChange(newValues, selectItem)
                                    } else {
                                        const newValues = uniqueAclOriginResource([...values, val]);
                                        setValues(newValues);

                                        props.onChange(newValues, selectItem)
                                    }

                                } else {
                                    const newValues = uniqueAclOriginResource([...values, val]);
                                    setValues(newValues);

                                    props.onChange(newValues, selectItem)
                                }

                                setStep(-1);
                            }} ></SelectItem> : <Dropdown.Menu>
                            {
                                props.selectItems.map((item, index) => {
                                    return <Dropdown.Item style={{ minWidth: 180 }} key={index} onClick={(e) => {
                                        e.stopPropagation()
                                        setStep(2);
                                        setCurrentSelect(item)
                                    }}>{item.label}</Dropdown.Item>
                                })
                            }
                        </Dropdown.Menu>
                    }>
                    <Button icon={<IconPlusCircle />}></Button>
                </Dropdown>
            </Col>
        </Row>
        <Divider className='mb10'></Divider>
        <div className={styles.mainArea}>
            {showEdit && editAcl && <div className={styles.editArea}>
                <EditItem
                    editAcl={editAcl}
                    existValues={props.existValues}
                    close={() => {
                        setShowEdit(false);
                        setEditAcl(undefined)
                        setEditSelectItemParam(undefined)
                    }}
                    selectItemParam={editSelectItemParam} onChange={(val, selectItem) => {
                        let newValues = [...values];
                        newValues[eidtIndex] = val;
                        newValues = uniqueAclOriginResource(newValues);
                        setValues(newValues);
                        props.onChange(newValues, selectItem)
                    }} ></EditItem>
            </div>}
            {values.length > 0 ? <>
                {values.filter((val)=>{
                    let isDisplay = false;
                    if(props.getDisplayValue && props.getDisplayValue(val)) {
                        isDisplay = true;
                    }
                    return isDisplay;
                }).map((val, index) => {

                    return <Row key={index} style={{ marginBottom: 10 }}>
                        <Col span={20}>
                            <Paragraph
                                ellipsis={{ rows: 1, expandable: false }}
                                style={{ lineHeight: '32px', overflowWrap: 'anywhere' }}
                                title={props.getDisplayValue ? props.getDisplayValue(val) : val.value}>
                                {props.getDisplayValue ? props.getDisplayValue(val) : val.value}
                            </Paragraph>
                        </Col>
                        <Col span={4} className={styles.rightColumn}>
                            <Popover
                                position='left'
                                style={{
                                    padding: 5,
                                }}
                                clickToHide
                                content={<>
                                    <Space>
                                        <Button icon={<IconAlignTop />} disabled={index == 0}
                                            onClick={() => {
                                                if (index > 0) {
                                                    const newValues = [...values];
                                                    const temp = newValues[index];
                                                    newValues[index] = newValues[0];
                                                    newValues[0] = temp;
                                                    setValues(newValues);
                                                    props.onChange(newValues, props.selectItems[index])
                                                }
                                            }}
                                        >
                                        </Button>
                                        <Button icon={<IconArrowDown />}
                                            onClick={() => {
                                                if (index < values.length - 1) {
                                                    const newValues = [...values];
                                                    const temp = newValues[index];
                                                    newValues[index] = newValues[index + 1];
                                                    newValues[index + 1] = temp;
                                                    setValues(newValues)
                                                    props.onChange(newValues, props.selectItems[index])
                                                }
                                            }}
                                            disabled={index === values.length - 1}
                                        ></Button>
                                        <Button icon={<IconArrowUp />}
                                            onClick={() => {
                                                if (index > 0) {
                                                    const newValues = [...values];
                                                    const temp = newValues[index];
                                                    newValues[index] = newValues[index - 1];

                                                    newValues[index - 1] = temp;
                                                    setValues(newValues);
                                                    props.onChange(newValues, props.selectItems[index])
                                                }
                                            }}
                                            disabled={index === 0}
                                        ></Button>
                                        <Button icon={<IconAlignBottom />} disabled={index == values.length - 1}
                                            onClick={() => {
                                                if (index < values.length - 1) {

                                                    const newValues = [...values];
                                                    const temp = newValues[index];
                                                    newValues[index] = newValues[values.length - 1];
                                                    newValues[values.length - 1] = temp;
                                                    setValues(newValues)
                                                    props.onChange(newValues, props.selectItems[index])
                                                }
                                            }}
                                        >
                                        </Button>

                                        <Button
                                            icon={<IconEdit />}
                                            onClick={() => {
                                                setShowEdit(true);

                                                props.selectItems.forEach((item) => {
                                                    if (item.value.type === val.type) {
                                                        setEditSelectItemParam(item);
                                                    }
                                                })
                                                setEditIndex(index);
                                                setEditAcl(val);
                                            }}
                                        ></Button>
                                        <Button
                                            type='danger'
                                            theme='borderless'
                                            icon={<IconMinusCircle />}
                                            onClick={() => {
                                                // 删除index位置的元素
                                                const newValues = [...values];
                                                newValues.splice(index, 1)
                                                if (newValues.length > 0) {
                                                    setValues(newValues);
                                                } else {
                                                    setValues([]);
                                                }

                                                props.onChange(newValues, props.selectItems[index])
                                            }}
                                        />
                                    </Space>
                                </>}><Button style={{}} icon={<IconMore />}></Button></Popover>
                        </Col>
                    </Row>
                })}

            </> : <TableEmpty loading={false}></TableEmpty>}
        </div>
    </div>
}

export default Index;
