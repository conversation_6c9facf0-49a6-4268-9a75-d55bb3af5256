.filterItem {
    width: 285px;
    .header {
        display: flex;
        align-items: center;
        border-bottom: 1px solid var(--semi-color-border);
        padding: 10px;
    }
    .body {
        padding: 10px;
    }
    .footer {
        border-top: 1px solid var(--semi-color-border);
        padding: 5px;
    }
}


.rightColumn {
    display: flex!important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0!important;
}

.mainArea {
    min-height: 300px;
    max-height: calc(100vh - 400px);
    overflow-y: auto;
    
}
.editArea {
    position: absolute;
    // background-color: #fff;
    z-index: 9999;

    box-sizing: border-box;
    flex-direction: column;
    background-color: var(--semi-color-bg-2);
    border: 1px solid var(--semi-color-border);
    border-radius: var(--semi-border-radius-small);
    padding: 0 0;
    background-clip: padding-box;
    overflow: hidden;
    box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.3), 0px 4px 14px rgba(0, 0, 0, 0.1);
}