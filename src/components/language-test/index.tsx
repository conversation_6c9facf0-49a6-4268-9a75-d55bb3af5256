import React from 'react';
import { Card, Space, Typography, Button } from '@douyinfe/semi-ui';
import { LocaleFormatter, useLocale } from '@/locales';
import { useLocaleSwitch } from '@/hooks/useLocale';

const { Title, Text } = Typography;

/**
 * 语言切换测试组件
 * Language switching test component
 */
const LanguageTest: React.FC = () => {
    const { locale, switchLocale } = useLocaleSwitch();
    const { formatMessage } = useLocale();

    return (
        <Card title="语言切换测试 / Language Switch Test" style={{ margin: '20px' }}>
            <Space vertical align="start" spacing="loose">
                <div>
                    <Title heading={4}>当前语言 / Current Language: {locale}</Title>
                </div>
                
                <div>
                    <Title heading={5}>导航菜单测试 / Navigation Menu Test:</Title>
                    <Space wrap>
                        <Text><LocaleFormatter id="nav.overview" /></Text>
                        <Text><LocaleFormatter id="nav.services" /></Text>
                        <Text><LocaleFormatter id="nav.policies" /></Text>
                        <Text><LocaleFormatter id="nav.logs" /></Text>
                        <Text><LocaleFormatter id="nav.devices" /></Text>
                        <Text><LocaleFormatter id="nav.users" /></Text>
                        <Text><LocaleFormatter id="nav.resources" /></Text>
                        <Text><LocaleFormatter id="nav.settings" /></Text>
                        <Text><LocaleFormatter id="nav.networks" /></Text>
                    </Space>
                </div>

                <div>
                    <Title heading={5}>工具提示测试 / Tooltip Test:</Title>
                    <Space wrap>
                        <Text><LocaleFormatter id="tooltip.help" /></Text>
                        <Text><LocaleFormatter id="tooltip.theme" /></Text>
                        <Text><LocaleFormatter id="tooltip.darkMode" /></Text>
                        <Text><LocaleFormatter id="tooltip.lightMode" /></Text>
                        <Text><LocaleFormatter id="tooltip.language" /></Text>
                    </Space>
                </div>

                <div>
                    <Title heading={5}>通用文本测试 / Common Text Test:</Title>
                    <Space wrap>
                        <Text><LocaleFormatter id="common.expired" /></Text>
                        <Text><LocaleFormatter id="common.expiresSoon" /></Text>
                        <Text><LocaleFormatter id="common.accountExpired" /></Text>
                        <Text><LocaleFormatter id="common.clickToRenew" /></Text>
                        <Text><LocaleFormatter id="common.expireTime" /></Text>
                    </Space>
                </div>

                <div>
                    <Title heading={5}>语言选项测试 / Language Options Test:</Title>
                    <Space>
                        <Text><LocaleFormatter id="language.chinese" /></Text>
                        <Text><LocaleFormatter id="language.english" /></Text>
                    </Space>
                </div>

                <div>
                    <Title heading={5}>使用 formatMessage Hook:</Title>
                    <Text>{formatMessage({ id: 'nav.overview' })} - {formatMessage({ id: 'tooltip.help' })}</Text>
                </div>

                <div>
                    <Title heading={5}>语言切换按钮 / Language Switch Buttons:</Title>
                    <Space>
                        <Button 
                            type={locale === 'zh_CN' ? 'primary' : 'secondary'}
                            onClick={() => switchLocale('zh_CN')}
                        >
                            切换到中文
                        </Button>
                        <Button 
                            type={locale === 'en_GB' ? 'primary' : 'secondary'}
                            onClick={() => switchLocale('en_GB')}
                        >
                            Switch to English
                        </Button>
                    </Space>
                </div>
            </Space>
        </Card>
    );
};

export default LanguageTest;
