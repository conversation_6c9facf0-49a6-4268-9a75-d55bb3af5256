import React, { FC, useCallback, useContext, useEffect, useState } from "react";
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { Avatar, Notification, Tag, Select } from '@douyinfe/semi-ui';

import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { debounce } from "lodash";
import { renderCustomOption } from "@/utils/user";
import { useLocale } from '@/locales';
interface Props {
    value: string[];
    onChange: (value: string[]) => void;
    onLoadingChange(boolean: boolean): void;
    style?: React.CSSProperties;
    insetLabel?: string;
    noLabel?: boolean;
    field?:string;
    maxTagCount?:number
    className?: string
}

// 用户组件
const UserSelector: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const field = props.field ? props.field : 'id';
    const maxTagCount = props.maxTagCount ? props.maxTagCount : 1;
    const flynet = useContext(FlynetGeneralContext);
    // 用户列表
    const [users, setUsers] = useState<User[]>();
    // 所有用户列表
    const [allUsers, setAllUsers]  = useState<User[]>();

    // 是否正在加载用户数据 
    const [loading, setLoading] = useState(false);

    const queryUser = async (filterKeywords?: string) => {
        try {
            const res = await flylayerClient.listUsers({
                flynetId: flynet?.id,
                query: filterKeywords ? `keywords=${encodeURIComponent(filterKeywords)}&limit=20` : 'limit=20'
            })
            setUsers(res.users.filter(user => !user.disabled));
        } catch (err) {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'userSelector.fetchFailed' }) })
        }
    }

    const debounceQuery = useCallback(debounce((queryStr) => queryUser(queryStr), 500), []);

    // 查询用户数据
    const queryActor =  (val: string) => {
        debounceQuery(val);
    }

    const [value, setValue] = useState(props.value);

    

    useEffect(() => {
        setLoading(true);
        props.onLoadingChange(true)
        flylayerClient.listUsers({
            flynetId: flynet?.id,
            query: 'limit=20&offset=0'
        }).then((res) => {
            const users = res.users.filter(user => !user.disabled)
            
            setUsers(users);
            setAllUsers(users)
        }).catch((err) => {
            console.error(err)
            Notification.error({ content: formatMessage({ id: 'userSelector.fetchFailed' }) })
        }).finally(() => {
            setLoading(false);
            props.onLoadingChange(false)
        })
    }, []);

    const renderSelectedItem = (optionNode: any, { onClose }: any) => {
        const item: User = optionNode as User;

        const content = (
            <Tag avatarSrc={item.avatarUrl}
                avatarShape="square" closable={true} onClose={onClose} size="large">
                {item.displayName}
            </Tag>
        );
        return {
            isRenderInTag: false,
            content,
        };
    };

    useEffect(()=>{
        setValue(props.value)
    }, [props.value]);

    // const renderCustomOption = (item: User, index: number) => {

    //     const optionStyle = {
    //         display: 'flex',
    //         paddingLeft: 24,
    //         paddingTop: 10,
    //         paddingBottom: 10,
    //     };
    //     return (
    //         <Select.Option value={(item as any)[field] + ''} style={optionStyle} showTick={true} key={index + 1} {...item}>
    //             <Avatar size="extra-small" src={item.avatarUrl} />
    //             <div style={{ marginLeft: 8 }}>
    //                 <div style={{ fontSize: 14 }}>{item.displayName}</div>
    //             </div>
    //         </Select.Option>
    //     );
    // };
    return <Select
        insetLabel= { props.noLabel ? '' :  props.insetLabel ? props.insetLabel : formatMessage({ id: 'userSelector.operator' })}
        filter
        remote
        showClear
        multiple
        value={value}
        style={props.style}
        className={props.className}
        onSearch={queryActor}
        position='bottom'
        renderSelectedItem={renderSelectedItem}
        maxTagCount={maxTagCount}
        virtualize={{ itemSize: 44 }}
        loading={loading}
        onChange={(value: any) => {
            setValue(value)
            props.onChange(value);
        }}
        emptyContent={<>{formatMessage({ id: 'userSelector.noUsers' })}</>}
    >

        {users?.map((user, index) => renderCustomOption(user, index, field, false))}
    </Select>
}

export default UserSelector;