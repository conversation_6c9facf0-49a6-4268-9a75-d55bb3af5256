import { FC, ReactNode, useState, useContext } from 'react';
import { Typography, Notification, Popover, Spin } from '@douyinfe/semi-ui';
import { User } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';

import Handlebars from 'handlebars';

import DOMPurify from 'dompurify';
import { flylayerClient } from '@/services/core';
import { getUserDisplayName } from '@/utils/user';

const { Paragraph, Text } = Typography;

const mapUser : Map<string, ReactNode> = new Map();
const mapLoginName : Map<string, string> = new Map();
const Index: FC<{
    loginName: string,
    displayName: string,
    templateUserListTitle: string
}> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    // 用户加载标识，用于骨架屏
    const [userLoading, setUserLoading] = useState(false);
    const [ele, setEle] = useState<ReactNode>();
    const [loginName, setLoginName] = useState<string>();
    const query = () => {
        if(mapUser.has(props.loginName)) {
            setEle(mapUser.get(props.loginName));
            setLoginName(mapLoginName.get(props.loginName));
            return;
        }

        setUserLoading(true);
        flylayerClient.getUser({
            flynetId: flynetGeneral.id,
            identifier: {
                case: 'loginName',
                value: props.loginName
            }
        }).then(res => {
            if (res.user) {
                let ele = getUserDisplayName(res.user, props.templateUserListTitle);
                mapUser.set(props.loginName, ele);
                mapLoginName.set(props.loginName, res.user.loginName);
                setEle(ele);
                setLoginName(res.user.loginName);
            }
        }).catch(err => {
            console.error(err)
        }).finally(() => {
            setUserLoading(false);
        });
    }

    return <Popover
        onVisibleChange={(visible) => {
            if (visible && !ele) {
                query();
            }
        }}
        content={<div className='p10'>{ ele ?
            <>{ele}
            <Paragraph ellipsis={true}>{loginName}</Paragraph>
            </> : <Spin spinning={userLoading} />
        }</div>}>
        <Text ellipsis={true}
            style={{ maxWidth: 240, cursor: 'pointer' }}>{props.displayName}</Text>
    </Popover>;
};

export default Index;