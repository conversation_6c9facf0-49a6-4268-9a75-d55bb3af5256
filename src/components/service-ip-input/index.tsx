import { FC, useState } from 'react'
import { withField, Input, InputGroup, Select, Button } from '@douyinfe/semi-ui';
import { IconPlusCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import MachineSelector from '@/components/machine-selector'
import { Machine } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb'
import MachineViewer from '@/components/machine-viewer';

import { RoutingPoolType } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/services_pb';

const ServiceIpInput: FC<{ value: string, onChange?: (value: string) => void }> = (props) => {
    const { value, onChange } = props;

    const [ipInput, setIpInput] = useState(value);

    const [inputType, setInputType] = useState<RoutingPoolType>(RoutingPoolType.VIRTUAL_IP);

    const [machineSelectorVisible, setMachineSelectorVisible] = useState(false);

    const [nodeViewerVisible, setNodeViewerVisible] = useState(false);

    return <><InputGroup>
        {inputType === RoutingPoolType.VIRTUAL_IP ? <>
            <Input
                value={ipInput}
                style={{ width: 156 }}
                readonly
                placeholder="请选择IP"
            />
            <Button icon={<IconArrowUpRight />} disabled={!ipInput} onClick={() => setNodeViewerVisible(true)}></Button>
            <Button icon={<IconPlusCircle />} onClick={() => setMachineSelectorVisible(true)}></Button>
        </> : <Input
            value={ipInput}
            style={{ width: 220 }}
            onChange={(val) => {
                setIpInput(val);
                onChange && onChange(val);
            }}
            placeholder="请输入IP"
        />}

        <Select
            style={{ width: 120 }}
            value={inputType}
            onChange={(val) => {
                const value = val as RoutingPoolType;
                setInputType(value);
            }}
        >
            <Select.Option value={RoutingPoolType.VIRTUAL_IP}>零信任组网</Select.Option>
            <Select.Option value={RoutingPoolType.LOCAL_IP}>本地网络</Select.Option>
            <Select.Option value={RoutingPoolType.PUBLIC_IP}>公共网络</Select.Option>
        </Select>
    </InputGroup>
        {machineSelectorVisible && <MachineSelector
            multi={false}
            value={ipInput ? [ipInput] : []}
            onChange={(val) => {
                if (val instanceof Machine) {
                    setIpInput(val.ipv4);
                    onChange && onChange(val.ipv4);
                }
                setMachineSelectorVisible(false);
            }}
            close={() => setMachineSelectorVisible(false)}
        />}
        {nodeViewerVisible && ipInput && <MachineViewer ip={ipInput} close={() => setNodeViewerVisible(false)}></MachineViewer>}

    </>
}

const FormServiceIpInput = withField(ServiceIpInput);

export default FormServiceIpInput;