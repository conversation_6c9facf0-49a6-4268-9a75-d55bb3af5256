import React, { FC, useState, useEffect } from 'react'
import { Card, Input, Select, Typography, Notification, Spin, Space, Button, Row, Col, Table, BackTop, Tag, Modal } from '@douyinfe/semi-ui';
const { Paragraph, Title } = Typography;

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { getMachine, setMachineKeyExpiry, authorizeMachine } from '@/services/device';
import { flylayerClient } from '@/services/core';
import { AdvertisedRoute } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";

interface Props {
    ip: string,
    close: () => void,
}


const Index: FC<Props> = (props) => {

    // 设备加载标识，用于骨架屏
    const [deviceLoading, setDeviceLoading] = useState(false);
    const [device, setDevice] = useState<Machine>()

    const [advertisedRoutes, setAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);

    const query = () => {
        setDeviceLoading(true)
        getMachine(props.ip).then((machine: Machine) => {
            setDevice(machine)
            setDeviceLoading(false)

            // setAdvertisedRoutes(machine.advertisedRoutes)

            // flylayerClient.getMachineRoutes({ machineId: machine.id }).then(res => {
            //     if (res.routes && res.routes.advertisedRoutes) {
            //         setAdvertisedRoutes(res.routes.advertisedRoutes)
            //     } else {
            //         setAdvertisedRoutes([])
            //     }
            // }).catch
            //     (err => {
            //         console.error(err)
            //         Notification.error({ content: '获取设备路由信息失败，请稍后重试', position: "bottomRight" })
            //     })
        }, err => {
            console.error(err)
            Notification.error({ content: '获取设备失败，请稍后重试', position: "bottomRight" })
        })
    }

    useEffect(() => {
        query()
    }, [])
    return <>
        <Modal
            width={430}
            title="查看子网路由"
            visible={true}
            onOk={() => { }}
            onCancel={props.close}
            footer={null}
            className='semi-modal'
        >
            <Title className='mb20' heading={5}>设备IP：{props.ip}</Title>
            {device?.advertisedRoutes.length == 0 ? <Card className='mb40'>这台设备没有宣告任何路由</Card> :
                <Card className='mb40'>
                    {device?.advertisedRoutes.map((route: string, index: number) => {
                        return <Paragraph key={index} >
                            <span style={{ display: 'inline-block', width: 200 }}>{route}</span> 
                        </Paragraph>;
                    })}
                </Card>}</Modal>
    </>
}

export default Index;