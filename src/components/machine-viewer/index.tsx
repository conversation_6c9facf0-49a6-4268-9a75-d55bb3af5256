import React, { FC, useState, useEffect } from 'react'
import { Card, Input, Select, Typography, Notification, Spin, Space, Button, Row, Col, Table, BackTop, Tag, Modal, Skeleton } from '@douyinfe/semi-ui';
import MachineDetail from '../machine-detail';
interface Props {
    ip: string,
    close: () => void,
}


const Index: FC<Props> = (props) => {




    return <>
        <Modal
            width={800}
            title="查看设备"
            visible={true}
            onOk={() => { }}
            onCancel={props.close}
            footer={null}
            className='semi-modal'
        >
          <MachineDetail machineIp={props.ip}></MachineDetail>
        </Modal>
    </>
}

export default Index;