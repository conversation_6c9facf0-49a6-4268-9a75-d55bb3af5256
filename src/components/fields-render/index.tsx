import { FC, useState, useContext, useEffect } from 'react'
import { Typo<PERSON>, Modal, Form, Notification, Button, Row, Col, Tag, Popover, ArrayField, Select, Divider } from '@douyinfe/semi-ui';
import { Field } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/base_pb';

const { Title } = Typography;

const Index: FC<{
    col?: 2 | 3 | 4 | 6 | 12
    gutter?: number
    fields: Array<Field>
}> = (props) => {
    let nodes: React.ReactNode[] = [];
    let nodeIndex = 0;
    const col = props.col || 2;
    props.fields.forEach((field, index) => {
        if (field.type == 'string') {
            let isRequired = false, isDate = false, isDateTime = false, isEmail = false, isHostname = false, isIpv4 = false, isIpv6 = false, isUri = false, isPassword = false;
            field.rules.forEach(rule => {
                if(rule.required) {
                    isRequired = true;
                } else if (rule.format) {
                    if (rule.format == 'date') {
                        isDate = true;
                    } else if (rule.format == 'date-time') {
                        isDateTime = true;
                    } else if (rule.format == 'email') {
                        isEmail = true;
                    } else if (rule.format == 'hostname') {
                        isHostname = true;
                    } else if (rule.format == 'ipv4') {
                        isIpv4 = true;
                    } else if (rule.format == 'ipv6') {
                        isIpv6 = true;
                    } else if (rule.format == 'uri') {
                        isUri = true;
                    } else if (rule.format == 'password') {
                        isPassword = true;
                    }
                }
            });

            if (isDate) {
                nodes.push(<Col span={24/col} key={index}>
                    <Form.DatePicker field={field.key}
                        validate={(value) => {
                            if(isRequired && !value) {
                                return `${field.label} 不能为空`;
                            }
                            return '';
                        }}
                        label={field.label} defaultValue={field.default} placeholder={field.placeholder} />
                </Col>)
            } else if (isDateTime) {
                nodes.push(<Col span={24/col} key={index}>
                    <Form.DatePicker type="dateTime" field={field.key}
                        validate={(value) => {
                            if(isRequired && !value) {
                                return `${field.label} 不能为空`;
                            }
                            return '';
                        }}
                        label={field.label} defaultValue={field.default} placeholder={field.placeholder} />
                </Col>)
            } else {
                nodes.push(<Col span={24/col} key={index}>
                    <Form.Input field={field.key} label={field.label} defaultValue={field.default} placeholder={field.placeholder}
                        validate={(value) => {
                            if(isRequired && !value) {
                                return `${field.label} 不能为空`;
                            } else if(isEmail && value && !/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isHostname && value && !/^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isIpv4 && value && !/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isIpv6 && value && !/^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isUri && value && !/^(http|https):\/\/[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else if(isPassword && value && !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/.test(value)) {
                                return `${field.label} 格式不正确`;
                            } else {
                                
                                for (let i = 0; i < field.rules.length; i++) {
                                    let rule = field.rules[i];
                                    if (rule.pattern) {
                                        if (!new RegExp(rule.pattern).test(value)) {
                                            return `${field.label} 格式不正确`;
                                        }
                                    } else if (rule.minLength && rule.minLength > 0) {
                                        if (value.length < rule.minLength) {
                                            return `${field.label} 长度不能小于${rule.minLength}`;
                                        }
                                    } else if (rule.maxLength && rule.maxLength > 0) {
                                        if (value.length > rule.maxLength) {
                                            return `${field.label} 长度不能大于${rule.maxLength}`;
                                        }
                                    }

                                }
                            } 

                            return '';
                        }} />
                </Col>)
            }
            nodeIndex++;
        } else if (field.type == 'number') {
            let node = <Col span={24/col} key={index}>
                <Form.InputNumber field={field.key}
                    label={field.label} defaultValue={field.default} placeholder={field.placeholder}></Form.InputNumber>
            </Col>
            nodes.push(node);
            nodeIndex++;
        } else if (field.type == 'boolean') {
            let node = <Col span={24/col} key={index}>
                <Form.Switch field={field.key}  label={field.label} defaultValue={field.default} />
            </Col>
            nodes.push(node);
            nodeIndex++;
        } else if (field.type == 'array') {
            let node = <Col span={24/col} key={index}>
                <Form.TagInput field={field.key} addOnBlur
                    validate={(value) => {
                        if (field.rules.length > 0) {
                            for (let i = 0; i < field.rules.length; i++) {
                                let rule = field.rules[i];
                                if (rule.required) {
                                    if (!value) {
                                        return `${field.label} 不能为空`;
                                    }
                                }
                            }
                        }
                        return '';
                    }} label={field.label} defaultValue={field.default} placeholder={field.placeholder} />
            </Col>
            nodes.push(node);
            nodeIndex++;
        }
        if(nodeIndex % col == 0) {
            nodes.push(<div key={'span' + index} style={{width: '100%', float:'left'}}></div>);
        }

    });

    return <>
        <Row gutter={props.gutter || 10}>
            {nodes}
        </Row>
    </>
}

export default Index
