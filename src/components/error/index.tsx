import React, { <PERSON> } from 'react'
import { Empty, Button } from '@douyinfe/semi-ui'
import { useNavigate } from 'react-router-dom'
import { IllustrationNoAccess, IllustrationConstruction } from '@douyinfe/semi-illustrations'
import { DEFAULT_ROUTER } from '@/constants/router'
import { useLocale } from '@/locales'

interface Props {
	title?: string
	description?: string
	type: string
}

const Error: FC<Props> = ({ title, description, type }) => {
	const navigate = useNavigate()
	const { formatMessage } = useLocale()

	// 如果title或description包含'.'，则视为翻译key
	const translatedTitle = title && title.includes('.') ? formatMessage({ id: title }) : title;
	const translatedDescription = description && description.includes('.') ? formatMessage({ id: description }) : description;

	return (
		<Empty
			image={
				type == '403' ? (
					<IllustrationNoAccess style={{ width: 150, height: 150 }} />
				) : (
					<IllustrationConstruction style={{ width: 150, height: 150 }} />
				)
			}
			title={translatedTitle}
			description={translatedDescription}
			style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', paddingTop: 100 }}
		>
			{ type != '403' && <Button
				style={{ padding: '6px 24px', width: ' 180px' }}
				theme="solid"
				type="primary"
				onClick={
					type === '401'
						? () => {location.reload();}
						: () => navigate(DEFAULT_ROUTER, { replace: true })
				}
			>
				{type === '401' ? formatMessage({ id: 'components.error.goLogin' }) : formatMessage({ id: 'components.error.backHome' })}
			</Button>}
		</Empty>
	)
}

export default Error
