import { FC, useState } from "react";
import { Typo<PERSON>, Button, Dropdown, List, Tag, Row, Col, Banner, Popover, Switch, Space } from '@douyinfe/semi-ui';
import { IconMore, IconGlobeStroke, IconInfoCircle } from '@douyinfe/semi-icons';
import { DNSConfig, Routes } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/dns_pb';
import { GetMachineDNSConfigResponse } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";


import IconSplitDns from "@/assets/icon/split-dns.svg";
import styles from './index.module.scss'
import { find } from "lodash";
import { BASE_PATH } from "@/constants/router";

const { Title, Text, Paragraph } = Typography;

const OverrideLocalDnsSource: React.FC<{
    fullDNSConfig: GetMachineDNSConfigResponse
}> = (props) => {
    const { fullDNSConfig } = props;
    if (fullDNSConfig.config) {
        return <>当前设备</>
    }
    if (fullDNSConfig.groupConfigs) {
        let find = false;
        let groupDisplay;
        fullDNSConfig.groupConfigs.forEach(groupConfig => {
            if (groupConfig.config) {
                groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;

                find = true;
                return;
            }
        })
        if (find) {

            return <>设备组 {groupDisplay}</>
        }
    }

    if (fullDNSConfig.userConfig && fullDNSConfig.userConfig.user) {
        return <>用户 {fullDNSConfig.userConfig.user.displayName || ''}</>
    }

    if (fullDNSConfig.userGroupConfigs) {

        let find = false;
        let groupDisplay;
        fullDNSConfig.userGroupConfigs.forEach(groupConfig => {
            if (groupConfig.config) {
                groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;

                find = true;
                return;
            }
        })
        if (find) {

            return <>用户组 {groupDisplay}</>
        }
    }

    return <>全局</>;
}

const SplitDNSSource: React.FC<{
    fullDNSConfig: GetMachineDNSConfigResponse
    domain: string,
    ip: string
}> = (props) => {
    const { ip, domain, fullDNSConfig } = props;


    const ConfigDisplay: React.FC<{
        routes: { [key: string]: Routes }
        label: string,
        value: string
        domain: string,
        ip: string

        nav?: string
    }> = (props) => {
        let find = false;
        Object.keys(props.routes).forEach(domain => {
            let domainRoutes = props.routes[domain];
            if (domainRoutes) {
                domainRoutes.routes.forEach(r => {
                    if (r == props.ip) {
                        find = true;
                    }
                })
            }
        })
        if (!find) {
            return null;
        }
        return <div style={{ marginBottom: 0 }}>
            <Space><Text type='secondary'>{props.label}</Text>
                {props.nav ? <a href={props.nav} target="_blank"><Text type='tertiary' size="small">{props.value}</Text></a> : <Text type='tertiary'>{props.value}</Text>}
            </Space>
        </div>
    }





    return <>
        {
            fullDNSConfig.config && fullDNSConfig.config.routes && <ConfigDisplay
                routes={fullDNSConfig.config.routes}
                label="当前设备"
                value=""
                domain={props.domain}
                ip={props.ip}
            ></ConfigDisplay>
        }
        {fullDNSConfig.groupConfigs && fullDNSConfig.groupConfigs.length > 0 && fullDNSConfig.groupConfigs.map((groupConfig, index) => {
            if (groupConfig.config && groupConfig.config.routes) {
                let groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;

                return <ConfigDisplay
                    key={index}
                    routes={groupConfig.config.routes}
                    label="设备组"
                    nav={`${BASE_PATH}/devices/group/?query=${groupDisplay}`}
                    value={groupDisplay || ''}
                    domain={props.domain}
                    ip={props.ip}
                />
            }
            return null;
        })}
        {fullDNSConfig.userConfig && fullDNSConfig.userConfig.config && fullDNSConfig.userConfig.user && <ConfigDisplay
            routes={fullDNSConfig.userConfig.config.routes}
            label="用户"
            value={fullDNSConfig.userConfig.user.displayName || ''}
            nav={`${BASE_PATH}/users/${fullDNSConfig.userConfig?.user?.loginName}`}
            domain={props.domain}
            ip={props.ip}
        />
        }
        {
            fullDNSConfig.userGroupConfigs && fullDNSConfig.userGroupConfigs.length > 0 && fullDNSConfig.userGroupConfigs.map((groupConfig, index) => {
                if (groupConfig.config && groupConfig.config.routes) {
                    let groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;
                    return <ConfigDisplay
                        key={index}
                        routes={groupConfig.config.routes}
                        label="用户组"
                        nav={`${BASE_PATH}/users/group/?query=${groupDisplay}`}
                        value={groupDisplay || ''}
                        domain={props.domain}
                        ip={props.ip}
                    />
                }
                return null;
            })
        }
        {fullDNSConfig.flynetConfig && fullDNSConfig.flynetConfig.routes && <ConfigDisplay
            routes={fullDNSConfig.flynetConfig.routes}
            label="全局"
            value=""
            domain={props.domain}
            ip={props.ip}
        ></ConfigDisplay>}
    </>
}

const NameserverSource: React.FC<{
    fullDNSConfig: GetMachineDNSConfigResponse
    ip: string
}> = (props) => {
    const { ip, fullDNSConfig } = props;

    const CodeDisplay: React.FC<{
        nameservers?: string[],
        label: string,
        value: string
        ip: string

        nav?: string
    }> = (props) => {
        let find = false;
        if (props.nameservers && props.nameservers.length > 0) {
            props.nameservers.forEach(nameserver => {
                if (nameserver == props.ip) {
                    find = true;
                }
            })
        }

        if (!find) {
            return null;
        }
        return <div style={{ marginBottom: 0 }}>
            <Space><Text type='secondary'>{props.label}</Text> 
            {props.nav ? <a href={props.nav} target="_blank"><Text type='tertiary' size="small">{props.value}</Text></a> : <Text type='tertiary'>{props.value}</Text>}
            </Space>
        </div>
    }

    return <>
        {
            fullDNSConfig.config && <CodeDisplay
                nameservers={fullDNSConfig.config.nameservers}
                label="当前设备"
                value=""
                ip={props.ip}
            ></CodeDisplay>
        }
        {
            fullDNSConfig.groupConfigs && fullDNSConfig.groupConfigs.map((groupConfig, index) => {
                let groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;

                return <CodeDisplay
                    key={index}
                    nameservers={groupConfig.config?.nameservers}
                    label="设备组"
                    value={groupDisplay || ''}
                    nav={`${BASE_PATH}/devices/group/?query=${groupDisplay}`}
                    ip={props.ip}
                />
            })
        }
        {
            fullDNSConfig.userConfig && fullDNSConfig.userConfig.user && <CodeDisplay
                nameservers={fullDNSConfig.userConfig.config?.nameservers}
                label="用户"
                nav={`${BASE_PATH}/users/${fullDNSConfig.userConfig?.user?.loginName}`}
                value={fullDNSConfig.userConfig.user.displayName || ''}
                ip={props.ip}
            />
        }
        {
            fullDNSConfig.userGroupConfigs && fullDNSConfig.userGroupConfigs.map((groupConfig, index) => {
                let groupDisplay = groupConfig.group?.alias ? groupConfig.group?.alias : groupConfig.group?.name;

                return <CodeDisplay
                    key={index}
                    nameservers={groupConfig.config?.nameservers}
                    label="用户组"
                    nav={`${BASE_PATH}/users/group/?query=${groupDisplay}`}
                    value={groupDisplay || ''}
                    ip={props.ip}
                />
            })
        }
        {
            fullDNSConfig.flynetConfig && <CodeDisplay
                nameservers={fullDNSConfig.flynetConfig.nameservers}
                label="全局"
                value=""
                ip={props.ip}

            />
        }
    </>
}


interface Props {
    flynetId: bigint,
    fullDNSConfig: GetMachineDNSConfigResponse,
    onEdit?: () => void
}
const Index: FC<Props> = (props) => {


    const getRoutes = (domain: string, config: DNSConfig): string[] => {
        let routes = config.routes[domain];
        if (routes) {
            return routes.routes
        }
        return [];
    }
    const [dnsConfig, setDnsConfig] = useState<DNSConfig>(props.fullDNSConfig.finalConfig || new DNSConfig());

    // 限定域数据
    let initSplitRoutes: Array<{
        domain: string,
        routes: Array<string>
    }> = []
    if (props.fullDNSConfig.finalConfig?.routes && props.fullDNSConfig.finalConfig) {
        Object.keys(props.fullDNSConfig.finalConfig.routes).forEach(domain => {
            if (props.fullDNSConfig.finalConfig) {
                initSplitRoutes.push({
                    domain,
                    routes: getRoutes(domain, props.fullDNSConfig.finalConfig)
                })
            }
        })
    }
    // 限定域数据
    const [splitRoutes, setSplitRoutes] = useState<{
        domain: string,
        routes: Array<string>
    }[]>(initSplitRoutes);


    const handleEdit = () => {
        debugger
        if (props.onEdit) {
            props.onEdit();
        }
    }

    return <>
        <section className="mb40">
            <Row>
                <Col span={18}>
                    <Title heading={4} className="mb2">DNS服务器</Title>
                    <Paragraph type='tertiary' className='mb20'>查看本设备使用的DNS服务器</Paragraph>

                </Col>
                <Col span={6}>
                    <div className="btn-right-col">
                        <Button theme="solid" onClick={handleEdit}>修改当前设备DNS服务器</Button>
                    </div>

                </Col>
            </Row>
            <div className={styles.nameForm}>
                {
                    splitRoutes.map((item, index) => {
                        return <div key={index} className={styles.nameForm}>
                            <Title
                                className={styles.splitDNSTitle}
                                heading={6}>
                                <IconGlobeStroke className={styles.splitDNSIcon} /> {item.domain}
                                <Tag style={{ marginLeft: 8 }}> <img className={styles.iconSplitDNS} src={IconSplitDns}></img>&nbsp;限定域</Tag>
                            </Title>
                            <List className={styles.ipList}>
                                {item.routes.map(ip => {
                                    return <List.Item key={ip} className={styles.ipListItem}>
                                        {ip}
                                        <SplitDNSSource ip={ip} domain={item.domain} fullDNSConfig={props.fullDNSConfig}></SplitDNSSource>

                                    </List.Item>
                                })}
                            </List>
                        </div>
                    })
                }
                <Row>
                    <Col span={12}> <Title className="mb10" heading={6} >全局DNS服务器</Title></Col>
                    <Col span={12}>
                        <div className={styles.overrideLocalDns}>
                            <Popover content={<div className="p10 mw400">启用该选项后，客户端将忽略本地 DNS 设置并始终使用全局DNS服务器。
                                <br />
                                禁用该选项时，客户端优先使用本地 DNS 设置，仅在需要时使用全局DNS服务器。</div>}>
                                <IconInfoCircle />
                            </Popover>&nbsp;
                            覆盖本地DNS&nbsp;<Switch disabled={dnsConfig?.nameservers.length == 0} size="small" checked={dnsConfig?.overrideLocalDns}></Switch>
                            <OverrideLocalDnsSource fullDNSConfig={props.fullDNSConfig}></OverrideLocalDnsSource>
                        </div>
                    </Col>
                </Row>
                {dnsConfig?.overrideLocalDns && dnsConfig?.nameservers.length == 0 ? <Banner className={styles.banner} type="info" description="您还没有设置全局DNS服务器" closeIcon={null} ></Banner> : <List className={styles.ipList}>
                    {!dnsConfig?.overrideLocalDns ? <List.Item className={styles.ipListItem} style={{ color: 'var(--semi-color-text-2)' }}>本地DNS设置</List.Item>
                        : ''}
                    {dnsConfig?.nameservers.map((item, index) => {
                        return <List.Item key={index} className={styles.ipListItem}>
                            {item}
                            <NameserverSource
                                ip={item}
                                fullDNSConfig={props.fullDNSConfig}
                            ></NameserverSource>
                            {/* <Dropdown
                                trigger={'hover'}
                                position={'bottomLeft'}
                                render={
                                    <Dropdown.Menu>
                                        <Dropdown.Divider></Dropdown.Divider>

                                    </Dropdown.Menu>
                                }
                            >
                                <IconMore />
                            </Dropdown> */}

                        </List.Item>
                    })}
                </List>}

            </div>
        </section>

    </>
}


export default Index;
