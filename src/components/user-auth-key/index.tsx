import { useEffect, useState, useContext, FC } from 'react';
import { Typography, Modal, Notification, Banner, Skeleton } from '@douyinfe/semi-ui';
import { Duration } from "@bufbuild/protobuf";
import { IconRefresh } from '@douyinfe/semi-icons';

import moment from 'moment'
import QRCode from "react-qr-code";
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss';
import { CreateAuthKeyRequest } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/auth_keys_pb";

import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral'
import { GlobalConfigContext } from '@/hooks/useGlobalConfig';
import { useLocale } from '@/locales';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void
}

let getAuthKetTimer: any = null;

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const flynetGeneral = useContext(FlynetGeneralContext);
    const globalConfig = useContext(GlobalConfigContext);

    const [loading, setLoading] = useState(true);

    const [authKey, setAuthKey] = useState<string>('');

    const [isExpired, setIsExpired] = useState(true);

    const queryAuthKey = (authKeyId: bigint) => {
        getAuthKetTimer = setTimeout(() => {
            flylayerClient.getAuthKey({ authKeyId }).then((res) => {
                let expiryData = res.authKey?.expiresAt?.toDate();
                if (expiryData && expiryData < new Date()) {
                    // // 过期
                    // Notification.error({ content: `认证码已过期，重新生成认证码`, position: "bottomRight" })
                    // createAuthKey();
                    setIsExpired(true)
                    clearTimeout(getAuthKetTimer)
                } else {
                    queryAuthKey(authKeyId)
                }

            });

        }, 5000)
    }

    const createAuthKey = () => {
        setLoading(true)
        let request = new CreateAuthKeyRequest({
            flynetId: flynetGeneral.id,
            ephemeral: false,
            reusable: false,
            tags: [],
            expiry: new Duration({ seconds: BigInt(10 * 60), nanos: 0 }) // 10分钟内到期
        });
        flylayerClient.createAuthKey(request).then((res) => {
            if (res.authKey) {

                // let qrStr = `${globalConfig?.code}:${res.value}`
                setIsExpired(false)
                queryAuthKey(res.authKey.id)

                const authInfo = {
                    authKey: res.value,
                    controlURL: globalConfig.ctrlUrl,
                  };
                
                  // 将authInfo转换为json字符串
                  const authInfoStr = JSON.stringify(authInfo);
                  // 将 authInfoStr 转换为base64字符串
                  const base64Str = window.btoa(authInfoStr);
                  setAuthKey(base64Str);
            }
        }, (err) => {
            console.error(err);
            Notification.error({ content: formatMessage({ id: 'userAuthKey.createFailed' }), position: "bottomRight" })
        }).finally(() => setLoading(false))

    }

    useEffect(() => {
        createAuthKey()
    }, [])


    return <>
        <Modal
            width={420}
            title={formatMessage({ id: 'userAuthKey.title' })}
            visible={true}
            footer={null}
            onCancel={() => {

                clearTimeout(getAuthKetTimer)
                props.close()
            }}
            closeOnEsc={true}
            maskClosable={false}
        >
            <Banner
                type='warning'
                closeIcon={null}
                description={formatMessage({ id: 'userAuthKey.description' })}
            />
            <Skeleton placeholder={<>
                <div className={styles.authKeyWrap}>
                    <Skeleton.Image style={{ width: 256, height: 256, marginBottom: 20 }} />
                    <Skeleton.Image style={{ width: '100%', height: 20 }} />
                </div>
            </>} loading={loading}>
                <div className={styles.authKeyWrap}>
                    <div className={styles.qrCodeWrap}>
                        <QRCode value={`${authKey}`}></QRCode>
                        {isExpired && <div className={styles.refreshWrap}>
                            <IconRefresh style={{ marginRight: 10 }} onClick={() => createAuthKey()} />
                            <span className={styles.refresh} onClick={() => createAuthKey()}>{formatMessage({ id: 'userAuthKey.refresh' })}</span>
                        </div>}
                    </div>

                    <Paragraph type='tertiary' className='mb20' style={{ width: '100%', wordWrap: 'break-word', wordBreak: 'break-all' }} copyable>{authKey}</Paragraph>
                    <Paragraph type='tertiary'>{formatMessage({ id: 'userAuthKey.generateTime' })}：{moment(new Date()).format('YYYY/MM/DD HH:mm:ss')}</Paragraph>
                    {/* <Paragraph className='mb10' type='tertiary' copyable style={{wordBreak: 'break-all'}}>{authKey}</Paragraph> */}
                    <Paragraph type='secondary'>{formatMessage({ id: 'userAuthKey.scanInstruction' })}</Paragraph>
                </div>
            </Skeleton>

        </Modal></>
}
export default Index;