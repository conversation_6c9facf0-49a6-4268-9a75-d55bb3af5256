import { FC, useState } from "react";
import { Typography, Button, Space, Popover } from '@douyinfe/semi-ui';
import { IconInfoCircle } from '@douyinfe/semi-icons';

import CodeEditor from '@/components/code-editor';
import CodeViewer from '@/components/code-viewer';
import { useLocale } from '@/locales';
const { Title, Paragraph } = Typography;

interface Props {
    flynetId: bigint,
    value: Uint8Array,
    className?: string,
    onSave: (value: Uint8Array) => void,
    saveLoading?: boolean
}

const Index: FC<Props> = (props) => {
    const { formatMessage } = useLocale();
    const decoder = new TextDecoder('utf-8');
    let result = decoder.decode(props.value);
    const [jsonEditorValue, setJsonEditorValue] = useState<string>(JSON.stringify(JSON.parse(result), null, 2));

    const handleSave = () => {
        const encoder = new TextEncoder();
        let value = encoder.encode(jsonEditorValue);
        props.onSave(value);
    }

    return <div className={props.className}>
        <Title heading={4} className='mb2'>{formatMessage({ id: 'components.relayMapManager.title' })}</Title>
        <Space className='mb20'><Paragraph type='tertiary'>
            {formatMessage({ id: 'components.relayMapManager.description' })}
            {/* 配置示例:  */}

        </Paragraph>
        {/* <Popover content={<div className='p10'  style={{width: 800}}>
                <CodeViewer language='json' height="300px" value={`
{
  "Regions": {
    "1": {
      "RegionID": 1,
      "RegionCode": "r1",
      "RegionName": "r1",
      "Nodes": [
        {
          "Name": "1c1",
          "RegionID": 1,
          "HostName": "derp1c.flylayer.com",
          "IPv4": "*************",
          "CanPort80": true
        }
      ]
    }
  }
}
                `} />
            </div>}>
                <IconInfoCircle />
            </Popover>     */}
            </Space>

        <CodeEditor height='320px'
            value={jsonEditorValue}
            language='json'
            onChange={(val) => {
                setJsonEditorValue(val)
            }}></CodeEditor>
        <Space style={{ marginTop: 10 }}>
            <Button type="primary" loading={props.saveLoading} onClick={handleSave} theme='solid'>{formatMessage({ id: 'components.relayMapManager.saveConfig' })}</Button>
        </Space>
    </div>
}

export default Index
