import { FC, useContext } from 'react';
import Editor, { DiffEditor } from '@monaco-editor/react';
import { GlobalThemeContext } from '@/hooks/useGlobalTheme';
import styles from './index.module.scss';

const Index: FC<{
    language?: string,
    height?: string,
    original: string,
    modified: string,
}> = (props) => {
    const globalTheme = useContext(GlobalThemeContext);

    return <DiffEditor
        options={{
            
            readOnly: true,
            minimap: { enabled: false },
            
        }}
        className={styles.editor}
        original={props.original}
        modified={props.modified}
        language={props.language || 'javascript'}
        height={props.height || '200px'} // 可以根据需要调整编辑器的高度
        theme={globalTheme.colorMode === 'dark' ? 'vs-dark' : 'vs-light'}
    />
}

export default Index;