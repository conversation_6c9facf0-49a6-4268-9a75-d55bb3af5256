import React, { FC, useCallback, useContext, useEffect, useState } from "react";

import { Machine} from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { Avatar, Notification, Tag, Select } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
import { debounce } from "lodash";

interface Props {
    value: string[] | string;
    onChange: (value: string[] | string) => void;
    onLoadingChange(boolean: boolean): void;
    style?: React.CSSProperties;
    className?: string;
    multiple?: boolean;
    insetLabel?: string;
    maxTagCount?: number;
}

// 设备选择组件
const MachineSelector: FC<Props> = (props) => {
    const flynet = useContext(FlynetGeneralContext);
    const maxTagCount = props.maxTagCount ? props.maxTagCount : 1;
    // 设备列表
    const [machines, setMachines] = useState<Machine[]>();
    // 所有设备列表
    const [allMachines, setAllMachines]  = useState<Machine[]>();
    // 是否正在加载设备数据 
    const [loading, setLoading] = useState(false);

    const queryMachine = async (filterKeywords?: string) => {
        setLoading(true);
        try {
            const res = await flylayerClient.listMachines({
                flynetId: flynet?.id,
                query: filterKeywords ? `keywords=${encodeURIComponent(filterKeywords)}&limit=20` : 'limit=20'
            })
            setMachines(res.machines);
            setAllMachines(res.machines);
            setLoading(false);
        } catch (err) {
            setLoading(false);
            console.error(err)
            Notification.error({ content: '获取设备列表失败, 请稍后重试' })
        }
    }
    const debounceQuery = useCallback(debounce((queryStr) => queryMachine(queryStr), 500), []);
    // 查询设备数据
    const queryActor =  (val: string) => {
        debounceQuery(val);
    }
    const [value, setValue] = useState(props.value);
    useEffect(() => {
        queryMachine();
    }, []);

    useEffect(() => {
        setValue(props.value);
    }, [props.value]);
    
    // 处理选择的设备
    const handleChange = (value: any) => {
        setValue(value);
        props.onChange(value);
    }
    
    // 处理清空
    const handleClear = () => {
        if (props.multiple) {
            setValue([]);
            props.onChange([]);
        } else {
            setValue('');
            props.onChange('');
        }
        
    }
    
    

    return (
        <Select
            insetLabel= { props.insetLabel }
            filter
            remote
            showClear
            multiple={props.multiple}
            value={value}
            className={props.className}
            style={props.style}
            
            loading={loading}
            onSearch={queryActor}
            position='bottom'
            maxTagCount={maxTagCount}
            onClear={handleClear}
            onChange={handleChange}
            emptyContent={<>暂无设备</>}
        >
            {machines?.map((machine) => (
                <Select.Option key={machine.id + ''} value={machine.id + ''}>
                    {machine.name}
                </Select.Option>
            ))}
        </Select>
    );
}
export default MachineSelector;

