import { useLayoutEffect, ReactElement } from "react";
import { useLocation } from "react-router-dom";

const RouterScrollTop = ({ children }: { children: ReactElement }) => {
    const location = useLocation();
    useLayoutEffect(() => {
        
        const $root = document.querySelector('#root');
        if($root) {
            $root.scrollTo(0, 0)
        }

        document.documentElement.scrollTo(0, 0)

    }, [location.pathname])
    return children;
}

export default RouterScrollTop;