import { useEffect, FC, ReactNode } from 'react';

import { Timestamp } from "@bufbuild/protobuf";
import { Tag, Popover, Space } from '@douyinfe/semi-ui';
import { IconInfoCircle, IconMinusCircle } from '@douyinfe/semi-icons';

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { formatDisplayTimestamp, formatRelativeMin } from '@/utils/format';
import styles from './index.module.scss';

// 设备标签
const DeviceTag: FC<{ record: Machine, onLoad?: Function }> = (props) => {
    const { record } = props;

    // 用户被禁用逻辑
    let $userDisabled: ReactNode | undefined = undefined;
    if (record.user?.disabled) {
        $userDisabled = <Popover content={<div className="p10 mw300">此设备的用户被禁用</div>}>
            <Tag color='red'>用户被禁用&nbsp;<IconMinusCircle size='small' /></Tag></Popover>
    }

    // 密钥过期逻辑
    let $expires: ReactNode | undefined = undefined;
    if (!record.keyExpiryDisabled) {
        if (record.expiresAt) {
            if(record.createdAt && record.createdAt.toDate().getTime() > record.expiresAt.toDate().getTime()) {
                // 创建时间大于过期时间，说明是旧数据，不显示过期时间
                $expires = undefined;
                
                // $expires = <Tag>
                //     {`已过期`}
                // </Tag>
            } else {
                const expiry = record.expiresAt.toDate()
                // 测试用
                //let expiry = new Date();
                //expiry.setTime(1000 * 60 * 60 * 24 * 365 * 53 + 1000 * 60 * 60 * 24 * 154 +  + 1000 * 60 * 60 * 4) 
        
                const now = new Date()
                const isExpired = expiry < now
                if (!isExpired) {
                    const expireTimes = expiry.getTime() - now.getTime()
                    const relExpiry = formatRelativeMin(expiry)
                    if (expireTimes / 60000 <= 30) {
                        // 30分钟内过期
                        $expires = <Popover
                            content={<div className="p10 ">{`这台设备的密钥将在${relExpiry}后过期`}</div>}
                        >
                            <Tag>
                                {`${relExpiry}后过期`}
                            </Tag>
                        </Popover>
                    }
                } else { // 已过期
                    const expiryDate = formatDisplayTimestamp(Timestamp.fromDate(expiry))
                    if (now.getTime() - expiry.getTime() > 1000 * 60 * 60 * 24 * 365) { // 一年前过期
        
                        $expires = <Tag>
                            {`已过期`}
                        </Tag>
                    } else {
                        // 一年内过期
                        $expires = <Popover content={<div className="p10 mw300">{`这台设备的密钥已于${expiryDate}过期，需要重新认证`}</div>}>
                            <Tag>
                                {`已过期`}
                            </Tag>
                        </Popover>
                    }
        
                }
            }
            
        }
        
    } else {
        // 永不过期
        $expires = <Popover content={<div className="p10 mw300">这台设备禁用了密钥过期</div>}>
            <Tag>密钥永不过期</Tag>
        </Popover>
    }

    // 子网路由逻辑
    let $subnets: ReactNode | undefined = undefined;
    if (record.advertisedRoutes && record.advertisedRoutes.length > 0) {
        if (record.enabledRoutes && record.enabledRoutes.length == record.advertisedRoutes.length) {
            $subnets = <Popover content={<div className="p10 mw300">这台设备宣告了子网路由</div>}>
                <Tag color='blue'>子网路由</Tag>
            </Popover>;
        } else {
            $subnets = <Popover content={<div className="p10 mw300">这台设备有未开启的路由。从右侧菜单中的“编辑路由设置”选项中查看此内容</div>}>
                <Tag color='blue'>子网路由&nbsp;<IconInfoCircle size='small' /></Tag>
            </Popover>;
        }
    }

    //待审批逻辑
    let $unapproved: ReactNode | undefined = undefined;
    if (!record.authorized) {
        $unapproved = <Popover content={<div className="p10 mw300">这台设备还没有被批准，需要审核</div>}>
            <Tag color='red'>待审批</Tag></Popover>
    }

    // 出口节点逻辑
    let $exitNode: ReactNode | undefined = undefined;
    if (record.advertisedExitNode) {
        if (record.enabledExitNode) {
            $exitNode = <Popover content={<div className="p10 mw300">这台设备作为网络的出口节点</div>}>
                <Tag color='blue'>出口节点</Tag></Popover>
        } else {
            $exitNode = <Popover content={<div className="p10 mw300" style={{ width: 200 }}>这台设备作为网络的出口节点。从右侧菜单中的“编辑路由设置”选项中查看此内容</div>}>
                <Tag color='blue'>出口节点&nbsp;<IconInfoCircle size='small' /></Tag></Popover>
        }
    }

    // 临时节点逻辑
    let $ephemeralNode: ReactNode | undefined = undefined;
    if (record.ephemeral) {
        $ephemeralNode = <Popover content={<div className="p10 mw300">这台设备是临时接入网络的设备，离线后将会被删除</div>}>
            <Tag color='amber'>临时设备</Tag></Popover>
    }

    useEffect(() => {
        const isEmpty = $userDisabled == undefined
            && $unapproved == undefined
            && $expires == undefined
            && $subnets == undefined
            && $exitNode == undefined
            && $ephemeralNode == undefined;
        if (props.onLoad) {
            props.onLoad(isEmpty)
        }

    }, [])

    return <div className={styles.nameMemo}>
        <Space>
            {$userDisabled}
            {$unapproved}
            {$expires}
            {$subnets}
            {$exitNode}
            {$ephemeralNode}
        </Space>
    </div>
}

export default DeviceTag