import { FC, useState } from 'react'
import { Typography, Modal, Input, Notification, Collapsible, Descriptions, Row, Col } from '@douyinfe/semi-ui';
import { flylayerClient } from '@/services/core';
import styles from './index.module.scss'
import { IconChevronRight, IconChevronDown } from '@douyinfe/semi-icons';

import { FlynetGeneral } from '@/services/flynet';
import { CUSTOMER_SERVICE_QR } from '@/constants';
const { Paragraph, Title } = Typography;

interface Props {
    close: () => void,
    success?: () => void,
    flynetGeneral : FlynetGeneral
}
const Index: FC<Props> = (props) => {
    
    const [name, setName] = useState('')
    // 点击确定按钮操作是否正在加载中
    const [loading, setLoading] = useState(false); const handleOk = () => {
        
        setLoading(true)
        flylayerClient.renewFlynet({
            flynetId: props.flynetGeneral.id,
            code: name
        }).then((res) => {
            Notification.success({ 
                content: "网络续费成功",
                position: "bottomRight",
                onClose: () => {
                    props.success && props.success();
                }
            })
        }).catch((err) => {
            console.error(err);
            Notification.error({ content: "网络续费失败，请稍后重试", position: "bottomRight" })
        }).finally(() => setLoading(false))
  
    };
    function isValidDeviceName(name: string): string {
        let errMessage = "";
        if (name.length === 0) {
            errMessage = "名称不能为空";
        }
        if (name.length > 63) {
            errMessage = "名称长度必须小于64个字符";
        }

        return errMessage;
    }

    const handleCancel = () => {
        props.close();
    };

    const [qrShow, setQrShow] = useState(false);
    

    return <><Modal
        width={600}
        title={`网络续费`}
        visible={true}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        closeOnEsc={true}
        okButtonProps={{ loading }}
    >
        <Row>
            <Col span={14}>
                <Paragraph className='mb10' type='secondary'>网络标识</Paragraph>
                <Paragraph className='mb20' type='tertiary' copyable>{props.flynetGeneral.name}</Paragraph>
                </Col>
            <Col span={10}>
            <Paragraph className='mb10' type='secondary'>网络名称</Paragraph>
            <Paragraph type='tertiary'>{props.flynetGeneral.alias}</Paragraph>
            </Col>
        </Row>
        
        <Paragraph className='mb10' type='secondary'>兑换码</Paragraph>
        <Input className='mb20' onChange={(value) => setName(value)} value={name}></Input>
        
            
        <Paragraph type={'tertiary'} className={styles.qrToggle} onClick={() => setQrShow(!qrShow)}>{qrShow ? <IconChevronDown /> : <IconChevronRight />}&nbsp;联系客服续费</Paragraph>

        <Collapsible isOpen={qrShow}><div className={styles.qr}><img src={CUSTOMER_SERVICE_QR} /></div> </Collapsible>
    </Modal></>

}

export default Index;