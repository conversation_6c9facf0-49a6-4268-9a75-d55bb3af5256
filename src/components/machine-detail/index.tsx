import { FC, useEffect, useState, useContext } from 'react'
import { Row, Col, Button, Typography, Tag, Notification, Tabs, TabPane, Descriptions, Popover, Card, Badge, Avatar, Space, Divider, Skeleton, List, Banner } from '@douyinfe/semi-ui';

import { IconSetting, IconInfoCircle, IconArrowUpRight } from '@douyinfe/semi-icons';
import { Flynet } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/flynets_pb";

import { Machine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";
import { FlynetGeneralContext } from '@/hooks/useFlynetGeneral';
const { Title, Paragraph, Text } = Typography;
import styles from './index.module.scss'
import { BASE_PATH } from '@/constants/router';
import { useNavigate, useParams } from 'react-router-dom';
import DeviceTag from '@/components/device-tag';
import { getMachine, getRelayName } from '@/services/device';
import { formatDefaultTimestamp, formatIPNVersion } from '@/utils/format';
import { flylayerClient } from '@/services/core';
import { getFlynet } from '@/services/flynet';
import { AdvertisedRoute, AdvertisedRoute_RouteType } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/routes_pb";


interface Props {
    machineIp: string,
}

const Index: FC<Props> = (props) => {
    const flynetGeneral = useContext(FlynetGeneralContext);
    
    
    const machineIp = props.machineIp;

    // 设备加载标识，用于骨架屏
    const [deviceLoading, setDeviceLoading] = useState(false);
    const [device, setDevice] = useState<Machine>()

    // 状态是否为空
    const [isStatusEmpty, setIsStatusEmpty] = useState(true);

    // 宣告路由
    const [advertisedRoutes, setAdvertisedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 排除路由
    const [excludedRoutes, setExcludedRoutes] = useState<Array<AdvertisedRoute>>([]);
    // 预览路由
    const [previewRoutes, setPreviewRoutes] = useState<string[]>([]);


    const [flynet, setFlynet] = useState<Flynet>();
    useEffect(() => {
        getFlynet(flynetGeneral.id).then(res => {
            setFlynet(res.flynet)
        })
    }, [])


    const connectDetails = [
        {
            key: 'WorkingIcmpV4',
            value: device?.netInfo?.workingIcmpV4 ? 'YES' : 'NO',
            displayName: 'WorkingIcmpV4'
        }, {
            key: 'PreferredRelay',
            value: device?.netInfo?.preferredRelay ? 'YES' : 'NO',
            displayName: 'PreferredRelay'
        }, {
            key: 'Hairpinning',
            value: device?.netInfo?.hairPinning ? 'YES' : 'NO',
            displayName: 'Hairpinning'
        }, {
            key: 'WorkingIpv6',
            value: device?.netInfo?.workingIpv6 ? 'YES' : 'NO',
            displayName: 'WorkingIpv6'
        }, {
            key: 'OsHasIpv6',
            value: device?.netInfo?.osHasIpv6 ? 'YES' : 'NO',
            displayName: 'OsHasIpv6'
        }, {
            key: 'UDP',
            value: device?.netInfo?.workingUdp ? 'YES' : 'NO',
            displayName: 'UDP'
        }, {
            key: 'UPnP',
            value: device?.netInfo?.upnp ? 'YES' : 'NO',
            displayName: 'UPnP'
        }, {
            key: 'PCP',
            value: device?.netInfo?.pcp ? 'YES' : 'NO',
            displayName: 'PCP'
        }, {
            key: 'NAT-PMP',
            value: device?.netInfo?.pmp ? 'YES' : 'NO',
            displayName: 'NAT-PMP'
        }
    ]
    const [preferredRelayName, setPreferredRelayName] = useState('')
    const query = () => {
        setDeviceLoading(true)
        getMachine(machineIp).then((machine: Machine) => {
            setDevice(machine)
            let relayId = -1;
            if (machine && machine.netInfo && machine.netInfo.preferredRelay) {
                relayId = machine.netInfo.preferredRelay
            }
            getRelayName(relayId).then(res => {
                if (res) {
                    setPreferredRelayName(res)
                }
            })

            flylayerClient.getMachineRoutes({ machineId: machine.id }).then(res => {
                if (res.routes && res.routes.advertisedRoutes) {
                    const _advertisedRoutes: AdvertisedRoute[] = [];
                    const _excludedRoutes: AdvertisedRoute[] = [];
                    if (res.routes.advertisedRoutes && res.routes.advertisedRoutes.length > 0) {
                        res.routes.advertisedRoutes.forEach((value, index) => {
                            if (value.routeType == AdvertisedRoute_RouteType.EXCLUDE) {
                                _excludedRoutes.push(value);
                            }
                            else {
                                _advertisedRoutes.push(value);
                            }
                        });
                    }

                    setAdvertisedRoutes(_advertisedRoutes);
                    setExcludedRoutes(_excludedRoutes);

                    flylayerClient.previewMachineRoutes({
                        machineId: machine.id,
                        advertisedRoutes: res.routes.advertisedRoutes,
                    }).then((res) => {

                        setDeviceLoading(false)
                        setPreviewRoutes(res.routes);
                    }).catch((err) => {

                        setDeviceLoading(false)
                        console.error(err);
                        Notification.error({ content: "预览子网路由失败，请稍后重试", position: "bottomRight" })
                    })
                } else {
                    setAdvertisedRoutes([])
                }
            }).catch
                (err => {

                    setDeviceLoading(false)
                    console.error(err)
                    Notification.error({ content: '获取设备路由信息失败，请稍后重试', position: "bottomRight" })
                })


        }, err => {
            console.error(err)

            setDeviceLoading(false)
            Notification.error({ content: '获取设备失败，请稍后重试', position: "bottomRight" })
        })
    }
    useEffect(() => {
        query()
    }, [])
   
    return <><Skeleton placeholder={<div>
        <div style={{ height: 16, width: 100, marginBottom: 8 }}><Skeleton.Image /></div>

        <div style={{ height: 32, width: 300 }} className='mb20' ><Skeleton.Image /></div>
        <Skeleton.Image style={{ height: 60 }} className='mb40' />
        <Skeleton.Image style={{ height: 200 }} />
    </div>} loading={deviceLoading}>
        <div>
           
            <Row className='mb20'>
                <Col span={20}>
                    <Title className={styles.heading} heading={3}>
                        <span>{device?.autoGeneratedName ? device.name : device?.givenName}&nbsp;</span>
                        {device?.connected ?
                            <Badge className={styles.headingBadge} dot style={{ backgroundColor: 'var(--semi-color-success)' }} /> :
                            <Badge className={styles.headingBadge} dot type='tertiary' />}
                    </Title>
                </Col>
                
            </Row>

            <Divider style={{ marginBottom: 10 }} />

            <Descriptions className={isStatusEmpty ? styles.generalInfoNoStatus : styles.generalInfo} data={[
                {
                    key: <div className='align-v-center'>{device?.tags && device.tags.length > 0 ? '标签' : '用户'} &nbsp;
                        <Popover content={<div className="p10">{device?.tags && device.tags.length > 0 ? '打上标签后，设备的权限由访问控制中的标签维护' : '默认为设备的接入者'}</div>}>
                            <IconInfoCircle size='small' />
                        </Popover></div>, value: device?.tags && device.tags.length > 0 ? <>
                            <div>
                                {device.tags.map((tag, index) => {
                                    return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 15, marginBottom: 0 }}>{tag}</Tag>
                                })}
                            </div>
                        </> : <div className='pt5'><Avatar
                            size="extra-small"
                            src={device?.user?.avatarUrl}
                        ></Avatar>&nbsp;<Text>{device?.user?.displayName} <Text type="tertiary" size='small'>{device?.user?.loginName}</Text></Text></div>
                },
                {
                    key: isStatusEmpty ? '' : '状态',
                    value: (
                        <>{device ? <div className='pt5'><DeviceTag onLoad={(isEmpty: boolean) => setIsStatusEmpty(isEmpty)} record={device}></DeviceTag></div> : ''}</>
                    ),
                }
            ]} row />
            <Row className='mb2'>
                <Col span={20}>
                    <div><Title heading={4}>连接器</Title></div></Col>
                <Col span={4}></Col>
            </Row>
            <Paragraph className='mb20' type='tertiary'>连接器将分散在各处的物理网络联结成一个可以互相访问的网络。 </Paragraph>
            <Tabs type="card" className='mb40'>
                <TabPane tab="宣告路由" itemKey="1" style={{ paddingTop: 10 }}>
                    {advertisedRoutes.length > 0 ? <div className='mb40'><Row gutter={4}>{advertisedRoutes.map((route, index) => {
                        return <Col sm={24} md={12} lg={8} key={index}><Text title={route.enabled ? '' : '禁用'} type={ route.enabled ? 'primary' : 'quaternary'}>{route.route} {route.remark}</Text></Col>
                    })}</Row></div> : <Card className='mb40'>这台设备没有宣告任何路由</Card>}
                </TabPane>
                <TabPane tab="排除路由" itemKey="2" style={{ paddingTop: 10 }}>
                    {excludedRoutes.length > 0 ? <div className='mb40'><Row gutter={4}>{excludedRoutes.map((route, index) => {
                        return <Col sm={24} md={12} lg={8} key={index}><Text title={route.enabled ? '' : '禁用'} type={ route.enabled ? 'primary' : 'quaternary'}>{route.route} {route.remark}</Text></Col>
                    })}</Row></div> : <Card className='mb40'>这台设备没有任何排除路由</Card>}
                </TabPane>
                <TabPane tab="预览" itemKey="3" style={{ paddingTop: 10 }}>
                    {previewRoutes.length > 0 ? <div className='mb40'><Row gutter={4}>{previewRoutes.map((route, index) => {
                        return <Col xs={24} sm={12} md={8} lg={6} key={index}><Text>{route}</Text></Col>
                    })}</Row></div> : <Card className='mb40'>这台设备没有宣告任何路由</Card>}
                </TabPane>
            </Tabs>


            <Title heading={4} className='mb2'>设备详情</Title>
            <Paragraph className='mb10' type='tertiary'>设备和网络详情，供排查网络问题时查看。</Paragraph>
            <Row>
                <Col xs={24} sm={18}><Descriptions className={styles.machineDetail}>
                    {device?.tags && device.tags.length > 0 ? <>
                        <Descriptions.Item key="tags" itemKey='访问控制标签'>
                            {device.tags.map((tag, index) => {
                                return <Tag key={index} size='large' shape='circle' color='white' style={{ marginRight: 4, marginTop: 0, marginBottom: 0 }}>{tag}</Tag>
                            })}</Descriptions.Item>
                    </> : null}
                    <Descriptions.Item key="loginName" itemKey='创建人'><Paragraph copyable>{device?.user?.loginName}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="machineName" itemKey='设备名'><Paragraph copyable>{device?.givenName}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="osHostname" itemKey='主机名'><Paragraph copyable>{device?.hostname}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="os" itemKey='操作系统'><Paragraph copyable>{device?.os} {device?.osVersion ? `(${device.osVersion})` : ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="clientVersion" itemKey='版本号'><Paragraph copyable>{formatIPNVersion(device ? device.clientVersion : '')}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="IPv4" itemKey='IPv4'><Paragraph copyable>{device?.ipv4}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="IPv6" itemKey='IPv6'><Paragraph copyable>{device?.ipv6}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="id" itemKey='ID'><Paragraph copyable>{device?.id + ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="created" itemKey='创建时间'><Paragraph copyable>{device ? formatDefaultTimestamp(device.createdAt) : ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="lastSeen" itemKey='最近上线时间'><Paragraph copyable>{device ? formatDefaultTimestamp(device.lastSeen) : ''}</Paragraph></Descriptions.Item>
                    <Descriptions.Item key="endpoint" itemKey='端点信息'>
                        <List bordered={false}>
                            {device?.clientConnectivity?.endpoints?.map((endpoint: string, index: number) => {
                                return <List.Item style={{ border: 'none', paddingBottom: '5px', paddingLeft: 0, paddingTop: 2 }} key={index}>{endpoint}</List.Item>
                            })}
                        </List>
                    </Descriptions.Item>
                    {device ? device.keyExpiryDisabled ? '' : <Descriptions.Item key="keyExpiry" itemKey='密钥过期时间'><Paragraph copyable>{formatDefaultTimestamp(device.expiresAt)}</Paragraph></Descriptions.Item> : ''}
                    {preferredRelayName ? <Descriptions.Item key="preferredRelay" itemKey="首选中继"><Paragraph copyable>{preferredRelayName}</Paragraph></Descriptions.Item> : ''}
                    {/* {device && (device.os == 'windows' || device.os == 'macOS') && !flynet?.rdpEnabled && <Descriptions.Item key="rdp" itemKey="远程桌面">
                        <Banner
                            type="info"
                            closeIcon={null}
                            description={<>远程桌面功能未开启，请在<a target='_blank' href={`${BASE_PATH}/settings/device`} className='link-external'>设置/设备/远程桌面<IconArrowUpRight /></a>中启用</>}
                        />
                    </Descriptions.Item>} */}
                    {/* {device && (device.os == 'windows' || device.os == 'macOS') && flynet?.rdpEnabled && !device?.user?.rdpEnabled && <Descriptions.Item key="rdp" itemKey="远程桌面">
                        <Banner
                            type="info"
                            closeIcon={null}
                            description={<>该设备所属用户远程桌面功能未开启，请在<a target='_blank' href={`${BASE_PATH}/users/${device.user?.loginName}`} className='link-external'>用户详情<IconArrowUpRight /></a>中启用</>}
                        />
                    </Descriptions.Item>} */}
                    {/* {device && (device.os == 'windows' || device.os == 'macOS') && flynet?.rdpEnabled && device?.user?.rdpEnabled && <Descriptions.Item key="rdp" itemKey="远程桌面">{device?.rdpEnabled ? '启用' : '禁用'}</Descriptions.Item>} */}
                </Descriptions></Col>
                <Col xs={24} sm={6}>
                    <Descriptions className={styles.machineDetail}>
                        {connectDetails.map(item => {
                            return <Descriptions.Item key={item.key} itemKey={item.displayName}><Paragraph>{item.value}</Paragraph></Descriptions.Item>
                        })}
                    </Descriptions></Col>
            </Row>

        </div>
    </Skeleton>
        
    </>
}

export default Index