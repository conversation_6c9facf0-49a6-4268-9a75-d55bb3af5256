// 子网显示样式
.subnet {
    border-radius: var(--semi-border-radius-medium);
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-fill-0);
    padding: 20px;
}

.machineDetail {
    // box-shadow: var(--semi-shadow-elevated);
    background-color: var(--semi-color-bg-2);
    border-radius: 4px;
    padding: 10px;
    flex-grow: 1;
}

.heading {
    display: flex;
    align-items: flex-start;
}
.headingBadge {
    display: flex;
    width: 12px;
    height: 12px;
    margin-top: 10px;
    margin-left: 4px;
}
.generalInfo {
    margin-bottom: 40px;
    tr:last-of-type {
        padding-left: 10px;
        border-left: 1px solid var(--semi-color-border);
    }
}
.generalInfoNoStatus{
    margin-bottom: 40px;
    tr:last-of-type {
        padding-left: 11px;
    }
}