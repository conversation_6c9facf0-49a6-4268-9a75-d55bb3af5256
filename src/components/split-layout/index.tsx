import React, { useRef, useState, FC, ReactNode, useEffect } from 'react';
import { IconChevronLeft, IconChevronRight } from '@douyinfe/semi-icons';
import { Button } from '@douyinfe/semi-ui';

import styles from './index.module.scss';

// dom 元素使用的三个变量
let _isResizing = false;
let _initialX = 0;
// 滚动开始位置
let _scrollStartWidth = 0;
let _sideBarWidth = 0;

const getStoredWidth = () => {
    const storedWidth = localStorage.getItem('sideBarWidth');
    
    if (storedWidth || storedWidth === '0') {
        return parseInt(storedWidth);
    }
    return -1;
}
const setStoredWidth = (width: number) => {
    
    localStorage.setItem('sideBarWidth', width.toString());
}

const Index: FC<{
    sideBarWidth?: number,
    sideBar: ReactNode,
    children: ReactNode,
    sideBarVisible: boolean,
    minLeftWidth?: number,
}> = (props) => {

    let initWidth = props.sideBarWidth;

    if (getStoredWidth() >= 0) {
        initWidth = getStoredWidth();
    }

    if (initWidth != 0 && !initWidth) {
        initWidth = 300;
    }

    _sideBarWidth = initWidth;

    const { sideBar, children } = props;

    const [sideBarVisible, setSideBarVisible] = useState(props.sideBarVisible);
    // useEffect(() => {
    //     // setSideBarVisible(props.sideBarVisible)
    //     // console.log('sideBarVisibledddd', props.sideBarVisible)
    // }, [props.sideBarVisible])


    const leftColumnRef = useRef<HTMLDivElement>(null);
    const splitterRef = useRef<HTMLDivElement>(null);
    const rightColumnRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    const leftSplitButton = useRef<HTMLSpanElement>(null);
    const rightSplitButton = useRef<HTMLSpanElement>(null);

    const startResize = (e: React.MouseEvent) => {
        console.log('startResize')
        if (!sideBarVisible) {
            return;
        }
        if (!leftColumnRef.current || !rightColumnRef.current || !containerRef.current || !splitterRef.current) {
            return;
        }
        _isResizing = true;
        _initialX = e.clientX;
        _scrollStartWidth = leftColumnRef.current.offsetWidth;


        document.addEventListener("mousemove", resize);
        document.addEventListener("mouseup", stopResize);

        e.preventDefault();
    };

    const resize = (e: MouseEvent) => {
        console.log('resize')
        if (!leftColumnRef.current ||
            !rightColumnRef.current ||
            !containerRef.current ||
            !splitterRef.current) {
            return;
        }
        if (!_isResizing) return;

        const offsetX = e.clientX - _initialX;
        const newLeftWidth = _scrollStartWidth + offsetX;


        leftColumnRef.current.style.width = newLeftWidth + "px";


        requestAnimationFrame(() => {
            if (!leftColumnRef.current || !rightColumnRef.current || !containerRef.current || !splitterRef.current) {
                return;
            }
            const containerRect = containerRef.current.getBoundingClientRect();
            const newRightWidth = containerRect.width - newLeftWidth;
            rightColumnRef.current.style.width = newRightWidth + "px";
        });
    };

    const stopResize = () => {
        console.log('stopResize')
        _isResizing = false;

        if (leftColumnRef.current) {
            setStoredWidth(leftColumnRef.current.offsetWidth);
            if (leftColumnRef.current.offsetWidth > 0) {
                _sideBarWidth = leftColumnRef.current.offsetWidth;
            }

        }
        document.removeEventListener("mousemove", resize);
        document.removeEventListener("mouseup", stopResize);
    };

    const doHideSideBar = () => {
        if (!leftColumnRef.current || !rightColumnRef.current || !containerRef.current || !splitterRef.current) {
            return;
        }
        setStoredWidth(0);
        leftSplitButton.current?.style.setProperty('display', 'none');
        rightSplitButton.current?.style.setProperty('display', 'block');

        leftColumnRef.current.style.width = '0px';
        rightColumnRef.current.style.width = '100%';
    }
    const doShowSideBar = () => {
        if (!leftColumnRef.current || !rightColumnRef.current || !containerRef.current || !splitterRef.current) {
            return;
        }
        leftSplitButton.current?.style.setProperty('display', 'block');
        rightSplitButton.current?.style.setProperty('display', 'none');
        if (_sideBarWidth <= 0) {
            _sideBarWidth = 300;
        }
        setStoredWidth(_sideBarWidth);
        leftColumnRef.current.style.width = _sideBarWidth + "px";
        rightColumnRef.current.style.width = `calc(100% - ${_sideBarWidth}px)`;
    }

    useEffect(() => {
        if (!leftColumnRef.current || !rightColumnRef.current || !containerRef.current || !splitterRef.current) {
            return;
        }

        if (_sideBarWidth == 0) {
            console.log('effect hide')
            doHideSideBar();
        }
    }, [])
    if (!sideBarVisible) {
        return <>{children}</>
    }
    return <div className={styles.container} ref={containerRef}>

        <div className={styles.leftColumn} style={{ width: initWidth }} ref={leftColumnRef}>
            {sideBar}
        </div>
        <div className={styles.splitter} ref={splitterRef} onMouseDown={startResize}>
            <span ref={rightSplitButton} style={{ display: 'none' }}>

                <Button
                    size='small'
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => doShowSideBar()}
                    icon={<IconChevronRight />}
                    className={styles.toggleNavIconRight} />
            </span>
            <span ref={leftSplitButton} style={{ display: 'block' }}>

                <Button
                    size='small'
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => doHideSideBar()}
                    icon={<IconChevronLeft />}
                    className={styles.toggleNavIconLeft} />
            </span>
        </div>
        <div className={styles.rightColumn} ref={rightColumnRef}>
            {children}
        </div>
    </div>

}

export default Index;