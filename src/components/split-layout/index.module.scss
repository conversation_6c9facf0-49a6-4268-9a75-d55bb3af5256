
/* 设置容器和左右列的样式 */
.container {
    display: flex;
    height: 100%;
    width: 100%;
    border-radius: var(--semi-border-radius-small);
    border: 1px solid var(--semi-color-border);
}

.leftColumn {
    flex-shrink: 0;
    overflow: auto;
    transition: width 0.1s;
}

.rightColumn {
    flex-grow: 1;
    overflow: auto;
    transition: width 0.1s;
    padding: 10px 10px 10px 20px;
}
/* 设置中分线的样式 */
.splitter {
    position: relative;
    width: 4px;
    cursor: col-resize;
    flex-shrink: 0;
    // background-color: var(--semi-color-fill-0);
    background-color: rgba(var(--semi-grey-1), 1)!important;
}

.toggleNavIconLeft {
    position: absolute;
    z-index: 1;
    left: 4px;
    top: 8px;
    width: 16px;
    height: 32px;
    background-color: rgba(var(--semi-grey-1), 1)!important;
    border-top-right-radius: 60px!important;
    border-bottom-right-radius: 60px!important;
    // background-color: var(--semi-color-fill-0);
}

.toggleNavIconRight {
    position: absolute;
    z-index: 1;
    left: 4px;
    top: 8px;
    width: 16px;
    height: 32px;
    border-top-right-radius: 60px!important;
    border-bottom-right-radius: 60px!important;
    background-color: rgba(var(--semi-grey-1), 1)!important;
    // background-color: var(--semi-color-fill-0);
}