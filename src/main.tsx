import ReactDOM from 'react-dom/client'

import { BASE_PATH } from '@/constants/router';
import loader from '@monaco-editor/loader'
import App from './App'
import './index.scss'
import './index-mobile.scss'

import { initChartTheme } from './utils/charts'

// monaco-editor 配置，设置loader路径为本地加载
loader.config({
  paths: {
      vs: `${BASE_PATH}/public/vs`,
  },
});

// 初始化echarts主题
initChartTheme()

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <App />,
)
