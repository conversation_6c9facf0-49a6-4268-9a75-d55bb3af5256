
// 动态表单
export interface AccountSchema {
    name: string,
    type: 'number' | 'string' | 'boolean' | 'array' | 'object',
    title: string,
    description?: string,
    format?: 'date' | 'date-time' | 'time' | 'email' | 'hostname' | 'ipv4' | 'ipv6' | 'uri' | 'password',
    properties?: { [key: string]: AccountSchema }
    "ory.sh/kratos"?: {
        credentials: {
            password: {
                identifier: boolean
            },
            webauthn: {
                identifier: boolean
            },
            token: {
                identifier: boolean
            }
        },
        verification: {
            via: string
        },
        recovery: {
            via: string
        }
    },
    required?: boolean,
    minLength?: number,
    maxLength?: number,
    pattern?: string, // 正则表达式
    items?: {
        type?: 'number' | 'string',
        enum?: string[]
    }[]
    sys?: boolean, // 是否系统字段
}
