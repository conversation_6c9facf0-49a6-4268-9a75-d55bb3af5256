import { User, UserGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/users_pb';
import { Machine, MachineGroup } from '@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb';


// 服务范围设置
export interface ServiceRange {
    id: bigint
    // 服务范围
    name: string;
    // 别名
    alias: string;
    // 服务范围描述
    description: string;
    // 是否全局
    global: boolean;
    // 用户组
    userGroups: UserGroup[];
    // 用户
    users: User[];
    // 设备组
    machineGroups: MachineGroup[];
    // 设备
    machines: Machine[];
}
