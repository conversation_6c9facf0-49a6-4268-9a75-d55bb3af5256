
// 动态表单
export interface DynamicForm {
    type: 'number' | 'string' | 'boolean' | 'array' | 'object',
    description: string,
    title: string,
    format?: 'date' | 'date-time' | 'time' | 'email' | 'hostname' | 'ipv4' | 'ipv6' | 'uri' | 'password',
    properties: { [key: string]: DynamicForm }
    required?: string[],
    minLength?: number,
    maxLength?: number,
    pattern?: string, // 正则表达式
    items?: {
        type?: 'number' | 'string',
        enum?: string[]
    }[]
}
