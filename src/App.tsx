import { useState, useMemo } from 'react'
import { LocaleProvider } from '@douyinfe/semi-ui'
import { IntlProvider } from 'react-intl'
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom'
import zh_CN from '@douyinfe/semi-ui/lib/es/locale/source/zh_CN'
import en_GB from '@douyinfe/semi-ui/lib/es/locale/source/en_GB'

import { localeConfig } from './locales'
import RenderRouter from './router'

import RouterScrollTop from './components/router-scroll-top'
import {
	isAndroid,
	isIOS,
} from 'react-device-detect';
import { LocaleContext } from './hooks/useLocale'


function App() {

	// 手机端样式
	if (isAndroid || isIOS) {
		document.body.classList.add('app-mobile');
	}

	// 从localStorage获取保存的语言设置，默认为中文
	const [locale, setLocale] = useState(() => {
		return localStorage.getItem('app-locale') || 'zh_CN'
	})

	// 语言切换函数
	const switchLocale = (newLocale: string) => {
		setLocale(newLocale)
		localStorage.setItem('app-locale', newLocale)
	}

	const getLocale = useMemo(() => {
		if (locale === 'en_GB') {
			return en_GB
		} else if (locale === 'zh_CN') {
			return zh_CN
		}
	}, [locale])

	// Context值
	const localeContextValue = useMemo(() => ({
		locale,
		switchLocale
	}), [locale])

	return (
		<div className={isAndroid || isIOS ? 'app-mobile' : 'app'}>
			<LocaleContext.Provider value={localeContextValue}>
				<LocaleProvider locale={getLocale}>
					<IntlProvider locale={locale.split('_')[0]} messages={localeConfig[locale]}>
						<BrowserRouter>
							<RouterScrollTop>
								<RenderRouter />
							</RouterScrollTop>
						</BrowserRouter>
					</IntlProvider>
				</LocaleProvider>
			</LocaleContext.Provider>
		</div>
	)
}

export default App
