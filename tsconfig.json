{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "baseUrl": ".", "allowJs": true, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": false, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["src/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}