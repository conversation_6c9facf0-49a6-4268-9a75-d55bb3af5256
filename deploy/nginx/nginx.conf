worker_processes auto;

daemon off;

events {
  worker_connections 65535;
  use epoll;
}

http {

  gzip  on;
  gzip_min_length 1k;
  gzip_buffers    16 64k;
  gzip_http_version 1.1;
  gzip_comp_level 4;
  gzip_types  text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
  gzip_vary on;

  include /etc/nginx/nginx-mime.types;

  server {
    listen 80;
    root /app;

    location /mesh {
      alias /app;
      # add_header Cache-Control no-cache;
      try_files $uri /index.html;
    }

  }
}
