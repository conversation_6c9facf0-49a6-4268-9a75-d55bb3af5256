{"$id": "https://example.com/person.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "title": "attribute", "description": "属性模板", "properties": {"input": {"type": "object", "title": "输入", "description": "输入", "properties": {"User": {"type": "string", "title": "用户", "description": "用户", "properties": {"Name": {"type": "string", "title": "名称", "description": "用户名称"}, "UserType": {"type": "string", "title": "用户类型", "description": "用户类型", "enum": ["service", "person"]}, "Roles": {"type": "string", "title": "角色", "description": "用户的角色", "enum": ["member", "admin"]}, "Disabled": {"type": "boolean", "title": "禁用状态", "description": "User disabled status"}, "CreatedAt": {"type": "object", "format": "date-time", "title": "创建时间", "description": "User creation timestamp"}, "UpdatedAt": {"type": "object", "title": "更新时间", "description": "更新时间", "format": "date-time"}, "ExpiredAt": {"type": "object", "title": "过期时间", "format": "date-time", "description": "Expiration timestamp"}, "Account": {"type": "string", "title": "账号", "description": "账号", "properties": {"Attrs": {"type": "object", "title": "属性", "description": "属性", "properties": {"departName": {"type": "string", "title": "部门", "description": "部门", "enum": ["产品部", "开发部", "测试部"]}, "extra": {"type": "object", "title": "三方数据", "properties": {"main_department": {"type": "number", "title": "部门编号"}, "position": {"type": "string", "title": "岗位名称"}}}}}, "DisplayName": {"type": "string", "title": "显示名称", "description": "显示名称"}, "LoginName": {"type": "string", "title": "登录名", "description": "登录名"}, "ExternalId": {"type": "string", "title": "外部账号ID", "description": "External account ID"}, "Disabled": {"type": "boolean", "title": "禁用状态", "description": "Account disabled status"}, "AvatarUrl": {"type": "string", "title": "头像URL", "description": "Account avatar URL"}, "Temporary": {"type": "boolean", "title": "临时用户状态", "description": "User temporary status"}, "CreatedAt": {"type": "object", "format": "date-time", "title": "创建时间", "description": "User creation timestamp"}, "ExpiredAt": {"type": "object", "title": "有效期", "description": "有效期", "format": "date-time"}, "UpdatedAt": {"type": "object", "title": "更新时间", "description": "更新时间", "format": "date-time"}}}}}, "Device": {"type": "object", "title": "设备", "description": "设备", "properties": {"Name": {"type": "string", "title": "设备名", "description": "设备名"}, "GivenName": {"type": "string", "title": "设备名称(重命名)", "description": "Given name of the machine"}, "AutoGeneratedName": {"type": "boolean", "title": "自动生成名称", "description": "Auto-generated name status"}, "Ephemeral": {"type": "boolean", "title": "临时状态", "description": "Ephemeral status"}, "RegisteredTags": {"type": "array", "title": "注册标签", "description": "List of tags"}, "Tags": {"type": "array", "title": "标签", "description": "List of tags"}, "KeyExpiryDisabled": {"type": "boolean", "title": "密钥过期禁用状态", "description": "Key expiry disabled status"}, "Authorized": {"type": "boolean", "title": "授权状态", "description": "Authorization status"}, "HostInfo": {"type": "object", "title": "设备属性", "properties": {"OS": {"type": "string", "title": "操作系统"}}}, "Endpoints": {"type": "array", "title": "端点", "description": "List of endpoints"}, "IPv4": {"type": "string", "title": "IPv4地址", "description": "IPv4 address"}, "IPv6": {"type": "string", "title": "IPv6地址", "description": "IPv6 address"}, "CreatedAt": {"type": "object", "format": "date-time", "title": "创建时间", "description": "User creation timestamp"}, "UpdatedAt": {"type": "object", "title": "更新时间", "description": "更新时间", "format": "date-time"}, "ExpiresAt": {"type": "object", "format": "date-time", "title": "创建时间", "description": "User creation timestamp"}, "LastSeen": {"type": "object", "title": "更新时间", "description": "更新时间", "format": "date-time"}, "CtrlRoutableIPsEnabled": {"type": "boolean", "title": "控制器可路由IP启用状态", "description": "Controller routable IPs enabled status"}, "ClientIP": {"type": "string", "title": "客户端IP", "description": "Client IP address"}, "Description": {"type": "string", "title": "描述", "description": "Description of the device"}, "Relay": {"type": "number", "title": "中继ID", "description": "Relay identifier"}, "MeshDisabled": {"type": "boolean", "title": "Mesh禁用状态", "description": "Mesh disabled status"}}}}}}}