{"$id": "https://example.com/person.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "title": "AclGroup", "description": "策略组", "properties": {"input": {"type": "object", "title": "输入", "description": "输入", "properties": {"Name": {"type": "string", "title": "名称", "description": "名称"}, "Description": {"type": "string", "title": "描述", "description": "描述"}, "Priority": {"type": "number", "title": "优先级", "description": "优先级"}, "Action": {"type": "string", "title": "动作", "enum": ["accept", "reject"]}, "Source": {"type": "object", "title": "源", "description": "源", "properties": {"All": {"type": "boolean", "title": "所有", "description": "是否所有条件都放行"}, "Ip": {"type": "string", "title": "IP", "description": "IP"}, "User": {"type": "string", "title": "用户", "description": "用户"}, "UserGroup": {"type": "string", "title": "用户组", "description": "用户组"}, "BuildInGroup": {"type": "string", "title": "内置用户组", "description": "内置用户组 0:自己 1:成员 2:互联网", "enum": ["autogroup:self", "autogroup:members", "autogroup:internet"]}, "Expressions": {"type": "array", "title": "表达式", "description": "表达式"}, "Tag": {"type": "string", "title": "标签", "description": "标签"}}}, "Target": {"type": "object", "title": "目标", "description": "目标", "properties": {"All": {"type": "boolean", "title": "所有", "description": "是否所有条件都放行"}, "Ip": {"type": "string", "title": "IP", "description": "IP"}, "IpGroup": {"type": "string", "title": "资源组", "description": "资源组"}, "User": {"type": "string", "title": "用户", "description": "用户"}, "UserGroup": {"type": "string", "title": "用户组", "description": "用户组"}, "BuildInGroup": {"type": "string", "title": "内置用户组", "description": "内置用户组 0:自己 1:成员 2:互联网", "enum": ["autogroup:self", "autogroup:members", "autogroup:internet"]}, "Expressions": {"type": "array", "title": "表达式", "description": "表达式"}, "Tag": {"type": "string", "title": "标签", "description": "标签"}}}}}}}