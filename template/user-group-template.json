{"$id": "https://example.com/person.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "title": "UserGroup", "description": "用户组", "properties": {"input": {"type": "object", "title": "输入", "description": "输入", "properties": {"DisplayName": {"type": "string", "title": "显示名称", "description": "显示名称"}, "LoginName": {"type": "string", "title": "登录名", "description": "登录名"}, "Role": {"type": "string", "title": "角色", "description": "角色", "enum": ["role:admin", "role:user"]}, "Connected": {"type": "boolean", "title": "在线状态", "description": "是否在线"}, "Disabled": {"type": "boolean", "title": "状态", "description": "是否禁用"}, "CreatedAt": {"type": "object", "title": "加入时间", "description": "加入时间", "format": "date-time"}, "LastSeen": {"type": "object", "title": "加入时间", "description": "加入时间", "format": "date-time"}, "Temporary": {"type": "boolean", "title": "临时用户", "description": "是否为临时用户"}, "Expired": {"type": "boolean", "title": "过期", "description": "是否过期"}, "ExpiredAt": {"type": "object", "title": "有效期", "description": "有效期", "format": "date-time"}}}}}