{"$id": "https://example.com/person.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "title": "DeviceGroup", "description": "设备组", "properties": {"input": {"type": "object", "title": "输入", "description": "输入", "properties": {"User": {"type": "object", "title": "用户", "description": "用户", "properties": {"LoginName": {"type": "string", "title": "登录名", "description": "登录名"}, "Alias": {"type": "string", "title": "显示名", "description": "显示名"}, "ExpiredAt": {"type": "object", "title": "有效期", "description": "有效期", "format": "date-time"}, "CreatedAt": {"type": "object", "title": "加入时间", "description": "加入时间", "format": "date-time"}, "MfaEnabled": {"type": "boolean", "title": "强制启用MFA", "description": "强制启用MFA"}, "Connected": {"type": "boolean", "title": "在线状态", "description": "是否在线"}, "RdpEnabled": {"type": "boolean", "title": "远程桌面是否开启", "description": "远程桌面是否开启"}, "UserGroup": {"type": "array", "title": "用户组", "description": "用户组"}, "Disabled": {"type": "boolean", "title": "状态", "description": "是否禁用"}, "Role": {"type": "string", "title": "角色", "description": "角色", "enum": ["role:admin", "role:user"]}}}, "MeshEnabled": {"type": "boolean", "title": "Mesh模式是否开启", "description": "Mesh模式是否开启"}, "DeviceGroup": {"type": "array", "title": "设备组", "description": "设备组"}, "MachineName": {"type": "string", "title": "设备名", "description": "设备名"}, "OsHostname": {"type": "string", "title": "主机名", "description": "主机名"}, "os": {"type": "string", "title": "操作系统", "description": "操作系统"}, "clentVersion": {"type": "string", "title": "客户端版本", "description": "客户端版本"}, "iPv4": {"type": "string", "title": "IPv4", "description": "IPv4"}, "iPv6": {"type": "string", "title": "IPv6", "description": "IPv6"}, "CreatedAt": {"type": "object", "title": "创建时间", "description": "创建时间", "format": "date-time"}, "LastSeen": {"type": "object", "title": "最近上线时间", "description": "最近上线时间", "format": "date-time"}, "Endpoint": {"type": "string", "title": "端点信息", "description": "端点信息"}, "KeyExpiry": {"type": "object", "title": "密钥过期时间", "description": "密钥过期时间", "format": "date-time"}, "RelayNode": {"type": "string", "title": "中继节点", "description": "中继节点"}, "RdpEnabled": {"type": "boolean", "title": "远程桌面是否开启", "description": "远程桌面是否开启"}}}}}