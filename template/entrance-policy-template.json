{"$id": "https://example.com/person.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "title": "Entrance Policy", "description": "网络准入策略", "properties": {"input": {"type": "object", "title": "输入", "description": "输入", "properties": {"Request": {"type": "object", "title": "请求", "properties": {"RealIP": {"type": "string", "title": "客户端IP"}, "Browser": {"type": "string", "title": "浏览器类型", "enum": ["Chrome", "Firefox", "Safari", "IE", "Edge"]}, "UpstreamTime": {"type": "number", "title": "上行延时", "description": "上行延时毫秒数"}, "time": {"type": "object", "title": "请求时间", "description": "请求时间", "format": "date-time"}, "SupportMethod": {"type": "array", "title": "支持方法", "description": "支持的请求方法", "enum": ["Get", "Post", "Put", "Delete"]}, "Tags": {"type": "array", "title": "标签", "description": "标签"}}}, "Device": {"type": "object", "title": "设备", "description": "设备", "properties": {"Name": {"type": "string", "title": "设备名", "description": "设备名"}}}, "User": {"type": "string", "title": "用户", "description": "用户", "properties": {"IsAdmin": {"type": "boolean", "title": "是否管理员", "description": ""}, "Name": {"type": "string", "title": "用户名", "description": "用户名"}, "LoginName": {"type": "string", "title": "登录名", "description": "登录名"}}}, "Account": {"type": "string", "title": "账号", "description": "账号", "properties": {"Attrs": {"type": "object", "title": "属性", "description": "属性", "properties": {"departName": {"type": "string", "title": "部门", "description": "部门", "enum": ["产品部", "开发部", "测试部"]}}}, "LoginCount": {"type": "number", "title": "登录次数", "description": "用户的总登录次数"}, "LoginName": {"type": "string", "title": "登录名", "description": "登录名"}}}}}}}