stages:
  - build-and-deploy

build-image-job:
  stage: build-and-deploy
  environment: production
  tags:
    - docker
    - fyy
  before_script:
    - echo "test message"
    - echo "@buf:registry=https://buf.build/gen/npm/v1" >> .npmrc
    - echo "//buf.build/gen/npm/v1/:_authToken=$BUF_TOKEN" >> .npmrc
  script:
    # - export https_proxy=http://**********:6152
    # - export http_proxy=http://**********:6152
    - npm config set registry https://registry.npmmirror.com
    - npm ci
    - npm run build
    # - docker login https://registry.fyy.dev -u $HARBOR_CRED_USERNAME -p $HARBOR_CRED_PASSWORD
    # - docker buildx build --platform linux/amd64,linux/arm64 -t registry.fyy.dev/console/console-feiyue-cloud:$CI_COMMIT_SHORT_SHA -f deploy/Dockerfile . --cache-from type=local,src=/tmp/console/console-feiyue-cloud --cache-to type=local,dest=/tmp/console/console-feiyue-cloud --push
    - docker login https://fyymagic-docker.pkg.coding.net/console -u $CODING_CONSOLE_DEPLOY_USERNAME -p $CODING_CONSOLE_DEPLOY_PASSWORD
    - docker buildx build --platform linux/amd64,linux/arm64 -t fyymagic-docker.pkg.coding.net/console/platform/console-feiyue-cloud:$CI_COMMIT_SHORT_SHA -f deploy/Dockerfile . --cache-from type=local,src=/tmp/console/console-feiyue-cloud --cache-to type=local,dest=/tmp/console/console-feiyue-cloud --push
    # - docker login registry.jihulab.com -u $DEPLOY_CRED_USERNAME -p $DEPLOY_CRED_PASSWORD
    # - docker buildx build --platform linux/amd64,linux/arm64 -t registry.jihulab.com/fyy/console/console-feiyue-cloud:$CI_COMMIT_SHORT_SHA -f deploy/Dockerfile . --cache-from type=local,src=/tmp/console/console-feiyue-cloud --cache-to type=local,dest=/tmp/console/console-feiyue-cloud --push

